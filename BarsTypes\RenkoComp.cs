#region Using declarations
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Gui.Tools;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
#endregion

//This namespace holds Bars types in this folder and is required. Do not change it.
namespace NinjaTrader.NinjaScript.BarsTypes
{
	public class RenkoComp : BarsType
	{
		private const string	VERSION = "*******";

		private double barMax = 0, barMin = 0;
		private bool debugOn = false;
		private DateTime startTime = DateTime.Now;
		private int lastBC = 0;
		private string dbg = "";

		protected override void OnStateChange()
		{
			// Note: 8681314 below is just meant as unique ID
			if (State == State.SetDefaults)
			{
				Description			= @"Comprehensive Renko with configurable overlap and side-by-side reversals. Body Size controls the bar size in ticks, Overlap controls how many ticks bars will overlap with the previous bar (0 for standard Renko), Side-By-Side (Tick=On, Percent=Off) makes reversals require a full 2x body size movement, and Minimum Timespan sets the minimum time between bars in milliseconds.";
				Name				= "RenkoComp";
				BarsPeriod			= new BarsPeriod { BarsPeriodType = (BarsPeriodType) 93977939, Value = 1000 };
				BuiltFrom			= BarsPeriodType.Tick;
				DaysToLoad			= 2;
				IsIntraday			= true;
			}
			else if (State == State.Configure)
			{
				Properties.Remove(Properties.Find("PointAndFigurePriceType", true));
				//Properties.Remove(Properties.Find("ReversalType", true));			// Use ReversalType for SideBySide (Tick=On, Percent=Off)
				Properties.Remove(Properties.Find("BaseBarsPeriodType", true));	// Used for ATR type (e.g. min, hour, etc)
				//Properties.Remove(Properties.Find("BaseBarsPeriodValue", true));	// Used for ATR number (e.g. 5 = 5 min)

				SetPropertyName("Value", "Body Size");
				SetPropertyName("Value2", "Overlap");
				SetPropertyName("ReversalType", "Side By Side = Percent");
				SetPropertyName("BaseBarsPeriodValue", "Minimum Timespan (MS)");
			}
		}

		public override int GetInitialLookBackDays(BarsPeriod barsPeriod, TradingHours tradingHours, int barsBack)
		{
			return 1; //replace with your bars logic
		}

		public override bool IsRemoveLastBarSupported { get { return true; } }
		
		protected override void OnDataPoint(Bars bars, double open, double high, double low, double close, DateTime time, long volume, bool isBar, double bid, double ask)
		{
			if (SessionIterator == null)
				SessionIterator = new SessionIterator(bars);

			bool isNewSession = SessionIterator.IsNewSession(time, isBar);
			if (isNewSession)
				SessionIterator.CalculateTradingDay(time, isBar);

			int brickTicks = bars.BarsPeriod.Value;
			int overlapTicks = bars.BarsPeriod.Value2;

			// Ensure overlap doesn't exceed brickTicks-1 & not negative
			overlapTicks = Math.Min(overlapTicks, brickTicks - 1);
			overlapTicks = Math.Max(overlapTicks, 0);

			// This is now hard-coded so I can use the input for ATR
			// Get the minimum time between bars -if ticks come in too fast, it will keep building the current bar
			int minMS = bars.BarsPeriod.BaseBarsPeriodValue;

			double tickSz = bars.Instrument.MasterInstrument.TickSize;	// Get the tick size

			// Check if SideBySide mode is enabled
			bool isSideBySide = bars.BarsPeriod.ReversalType == ReversalType.Tick;

			// Calculate the effective movement (advance) size. This is how far it will have
			// to move to make a new candle (in the same direction as prev).  To make an opposite
			// candle, it will have to move BrickSize + advanceSize, unless SideBySide is on.
			double overlapSize = overlapTicks * tickSz;
			double brickSize = brickTicks * tickSz;
			double advanceSize = brickSize - overlapSize;

			//Print($"brickTicks = {brickTicks}, brickSize = {brickSize}, overlapTicks = {overlapTicks}, overlapSize = {overlapSize}, advanceSize = {advanceSize}, isSideBySide = {isSideBySide}");

			
			
			
			
			// Set up debug limits
			/*
			//DateTime timeLimit = new DateTime(2025, 04, 23, 19, 30, 0);
			//debugOn = (bars.Count > 1500);//  &&  time < startTime);
			debugOn = false;
			if (lastBC != bars.Count)//  &&  bars.Count > 500)
			{
				debugOn = true;
				Output(dbg, bars.Count);
				lastBC = bars.Count;
			}
			dbg = "";
			*/
			
			
		
			
			
			//Print($"bars.Count = {bars.Count}, debugOn = {debugOn}"); // ~3062
			if (bars.Count == 0 || (bars.IsResetOnNewTradingDay && isNewSession))
			{
				// For the first bar, just add it as is
				barMax = close + brickSize;
				barMin = close - brickSize;

				AddBar(bars, close, close, close, close, time, volume);

				bars.LastPrice = close;
				return;
			}

			else // (bars.Count > 0)
			{
				double prvOpen, prvClose, curHigh, curLow, curOpen, o, h, l, c;
				long curVol = 0;
				prvOpen = prvClose = curHigh = curLow = 0;

				DateTime prvTime = DateTime.MinValue;
				int curIdx = bars.Count - 1;
				int prvIdx = bars.Count - 2;
				int prvBarDir = 0;
				if (prvIdx >= 0)
				{
					// Get the Open High, Low, Close, and Time of the previous bar
					prvOpen = bars.GetOpen(prvIdx);
					prvClose = bars.GetClose(prvIdx);
					prvBarDir = (prvOpen < prvClose) ? 1 : -1;
					prvTime = bars.GetTime(prvIdx);
				}

				DateTime curTime = bars.GetTime(curIdx);
				curOpen = bars.GetOpen(curIdx);
				curHigh = bars.GetHigh(curIdx);
				curLow = bars.GetLow(curIdx);
				curVol = bars.GetVolume(curIdx);
				int curBarDir = (curOpen < close) ? 1 : -1;
				
				//dbg += $"\n\nbars.Count = {bars.Count}           isSideBySide = {isSideBySide}";
				//dbg += $"\ntime = {time.ToString()}, close = {close}, curOpen = {curOpen}, curHigh = {curHigh}, curLow = {curLow},   barMin = {barMin}, barMax = {barMax}";
				//if (prvIdx >= 0)
				//	dbg += $"\nprvTime = {prvTime.ToString()}, prvBarDir = {prvBarDir}, prvOpen = {prvOpen}, prvClose = {prvClose}";

				// Check if price has exceeded the boundaries
				bool maxExceeded = bars.Instrument.MasterInstrument.Compare(close, barMax) >= 0;
				bool minExceeded = bars.Instrument.MasterInstrument.Compare(close, barMin) <= 0;
				bool timeExceeded = time.Subtract(prvTime).TotalMilliseconds >= minMS;

				// Defined Range Exceeded?
				if (timeExceeded && (maxExceeded || minExceeded))
				{
					// Get final prices to finish out the current bar
					c = maxExceeded ? barMax : barMin;
					h = maxExceeded ? c : curHigh;
					l = minExceeded ? c : curLow;
					
					// If !isSideBySide and we had a reversal, then the current bar has 
					// to be removed, and re-added, so we can change the open price.
					if (!isSideBySide  &&  prvBarDir != curBarDir  &&  prvBarDir != 0)
					{
						o = maxExceeded ? c - brickSize : c + brickSize;
						RemoveLastBar(bars);
						//dbg += $"\n        =============>>  REMOVED A BAR";
						AddBar(bars, o, h, l, c, curTime, curVol);
						//dbg += $"\n        =============>>  ADDED A BAR WITH O,H,L,C,T,V = ( {o}, {h}, {l}, {c}, {curTime.ToString()}, {curVol} )";

						// After a reversal, update the direction for boundary calculation
						curBarDir = maxExceeded ? 1 : -1;
					}
					else
					{
						//c = maxExceeded ? curOpen + brickSize : curOpen - brickSize;
						UpdateBar(bars, h, l, c, time, volume);
					}
					
					// Handle the possibility of making more than one bar at a time. Stopping 
					// this phenomenon of bricks all having the same time is the whole point 
					// of the time component (minMS).  But it is possible the user does not want 
					// the large bricks this can form and may set minMS==1 or some very low value, 
					// in which case we are likely to have multiple bricks form in an instant.
					//
					// Add empty bars to fill gap if price jumps
					/*
					while (close.ApproxCompare(renkoHigh) >= 0)
					{
						AddBar(bars, renkoHigh - offset, Math.Max(renkoHigh - offset, renkoHigh), Math.Min(renkoHigh - offset, renkoHigh), renkoHigh, time, 0);
						renkoLow	= renkoHigh - 2.0 * offset;
						renkoHigh	= renkoHigh + offset;
					}
					*/
					
					
					// The old bar is now 'closed out'; it does not change anymore
					// Now, define the open price of new bar (and H, L, C)
					//
					// For non-side-by-side, we always assume the next bar will be the same 
					// direction as previous one.  If not, it will have to be removed and 
					// re-added (above).  Which means O,H,L,C starts the same as side-by-sde
					if (maxExceeded)	// New bar upward
					{
						o = l = barMax - overlapSize;
						h = c = close;
					}
					else	// New bar downward
					{
						o = h = barMin + overlapSize;
						l = c = close;
					}

					// Add the new bar
					AddBar(bars, o, h, l, c, time, volume);
					curOpen = o;
					string newDir = maxExceeded ? "LONG" : "SHORT";
					//dbg += $"\n        =============>>  FINALIZED LAST {newDir} BAR, AND ADDED NEW ( {o},{h},{l},{c},  {time.ToString()} )";
	
					// IF side by side it is just curOpen +- bricksize.
					// BUT IF NOT, we have to look at prvBarDir and base on 1x or 2x from prvOpen
					//
					// Calculate the new bar boundaries
					// If side-by-side, it is simple:
					if (isSideBySide)
					{
						//dbg += $"\nisSideBySide, so simply add/subtract BrickSize";
						barMax = curOpen + brickSize;
						barMin = curOpen - brickSize;
					}
					else
					{
						if (maxExceeded)			// last bar was Long
						{
							barMax = curOpen + brickSize;
							barMin = curOpen - 2*advanceSize;
							//dbg += $"\nBar just-finalized is Long, curOpen = {curOpen}, brickSize = {brickSize}, 2*advanceSize = {2*advanceSize}.  Set barMax = {barMax}, barMin = {barMin}";
						}
						else if (minExceeded)		// last bar was Short
						{
							barMax = curOpen + 2*advanceSize;
							barMin = curOpen - brickSize;
							//dbg += $"\nBar just-finalized is Short, curOpen = {curOpen}, brickSize = {brickSize}, 2*advanceSize = {2*advanceSize}.  Set barMax = {barMax}, barMin = {barMin}";
						}
					}
				}
				else
				{
					// Current bar still developing
					h = close > curHigh ? close : curHigh;
					l = close < curLow ? close : curLow;
					UpdateBar(bars, h, l, close, time, volume);
				}
			}
			bars.LastPrice = close;
			//Output(dbg, bars.Count);
		}

		private void Output(string str, int cb)
		{
			PrintTo = PrintTo.OutputTab2;
			if (debugOn)
			{
				Print(str);
			}
		}

		public override void ApplyDefaultBasePeriodValue(BarsPeriod period)
		{
			//replace with any default base period value logic
		}

		public override void ApplyDefaultValue(BarsPeriod period)
		{
			period.Value = 12;							// Body Size Value
			period.Value2 = 0;							// Overlap (tick)
			period.ReversalType = ReversalType.Percent; // Default to SideBySide Off (use Tick for on)
			period.BaseBarsPeriodValue = 1000;			// Minimum Timespan
		}

		public override string ChartLabel(DateTime time)
		{
			return time.ToString("T", Core.Globals.GeneralOptions.CurrentCulture);
		}

		public override double GetPercentComplete(Bars bars, DateTime now)
		{
			return 1.0d; //replace with your bar percent logic
		}

	}
}
