
/// VERSION 2.0.0.3: 
/// 174 «   175 »  232 Φ   233 Θ   236 ∞   240 ≡   241 ±   242 ≥   243 ≤   248 °   249 ∙ 
#region USING_DECLARATIONS
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Net.Http;			/// Added for HttpClient	
using System.Net.Http.Headers;	/// Added for AuthenticationHeaderValue

using System.Linq;
using System.Runtime.CompilerServices;	/// For MemberCallerName
using System.Text;
//using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Gui.Tools;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.Indicators;
using NinjaTrader.NinjaScript.DrawingTools;
using NinjaTrader.NinjaScript.Indicators.VOLAREIndicators;
using NinjaTrader.NinjaScript.SharedPOC;
#endregion

/// This namespace holds Strategies in this folder and is required. Do not change it. 
namespace NinjaTrader.NinjaScript.Strategies
{
	/// This strategy looks for crosses of the 50-level line (e.g. 19450, 19350, 19250, etc).
	/// When it happens, it places a long limit order 14 points down at the 26 level.  If 
	/// price then rises above the 50-level, it cancels the order and starts over.  
	/// If instead, the 26-level is reached, the long trade opens, and sets first TP level 
	/// at the 33 level (28 ticks), and the second TP level back at the 50-level (96 ticks)
	/// Move to BreakEven and Trail are optional.
	/// 
	/// Also possible [optional] to trade shorts, just mirror image the entry level.
	/// Default StopLoss is 15 points (60 ticks)
	public class StraddlePOC1 : Strategy
	{
		private const string	VERSION = "*******";
		
		#region GLOBALS			
		
		/// HttpClient is intended to be instantiated once per application, rather than per-use
		private double 			dailyProfit = 0;
		private double			cAsk, pAsk, ppAsk = 0;
		private double			cBid, pBid, ppBid = 0;
		private	double			pendingPrice = 0;
		private	double			crossPrice = 0;
		private	double			lastATR = 0;

		private int				mult100 = 0;
		private int				price0 = 0;
		private int				price100 = 0;
		private int				price50 = 0;
		private int				firstEntryBarIdx = -1;
		private int				firstEntryBarsAgo = -1;
		private int				lngQuantityTP2;
		private int				shtQuantityTP2;
		private int				activeSessionNum = 0;
		
		private string 			lastTradeDir = "None";
		private string			pendingDir = "None";
		private string			activeDir = "None";
		private string			maPriceDir = "None";
		private string			maSlopeDir = "None";
		private string			placeTrade = "None";
		private string			workingOrder = "None";
		
		private string 			lastDashboard = "";
		private string 			lastLogMsg = "";
		private string 			lastCaller = "";
		private string			chartInstrument;
		private string			logToFileName = "";
		private string			lastLogTime;
		private string			logPath = "";
		
		private bool			longOn = true;
		private bool			shortOn = true;
		private bool 			trailTriggered = false;
		private bool 			dailyLimitHit = false;
		private bool			userRequestedExit = false;
		private bool			inSession = true;
		private bool			inSessionChanged = false;
		private bool			isBreakeven = false;
		private bool			MoveToBEWDone = false;
		private bool			MoveToBELDone = false;
		private bool			pendingPlaced = false;	// Not used, as of version 9
		private bool			prvLongOn = true;
		private bool			prvShortOn = true;
		private bool			previouslyOn = true;
		private bool			dataLoadedOk = false;
		
		private Order			entryOrder = null;
		private Order			slOrder = null;
		private Order			tpOrder1 = null;
		private Order			tpOrder2 = null;
		private string			oco1, oco2;
		private bool			stopsSubmitted = false;
		
		private DateTime		startTime1, startTime2, startTime3, startTime4, startTime5;
		private DateTime		endTime1, endTime2, endTime3, endTime4, endTime5;
		private DateTime		closeTime, closeTime1, closeTime2, closeTime3, closeTime4, closeTime5;
		private DateTime		dbg1, dbg2;

			/// For indicators used
		private SignalType			signalType = SignalType.Percentage;
		private StraddlePOCSignal	volare;
		private StochasticsFast		stoch;
		private ATR 				Atr;
		
		private System.Windows.Controls.Button	shortButton, longButton, exitButton;
		private System.Windows.Controls.Grid	myGrid;
		#endregion
		
		/// https://ninjatrader.com/support/helpGuides/nt8/NT%20HelpGuide%20English.html?order.htm
		protected override void OnStateChange()
		{
			/// Set Input Defaults
			if (State == State.SetDefaults)
			{
				Description						= @"Strategy to automatically trade NQ Fifty Levels";
				Name							= "StraddlePOC1";
				LogToFileName					= Name;
				Calculate						= Calculate.OnBarClose;
				EntriesPerDirection				= 1;
				EntryHandling					= EntryHandling.AllEntries;
				IsExitOnSessionCloseStrategy	= true;
				ExitOnSessionCloseSeconds		= 300;		// 45 mins: Exit @ 13:15 PST
				IsFillLimitOnTouch				= false;
				MaximumBarsLookBack				= MaximumBarsLookBack.TwoHundredFiftySix;
				OrderFillResolution				= OrderFillResolution.Standard;
				Slippage						= 0;
				StartBehavior					= StartBehavior.WaitUntilFlat;
				TimeInForce						= TimeInForce.Gtc;
				RealtimeErrorHandling			= RealtimeErrorHandling.StopCancelClose;
				StopTargetHandling				= StopTargetHandling.ByStrategyPosition;
				BarsRequiredToTrade				= 20;
				IsUnmanaged 					= true;
				TraceOrders						= false;
				
				/// Disable this property for performance gains in Strategy Analyzer optimizations
				/// See the Help Guide for additional information
				IsInstantiatedOnEachOptimizationIteration	= true;
				
				
				/// Quantity of contract to trade
				TimeZoneOffset			= -3; 		// Everythig defaulted in east coast time
				
				TradeLong				= true;
				TradeShort				= true;
				LngQuantity				= 2;
				ShtQuantity				= 2;
				EntryLevel1				= 26;
				EntryLevel2				= 77;
				
				/// StopLoss & TakeProfit
				LngStopLoss				= 60;
				LngTakeProfit1			= 28;
				LngQuantityTP1			= 1;
				LngTakeProfit2			= 96;
				ShtStopLoss				= 60;
				ShtTakeProfit1			= 28;
				ShtQuantityTP1			= 1;
				ShtTakeProfit2			= 104;

				/// Daily Targets
				DailyMinTarget			= 0;		
				DailyMaxLoss			= 0;
				
				/// Move to BreakEven
				UseBreakeven			= true;
				BEW_ActivTicks			= 16;
				BEW_Offset				= 1;
				BEL_ActivTicks			= -40;
				BEL_Offset				= -12;
				
				/// Trailing
				UseTrailingStop 		= true;
				LngActivationTicks 		= 60;
				LngTrailTicks			= 30;
				ShtActivationTicks 		= 60;
				ShtTrailTicks			= 30;
				
				PeriodD					= 3;
				PeriodK					= 3;
				UpperLevel				= 80;
				LowerLevel				= 20;

				SigType					= SignalType.Percentage;
				Percentage1				= 50;
				Percentage2				= 10;
				OpensX_Ticks			= 2;
				ClosesX_Ticks			= 2;
				
				/// ATR Filter
				UseATR					= true;
				MinutesATR				= 2;
				PeriodATR				= 5;
				DisableOnMaxATR			= 50;
				
				/// Min Volume Filter
				AveVolumePeriod 		= 14;
				MinAveVolume			= 1;
				
				/// Trading session days / hours
				TradeSunday				= true;
				TradeMonday				= true;
				TradeTuesday			= true;
				TradeWednesday			= true;
				TradeThursday			= true;
				TradeFriday				= true;
				
				UseSession1				= false;
				StartTime1				= DateTime.Parse("10:00 AM", System.Globalization.CultureInfo.InvariantCulture);
				EndTime1				= DateTime.Parse("03:30 PM", System.Globalization.CultureInfo.InvariantCulture);
				UseCloseTime1			= false;
				CloseTime1				= DateTime.Parse("03:50 PM", System.Globalization.CultureInfo.InvariantCulture);

				UseSession2				= false;
				StartTime2				= DateTime.Parse("09:30 AM", System.Globalization.CultureInfo.InvariantCulture);
				EndTime2				= DateTime.Parse("03:30 PM", System.Globalization.CultureInfo.InvariantCulture);
				UseCloseTime2			= false;
				CloseTime2				= DateTime.Parse("03:50 PM", System.Globalization.CultureInfo.InvariantCulture);

				UseSession3				= false;
				StartTime3				= DateTime.Parse("08:00 AM", System.Globalization.CultureInfo.InvariantCulture);
				EndTime3				= DateTime.Parse("09:30 AM", System.Globalization.CultureInfo.InvariantCulture);
				UseCloseTime3			= false;
				CloseTime3				= DateTime.Parse("03:50 PM", System.Globalization.CultureInfo.InvariantCulture);

				UseSession4				= false;
				StartTime4				= DateTime.Parse("10:00 AM", System.Globalization.CultureInfo.InvariantCulture);
				EndTime4				= DateTime.Parse("03:00 PM", System.Globalization.CultureInfo.InvariantCulture);
				UseCloseTime4			= false;
				CloseTime4				= DateTime.Parse("03:50 PM", System.Globalization.CultureInfo.InvariantCulture);

				UseSession5				= false;
				StartTime5				= DateTime.Parse("06:00 PM", System.Globalization.CultureInfo.InvariantCulture);
				EndTime5				= DateTime.Parse("08:00 AM", System.Globalization.CultureInfo.InvariantCulture);
				UseCloseTime5			= false;
				CloseTime5				= DateTime.Parse("03:50 PM", System.Globalization.CultureInfo.InvariantCulture);

				UseCloseTime			= false;
				CloseTime				= DateTime.Parse("03:50 PM", System.Globalization.CultureInfo.InvariantCulture);

				DisplayOCD				= true;
				StartingLine			= 2;
				PlotHist 				= true;
				ApplyDailyToHist		= false;
				StrategyName			= "Fifty Level";
				SwapPrices				= true;
				BP						= 5;
				DrawLines				= true;
				DisableLogging			= false;
				UseOutput2				= true;
				UniqueID				= "1";
			}
			
			/// Initialize Member Variables
			else if (State == State.Configure)
			{
				chartInstrument = this.Instrument.FullName;
				
				AddDataSeries(BarsPeriodType.Tick, 1);
				AddDataSeries(BarsPeriodType.Minute, MinutesATR);

				// globals dependent on inputs
				lngQuantityTP2 = LngQuantity - LngQuantityTP1;
				shtQuantityTP2 = ShtQuantity - ShtQuantityTP1;
				dbg1 = new DateTime(2024, 12, 6, 5, 42, 0);
				dbg2 = dbg1.AddMinutes(60);
			}
			
			else if (State == State.DataLoaded)
			{
				/// Set up file name to copy log to,since NT doesn't allow enough lines
				if (LogToFileName != "")
				{
					logPath = NinjaTrader.Core.Globals.UserDataDir + @"log\" + LogToFileName + @"\" + LogToFileName + "Log.txt";
					//Print($"                            logPath = {logPath}");
					if (System.IO.File.Exists(logPath))
					{
						try
						{
							System.IO.File.Delete(logPath);
						}
						catch (Exception ex)
						{
							Print("Could not delete log file (logPath): {ex.Message}");
						}
					}
				}
				else
					Print(@"   /  /  /  /  /  /  /  /  /  /  /   LogToFileName == null; No Log File will be made  \  \  \  \  \  \  \  \  \  \  \  \");

				/// /  /  /  /  /  /  /  /  /  /  /  /  /  /  /  /  /  /  /  /  /  /  /  /  /  /  /  /  /  /  /  /  /  /  /  /  /  /  /  /  /  /  /  /
				/// Minimum bars/ticks for stability (adjust as needed).  There was an issue where the whole 
				/// program crashed when I loaded only 80 bars (which was done to reduce the amount of 
				/// historical processing for troubleshooting).  It took a long time (w/Grok) to find, so I 
				/// have added this code to check there are enough ticks for the volumetric bars, and at 
				/// least ecit gracefully.
				int minRangeBars = Math.Max(PeriodD, PeriodK) + 5;	/// Enough for stoch and volumetric warm-up
				int avgTicksPerBar = 5000;							/// Guess based on brief NQ observation
				int minTicks = minRangeBars * avgTicksPerBar;	    /// Guessing 3000 (ave) per bar is enough
				
				Print($"Bars[0].Count = {BarsArray[0]?.Count ?? -1}, Ticks[1].Count = {BarsArray[1]?.Count ?? -1}");
				
				// Check if BarsArray[0] and BarsArray[1] are fully loaded before accessing GetTime
				if (BarsArray[0] == null  ||  BarsArray[0].Count <= 0  ||  BarsArray[1] == null  ||  BarsArray[1].Count <= 0)
				{
					Print($"Error: BarsArray[0] or BarsArray[1] not fully loaded. Bars[0].Count = {BarsArray[0]?.Count ?? -1}, Ticks[1].Count = {BarsArray[1]?.Count ?? -1}");
					dataLoadedOk = false;
					return;
				}
				
			    // Safely log the time range ONLY if tick data is available
			    if (BarsArray[1] != null && BarsArray[1].Count > 0)
			    {
			        try
			        {
			            Print($"Time range: First bar = {BarsArray[1].GetTime(0)}, Last bar = {BarsArray[1].GetTime(BarsArray[1].Count - 1)}");
			        }
			        catch (Exception ex)
			        {
			            Print($"Error accessing BarsArray[1].GetTime: {ex.Message}");
			            dataLoadedOk = false;
			            return;
			        }
			    }
			    else
			    {
			        Print("Tick data (BarsArray[1]) not loaded.");
			        dataLoadedOk = false;
			        return;
			    }

				if (BarsArray[0].Count < minRangeBars)
				{
					Print($"Error: Insufficient range bars loaded. Required: {minRangeBars}, Loaded: {BarsArray[0].Count}. Increase 'Bars to load' or use days.");
					dataLoadedOk = false;
					return;
				}
				if (BarsArray[1].Count < minTicks)
				{
					Print($"Error: Insufficient tick data loaded. Required: {minTicks}, Loaded: {BarsArray[1].Count}. Increase 'Bars to load' or use days.");
					dataLoadedOk = false;
					return;
				}
				
			    dataLoadedOk = true;
				
				/// LOad indicators
				volare		= StraddlePOCSignal(SigType, Percentage1, Percentage2, OpensX_Ticks, ClosesX_Ticks);
				stoch		= StochasticsFast(PeriodD, PeriodK);
				Atr			= ATR(BarsArray[2], PeriodATR);
				
				longOn		= TradeLong;
				shortOn		= TradeShort;

				startTime1 	= StartTime1.AddHours(TimeZoneOffset);
				endTime1	= EndTime1.AddHours(TimeZoneOffset);
				closeTime1	= CloseTime1.AddHours(TimeZoneOffset);

				startTime2 	= StartTime2.AddHours(TimeZoneOffset);
				endTime2	= EndTime2.AddHours(TimeZoneOffset);
				closeTime2	= CloseTime2.AddHours(TimeZoneOffset);

				startTime3 	= StartTime3.AddHours(TimeZoneOffset);
				endTime3	= EndTime3.AddHours(TimeZoneOffset);
				closeTime3	= CloseTime3.AddHours(TimeZoneOffset);

				startTime4 	= StartTime4.AddHours(TimeZoneOffset);
				endTime4	= EndTime4.AddHours(TimeZoneOffset);
				closeTime4	= CloseTime4.AddHours(TimeZoneOffset);

				startTime5 	= StartTime5.AddHours(TimeZoneOffset);
				endTime5	= EndTime5.AddHours(TimeZoneOffset);
				closeTime5	= CloseTime5.AddHours(TimeZoneOffset);

				closeTime	= CloseTime.AddHours(TimeZoneOffset);
			}
			
			/// Historical is called once the object begins to process historical data. This state is called once 
			/// when running an object in real-time. This object is called multiple times when running a backtest 
			/// optimization and the property IsInstantiatedOnEachOptimizationIteration is true (default behavior)
			else if (State == State.Historical)
			{
				if (ChartControl != null)
				{
					ChartControl.Dispatcher.InvokeAsync(() =>
					{
						CreateButtons();
					});
				}
			}
			
			/// Transition is called once as the object has finished processing historical data but before it starts to process realtime data.
			else if (State == State.Transition)
			{
				dailyProfit = 0;
				dailyLimitHit = false;
				/*
				if (entryOrder != null)
				{
					if (Position.MarketPosition != MarketPosition.Flat)
						ClosePosition("Transition");
					else
						CancelEntry();
					CancelAllStops();
					entryOrder = null;
				}
				Log($"                           --  State == Transition: Reset Daily Profit to ZERO & Closed any pending orders  --");
				*/
			}
			
			/// Realtime is called once when the object begins to process realtime data.
			else if (State == State.Realtime)
			{
				/// One time only, as we transition from historical to live, convert any old historical 
				/// order object references to the live order submitted to the real-time account
				/// This addresses the error: "Strategy XXX has been disabled because it attempted to 
				/// modify a historical order that has transitioned to a live order"
				if (entryOrder != null  &&  entryOrder.IsBacktestOrder)		entryOrder = GetRealtimeOrder(entryOrder);
				if (slOrder  != null  &&  slOrder.IsBacktestOrder)			slOrder  = GetRealtimeOrder(slOrder);
				if (tpOrder1 != null  &&  tpOrder1.IsBacktestOrder)			tpOrder1 = GetRealtimeOrder(tpOrder1);
				if (tpOrder2 != null  &&  tpOrder2.IsBacktestOrder)			tpOrder2 = GetRealtimeOrder(tpOrder2);
			
				Print("All historical orders converted to live orders.");
				Print("\n\n\n\n\n-=-  -=-  SWITCHING TO REAL TIME PROCESSING  -=-  -=-  -=-  -=-  -=-  -=-  -=-  -=-  -=-  -=-  -=-  -=-  -=-  -=-  -=-");
				Print("");	Print("");	Print("");	Print("");
			}
			
			else if (State == State.Terminated)
			{
				if (entryOrder != null)
					CancelEntry();
				if (ChartControl != null)
				{
					ChartControl.Dispatcher.InvokeAsync(() =>
					{
						DisposeButtons();
					});
				}
			}
		}


		protected override void OnExecutionUpdate(Cbi.Execution execution, string executionId, double price, int quantity, 
												  Cbi.MarketPosition marketPosition, string orderId, DateTime time)
		{
			bool log = true;
			
			Log($"Name = {execution.Name}, IsEntryStrategy = {execution.IsEntryStrategy},  OrderAction = {execution.Order.OrderAction},   marketPosition = {marketPosition},   quantity = {quantity},   Position.Quantity = {Position.Quantity},   price = {price}");
			
			// We advise monitoring OnExecution to trigger submission of stop/target orders instead of OnOrderUpdate()
			// since OnExecution() is called after OnOrderUpdate() which ensures your strategy has received the execution
			// which is used for internal signal tracking.
			
			int sl, tp1, tp2, qty1, qty2;
			double m, prc, avePrice = AvePrice();

			/// ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡
			/// Check the order state for both entry end exit
			string type = (execution.IsEntryStrategy) ? "        ENTRY" : "        EXIT";
			
			/// If part filled, I do nothing, and hope that this method gets called again with order state == filled. 
			/// If that never happens, not sure what is the "right" thing to do, or how to tell it never happened...
			if (execution.Order.OrderState == OrderState.PartFilled)
				Log(type + $" ORDER ONLY PARTIALLY FILLED; execution.Order.Filled = {execution.Order.Filled}, Position.Quantity = {Position.Quantity}; WAITING FOR RESLOUTION...");
			
			/// If the order was somehow cancelled, then we need to close the position if partially opened
			else if (execution.Order.OrderState == OrderState.Cancelled  ||  execution.Order.OrderState == OrderState.Rejected)
			{
				if (execution.Order.Filled > 0)
				{
					Log(type + $" ORDER CANCELLED AFTER PARTIAL FILL! dir = {marketPosition.ToString()}, execution.Order.Filled = {execution.Order.Filled}, so closing entire open position");
					ClosePosition("Partial Fill", marketPosition.ToString());
				}
				else
				{
					Log(type + $" ORDER CANCELLED; dir = {marketPosition.ToString()}, execution.Order.Filled = {execution.Order.Filled}; Doing Nothing");
					lastCaller = "";	/// reset so if this method is entered again before other log, reprint the function name
					return;
				}
			}
			else if (execution.Order.OrderState == OrderState.Filled)
				if (log) Log(type + $" ORDER FILLED (execution.Order.OrderState == OrderState.Filled); execution.Order.Filled = {execution.Order.Filled}, Position.Quantity = {Position.Quantity}");

			
			/// ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡
			/// IS ENTRY STRATEGY - Handle actions for execution.Order.OrderAction.Buy or SellShort
			if (execution.IsEntryStrategy)
			{
				if (execution.Order.OrderAction == OrderAction.Buy)
				{
					m = 1.0;
					sl = LngStopLoss;
					tp1 = LngTakeProfit1;
					tp2 = LngTakeProfit2;
					qty1 = LngQuantityTP1;
					qty2 = lngQuantityTP2;
				}
				else
				{
					m = -1.0;
					sl = ShtStopLoss;
					tp1 = ShtTakeProfit1;
					tp2 = ShtTakeProfit2;
					qty1 = ShtQuantityTP1;
					qty2 = shtQuantityTP2;
				}
	
				/// If this is the opening of a trade (limit hit), store bar index
				if (firstEntryBarIdx == -1)
				{
					firstEntryBarIdx = CurrentBar;
					if (log) Log($"                                         UNSET, so set  firstEntryBarsIdx = {firstEntryBarIdx}");
				}

				if (sl > 0  ||  tp1 > 0  ||  tp2 > 0)
				{
					if (entryOrder != null  &&  entryOrder == execution.Order)
					{
						//if (log) Log($"IsEntryStrategy = True, execution.Order.OrderState = {execution.Order.OrderState}");
						
						if  (execution.Order.OrderState == OrderState.Filled)
						{
							activeDir = (execution.Order.OrderAction == OrderAction.Buy) ? "Long" : "Short";
							pendingPlaced = false;
							pendingDir = "None";
							
							if (!stopsSubmitted)
							{
								/// This used to check that slOrder & tpOrders were null, because we were initially setting, 
								/// but it won't place the stops until order is filled, so no need to check for that. 
								var action = (marketPosition == MarketPosition.Long) ? OrderAction.Sell : OrderAction.BuyToCover;

								if (log) Log($"INIT PLACEMENT OF SL & TP; Action = {action.ToString()}");
								if (sl > 0)
								{
									prc = avePrice - m * sl * TickSize;
									if (log) Log($"Submitting sl for Qty ( {qty1} + {qty2} ) @ {prc} as sepearate orders");
									SubmitOrderUnmanaged(1, action, OrderType.StopMarket, qty1+qty2, 0, prc, "", "SL");
								}
								if (tp1 > 0)
								{
									prc = avePrice + m * tp1 * TickSize;
									if (log) Log($"Submitting tp1 for Qty {qty1} @ {prc}");
									SubmitOrderUnmanaged(1, action, OrderType.Limit, qty1, prc, 0, "", "TP1");
								}
								if (tp2 > 0)
								{
									prc = avePrice + m * tp2 * TickSize;
									if (log) Log($"Submitting tp2 for Qty {qty2} @ {prc}");
									SubmitOrderUnmanaged(1, action, OrderType.Limit, qty2, prc, 0, "", "TP2");
								}
								stopsSubmitted = true;
							}
							else
								if (log) Log($"execution.Order.OrderState == OrderState.Filled & stops already submitted; DOING NOTHING");
						}
					}
					else
						if (log) Log($"entryOrder = {entryOrder}, execution.Order.OrderState = {execution.Order.OrderState}");
				}
			}
			
			/// ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡
			/// IS EXIT STRATEGY - Handle actions for execution.Order.OrderAction.Sell or BuyToCover
			else 
			{
		        /// Use SystemPerformance.AllTrades to get the last closed trade
				/// Need to check this every time there is a close since this exits in 2 stages
		        if (SystemPerformance.AllTrades.Count > 0)
				{
		            var lastTrade = SystemPerformance.AllTrades[SystemPerformance.AllTrades.Count - 1];
		
		            /// Access trade details
					double pnl = lastTrade.ProfitCurrency;	// includes commission
					dailyProfit += pnl;
					if (log) Log("Last trade PnL: " + pnl.ToString("C") + " (including " + lastTrade.Commission.ToString("C") + " commission).  Daily Proft: " + dailyProfit.ToString("C"));
				}

				if (Position.Quantity > 0)
				{
					/// Adjust the number of contracts for SL if a TP just hit
					if (execution.Order.Name == "TP1"  ||  execution.Order.Name == "TP2")
					{
						ModifyStopLoss(0, Position.Quantity);
					}
				}
				else if (Position.MarketPosition == MarketPosition.Flat)
				{
					/// Cancel any stops that are left open. SL & TP2 OCO, but check all to be sure
					if (log) Log($"Position.Quantity == 0; cancelling stops");
					lastTradeDir = activeDir;
					activeDir = "None";
					MoveToBEWDone = MoveToBELDone = false;
					CancelAllStops();
					
					/// Reset some variables
					if (log) Log($"Position.Quantity == 0; RESETTING VARS & checking DailyMaxLoss & DailyMinTarget..");
					stopsSubmitted = false;
					trailTriggered = false;
					firstEntryBarIdx = -1;
					crossPrice = 0;
					entryOrder = slOrder = tpOrder1 = tpOrder2 = null;
	
					/// Check for daily profit/loss limits
					if (DailyMaxLoss > 0  ||  DailyMinTarget > 0)
					{
						if (State == State.Realtime  ||  ApplyDailyToHist)
						{
							if (log) Log($"DailyMinTarget = {DailyMinTarget}, DailyMaxLoss = ${DailyMaxLoss}");
//							if (log) Log($"pDaily = (${dailyProfit}), SystemPerformance.RealTimeTrades.TradesPerformance.NetProfit = (${SystemPerformance.RealTimeTrades.TradesPerformance.NetProfit})");
							if (DailyMaxLoss > 0  &&  -dailyProfit > DailyMaxLoss)
							{
								/// if (Position.Quantity == 0), Exiting should be unnecessary, but are 
								/// left in case we are reversing position, or otherwise 'just to be safe'
//								if (log) Log($"pDaily (${dailyProfit}) - NetProfit (${SystemPerformance.AllTrades.TradesPerformance.NetProfit}) > DailyMaxLoss (${DailyMaxLoss})");
								Log($"DAILY LOSS HIT - NEW TRADING DISABLED\n");
								dailyLimitHit = true;
							}
							if (DailyMinTarget > 0  &&  dailyProfit > DailyMinTarget)
							{
//								if (log) Log($"NetProfit (${SystemPerformance.AllTrades.TradesPerformance.NetProfit}) - dailyProfit (${dailyProfit}) > DailyMinTarget (${DailyMinTarget})");
								Log($"DAILY PROFIT HIT - NEW TRADING DISABLED\n");
								dailyLimitHit = true;
							}
						}
					}
				}
			}
		}
		
		
		protected override void OnOrderUpdate(Cbi.Order order, double limitPrice, double stopPrice, int quantity, 
											  int filled, double averageFillPrice, Cbi.OrderState orderState, 
											  DateTime time, Cbi.ErrorCode error, string comment)
		{
			bool log = false;

			/// This is a safety step to prevent errors from transitioning from hist to realtime.
			/// The orders are actually transitioned in OnStateChange()
			if (State == State.Realtime  &&  order != null  &&  order.IsBacktestOrder)
			{
				if (log) Log($"[GUARD] Blocked update from historical order '{order.Name}' in Realtime. OrderId = {order.OrderId}");
				return;
			}
			
			string name = order.Name;
			string action = order.OrderAction.ToString();
			
			if (order.OrderState == OrderState.CancelSubmitted  
			||  order.OrderState == OrderState.CancelPending  
			||  order.OrderState == OrderState.Cancelled  
			||  order.OrderState == OrderState.Rejected  
			||  order.OrderState == OrderState.Unknown)
			{
				if (order.OrderState == OrderState.Cancelled  ||  order.OrderState == OrderState.Rejected)
				{
					if (name == "Entry")
					{
						if (log) Log($"Entry Order rejected/cancelled, so set activeDir back to None");
						activeDir = "None";
					}

					if (log) Log($"order.Name = {name} : Setting Order variable to null because OrderState is {order.OrderState}, OrderAction = {action}, OrderType = {order.OrderType}, limitPrice = {limitPrice}");
					order = null;
				}
				else
				{
					if (log) Log($"order.Name = {name} : SKIPPING setting Order variable because OrderState is {order.OrderState}, OrderAction = {action}, OrderType = {order.OrderType}, limitPrice = {limitPrice}");
					lastCaller = "";	// reset so if this method is entered again before other log, reprint the function name
					return;
				}
				/// else set to null below
			}

			/// Assign Order objects here
			/// This is more reliable than assigning Order objects in OnBarUpdate, as the assignment 
			/// is not guaranteed to be complete if it is referenced immediately after submitting
			if (name == "Entry")
			{
				if (log) Log($"order.Name = {name}; Setting entryOrder, OrderAction = {action}, stopPrice = {stopPrice}, limitPrice = {limitPrice}");
				if (order != null)	entryOrder = order;
			}
			else if (name == "SL")
			{
				if (log) Log($"order.Name = {name}; Setting slOrder, price = {stopPrice}");
				if (order != null)	slOrder = order;
			}
			else if (name == "TP1")
			{
				if (log) Log($"order.Name = {name}; Setting tpOrder1, price = {limitPrice}");
				if (order != null)	tpOrder1 = order;
			}
			else if (name == "TP2")
			{
				if (log) Log($"order.Name = {name}; Setting tpOrder2, price = {limitPrice}");
				if (order != null)	tpOrder2 = order;
			}
			lastCaller = "";	// reset so if this method is entered again before other log, reprint the function name
		}

		protected override void OnBarUpdate()
		{
			if (CurrentBars[0] < BarsRequiredToTrade  ||  CurrentBars[1] < BarsRequiredToTrade)
			{
				if (State != State.Historical)
					Log($"STATE = {State.ToString()}; Number of bars (BiP[0] = {CurrentBars[0]} & BiP[1] = {CurrentBars[1]}) is less than BarsRequiredToTrade ({BarsRequiredToTrade}); Returning");
				lastCaller = "";	// reset so if this method is entered again before other log, reprint the function name
				return;
			}

			/// Don't waste time on OCD for historical
			if (State == State.Historical  &&  !PlotHist)
				return;
			
			
			/// Effectively change OnBarUpdate to OnPriceChange
			/// There is no need to run this function at all if price has not changed.
			/// Check Last for now, but convert to Bid/Ask check if I change cAsk/cBid back to Ask & Bid
			if (BarsInProgress == 1)
			{
				//cAsk = GetCurrentAsk();
				//cBid = GetCurrentBid();
				cBid = cAsk = Closes[1][0];	/// Tick price from 1-tick series
	
				if (cBid == pBid  &&  cAsk == pAsk)
					return;	// Skip duplicate ticks
			}
			
			
			
			
			/// *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *
			/// LIMIT HISTORICAL PROCESSING   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *
			DateTime cutoff1 = new DateTime(2025, 4, 3, 23, 0, 0);
			DateTime cutoff2 = new DateTime(2025, 4, 30, 0, 30, 0);
			if (State == State.Historical)
			{
				if (false)
				{
					if (Time[0] < cutoff1  ||  Time[0] > cutoff2)
						return;
				}
			}
			


		
			///  Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ
			/// BEGIN BAR PROCESSING   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ
			/// Third data series (only for ATR)
			if (BarsInProgress == 2  ||  lastATR == 0)
			{
				lastATR = Atr[1];
				//Log($"UPDATED lastATR to {lastATR}.   Atr[0] = {Atr[0]}, Atr[1] = {Atr[1]}, Atr[2] = {Atr[2]}");
			}
			
			/// Entered on close of each candle of chart TF where strategy is applied (first data series)
			#region CHART SERIES  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
			else if (BarsInProgress == 0)
			{
				//Log($"CHART SERIES, Time[0] = {Time[0].ToString()}");
				if (Bars.IsFirstBarOfSession)
				{
					/// This is not useful for us because we do not let the EA run 24/5 such that this would trigger at 6pm ET
					/// And more importantly, we do not do anthing with historical (yet); that is where this is important:
					/// to reset some daily things that will be done with running over historical bars.

					if (Position.MarketPosition == MarketPosition.Flat)
					{
						trailTriggered = false;
						firstEntryBarIdx = -1;
						pendingDir = "None";
						entryOrder = entryOrder = null;
						pendingPrice = 0;
						pAsk = pBid = 0;

						dailyProfit = 0;	//SystemPerformance.AllTrades.TradesPerformance.NetProfit;
						dailyLimitHit = false;
						Log($"\nNEW SESSION (DAY) - RESET EVERYTHING - Time[0] = {Time[0].ToString()}\n");
					}
					else
						Log($"\n\n\n\nSESSION STARTED AND POSITION NOT FLAT -SHOULD NEVER HAPPEN\n\n\n");
				}
				
				/// Close all trades at end of user-defined session
				if (IsCloseTime())
				{
					Log($"End of Session Close activated @ {DateTime.Now.ToString()}; Closing all open orders");
					if (Position.MarketPosition != MarketPosition.Flat)
						ClosePosition();
					else if (entryOrder != null)
					{
						CancelEntry();
						entryOrder = null;
					}
					activeSessionNum = 0;
				}

				/// If not in market and not in-session (or limit hit), no need to go further
				if (!inSession  ||  dailyLimitHit)
					if (Position.MarketPosition == MarketPosition.Flat)
						return;
				
				/// Set up how many bars ago was the entry candle
				firstEntryBarsAgo = (firstEntryBarIdx != -1) ? CurrentBar - firstEntryBarIdx : -1;
			}
			#endregion

			/// Entered on each tick (second data series)
			#region TICK SERIES - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
			else if (BarsInProgress == 1)
			{
				/// Check each tick whether we are in-session (when it can place new trades)
				inSessionChanged = inSession;
				inSession = TradingOkay();
				inSessionChanged = (inSessionChanged != inSession);
				
				/// Go no further if daily profit or loss reached - No need to reset lastCaller var
				if (dailyLimitHit  &&  Position.MarketPosition == MarketPosition.Flat)
					return;

				if (!inSession  &&  inSessionChanged)
				{
					string db = (entryOrder != null) ? " - CANCELLING ORDER" : "";
					Log($"NOW OUT OF SESSION{db}");
					if (entryOrder != null)
						CancelEntry();
				}
				
				
				#region ENTRY - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
				
				//if (Time[0] > dbg1  &&  Time[0] < dbg2)				Log($"     XXXXX      Time[0] = {Time[0].ToString("HH:mm:ss") + ", inSession = {inSession}");

				/// Do nothing if already in an active open trade
				if (Position.MarketPosition == MarketPosition.Flat)
				{
					if (inSession  &&  inSessionChanged)
						Log($"Now in session; checking for zero and fifty crosses...");

					else if (!inSession  &&  inSessionChanged)
					{
						Log($"Out of session; not checking for zero and fifty crosses...");
						pendingDir = "None";
						if (entryOrder != null)
						{
							Log($"entryOrder != null; canceling perding order");
							CancelEntry();
						}
					}
					
					/// If not in session, just update prev ask/bid and return
					if (!inSession)
					{
						pAsk = cAsk;
						pBid = cBid;
						return;
					}
					
					/// Get the status of whether we are already in a pending order
					ManageWorkingOrder();
					
					string logStr = "";
					string sess = (!inSession) ? "  (OutOfSession)" : "";
					double level100;
					bool placePendingOrder = false;
					
					/// Now that we have buy OR sell by crossing the 50 up/down AND the 100 up/down, it is not useful 
					/// to switch back end forth every time it dips over the line and back.  Therefore, set up a buffer 
					/// amount by which is has to cross.  Hard code (for now) to 20 ticks, or 5 points.
					/// 
					/// ACTUALLY NO, we don't want to lose the 50/100 line cross.  We need to make it ANOTHER condition, so crossing 50 down makes 
					/// pendingDir = Long, but then it has to cross the 45 before the pending order is placed.  And of course this all only needs 
					/// to be done for hook = off.
					
					/// For Long Limit Order - If this bar crossed the 50 or 100 level downward
					if (longOn  &&  pendingDir != "Long"  &&  pAsk != 0)
					{
						/// Which level to use?
						level100 = (price100 - cBid < cBid - price0) ? price100 : price0;
				
						/// Check for crossing the ( 50/100 - buffer ) area, to actually place the pending trade
						if (pendingDir == "Long50"  &&  Crossed(price50 - BP, true) == "down")
						{
							pendingDir = "Long";
							logStr = $"\n ----> LONG (HookOff): DOWNWARD CROSS OF ({price50 - BP}) 50-LEVEL to {cAsk} on Candle @ {Time[0].ToString("HH:mm:ss")};  pendingDir = {pendingDir} : Place Order";
							
							/// Set vars to place pending order below
							pendingPrice = price0 + EntryLevel1;
							placePendingOrder = true;
						}
						else if (pendingDir == "Long100"  &&  Crossed(level100 - BP, true) == "down")
						{
							pendingDir = "Long";
							logStr = $"\n ----> LONG (HookOff): DOWNWARD CROSS OF ({level100 - BP}) 100-LEVEL to {cAsk} on Candle @ {Time[0].ToString("HH:mm:ss")};  pendingDir = {pendingDir} : Place Order";
							
							/// Set vars to place pending order below
							pendingPrice = level100 - (100 - EntryLevel2);
							placePendingOrder = true;
						}
						
						/// Set up direction; don't care if it flips back and forth over the line, cause not actually placing pending order
						/// Crossed 50 downward?
						else if (EntryLevel1 > 0  &&  Crossed(price50, true) == "down")
						{
							pendingDir = "Long50";
							//if (State == State.Realtime)	Log($"\nLONG (HookOff): DOWNWARD CROSS OF ({price50}) 50-LEVEL  cAsk/pAsk: {cAsk}/{pAsk};  pendingDir = {pendingDir}, Status: WAITING...");
						}
						/// Crossed 100 downward?
						else if (EntryLevel2 > 0  &&  Crossed(level100, true) == "down")
						{
							pendingDir = "Long100";
							//if (State == State.Realtime)	Log($"\nLONG (HookOff): DOWNWARD CROSS OF ({level100}) 100-LEVEL  cAsk/pAsk: {cAsk}/{pAsk};  pendingDir = {pendingDir}, Status: WAITING...");
						}
					}
					
					/// For Short Limit Order - If this bar crossed the 50 or 100 level upward
					if (shortOn  &&  pendingDir != "Short"  &&  pBid != 0  &&  !placePendingOrder)
					{
						/// Which level to use?
						level100 = (price100 - cBid < cBid - price0) ? price100 : price0;
						
						/// Check for crossing the ( 50/100 + buffer ) area, to actually place the pending trade
						if (pendingDir == "Short100"  &&  Crossed(level100 + BP) == "up")
						{
							pendingDir = "Short";
							logStr = $"\n ----> SHORT (HookOff): UPWARD CROSS OF ({level100}) 100-LEVEL to {cBid}  on Candle @ {Time[0].ToString("HH:mm:ss")};  pendingDir = {pendingDir} : Place Order";
							
							/// Set vars to place pending order below
							pendingPrice = level100 + EntryLevel1;
							placePendingOrder = true;
						}
						else if (pendingDir == "Short50"  &&  Crossed(price50 + BP) == "up")
						{
							pendingDir = "Short";
							logStr = $"\n ----> SHORT (HookOff): UPWARD CROSS OF ({price50}) 50-LEVEL to {cBid}  on Candle @ {Time[0].ToString("HH:mm:ss")};  pendingDir = {pendingDir} : Place Order";
							
							/// Set vars to place pending order below
							pendingPrice = price0 + EntryLevel2;
							placePendingOrder = true;
						}

						/// Set up direction; don't care if it flips back and forth over the line, cause not actually placing pending order
						/// Crossed 100 upward?
						else if (EntryLevel1 > 0  &&  Crossed(level100) == "up")
						{
							pendingDir = "Short100";
							//if (State == State.Realtime)	Log($"\nSHORT (HookOff): UPWARD CROSS OF ({level100}) 100-LEVEL  cBid/pBid: {cBid}/{pBid};  pendingDir = {pendingDir}, Status: WAITING...");
						}
						/// Crossed 50 upward?
						else if (EntryLevel2 > 0  &&  Crossed(price50) == "up")
						{
							pendingDir = "Short50";
							//if (State == State.Realtime)	Log($"\nSHORT (HookOff): UPWARD CROSS OF ({price50}) 50-LEVEL  (cBid/pBid: {cBid}/{pBid};  pendingDir = {pendingDir}, Status: WAITING...");
						}
					}							

					/// If there is already an order in at the 'pendingPrice', then 
					/// don't bother with checking (mostly to avoid unnec. logging)
					if (placePendingOrder  &&  entryOrder != null)
					{
						if (entryOrder.OrderType == OrderType.Limit)
						{
							if ((entryOrder.OrderAction == OrderAction.SellShort  &&  pendingDir == "Short")
							||  (entryOrder.OrderAction == OrderAction.Buy  &&  pendingDir == "Long"))
							{
								if (entryOrder.LimitPrice == pendingPrice)
								{
									//Log($"     @$$%^%*****%^%$$@     TURNED OFF CALL TO PLACE ({entryOrder.OrderAction}) LIMIT ORDER - ALREADY EXISTS     @$$%^%*****%^%$$@");
									placePendingOrder = false;
								}
							}
						}
						if (placePendingOrder)
							Log(logStr);
					}							
					
					if (placePendingOrder)
					{
						if (TradeFiltersOkay(pendingDir))
						{
							//Log($"\ncrossPrice = {crossPrice} and pendingDir = {pendingDir}");
							int qty = (pendingDir == "Long") ? LngQuantity : ShtQuantity;
							Log($"PLACING {pendingDir} PENDING LIMIT ORDER @ {pendingPrice}");
							PlaceLimitOrder(pendingDir, pendingPrice, qty);
						}
						else
							Log($"FAILED TRADE FILTERS - NO LIMIT ORDER PLACED (HOOK OFF)");
					}
				}		// end is Flat
				#endregion

				#region CURRENTLY IN TRADE  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
				
				/// in a trade currently
				else
				{
					/// Reset these when live
					pendingDir = "None";		/// This is done in OnExecUpdate, but doesn't hurt to do again....
					pendingPrice = 0;
				
					#region BREAKEVEN (POS or NEG) - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
					int dir = (Position.MarketPosition == MarketPosition.Long) ? 1 : -1;
					
					double curPrice = (dir == 1) ? GetCurrentBid() : GetCurrentAsk();
					double m = (dir == 1) ? 1.0 : -1.0;
					
					int o1 = 0;	int o2 = 0;
			        double newSL, newTP, oldSL1, oldSL2, oldTP1, oldTP2;
			        newSL = newTP = oldSL1 = oldTP1 = oldTP2 = 0;
					
					/// Get existing SL if exists.
					/// OrderState.Working seems to not mean 'active', but that it's still in exchange queue. 'Accepted' means it's active.
					if (slOrder != null  &&  (slOrder.OrderState == OrderState.Working  ||  slOrder.OrderState == OrderState.Accepted))
						oldSL1 = slOrder.StopPrice;

					/// Get existing TP if exists.  It may not, or the two halves may have diff TPs (see below)
					if (tpOrder1 != null  &&  (tpOrder1.OrderState == OrderState.Working  ||  tpOrder1.OrderState == OrderState.Accepted))
						oldTP1 = tpOrder1.LimitPrice;
					if (tpOrder2 != null  &&  (tpOrder2.OrderState == OrderState.Working  ||  tpOrder2.OrderState == OrderState.Accepted))
						oldTP2 = tpOrder2.LimitPrice;

					/// ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   
					/// Check for positive move-to-breakeven (e.g. when up 7 points, move SL to BE + 1 point)
					if (UseBreakeven  &&  !MoveToBEWDone  &&  BEW_ActivTicks != 0)
					{
						if ((dir ==  1  &&  curPrice >= AvePrice() + BEW_ActivTicks*TickSize)
						||  (dir == -1  &&  curPrice <= AvePrice() - BEW_ActivTicks*TickSize))
						{
							newSL = AvePrice() + m*BEW_Offset*TickSize;
							Log($"POS BREAKEVEN: m = {m}, Ave Price = {AvePrice()}, curPrice = {curPrice}, set new SL to be {newSL}");
							
							if (newSL != 0  &&  m*newSL > m*oldSL1)
							{
								isBreakeven = true;
								ModifyStopLoss(newSL);
								MoveToBEWDone = true;
							}
						}
					}

					/// ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   
					/// Check for negative move-to-breakeven (e.g. when down 10 points, move TP to BE - 3 points)
					if (UseBreakeven  &&  !MoveToBELDone  &&  BEL_ActivTicks != 0)
					{
						if ((dir ==  1  &&  curPrice <= AvePrice() + BEL_ActivTicks*TickSize)
						||  (dir == -1  &&  curPrice >= AvePrice() - BEL_ActivTicks*TickSize))
						{
							newTP = AvePrice() + m*BEL_Offset*TickSize;
							Log($"NEG BREAKEVEN m = {m}, Ave Price = {AvePrice()}, set new TP to be {newTP}");
							
							if (newTP != 0)
							{
								/// We are only going to modify TakeProfit; if one or both 
								/// halves do not have an existing TP, they will be skipped
								/// HOWEVER, unlike the SL, we do not care what the old TP was...
								isBreakeven = true;
								if (tpOrder1 != null)
									ModifyTakeProfit(tpOrder1, newTP);
								if (tpOrder1 != null)
									ModifyTakeProfit(tpOrder2, newTP);
								MoveToBELDone = true;
							}
						}
					}
					#endregion

					#region TRAIL  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
					if (UseTrailingStop)
					{
						if (!trailTriggered)
						{
							if (dir == 1  &&  curPrice >= AvePrice() + LngActivationTicks*TickSize)
							{
								Log($"                                   TRAILING STOP TRIGGERED cause Bid ({curPrice}) >= Entry Price ({AvePrice()}) + activation ticks ({LngActivationTicks})");
								trailTriggered = true;
							}
							else if (dir == -1  &&  curPrice <= AvePrice() - ShtActivationTicks*TickSize)
							{
								Log($"                                   TRAILING STOP TRIGGERED cause Ask ({curPrice}) <= Entry Price ({AvePrice()}) - activation ticks ({ShtActivationTicks})");
								trailTriggered = true;
							}
						}
						if (trailTriggered)
						{
							/// Trail usually when first leg closes, but not nec -we may need to move both stops
							newSL = GetNewTrailPrice(Position.MarketPosition.ToString());
							if (newSL != 0)
							{
								Log($"                                   MODIFYING TRAILING STOPLOSS TO {newSL} (if SL is tighter)");
								if (slOrder != null  &&  m*newSL > m*oldSL1)
									ModifyStopLoss(newSL);
							}
						}
					}
					#endregion
				}
				#endregion
			}
			#endregion
			
			/// Don't waste time on OCD for historical
			if (State != State.Historical)
				ManageOCD();	// Update the On-Chart Display
			
			ppAsk = pAsk;
			ppBid = pBid;
			pAsk = cAsk;
			pBid = cBid;
			lastCaller = "";		/// reset so if this method is entered again before other log, reprint the function name
		}
		
		#region TRADE MANAGEMENT - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
		private void PlaceLimitOrder(string dir, double price, int qty, string name="Entry")
		{
			/// If we are already in a limit order, cancel or ignore
			if (entryOrder != null)
			{
				/// If a limit order is already active...
				if (entryOrder.OrderType == OrderType.Limit)
				{
					if ((entryOrder.OrderAction == OrderAction.SellShort  &&  dir == "Short")
					||  (entryOrder.OrderAction == OrderAction.Buy  &&  dir == "Long"))
					{
						if (entryOrder.LimitPrice == price)
						{
							Log($"Already in Pending {dir} Limit order @ {price} - Doing nothing");
							lastCaller = "";	/// reset so if this method is entered again before other log, reprint the function name
							return;
						}
						else
						{
							Log($"Already in Pending {dir} Limit order, but at diff price ({entryOrder.LimitPrice}, not requested price of {price}) - Cancelling old {entryOrder.OrderAction.ToString()} pending order");
							CancelEntry();
						}
					}
					else
					if ((entryOrder.OrderAction == OrderAction.SellShort  &&  dir == "Long")
					||  (entryOrder.OrderAction == OrderAction.Buy  &&  dir == "Short"))
					{
						Log($"REVREQUEST: Already in Pending {entryOrder.OrderAction.ToString()} Limit order ({entryOrder.Name}), but {dir} requested, so cancelling old order");
						CancelEntry();
					}
				}
			}
			Log($"Place limit order @ {price}: dir = {dir}, qty = {qty}, name = {name}");
			var action = (dir == "Long") ? OrderAction.Buy : OrderAction.SellShort;
			SubmitOrderUnmanaged(1, action, OrderType.Limit, qty, price, 0, "", "Entry");
			pendingPlaced = true;
			lastCaller = "";	// reset so if this method is entered again before other log, reprint the function name
		}
		
		private void PlaceStopOrder(string dir, double price, int qty, string name="Entry")
		{
			/// If we are already in a limit order, cancel or ignore
			if (entryOrder != null)
			{
				/// If a stop order is already active...
				if (entryOrder.OrderType == OrderType.StopMarket)
				{
					if ((entryOrder.OrderAction == OrderAction.SellShort  &&  dir == "Short")
					||  (entryOrder.OrderAction == OrderAction.Buy  &&  dir == "Long"))
					{
						if (entryOrder.StopPrice == price)
						{
							Log($"Already in Pending {dir} Stop order @ {price} - Doing nothing");
							lastCaller = "";	// reset so if this method is entered again before other log, reprint the function name
							return;
						}
						else
						{
							Log($"Already in Pending {dir} Stop order, but at diff price ({entryOrder.StopPrice}, not requested price of {price}) - Cancelling old {entryOrder.OrderAction.ToString()} pending order");
							CancelEntry();
						}
					}
					else
					{
						Log($"REVREQUEST: Already in Pending {entryOrder.OrderAction.ToString()} StopMarket order, but {dir} requested, so cancelling old order");
						CancelEntry();
					}
				}
			}
			Log($"Place stop order @ {price}: dir = {dir}, qty = {qty}, name = {name}");
			var action = (dir == "Long") ? OrderAction.Buy : OrderAction.SellShort;
			SubmitOrderUnmanaged(1, action, OrderType.StopMarket, qty, 0, price, "", "Entry");
			pendingPlaced = true;
			lastCaller = "";	// reset so if this method is entered again before other log, reprint the function name
		}
		
		private void PlaceMarketOrder(string dir, int qty, string name="Entry")
		{
			Log($"Placing market order, dir = {dir}, qty = {qty}, name = {name}");
			
			// THIS just gets in it's own way, and  will
			if (Position.MarketPosition != MarketPosition.Flat  &&  Position.MarketPosition.ToString() != dir)
			{
				Log($"Existing position is {Position.MarketPosition.ToString()}, so closing position first");
				ClosePosition("CloseOnRev");
				
				// Assume the close worked; need to reset this NOW because it's multi-threaded
				stopsSubmitted = false;
			}
			var action = (dir == "Long") ? OrderAction.Buy : OrderAction.SellShort;
			double price = (dir == "Long") ? GetCurrentAsk() : GetCurrentBid();
			Log($"Placing {dir} Market order named '{name}' for {qty} contract(s). Current price is: {price}");
			SubmitOrderUnmanaged(1, action, OrderType.Market, qty, 0, 0, "", name);
			lastCaller = "";	// reset so if this method is entered again before other log, reprint the function name
		}
		
		private void ModifyStopLoss(double newSL, int qty=0)
		{
			if (slOrder == null)
			{
				Log($"         WARNING WARNING Cannot move stoploss because slOrder == null");
				return;
			}
			
			/// Used only for logging
			double curPrice = (Position.MarketPosition == MarketPosition.Long) ? GetCurrentBid() : GetCurrentAsk();
			
			/// If no newSL specified, use whole position
			if (qty == 0)
				qty = Position.Quantity;
			else if (qty > Position.Quantity)
			{
				Log($"qty ({qty}) > open quantity; setting to {Position.Quantity}");
				qty = Position.Quantity;
			}
			
			/// If no newSL specified, we are just changing quantity
			if (newSL == 0)
			{
				if (slOrder.OrderState == OrderState.Accepted)
					newSL = slOrder.StopPrice;
				else
				{
					Log($"No new SL specified, so must use existing SL, but although orderSL != NULL, the Order State is not Accepted, so cannot get existing SL; Aborting modify");
					return;
				}
			}
			
			try
			{
				/// If there is no existing SL, we do NOT place a new one; we fail out
				if (slOrder != null)
				{
					string why = "(Unknown where this came from)";
					if (isBreakeven)			why = "(BreakEven)";
					else if (UseTrailingStop)	why = "(Trail)";
					//Log($"Moving SL for {qty} contract(s) to new price @ {newSL} {why}");
					
					ChangeOrder(slOrder, qty, 0, newSL);
					isBreakeven = false;
				}
			}
			catch (Exception ex)
			{
				Log($"Error changing SL to {newSL}.  Current price = {curPrice}");
				Log($"Error changing SL to {newSL}.  Current price = {curPrice}", LogLevel.Error);	/// Logs to console
			}
			lastCaller = "";	// reset so if this method is entered again before other log, reprint the function name
		}

		private void ModifyTakeProfit(Order orderTP, double newTP, int qty=0)
		{
			/// Nothing to do if no order specified
			if (orderTP == null)
			{
				Log($"         WARNING WARNING Cannot move takeprofit because order passed in == null");
				return;
			}
			
			/// Used only for logging
			double curPrice = (Position.MarketPosition == MarketPosition.Long) ? GetCurrentBid() : GetCurrentAsk();
			
			/// If no newSL specified, use whole position
			if (qty == 0)
			{
				if (Position.MarketPosition == MarketPosition.Long)
					qty = (orderTP == tpOrder2) ? lngQuantityTP2 : LngQuantityTP1;
				else
					qty = (orderTP == tpOrder2) ? shtQuantityTP2 : ShtQuantityTP1;
			}
			if (qty > Position.Quantity)
			{
				Log($"qty ({qty}) > open quantity; setting to {Position.Quantity}");
				qty = Position.Quantity;
			}

			try
			{
				/// An existing TP is required; we do NOT place a new one if it does not exist
				string why = (isBreakeven) ? "(Neg BreakEven)" : "(Unknown where this came from)";
				Log($"Moving TP for {qty} contract(s) to new price @ {newTP} {why}");
				
				ChangeOrder(orderTP, qty, newTP, 0);
				isBreakeven = false;
			}
			catch (Exception ex)
			{
				Log($"Error changing TP to {newTP}.  Current price = {curPrice}");
				Log($"Error changing TP to {newTP}.  Current price = {curPrice}", LogLevel.Error);	/// Logs to console
			}
			lastCaller = "";	// reset so if this method is entered again before other log, reprint the function name
		}

		private void ClosePosition(string name="Close", string dir="", int qty=0)
		{
			if (Position.MarketPosition == MarketPosition.Flat)
				return;
			
			if (qty == 0)	qty = Position.Quantity;
			if (dir == "")	dir = Position.MarketPosition.ToString();
			var action = (dir == "Long") ? OrderAction.Sell : OrderAction.BuyToCover;
			
			if (qty == Position.Quantity)
			{
				if (slOrder != null  ||  tpOrder2 != null)
				{
					Log($"Closing all contracts, and slOrder != null  ||  tpOrder2 != null, so Cancelling Stops");
					CancelAllStops();
				}
			}
			Log($"Closing Position: name = {name}, dir = {dir}, qty = {qty}");
			SubmitOrderUnmanaged(1, action, OrderType.Market, qty, 0, 0, "", name);
			lastCaller = "";	// reset so if this method is entered again before other log, reprint the function name
		}

		private void CancelEntry()
		{
			CancelOrder(entryOrder);
			entryOrder = null;
		}
		
		private void CancelAllStops()
		{
			if (slOrder != null)
			{
				//Log($"Cancelling slOrder");
				CancelOrder(slOrder);
				slOrder = null;
			}
			if (tpOrder1 != null)
			{
				//Log($"Cancelling tpOrder1");
				CancelOrder(tpOrder1);
				tpOrder1 = null;
			}
			if (tpOrder2 != null)
			{
				//Log($"Cancelling tpOrder2");
				CancelOrder(tpOrder2);
				tpOrder2 = null;
			}
			lastCaller = "";	// reset so if this method is entered again before other log, reprint the function name
		}
		#endregion
		
		#region HELPER FUNCTIONS - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
		private double AvePrice()
		{
			double avePrice = Position.AveragePrice;
			
			/// If ave price does not fall on tick boundary, round up
			int numTicks = (int)(avePrice / TickSize);
			if (numTicks * TickSize != avePrice)
			{
				double prc = Instrument.MasterInstrument.RoundToTickSize(avePrice);
				avePrice = prc;
			}
			return avePrice;
		}

		private bool ManageWorkingOrder()
		{
			/// Set 'workingOrder' variable, for display on OCD
			bool cancelled = false;
			workingOrder = "None";
			
			/// This is only for PENDING orders.  Set to None if we are in open order
			/// The OrderType check below should handle this, but... to be safe...
			if (Position.MarketPosition == MarketPosition.Flat)
			{
				if (entryOrder == null)
					return cancelled;
				
				//Log($"entryOrder.OrderState = {entryOrder.OrderState}        entryOrder.OrderType = {entryOrder.OrderType}");
				if (entryOrder.OrderState == OrderState.Accepted  ||  entryOrder.OrderState == OrderState.Working)
				{
					/// Name the order (for OCD)
					if (entryOrder.OrderType == OrderType.StopMarket)
					{
						if (entryOrder.OrderAction == OrderAction.Buy)				workingOrder = "LongStop";
						else if (entryOrder.OrderAction == OrderAction.SellShort)	workingOrder = "ShortStop";
					}
					else if (entryOrder.OrderType == OrderType.Limit)
					{
						if (entryOrder.OrderAction == OrderAction.Buy)				workingOrder = "LongLimit";
						else if (entryOrder.OrderAction == OrderAction.SellShort)	workingOrder = "ShortLimit";
					}
					if (workingOrder == "None")
						Log($"ERROR?: entryOrder is 'Working', but not a Stop or Limit order");
					
					/// Cancel of the Stop order if price goes too far.   Only hook uses stop orders.
					if (entryOrder.OrderType == OrderType.StopMarket  &&  crossPrice != 0)
					{
						Log($"entryOrder.OrderType == OrderType.StopMarket");
						/// (This runs from the Tick series chart (BiP == 1), so Close[0] is 'last' price)
						/// If OrderAction not Buy, we assume it is SellShort
						cancelled = (entryOrder.OrderAction == OrderAction.Buy) ? (Close[0] < crossPrice - CancelPoints) 
																				: (Close[0] > crossPrice + CancelPoints);
						if (cancelled)
						{
							Log($"Price ({Close[0]}) moved 'CancelPoints' ({CancelPoints}) beyond Hook entry trigger ({crossPrice}) w/o filling stop order; cancelling order");
							CancelOrder(entryOrder);
						}
					}
				}
			}
			return cancelled;
		}
		
		private string Crossed(double level, bool useAsk=false)
		{
			/// This is an expanded function to check if Bid/Ask crossed a given price level
			double cPrc  = (useAsk) ? cAsk : cBid;
			double pPrc  = (useAsk) ? pAsk : pBid;
			double ppPrc = (useAsk) ? ppAsk : ppBid;
			
			/// Cannot check for first two ticks, until prev are populated
			if (cPrc > 0  &&  pPrc > 0  &&  ppPrc > 0)
			{
				/// Maybe we do not need the prev prev...?  
				/// Theoretically, this could declare a cross and then on the next tick, declare another cross
				/// But because of how it is used above, I do not think that should hurt anything...
				/// The only way to really know if we can skip prev prev is to test and watch it closely.
				/// Probably a good thing to do once all other bugs are fixed.
				//if (cPrc > level  &&  pPrc <= level)
				if (cPrc > level  &&  (pPrc <= level  ||  ppPrc <= level))
				{
					//Log($"Current price ({cPrc}) > {level}  &&  Previous price ({pPrc}) <= {level}  --  Crossed Up");
					return "up";
				}
				//if (cPrc < level  &&  pPrc >= level)
				if (cPrc < level  &&  (pPrc >= level  ||  ppPrc >= level))
				{
					//Log($"Current price ({cPrc}) < {level}  &&  Previous price ({pPrc}) >= {level}  --  Crossed Down");
					return "down";
				}
			}
			return "no";
		}
		
		/// Return new trailing stoploss value, or zero if none/unchanged
		private double GetNewTrailPrice(string direction)
		{
			double oldSL = 0;
			double newSL = 0;
			if (Position.MarketPosition.ToString() != direction  ||  !trailTriggered)
				return newSL;
			
			/// Working seems to not mean 'active', but that it's still in exchange queue. Accepted means it's active.
			if (slOrder != null  &&  (slOrder.OrderState == OrderState.Working  ||  slOrder.OrderState == OrderState.Accepted))
			{
				oldSL = slOrder.StopPrice;
				//Log($"Old Stop Price = {oldSL}");
			}

			/// This is called from tick BiP, not per candle
			double price = (direction == "Short") ? GetCurrentAsk() : GetCurrentBid();
			double ticksToOldSL = 0;
			if (oldSL != 0)
				ticksToOldSL = (direction == "Short") ? (oldSL - price) / TickSize : (price - oldSL) / TickSize;

			/// ticksToOldSL is zero if there is no old SL
			int trailBy = 0;
			if (direction == "Long"  &&  ticksToOldSL > LngTrailTicks)
				trailBy = LngTrailTicks;
			else
			if (direction == "Short"  &&  ticksToOldSL > ShtTrailTicks)
				trailBy = ShtTrailTicks;
			
			if (trailBy > 0)
			{
				double M = (direction == "Short") ? -1.0 : 1.0;
				newSL = price - M*trailBy*TickSize;
				
				// Make sure its moving in the right direction
				if (direction == "Long"  &&  newSL <= oldSL)
					newSL = 0;
				else
				if (direction == "Short"  &&  newSL >= oldSL)
					newSL = 0;
			}
			//if (newSL != 0)	Log($"Simple trail on {direction} trade: OldSL = {oldSL}, NewSL = {newSL}; trailBy = {trailBy}");
			
			lastCaller = "";	// reset so if this method is entered again before other log, reprint the function name
			return newSL;
		}
		#endregion

		#region DEBUG & OCD  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
		private void ManageOCD()
		{
			string tmp, text = "";
			for (int i=0; i < StartingLine; i++)
				text += "\n";
			text += " Strategy Name :\t\t" + StrategyName + " v" + VERSION + ", ID: " + UniqueID;
			text += "\n Long Trade Enabled :\t" + longOn;
			text += "\n Short Trade Enabled :\t" + shortOn;
			text += "\n Trade Session Active :\t" + inSession + "\n";
			
			text += "\n Last Trade Direction :\t" + lastTradeDir;
			text += "\n Pending Direction :\t" + pendingDir;
			text += "\n Working Order :\t\t" + workingOrder;
			text += "\n Active Direction :\t\t" + activeDir;
			if (pendingDir != "None")
			{
				if (pendingPrice != 0)
					text += "\n Pending Price :\t\t" + pendingPrice;
			}
			
			if (UseTrailingStop  &&  Position.MarketPosition != MarketPosition.Flat)
				text += "\n\n Trail Triggered :\t\t" + trailTriggered + "\n";
			
			if (DailyMinTarget != 0  ||  DailyMaxLoss != 0)
			{
				text += "\n";
				if (DailyMinTarget > 0)
					text += "Daily Min Profit :\t\t\t\t" + DailyMinTarget.ToString("C0") + "\n";
				if (DailyMaxLoss > 0)
					text += "Daily Max Loss :\t\t\t\t" + DailyMaxLoss.ToString("C0") + "\n";
				text += "Daily Profit :\t\t\t\t\t" + dailyProfit.ToString("C0") + "\n";
				if (dailyLimitHit)
					text += "Daily Limit Hit :\t\t\t\tYes - Trading Disabled\n";
				else
					text += "Daily Limit Hit :\t\t\t\tNo\n";
			}
			
			text += "\n\n High Price 100 :\t\t" + price100;
			text += "\n Mid Price 50 :\t\t"   + price50;
			text += "\n Low Price 100 :\t\t"  + price0;

			text += "\n\n " + MinutesATR + "-Minute ATR(" + PeriodATR + ") :\t\t"  + lastATR.ToString("F2");

			if (lastDashboard == text)
				return;
			
			Draw.TextFixed(this, "OCD", text, TextPosition.TopLeft);	
			lastDashboard = text;
		}
		
		private void Log(string message, [CallerMemberName] string memberName = "")
		{
			if (DisableLogging  ||  lastLogMsg == message)
				return;

			/// Debug time filtering...
			//if (Time[0] < dbg1  ||  Time[0] > dbg2)	return;
			
			/// Set this scripts Print() calls to the second output tab?
			if (UseOutput2)		PrintTo = PrintTo.OutputTab2;
			else				PrintTo = PrintTo.OutputTab1;
			
			string dateStr = "";
			string id = StrategyName + " " + UniqueID;
			
			if (lastLogMsg != message)
			{
				DateTime date = (State == State.Historical) ? Time[0] : DateTime.Now;
				dateStr = (State == State.Historical) ? date.ToString() : date.ToString("HH:mm:ss");
				
				string header = $"\n{id} - {chartInstrument} - [{memberName}]  -  {dateStr}  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -\n";
			
				/// Output just time if diff time but not new caller
				string output = message;
				if (lastLogTime != dateStr  &&  lastCaller == memberName) 
					output = message + "   ( " + dateStr + " )";
					
				/// If it is a new caller, write header line
				if (lastCaller != memberName)
					LogToFile(header);
	
				LogToFile(output);
			}
			
			lastLogMsg = message;
			lastCaller = memberName;
			lastLogTime = dateStr;
		}
		
		private void LogToFile(string message)
		{
			/// Always print to the output window as well
			Print(message);
			//Print($"                                                                         logPath = {logPath}         LogToFileName = {LogToFileName}");
			
			/// Additionally logging to file is skipped if there is no file name
			if (logPath == "")
				return;

			/// Ensure the directory exists
			try
			{
				string folder = System.IO.Path.GetDirectoryName(logPath);
				if (!System.IO.Directory.Exists(folder))
					System.IO.Directory.CreateDirectory(folder);
			}
			catch (Exception ex)
			{
				Print($"LogToFile() error creating folder: {ex.Message}");
				return;
			}
			
			/// Append to the log file
			try
			{
				System.IO.File.AppendAllText(logPath, message);
			}
			catch (Exception ex)
			{
				Print($"LogToFile() error writing file: {ex.Message}");
			}

		}
		#endregion
		
		#region FILTERS  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
		/// Enter a trade [set] if all enabled filters are satisfied
		private bool TradeFiltersOkay(string direction)
		{
			if (direction != "Long"  &&  direction != "Short")
			{
				Log($"Direction passed into TradeFiltersOkay ({direction}) must be 'Long' or 'Short'; Aborting");
				return false;
			}
			
			string d = direction;
			if (d == "Long")
			{
				direction = "None";
				if (!longOn)
					Log($"Long trade disabled; Aborting entry");
				else if (Position.MarketPosition == MarketPosition.Long)
					Log($"Long trade already open; Aborting entry");
				else
					direction = "Long";
			}
			else if (d == "Short")
			{
				direction = "None";
				if (!shortOn)
					Log($"Short trade disabled; Aborting entry");
				else if (Position.MarketPosition == MarketPosition.Short)
					Log($"Short trade already open; Aborting entry");
				else
					direction = "Short";
			}
			
			if (direction == "None")
				return false;

			if (!TradingOkay())
			{
				Log($"Out of session; Aborting {direction} entry");
				return false;
			}
			
			double prc = direction == "Long" ? Lows[0][0] : Highs[0][0];
			if (UseATR  &&  DisableOnMaxATR > 0  &&  lastATR > DisableOnMaxATR)
			{
				Log($"ATR[{PeriodATR}] ({Atr[1]}) is too large (max {3333} points); Aborting {direction} entry");
				Draw.Diamond(this, "ATRFilter" + CurrentBar, false, 0, prc, Brushes.Cyan);
				return false;
			}
			
			if (VOLMA(Closes[0], AveVolumePeriod)[1] < MinAveVolume)
			{
				Log($"Volume insufficient ({VOLMA(Closes[0], AveVolumePeriod)[1]} vs. {MinAveVolume} minimum); Aborting {direction} entry");
				Draw.Diamond(this, "VolFilter" + CurrentBar, false, 0, prc, Brushes.HotPink);
				return false;
			}
			return true;
		}
		
		/// Check trading sessions / days; 
		private bool TradingOkay()
		{
			activeSessionNum = 0;
			
			switch (Time[0].DayOfWeek)
			{
				case DayOfWeek.Sunday: 		if (!TradeSunday)		return false;	else break;
				case DayOfWeek.Monday: 		if (!TradeMonday)		return false;	else break;
				case DayOfWeek.Tuesday: 	if (!TradeTuesday)		return false;	else break;
				case DayOfWeek.Wednesday: 	if (!TradeWednesday)	return false;	else break;
				case DayOfWeek.Thursday: 	if (!TradeThursday)		return false;	else break;
				case DayOfWeek.Friday: 		if (!TradeFriday)		return false;	else break;
				default:																 break;
			}		
			
			if (!(UseSession1  ||  UseSession2  ||  UseSession3  ||  UseSession4  ||  UseSession5))
				return true;
			
			if (UseSession1)
				if (CheckSession(1, startTime1, endTime1))
					return(true);
			if (UseSession2)
				if (CheckSession(2, startTime2, endTime2))
					return(true);
			if (UseSession3)
				if (CheckSession(3, startTime3, endTime3))
					return(true);
			if (UseSession4)
				if (CheckSession(4, startTime4, endTime4))
					return(true);
			if (UseSession5)
				if (CheckSession(5, startTime5, endTime5))
					return(true);
				
			return false;
		}
		
		/// Check one trading sessions; return false if out-of-session
		private bool CheckSession(int num, DateTime startTime, DateTime endTime)
		{
			bool okay = false; 
			if (startTime.TimeOfDay < endTime.TimeOfDay)
			{
				if (Time[0].TimeOfDay >= startTime.TimeOfDay  &&  Time[0].TimeOfDay < endTime.TimeOfDay)
					okay = true;
				//if (Time[0] > dbg1  &&  Time[0] < dbg2)	Log($"     XXXXX    SessionOkay = {okay}, Time[0].TimeOfDay = {Time[0].TimeOfDay.ToString()}, startTime.TimeOfDay = {startTime.TimeOfDay.ToString()}, endTime.TimeOfDay = {endTime.TimeOfDay.ToString()}");
			}
			else if (startTime.TimeOfDay > endTime.TimeOfDay)
			{
				if (Time[0].TimeOfDay >= startTime.TimeOfDay  ||  Time[0].TimeOfDay < endTime.TimeOfDay)
					okay = true;
			}
			else // (startTime.TimeOfDay == endTime.TimeOfDay)
			{
				Log($"Start Time ({startTime.TimeOfDay.ToString()}) is the same as End Time (endTime.TimeOfDay.ToString()); Trading (always) approved");
					okay = true;
			}
			
			if (okay)	activeSessionNum = num;
			return okay;
		}
		
		private bool IsCloseTime()
		{
			if (Position.MarketPosition == MarketPosition.Flat  &&  entryOrder != null)
				return false;

			/// If we are not using any trading session (trade all times), then just check the main close time
			if (!(UseSession1  ||  UseSession2  ||  UseSession3  ||  UseSession4  ||  UseSession5))
			{
				/// Close all trades at end of user-defined session				//  Use if we want to make sure this only fires once, RIGHT AT close time?
				if (UseCloseTime  &&  Time[0].TimeOfDay >= CloseTime.TimeOfDay) //  &&  Time[1].TimeOfDay < CloseTime1.TimeOfDay)
					return true;
				return false;
			}
			
			/// Otherwise, check be which session is active 
			/// Close all trades at end of user-defined session
			if (activeSessionNum == 1  &&  UseCloseTime1  &&  Time[0].TimeOfDay >= closeTime1.TimeOfDay)
				return true;
			if (activeSessionNum == 2  &&  UseCloseTime2  &&  Time[0].TimeOfDay >= closeTime2.TimeOfDay)
				return true;
			if (activeSessionNum == 3  &&  UseCloseTime3  &&  Time[0].TimeOfDay >= closeTime3.TimeOfDay)
				return true;
			if (activeSessionNum == 4  &&  UseCloseTime4  &&  Time[0].TimeOfDay >= closeTime4.TimeOfDay)
				return true;
			if (activeSessionNum == 5  &&  UseCloseTime5  &&  Time[0].TimeOfDay >= closeTime5.TimeOfDay)
				return true;
			return false;
		}
		#endregion
		
		#region BUTTONS - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
		private void OnMyButtonClick(object sender, RoutedEventArgs rea)
		{
			System.Windows.Controls.Button button = sender as System.Windows.Controls.Button;
			
			Log($"\n");
			bool lWasOn = longOn;
			bool sWasOn = shortOn;
			if (button.Name == "longButton")
			{
				if (longOn)
				{
					button.Background = Brushes.Gray;
					button.Content = "Long Disabled";
					longOn = false;
					Log($"\nLONG ENTRIES DISABLED!");
				}
				else
				{
					button.Background = Brushes.Green;
					button.Content = "Long Enabled";
					longOn = true;
					Log($"\nLong entries Enabled!");
				}
			}
			else if (button.Name == "shortButton")
			{
				if (shortOn)
				{
					button.Background = Brushes.Gray;
					button.Content = "Short Disabled";
					shortOn = false;
					Log($"\nSHORT ENTRIES DISABLED!");
				}
				else
				{
					button.Background = Brushes.Crimson;
					button.Content = "Short Enabled";
					shortOn = true;
					Log($"\nShort entries Enabled!");
				}
			}
			else if (button.Name == "exitButton")
			{
				if (Position.MarketPosition == MarketPosition.Flat)
				{
					Log($"\nUser-Instigated Close of All Open Strategy Positions; nothing open...");
					if (entryOrder != null)
					{
						Log($"...but cancelling entryOrder");
						CancelOrder(entryOrder);
					}
				}
				else
				{
					Log($"\n\n=====================================================");
					Log($"User-Instigated Close of All Open Strategy Positions");
					Log($"=====================================================\n");
					ClosePosition();
				}
			}
			
			if (!longOn  &&  !shortOn)
				if (lWasOn  ||  sWasOn)
					Log($"ALL ENTRIES DISABLED!");
			else if (longOn  &&  shortOn)
				if (!lWasOn  ||  !sWasOn)
					Log($"ALL ENTRIES ENABLED!");
			Log($"\n");
		}
		
		protected void CreateButtons()
		{
			if (UserControlCollection.Contains(myGrid))
		        return;
		
			myGrid = new System.Windows.Controls.Grid
			{
				Name = "MyCustomGrid",
				HorizontalAlignment = HorizontalAlignment.Left,
				VerticalAlignment = VerticalAlignment.Bottom,
			};
	 	 
	        myGrid.ColumnDefinitions.Add(new System.Windows.Controls.ColumnDefinition());
	        myGrid.ColumnDefinitions.Add(new System.Windows.Controls.ColumnDefinition());
	        myGrid.ColumnDefinitions.Add(new System.Windows.Controls.ColumnDefinition());
	        myGrid.ColumnDefinitions.Add(new System.Windows.Controls.ColumnDefinition());
	        myGrid.ColumnDefinitions.Add(new System.Windows.Controls.ColumnDefinition());
	        myGrid.ColumnDefinitions.Add(new System.Windows.Controls.ColumnDefinition());
	        myGrid.ColumnDefinitions.Add(new System.Windows.Controls.ColumnDefinition());
	        myGrid.ColumnDefinitions.Add(new System.Windows.Controls.ColumnDefinition());
	        myGrid.ColumnDefinitions.Add(new System.Windows.Controls.ColumnDefinition());
	        myGrid.ColumnDefinitions.Add(new System.Windows.Controls.ColumnDefinition());
	        myGrid.ColumnDefinitions.Add(new System.Windows.Controls.ColumnDefinition());
			myGrid.RowDefinitions.Add(new System.Windows.Controls.RowDefinition());
			myGrid.RowDefinitions[0].Height    = new GridLength(20);
			myGrid.ColumnDefinitions[0].Width  = new GridLength(90);	/// Long Enable
			myGrid.ColumnDefinitions[1].Width  = new GridLength(2);		/// Blank spacer
			myGrid.ColumnDefinitions[2].Width  = new GridLength(90);	/// Short Enable
			myGrid.ColumnDefinitions[3].Width  = new GridLength(6);		/// Blank spacer
			myGrid.ColumnDefinitions[4].Width  = new GridLength(60);	/// Exit All

	        longButton = new System.Windows.Controls.Button
			{
				Name = "longButton",
				Content = "Long Enabled",
				Foreground = Brushes.White,
				Background = Brushes.Green,
				Height = 25,
				Width = 90,
				FontSize = 12,
				HorizontalAlignment = HorizontalAlignment.Center,
				VerticalAlignment = VerticalAlignment.Center,
				Focusable = false
			};
			/// Set initial state based on user input
			if (!longOn)
			{
				longButton.Content = "Long Disabled";
				longButton.Background = Brushes.Gray; // Change appearance to "pressed"
			}
			
			shortButton = new System.Windows.Controls.Button
			{
				Name = "shortButton",
				Content = "Short Enabled",
				Foreground = Brushes.White,
				Background = Brushes.Crimson,
				Height = 25,
				Width = 90,
				FontSize = 12,
				HorizontalAlignment = HorizontalAlignment.Center,
				VerticalAlignment = VerticalAlignment.Center,
				Focusable = false
			};
			/// Set initial state based on user input
			if (!shortOn)
			{
				shortButton.Content = "Short Disabled";
				shortButton.Background = Brushes.Gray; // Change appearance to "pressed"
			}
			
			exitButton = new System.Windows.Controls.Button
			{
				Name = "exitButton",
				Content = "Exit All",
				Foreground = Brushes.White,
				Background = Brushes.DarkMagenta,
				Height = 25,
				Width = 60,
				FontSize = 12,
				HorizontalAlignment = HorizontalAlignment.Center,
				VerticalAlignment = VerticalAlignment.Center,
				Focusable = false
			};
	 
			longButton.Click += OnMyButtonClick;
			shortButton.Click += OnMyButtonClick;
			exitButton.Click += OnMyButtonClick;
	 
			System.Windows.Controls.Grid.SetColumn(longButton, 0);
			System.Windows.Controls.Grid.SetRow(longButton, 0);
	        System.Windows.Controls.Grid.SetColumn(shortButton, 2);
			System.Windows.Controls.Grid.SetRow(shortButton, 0);
			System.Windows.Controls.Grid.SetColumn(exitButton, 4);
			System.Windows.Controls.Grid.SetRow(exitButton, 0);
			
	        myGrid.Children.Add(longButton);
			myGrid.Children.Add(shortButton);
			myGrid.Children.Add(exitButton);
	 
	        UserControlCollection.Add(myGrid);
		}
		
		public void DisposeButtons() 
		{
			if (myGrid != null)
			{
				if (longButton != null)
				{
					myGrid.Children.Remove(longButton);
					longButton.Click -= OnMyButtonClick;
					longButton = null;
				}
				if (shortButton != null)
				{
					myGrid.Children.Remove(shortButton);
					shortButton.Click -= OnMyButtonClick;
					shortButton = null;
				}
				if (exitButton != null)
				{
					myGrid.Children.Remove(exitButton);
					exitButton.Click -= OnMyButtonClick;
					exitButton = null;
				}
			}
		}
		
		private void UpdateTradeButtons(bool longOn, bool shortOn)
		{
			string stateL = (longOn)  ? " Enabled" : " Disabled";
			string stateS = (shortOn) ? " Enabled" : " Disabled";
			Brush bgLong  = (longOn)  ? Brushes.Green : Brushes.Gray;
			Brush bgShort = (shortOn) ? Brushes.Crimson : Brushes.Gray;
			
			if (Dispatcher.CheckAccess())
			{
				longButton.Content = "Long" + stateL;
				longButton.Background = bgLong;
				shortButton.Content = "Short" + stateS;
				shortButton.Background = bgShort;
			}
			else
			{
				Dispatcher.Invoke(() => {
					longButton.Content = "Long" + stateL;
					longButton.Background = bgLong;
				});
				Dispatcher.Invoke(() => {
					shortButton.Content = "Short" + stateS;
					shortButton.Background = bgShort;
				});
			}
		}
		
		#endregion
		
		#region PROPERTIES - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
		
		#region TimeZone    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙
		[NinjaScriptProperty]
		[Range(-23, 23)]
		[Display(Name = "Timezone Offset", Description = "Number of hours to add the signal PostTime to compare with local time", Order=0, GroupName = "00. TimeZone")]
		public int TimeZoneOffset
		{ get; set; }
		#endregion

		#region Entry  ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙
		[NinjaScriptProperty]
		[Display(Name="Trade Longs", Description = "Trade long direction", Order=3, GroupName = "01. Entry")]
		public bool TradeLong
		{ get; set; }
		
		[NinjaScriptProperty]
		[Display(Name="Trade Short", Description = "Trade short direction", Order=4, GroupName = "01. Entry")]
		public bool TradeShort
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name="Long Order Quantity", Description = "The initial number of contracts to place per long order", Order=7, GroupName = "01. Entry")]
		public int LngQuantity
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name="Short Order Quantity", Description = "The initial number of contracts to place per short order", Order=8, GroupName = "01. Entry")]
		public int ShtQuantity
		{ get; set; }

		[NinjaScriptProperty]
		[Range(16, 36)]
		[Display(Name="Entry Level 1", Description = "The price where limit order is set (16-36)", Order=11, GroupName = "01. Entry")]
		public int EntryLevel1
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(67, 87)]
		[Display(Name="Entry Level 2", Description = "The price where limit order is set (67-87)", Order=12, GroupName = "01. Entry")]
		public int EntryLevel2
		{ get; set; }
		
		[NinjaScriptProperty]
		[Display(Name="Disable Hook", Description = "Check to prevent from ever using Hook", Order=14, GroupName = "01. Entry")]
		public bool DisableHook
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(0, 60)]
		[Display(Name="Hook Gap Ticks Long Level1", Description = "Number of Ticks price must cross Entry Level for Long 'Hook' Entry off Level1", Order=16, GroupName = "01. Entry")]
		public int HookGapL1
		{ get; set; }		
		
		[NinjaScriptProperty]
		[Range(0, 60)]
		[Display(Name="Hook Gap Ticks Long Level2", Description = "Number of Ticks price must cross Entry Level for Long 'Hook' Entry off Level2", Order=18, GroupName = "01. Entry")]
		public int HookGapL2
		{ get; set; }		
		
		[NinjaScriptProperty]
		[Range(0, 60)]
		[Display(Name="Hook Gap Ticks Short Level1", Description = "Number of Ticks price must cross Entry Level for Short 'Hook' Entry off Level1", Order=21, GroupName = "01. Entry")]
		public int HookGapS1
		{ get; set; }		
		
		[NinjaScriptProperty]
		[Range(0, 60)]
		[Display(Name="Hook Gap Ticks Short Level2", Description = "Number of Ticks price must cross Entry Level for Short 'Hook' Entry off Level2", Order=23, GroupName = "01. Entry")]
		public int HookGapS2
		{ get; set; }		
		
		[NinjaScriptProperty]
		[Range(0, 80)]
		[Display(Name="Cancellation Points", Description = "Number of Points price must go beyond Hook trigger to cancel pending stop order.  Zero to disable", Order=26, GroupName = "01. Entry")]
		public int CancelPoints
		{ get; set; }		
		#endregion
		#region Long Exits  ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙
		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name="Long Stop Loss", Description = "Initial stop loss in ticks.  Zero to disable", Order=0, GroupName = "02a. Long Exits")]
		public int LngStopLoss
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name="Long Take Profit 1", Description = "Take profit #1 in ticks.  Zero to disable", Order=3, GroupName = "02a. Long Exits")]
		public int LngTakeProfit1
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name="Long Exit Qty 1", Description = "Number of contracts to exit on 1st TP target", Order=6, GroupName = "02a. Long Exits")]
		public int LngQuantityTP1
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name="Long Take Profit 2", Description = "Take profit #2 in ticks.  Zero to disable", Order=9, GroupName = "02a. Long Exits")]
		public int LngTakeProfit2
		{ get; set; }
		#endregion
		#region Short Exits ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙
		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name="Short Stop Loss", Description = "Initial stop loss in ticks.  Zero to disable", Order=0, GroupName = "02b. Short Exits")]
		public int ShtStopLoss
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name="Short Take Profit 1", Description = "Take profit #1 in ticks.  Zero to disable", Order=3, GroupName = "02b. Short Exits")]
		public int ShtTakeProfit1
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name="Short Exit Qty 1", Description = "Number of contracts to exit on 1st TP target", Order=6, GroupName = "02b. Short Exits")]
		public int ShtQuantityTP1
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name="Short Take Profit 2", Description = "Take profit #2 in ticks.  Zero to disable", Order=9, GroupName = "02b. Short Exits")]
		public int ShtTakeProfit2
		{ get; set; }
		#endregion
		#region BreakEven   ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙
		[NinjaScriptProperty]
		[Display(Name = "Use Break Even", Description = "When first TP is triggerd, move remaining stop to Break Even", Order=0, GroupName = "03. Break Even")]
		public bool UseBreakeven
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(int.MinValue, int.MaxValue)]
		[Display(Name = "Win Activation Ticks", Description = "Ticks in profit to activate Breakeven", Order=1, GroupName = "03. Break Even")]
		public int BEW_ActivTicks
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(int.MinValue, int.MaxValue)]
		[Display(Name="Win BreakEven Offset", Description = "Win Break Even offset from entry price", Order=3, GroupName = "03. Break Even")]
		public int BEW_Offset
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(int.MinValue, int.MaxValue)]
		[Display(Name = "Lose Activation Ticks", Description = "Ticks in loss to activate Breakeven", Order=7, GroupName = "03. Break Even")]
		public int BEL_ActivTicks
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(int.MinValue, int.MaxValue)]
		[Display(Name="Lose BreakEven Offset", Description = "Lose Break Even offset from entry price", Order=9, GroupName = "03. Break Even")]
		public int BEL_Offset
		{ get; set; }
		#endregion
		#region Trailing Stop    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙
		[NinjaScriptProperty]
		[Display(Name = "Use Trailing Stop", Description = "", Order=0, GroupName = "04. Trailing Stop")]
		public bool UseTrailingStop
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name = "Long Activation Ticks", Description = "For Long Trade, ticks in profit to activate trail", Order=3, GroupName = "04a. Long Trailing Stop")]
		public int LngActivationTicks
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(int.MinValue, int.MaxValue)]
		[Display(Name = "Long Trail Ticks", Description = "Number of ticks to trail by for a Long Trade", Order=6, GroupName = "04a. Long Trailing Stop")]
		public int LngTrailTicks
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name = "Short Activation Ticks", Description = "For Short Trade, ticks in profit to activate trail", Order=9, GroupName = "04b. Short Trailing Stop")]
		public int ShtActivationTicks
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(int.MinValue, int.MaxValue)]
		[Display(Name = "Short Trail Ticks", Description = "Number of ticks to trail by for a Short Trade", Order=12, GroupName = "04b. Short Trailing Stop")]
		public int ShtTrailTicks
		{ get; set; }
		#endregion
		
		#region Daily Targets    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙
		[NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name = "Daily Target Min $", Description = "Profit amount at which to stop trading for the day", Order=0, GroupName = "05. Daily Targets")]
        public int DailyMinTarget
		{ get; set; }
		
		[NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name = "Daily Loss Max $", Description = "Loss amount at which to stop trading for the day", Order=1, GroupName = "05. Daily Targets")]
        public int DailyMaxLoss
		{ get; set; }
		#endregion
		
		#region Stochastic  ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙
		[Range(1, int.MaxValue), NinjaScriptProperty]
		[Display(Name="Period D", Description="Stochastic parameter Period D", Order=0, GroupName = "04. Stochastic")]
		public int PeriodD
		{ get; set; }
		
		[Range(1, int.MaxValue), NinjaScriptProperty]
		[Display(Name = "Period K", Description="Stochastic parameter Period K", Order = 1, GroupName = "04. Stochastic")]
		public int PeriodK
		{ get; set; }

		[Range(0, 100), NinjaScriptProperty]
		[Display(Name = "Upper Level", Description="Stochastic upper level setting (50-100)", Order = 5, GroupName = "04. Stochastic")]
		public double UpperLevel
		{ get; set; }
		
		[Range(0, 100), NinjaScriptProperty]
		[Display(Name = "Lower Level", Description="Stochastic lower level setting (0-50)", Order = 6, GroupName = "04. Stochastic")]
		public double LowerLevel
		{ get; set; }
		#endregion	
		#region StraddlePOCSignal     ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙
		[NinjaScriptProperty]
		[Display(Name="Signal Type", Description="Signal type either x ticks or percetage.", Order=0, GroupName = "05. Volare POC Straddle")]
		public SignalType SigType
		{ get; set; }
		
		[NinjaScriptProperty]
		[Display(Name="Open Percentage", Description="Open percentage for the confirmation of signal.", Order=3, GroupName = "05. Volare POC Straddle")]
		public double Percentage1
		{ get; set; }
		
		[NinjaScriptProperty]
		[Display(Name="Close Percentage", Description="Close percentage for the confirmation of signal.", Order=6, GroupName = "05. Volare POC Straddle")]
		public double Percentage2
		{ get; set; }
		
		[NinjaScriptProperty]
		[Display(Name="X Ticks From the Open", Description="X ticks from the opening price to the high price of the bar.", Order=9, GroupName = "05. Volare POC Straddle")]
		public int OpensX_Ticks
		{ get; set; }
		
		[NinjaScriptProperty]
		[Display(Name="X Ticks From the Close", Description="X ticks from the closing price to the low price of the bar.", Order=12, GroupName = "05. Volare POC Straddle")]
		public int ClosesX_Ticks
		{ get; set; }
		#endregion
		
		#region ATR Filter  ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙
		[NinjaScriptProperty]
        [Display(Name = "Use ATR Filter", Description = "Prevent trading when ATR is too large", Order=0, GroupName = "06. ATR Filter")]
        public bool UseATR
		{ get; set; }
		
		[NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "Minutes Per Bar", Description = "Number of Minutes to use for ATR data series", Order=3, GroupName = "06. ATR Filter")]
        public int MinutesATR
		{ get; set; }
		
		[NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "ATR Period", Description = "Period parameter to use for Average True Range", Order=6, GroupName = "06. ATR Filter")]
        public int PeriodATR
		{ get; set; }
		
		[NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name = "Enable Hook Points", Description = "Minimum ATR Points to enable Hook.  Zero to disable", Order=9, GroupName = "06. ATR Filter")]
        public int HookPointsATR
		{ get; set; }
		
		[NinjaScriptProperty]
        [Range(0, int.MaxValue)]
		[Display(Name="Disable Trade Points", Description = "Maximum ATR Points before all trading disabled.  Zero to disable", Order=12, GroupName = "06. ATR Filter")]
		public int DisableOnMaxATR
		{ get; set; }
		#endregion
		#region Minimum Volume   ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙
		[NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "Minimum Average Volume", Description = "Minimum Average Volume to place trade", Order=0, GroupName = "06. Minimum Volume")]
        public int MinAveVolume
		{ get; set; }
		
		[NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "Average Volume Period", Description = "Period parameter to use for Average Volume", Order=1, GroupName = "06. Minimum Volume")]
        public int AveVolumePeriod
		{ get; set; }
		#endregion
		
		#region Trading Days     ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙
		[NinjaScriptProperty]
        [Display(Name = "Trade Sunday", Description = "Toggle Sunday Trading", Order=0, GroupName = "08. Trading Days")]
        public bool TradeSunday
		{ get; set; }

		[NinjaScriptProperty]
        [Display(Name = "Trade Monday", Description = "Toggle Monday Trading", Order=1, GroupName = "08. Trading Days")]
        public bool TradeMonday
		{ get; set; }

		[NinjaScriptProperty]
        [Display(Name = "Trade Tuesday", Description = "Toggle Tuesday Trading", Order=2, GroupName = "08. Trading Days")]
        public bool TradeTuesday
		{ get; set; }

		[NinjaScriptProperty]
        [Display(Name = "Trade Wednesday", Description = "Toggle Wednesday Trading", Order=3, GroupName = "08. Trading Days")]
        public bool TradeWednesday
		{ get; set; }

		[NinjaScriptProperty]
        [Display(Name = "Trade Thursday", Description = "Toggle Thursday Trading", Order=4, GroupName = "08. Trading Days")]
        public bool TradeThursday
		{ get; set; }

		[NinjaScriptProperty]
        [Display(Name = "Trade Friday", Description = "Toggle Friday Trading", Order=5, GroupName = "08. Trading Days")]
        public bool TradeFriday
		{ get; set; }
		#endregion
		#region Session 1 Hours  ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙
		[NinjaScriptProperty]
        [Display(Name = "Use Session 1", Description = "Use Trading Session #1", Order=0, GroupName = "09. Session 1 Hours")]
        public bool UseSession1
		{ get; set; }

        [NinjaScriptProperty]
        [PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
        [Display(Name = "Start Time", Description = "Enter Start Time for Session 1", Order=1, GroupName = "09. Session 1 Hours")]
        public DateTime StartTime1
		{ get; set; }

        [NinjaScriptProperty]
        [PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
        [Display(Name = "End Time", Description = "Enter End Time for Session 1", Order=2, GroupName = "09. Session 1 Hours")]
        public DateTime EndTime1
		{ get; set; }

		[NinjaScriptProperty]
        [Display(Name = "Use Close Time", Description = "Enable to close all Session 1 trades at given time", Order=4, GroupName = "09. Session 1 Hours")]
        public bool UseCloseTime1
		{ get; set; }

        [NinjaScriptProperty]
        [PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
        [Display(Name = "Close Time", Description = "Enter Time to Close Session 1 Trades", Order=5, GroupName = "09. Session 1 Hours")]
        public DateTime CloseTime1
		{ get; set; }
		#endregion
		#region Session 2 Hours  ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙
		[NinjaScriptProperty]
        [Display(Name = "Use Session 2", Description = "Use Trading Session #2", Order=0, GroupName = "10. Session 2 Hours")]
        public bool UseSession2
		{ get; set; }

        [NinjaScriptProperty]
        [PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
        [Display(Name = "Start Time", Description = "Enter Start Time for Session 2", Order=1, GroupName = "10. Session 2 Hours")]
        public DateTime StartTime2
		{ get; set; }

        [NinjaScriptProperty]
        [PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
        [Display(Name = "End Time", Description = "Enter End Time for Session 2", Order=2, GroupName = "10. Session 2 Hours")]
        public DateTime EndTime2
		{ get; set; }

		[NinjaScriptProperty]
        [Display(Name = "Use Close Time", Description = "Enable to close all Session 2 trades at given time", Order=4, GroupName = "10. Session 2 Hours")]
        public bool UseCloseTime2
		{ get; set; }

        [NinjaScriptProperty]
        [PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
        [Display(Name = "Close Time", Description = "Enter Time to Close Session 2 Trades", Order=5, GroupName = "10. Session 2 Hours")]
        public DateTime CloseTime2
		{ get; set; }
		#endregion
		#region Session 3 Hours  ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙
		[NinjaScriptProperty]
        [Display(Name = "Use Session 3", Description = "Use Trading Session #3", Order=0, GroupName = "11. Session 3 Hours")]
        public bool UseSession3
		{ get; set; }

        [NinjaScriptProperty]
        [PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
        [Display(Name = "Start Time", Description = "Enter Start Time for Session 3", Order=1, GroupName = "11. Session 3 Hours")]
        public DateTime StartTime3
		{ get; set; }

        [NinjaScriptProperty]
        [PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
        [Display(Name = "End Time", Description = "Enter End Time for Session 3", Order=2, GroupName = "11. Session 3 Hours")]
        public DateTime EndTime3
		{ get; set; }

		[NinjaScriptProperty]
        [Display(Name = "Use Close Time", Description = "Enable to close all Session 3 trades at given time", Order=4, GroupName = "11. Session 3 Hours")]
        public bool UseCloseTime3
		{ get; set; }

        [NinjaScriptProperty]
        [PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
        [Display(Name = "Close Time", Description = "Enter Time to Close Session 3 Trades", Order=5, GroupName = "11. Session 3 Hours")]
        public DateTime CloseTime3
		{ get; set; }
		#endregion
		#region Session 4 Hours  ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙
		[NinjaScriptProperty]
        [Display(Name = "Use Session 4", Description = "Use Trading Session #4", Order=0, GroupName = "12. Session 4 Hours")]
        public bool UseSession4
		{ get; set; }

        [NinjaScriptProperty]
        [PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
        [Display(Name = "Start Time", Description = "Enter Start Time for Session 4", Order=1, GroupName = "12. Session 4 Hours")]
        public DateTime StartTime4
		{ get; set; }

        [NinjaScriptProperty]
        [PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
        [Display(Name = "End Time", Description = "Enter End Time for Session 4", Order=2, GroupName = "12. Session 4 Hours")]
        public DateTime EndTime4
		{ get; set; }

		[NinjaScriptProperty]
        [Display(Name = "Use Close Time", Description = "Enable to close all Session 4 trades at given time", Order=4, GroupName = "12. Session 4 Hours")]
        public bool UseCloseTime4
		{ get; set; }

        [NinjaScriptProperty]
        [PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
        [Display(Name = "Close Time", Description = "Enter Time to Close Session 4 Trades", Order=5, GroupName = "12. Session 4 Hours")]
        public DateTime CloseTime4
		{ get; set; }
		#endregion
		#region Session 5 Hours  ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙
		[NinjaScriptProperty]
        [Display(Name = "Use Session 5", Description = "Use Trading Session #5", Order=0, GroupName = "13. Session 5 Hours")]
        public bool UseSession5
		{ get; set; }

        [NinjaScriptProperty]
        [PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
        [Display(Name = "Start Time", Description = "Enter Start Time for Session 5", Order=1, GroupName = "13. Session 5 Hours")]
        public DateTime StartTime5
		{ get; set; }

        [NinjaScriptProperty]
        [PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
        [Display(Name = "End Time", Description = "Enter End Time for Session 5", Order=2, GroupName = "13. Session 5 Hours")]
        public DateTime EndTime5
		{ get; set; }

		[NinjaScriptProperty]
        [Display(Name = "Use Close Time", Description = "Enable to close all Session 5 trades at given time", Order=4, GroupName = "13. Session 5 Hours")]
        public bool UseCloseTime5
		{ get; set; }

        [NinjaScriptProperty]
        [PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
        [Display(Name = "Close Time", Description = "Enter Time to Close Session 5 Trades", Order=5, GroupName = "13. Session 5 Hours")]
        public DateTime CloseTime5
		{ get; set; }
		#endregion
		#region No-Session Close ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙
		[NinjaScriptProperty]
        [Display(Name = "Use Close Time", Description = "Used only if sessions are disabled", Order=0, GroupName = "14. No-Session Close")]
        public bool UseCloseTime
		{ get; set; }

        [NinjaScriptProperty]
        [PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
        [Display(Name = "Close Time", Description = "Enter Time to Close All Open Trades", Order=1, GroupName = "14. No-Session Close")]
        public DateTime CloseTime
		{ get; set; }
		#endregion
		
		#region Short Misc / Debug    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙
		[NinjaScriptProperty]
        [Display(Name = "Display OCD", Description = "Show the On-Chart Display", Order=0, GroupName = "15. Misc / Debug")]
        public bool DisplayOCD
		{ get; set; }

		[NinjaScriptProperty]
		[Range(0, 50)]
        [Display(Name = "Starting Line", Description = "How many lines down to space the OCD", Order=3, GroupName = "15. Misc / Debug")]
        public int StartingLine
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name = "Plot Historical", Description = "Plot historical trades from DB", Order=6, GroupName = "15. Misc / Debug")]
		public bool PlotHist
		{ get; set; }
		
		[NinjaScriptProperty]
		[Display(Name = "Daily Targets on Historical", Description = "Apply Daily Profit / Loss settings to historical trades?", Order=9, GroupName = "15. Misc / Debug")]
		public bool ApplyDailyToHist
		{ get; set; }
		
		[NinjaScriptProperty]
        [Display(Name = "Strategy Name", Description = "Name of Strategy", Order=12, GroupName = "15. Misc / Debug")]
        public string StrategyName
		{ get; set; }

		[NinjaScriptProperty]
        [Display(Name = "Swap Bid & Ask on Hook", Description = "For Hook-enabled, use Ask for sells and Bid for buys", Order=15, GroupName = "15. Misc / Debug")]
        public bool SwapPrices
		{ get; set; }

		[NinjaScriptProperty]
		[Range(1, 10)]
        [Display(Name = "Buffer Points", Description = "How many Points on each side of Levels to actually pace pending order (Non-Hook)", Order=3, GroupName = "15. Misc / Debug")]
        public int BP
		{ get; set; }

		[NinjaScriptProperty]
        [Display(Name = "Draw Horz Lines", Description = "Draw the 50, 100, and 26 lines", Order=14, GroupName = "15. Misc / Debug")]
        public bool DrawLines
		{ get; set; }

		[NinjaScriptProperty]
        [Display(Name = "Disable Logging", Description = "Disable logging to Ninjascript Output windows", Order=15, GroupName = "15. Misc / Debug")]
        public bool DisableLogging
		{ get; set; }

		[NinjaScriptProperty]
        [Display(Name = "Use Output 2", Description = "Send logging to second Output window", Order=18, GroupName = "15. Misc / Debug")]
        public bool UseOutput2
		{ get; set; }

		[NinjaScriptProperty]
        [Display(Name = "Unique ID (v" + VERSION + ")", Description = "Unique number to identify strategy in Output window", Order=21, GroupName = "15. Misc / Debug")]
        public string UniqueID
		{ get; set; }

		[NinjaScriptProperty]
        [Display(Name = "Log to File", Description = "Log to file as well as NinjaScript Output window (name of log file -leave blank for same name as strategy", Order=21, GroupName = "15. Misc / Debug")]
		public string LogToFileName
		{ get; set; }
//		{
//			get { return logToFileName ?? ""; }		// <--- safe fallback
//			set { logToFileName = value; }
//		}
		#endregion
		
		#endregion
	}
}

