#region USING_DECLARATIONS
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Runtime.CompilerServices;	/// For MemberCallerName
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Gui.Tools;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.Indicators;
using NinjaTrader.NinjaScript.DrawingTools;
using NinjaTrader.NinjaScript.SharedPOC;
#endregion

namespace NinjaTrader.NinjaScript.Strategies
{
	public class StraddlePOCStrategy1 : Strategy
	{
		private const string		VERSION = "*******";
		#region GLOBALS
		private int					profitTargetTicks = 6;
		private int					stopLossTicks = 22;
		private int					barsSinceExit = 0;
		private int					minBarsToWait = 1;
		
		private bool				initialStopsSet = false;
		private bool				longEnabled = true;
		private bool				shortEnabled = true;
		private bool				inSession = true;
		
		private double				lastSignal = 0;
		
		// For Log/OCD
		private string 				lastDashboard = "";
		private string 				lastLogMsg = "";
		private string 				lastCaller = "";
		private string				chartInstrument = "";
		private string				lastLogTime;
		private string				tmpDate = "";
		private string				entryName;
		private string				dbgTTText;
		private string				logPath = "";
		
		// Time filter parameters
		private bool 				useTimeFilter = true;
		private DateTime 			startTime;
		private DateTime 			endTime;
		
		private Indicators.VOLAREIndicators.StraddlePOCSignal	straddleSignal;
		#endregion
		
		protected override void OnStateChange()
		{
			if (State == State.SetDefaults)
			{
				Description						= @"Strategy based on StraddlePOCSignal indicator";
				Name							= "StraddlePOCStrategy1";
				Calculate						= Calculate.OnBarClose;
				EntriesPerDirection				= 1;
				EntryHandling					= EntryHandling.AllEntries;
				IsExitOnSessionCloseStrategy	= true;
				ExitOnSessionCloseSeconds		= 30;
				IsFillLimitOnTouch				= false;
				MaximumBarsLookBack				= MaximumBarsLookBack.TwoHundredFiftySix;
				OrderFillResolution				= OrderFillResolution.Standard;
				StartBehavior					= StartBehavior.WaitUntilFlat;
				TimeInForce						= TimeInForce.Gtc;
				TraceOrders						= false;
				RealtimeErrorHandling			= RealtimeErrorHandling.StopCancelClose;
				StopTargetHandling				= StopTargetHandling.PerEntryExecution;
				BarsRequiredToTrade				= 20;
				
				// Set default time filter values
				StartTime 				= DateTime.Parse("09:30", System.Globalization.CultureInfo.InvariantCulture);
				EndTime					= DateTime.Parse("16:00", System.Globalization.CultureInfo.InvariantCulture);
				
				StrategyName			= "POCStraddle";
				DisplayOCD				= true;
				StartingLine			= 0;
				DisableLogging			= false;
				LogToFileName			= Name;
				UseOutput2				= false;
				UniqueID				= "";
			}
			else if (State == State.Configure)
			{
				if (LogToFileName != "")
				{
					logPath = NinjaTrader.Core.Globals.UserDataDir + @"log\" + LogToFileName + @"\" + LogToFileName + "Log.txt";
					//Print($"                            logPath = {logPath}");
					if (System.IO.File.Exists(logPath))
					{
						try
						{
							System.IO.File.Delete(logPath);
						}
						catch (Exception ex)
						{
							Print("Could not delete log file (logPath): {ex.Message}");
						}
					}
				}
				chartInstrument = this.Instrument.FullName;
				
				// Add the StraddlePOCSignal indicator
				straddleSignal = StraddlePOCSignal(SignalType.Percentage, 50, 10, 2, 2);
				AddChartIndicator(straddleSignal);
			}
			else if (State == State.DataLoaded)
			{
				// Setting initialStopsSet to false until first trade
				initialStopsSet = false;
				
				// Print configuration information
				Log($"Strategy Configuration:");
				Log($"Profit Target: {profitTargetTicks} ticks");
				Log($"Stop Loss: {stopLossTicks} ticks");
				Log($"Min Bars Between Trades: {minBarsToWait}");
				Log($"Bar Type: {BarsPeriod.BarsPeriodType}");
				Log($"Tick Size: {TickSize}");
				
				if (useTimeFilter)
				{
					Log($"Time Filter Enabled: {startTime.ToShortTimeString()} to {endTime.ToShortTimeString()}");
				}
				else
				{
					Log($"Time Filter Disabled");
				}
			}
		}

		protected override void OnBarUpdate()
		{
			if (CurrentBar < BarsRequiredToTrade)
				return;
            
			// Set initial profit target and stop loss once
			if (!initialStopsSet)
			{
				SetProfitTarget(CalculationMode.Ticks, profitTargetTicks);
				SetStopLoss(CalculationMode.Ticks, stopLossTicks);
				initialStopsSet = true;
				Log($"Initial stops set - PT: {profitTargetTicks} ticks, SL: {stopLossTicks} ticks");
			}
			
			// Check if we're in a position or flat
			if (Position.MarketPosition == MarketPosition.Flat)
			{
				barsSinceExit++;
				// Debug log
				if (barsSinceExit <= minBarsToWait)
				{
					Log($"Waiting for minimum bars since exit: {barsSinceExit} of {minBarsToWait}");
				}
			}
            
			// Check if current time is within the allowed trading time if filter is enabled
			if (useTimeFilter  &&  !IsTimeToTrade())
			{
				// Outside trading hours, exit any open positions
				if (Position.MarketPosition != MarketPosition.Flat)
				{
					Log($"Outside trading hours - exiting position");
					ExitLong();
					ExitShort();
				}
				return;
			}
			
			// Check for signal from the StraddlePOCSignal indicator
			double signal = straddleSignal.Straddle[0];
			
			// Only log new signals
			if (signal != lastSignal)
			{
				Log($"Signal changed from {lastSignal} to {signal}");
				lastSignal = signal;
			}
			
			// Exit any open position if signal changes to opposite direction
			if (Position.MarketPosition != MarketPosition.Flat)
			{
				if ((Position.MarketPosition == MarketPosition.Long  &&  signal == -1)  
				||  (Position.MarketPosition == MarketPosition.Short  &&  signal == 1))
				{
					Log($"Signal changed direction - exiting current position");
					ExitLong();
					ExitShort();
					barsSinceExit = 0;
				}
				return; // Don't process entries if we're already in a position
			}
			
			// Check if we've waited enough bars since the last exit
			if (barsSinceExit <= minBarsToWait)
			{
				return;
			}
			
			// Process long signal
			if (signal == 1  &&  Position.MarketPosition != MarketPosition.Long)
			{
				// Calculate entry price for visualization
				double entryPrice = Close[0];
				double longProfitTarget = entryPrice + (profitTargetTicks * TickSize);
				double longStopLoss = entryPrice - (stopLossTicks * TickSize);
				
				Log($"LONG Entry at {entryPrice.ToString("0.00")}, PT at {longProfitTarget.ToString("0.00")} ({profitTargetTicks} ticks), SL at {longStopLoss.ToString("0.00")} ({stopLossTicks} ticks)");
				
				// Visualize the levels
				Draw.Diamond(this, "PT_Long_" + CurrentBar, false, 0, longProfitTarget, Brushes.Green);
				Draw.Diamond(this, "SL_Long_" + CurrentBar, false, 0, longStopLoss, Brushes.Red);
				
				// Enter long position using market order
				EnterLong();
				
				barsSinceExit = 0;
			}
			
			// Process short signal
			else if (signal == -1  &&  Position.MarketPosition != MarketPosition.Short)
			{
				// Calculate entry price for visualization
				double entryPrice = Close[0];
				double shortProfitTarget = entryPrice - (profitTargetTicks * TickSize);
				double shortStopLoss = entryPrice + (stopLossTicks * TickSize);
				
				Log($"SHORT Entry at {entryPrice.ToString("0.00")}, PT at {shortProfitTarget.ToString("0.00")} ({profitTargetTicks} ticks), SL at {shortStopLoss.ToString("0.00")} ({stopLossTicks} ticks)");
				
				// Visualize the levels
				Draw.Diamond(this, "PT_Short_" + CurrentBar, false, 0, shortProfitTarget, Brushes.Green);
				Draw.Diamond(this, "SL_Short_" + CurrentBar, false, 0, shortStopLoss, Brushes.Red);
				
				// Enter short position using market order
				EnterShort();
				
				barsSinceExit = 0;
			}
		}
		
		// Check if current time is within allowed trading hours
		private bool IsTimeToTrade()
		{
			DateTime currentTime = Time[0];
			
			TimeSpan currentTimeOfDay = new TimeSpan(currentTime.Hour, currentTime.Minute, 0);
			TimeSpan startTimeOfDay = new TimeSpan(startTime.Hour, startTime.Minute, 0);
			TimeSpan endTimeOfDay = new TimeSpan(endTime.Hour, endTime.Minute, 0);
			
			inSession = (currentTimeOfDay >= startTimeOfDay  &&  currentTimeOfDay <= endTimeOfDay);
			return inSession;
		}
		
		#region DEBUG & OCD  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
		private void ManageOCD()
		{
			if (!DisplayOCD)
				return;
			
			string text = "";
			string state = (inSession) ? "Active" : "Inactive";
			for (int i=0; i < StartingLine; i++)
				text += "\n";
			text += " Strategy :\t" + StrategyName + " v" + VERSION + ", " + UniqueID;
			text += "\n Session :\t" + state + "\n";
			if (!longEnabled  &&  !shortEnabled)
				text += "\n ALL TRADING DISABLED" + "\n";
			else if (!longEnabled)
				text += "\n LONG TRADING DISABLED" + "\n";
			else if (!shortEnabled)
				text += "\n SHORT TRADING DISABLED" + "\n";
			
//			text += "\n Stoch[1] :\t" + stoch.K[1];
//			text += "\n Stoch[2] :\t" + stoch.K[2] + "\n";
			
//			text += "\n justExited :\t" + justExited;
			//if (UseTrailing  &&  Position.MarketPosition != MarketPosition.Flat)			text += "\n Trail Triggered :\t\t" + trailTriggered + "\n";

		//	if (DailyMinTarget != 0  ||  DailyMaxLoss != 0)
		//	{
		//		text += "\n";
		//		if (DailyMinTarget > 0)
		//			text += "Daily Min Profit :\t\t\t\t" + DailyMinTarget.ToString("C0") + "\n";
		//		if (DailyMaxLoss > 0)
		//			text += "Daily Max Loss :\t\t\t\t" + DailyMaxLoss.ToString("C0") + "\n";
		//		text += "Daily Profit :\t\t\t\t\t" + dailyProfit.ToString("C0") + "\n";
		//		if (dailyLimitHit)
		//			text += "Daily Limit Hit :\t\t\t\tYes - Trading Disabled\n";
		//		else
		//			text += "Daily Limit Hit :\t\t\t\tNo\n";
		//	}
			
			if (lastDashboard == text)
				return;
			
			Draw.TextFixed(this, "OCD", text, TextPosition.TopLeft);	
			lastDashboard = text;
		}
		
		private void Log(string message, [CallerMemberName] string memberName = "")
		{
			if (DisableLogging  ||  lastLogMsg == message)
				return;

			/// Debug time filtering...
			//if (Time[0] < dbg1  ||  Time[0] > dbg2)	return;
			
			/// Set this scripts Print() calls to the second output tab?
			if (UseOutput2)		PrintTo = PrintTo.OutputTab2;
			else				PrintTo = PrintTo.OutputTab1;
			
			string dateStr = "";
			string id = StrategyName + " " + UniqueID;
			
			if (lastLogMsg != message)
			{
				DateTime date = (State == State.Historical) ? Time[0] : DateTime.Now;
				dateStr = (State == State.Historical) ? date.ToString() : date.ToString("HH:mm:ss");
				
				string header = $"\n{id} - {chartInstrument} - [{memberName}]  -  {dateStr}  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -\n";
			
				/// Output just time if diff time but not new caller
				string output = (lastLogTime != dateStr) ? message + "   ( " + dateStr + " )" : message;
					
				/// If it is a new caller, write header line
				if (lastCaller != memberName)
					LogToFile(header);
	
				LogToFile(output);
			}
			
			lastLogMsg = message;
			lastCaller = memberName;
			lastLogTime = dateStr;
		}
		
		private void LogToFile(string message)
		{
			/// Always print to the output window as well
			Print(message);
			
			/// Additionally logging to file is skipped if there is no file name
			if (logPath == "")
				return;
			//Print($"                                                                         logPath = {logPath}         LogToFileName = {LogToFileName}");

			/// Ensure the directory exists
			try
			{
				string folder = System.IO.Path.GetDirectoryName(logPath);
				if (!System.IO.Directory.Exists(folder))
					System.IO.Directory.CreateDirectory(folder);
			}
			catch (Exception ex)
			{
				Print($"LogToFile() error creating folder: {ex.Message}");
				return;
			}
			
			/// Append to the log file
			try
			{
				System.IO.File.AppendAllText(logPath, "\n" + message);
			}
			catch (Exception ex)
			{
				Print($"LogToFile() error writing file: {ex.Message}");
			}

		}
		#endregion
		
		#region PROPERTIES - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
		[Display(Name = "Profit Target (Ticks)", Description = "Number of ticks for profit target", Order = 1, GroupName = "Parameters")]
		[Range(1, int.MaxValue)]
		public int ProfitTargetTicks
		{ get; set; }
		
		[Display(Name = "Stop Loss (Ticks)", Description = "Number of ticks for stop loss", Order = 2, GroupName = "Parameters")]
		[Range(1, int.MaxValue)]
		public int StopLossTicks
		{ get; set; }
		
		[Display(Name = "Min Bars Between Trades", Description = "Minimum number of bars to wait between trades", Order = 3, GroupName = "Parameters")]
		[Range(0, int.MaxValue)]
		public int MinBarsToWait
		{ get; set; }
		
		[Display(Name = "Use Time Filter", Description = "Enable/disable time-based filter for trading", Order = 1, GroupName = "Time Filter")]
		public bool UseTimeFilter
		{ get; set; }
		
		[NinjaScriptProperty]
		[PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
		[Display(Name = "Start Time", Description = "Trading start time", Order = 2, GroupName = "Time Filter")]
		public DateTime StartTime
		{ get; set; }
		
		[NinjaScriptProperty]
		[PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
		[Display(Name = "End Time", Description = "Trading end time", Order = 3, GroupName = "Time Filter")]
		public DateTime EndTime
		{ get; set; }
		
		#region Misc / Debug
		[NinjaScriptProperty]
		[Display(Name = "Display OCD", Description = "Show the On-Chart Display", Order=0, GroupName = "15. Misc / Debug")]
		public bool DisplayOCD
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(0, 50)]
		[Display(Name = "Starting Line", Description = "How many lines down to space the OCD", Order=3, GroupName = "15. Misc / Debug")]
		public int StartingLine
		{ get; set; }
		
		[NinjaScriptProperty]
		[Display(Name = "Strategy Name", Description = "Name of Strategy", Order=12, GroupName = "15. Misc / Debug")]
		public string StrategyName
		{ get; set; }
		
		[NinjaScriptProperty]
		[Display(Name = "Disable Logging", Description = "Disable logging to Ninjascript Output windows", Order=15, GroupName = "15. Misc / Debug")]
		public bool DisableLogging
		{ get; set; }
		
		[NinjaScriptProperty]
		[Display(Name = "Use Output 2", Description = "Send logging to second Output window", Order=18, GroupName = "15. Misc / Debug")]
		public bool UseOutput2
		{ get; set; }
		
		[NinjaScriptProperty]
		[Display(Name = "Unique ID (v" + VERSION + ")", Description = "Unique number to identify strategy in Output window", Order=20, GroupName = "15. Misc / Debug")]
		public string UniqueID
		{ get; set; }
		
		[NinjaScriptProperty]
		[Display(Name = "Log to File", Description = "Log to file as well as NinjaScript Output window (name of log file -leave blank for same name as strategy", Order=21, GroupName = "15. Misc / Debug")]
		public string LogToFileName
		{ get; set; }
		#endregion
		#endregion
    }
}