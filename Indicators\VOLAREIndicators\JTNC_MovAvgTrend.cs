#region Using declarations
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Gui.Tools;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.DrawingTools;
#endregion

//This namespace holds Indicators in this folder and is required. Do not change it. 
namespace NinjaTrader.NinjaScript.Indicators.VOLAREIndicators
{
	[Gui.CategoryOrder("Parameters", 1)]	
	[Gui.CategoryOrder("Signal", 2)]
	public class JTNC_MovAvgTrend : Indicator
	{				
	//--MACDBB
		private int MACD_Fast = 12;
		private int MACD_Slow = 26;
		private int MACD_Smooth = 15;
		private double BB_NumDev = 1.0;
		
		private MACD macd, diff;
		
	//--RSM combo
		private RSI combo_rsm;
		private Series<double> combo_stoch;
			
		private int SQZ_Period = 30;
		private Series<double> SQZ_Bandwidth;
		private Series<double> SQZ_Low;
		private Series<bool> SQZ_Squeeze;
		
	//--RSI
		private RSI rsi;
		private int rsiPeriod = 5;
		private int rsiSmooth = 3;
		private Series<double> RSI_r;
		private Series<double> RSI_InverseRSI;
		private Series<int> RSI_direction;		
		
	//--Fibonacci
		private int Fibo_Period = 11;
		private double Fibo_Retrace = 23.6;
		private bool Fibo_UseHighLow = true;
		private Series<double> Fibo_h;
		private Series<double> Fibo_l;
		private Series<double> Fibo_minL;
		private Series<double> Fibo_maxH;
		private Series<double> Fibo_Hh;
		private Series<double> Fibo_Ll;
		private Series<double> Fibo_trend;
		private Series<int> Fibo_direction;		
		private bool ShowSignalVerticalLines = true;
		private bool Include_Fibo = true; 
		private bool Include_RSI = true;
		private Series<bool> Buy;
		private Series<bool> Sell;
		
	//--Strategy
		private Series<bool> Strategy_Buy;
		private Series<bool> Strategy_Sell;
		
		private bool drawSignal = true;
		private Brush arrowUpBrush	= Brushes.Lime;
		private Brush arrowDnBrush	= Brushes.Red;
		private int arrowD = 15;
		private int opacity = 50;
		private Brush areaBrush	= Brushes.Black;
		
		SimpleFont myFont = new NinjaTrader.Gui.Tools.SimpleFont("Windings", 20);
		
		protected override void OnStateChange()
		{
			if (State == State.SetDefaults)
			{
				Description									= @"Enter the description for your new custom Indicator here.";
				Name										= "JTNC Moving Average Trend";
				Calculate									= Calculate.OnBarClose;
				IsOverlay									= false;
				DisplayInDataBox							= true;
				DrawOnPricePanel							= true;
				DrawHorizontalGridLines						= true;
				DrawVerticalGridLines						= true;
				PaintPriceMarkers							= true;
				ScaleJustification							= NinjaTrader.Gui.Chart.ScaleJustification.Right;
				//Disable this property if your indicator requires custom values that cumulate with each new market data event. 
				//See Help Guide for additional information.
				IsSuspendedWhileInactive					= true;
				base.ArePlotsConfigurable = true;
				AddPlot(new Stroke(Brushes.Orange, DashStyleHelper.Solid, 1), PlotStyle.Line, "Trend Line");
				AddPlot(new Stroke(Brushes.Gray, DashStyleHelper.Solid, 2), PlotStyle.Dot, "Trend Dots");
				AddPlot(new Stroke(Brushes.Gray, DashStyleHelper.Solid, 1), PlotStyle.Bar, "Trend Diff");				
				AddPlot(new Stroke(Brushes.Red, DashStyleHelper.Solid, 1), PlotStyle.Line, "Trend Upper");
				AddPlot(new Stroke(Brushes.YellowGreen, DashStyleHelper.Solid, 1), PlotStyle.Line, "Trend Lower");
				AddPlot(new Stroke(Brushes.Orange, DashStyleHelper.Solid, 1), PlotStyle.Line, "Trend Midline");
				AddPlot(new Stroke(Brushes.Transparent, DashStyleHelper.Solid, 1), PlotStyle.Dot, "BuySell");
				Plots[2].AutoWidth = true;
				AddLine(Brushes.DarkGray, 0, "Zero");
			}
			else if (State == State.Configure)
			{
				macd = MACD(BarsArray[0], MACD_Fast, MACD_Slow, MACD_Smooth);
				diff = MACD(BarsArray[0], MACD_Fast, MACD_Slow, 9);
				
				combo_rsm = RSI(Close, 7, rsiSmooth);
				combo_stoch = new Series<double>(this, MaximumBarsLookBack.Infinite);
				
				SQZ_Bandwidth = new Series<double>(this, MaximumBarsLookBack.Infinite);
				SQZ_Low = new Series<double>(this, MaximumBarsLookBack.Infinite);
				SQZ_Squeeze = new Series<bool>(this, MaximumBarsLookBack.Infinite);
				
				rsi = RSI(Close, rsiPeriod, rsiSmooth);
				RSI_r = new Series<double>(this, MaximumBarsLookBack.Infinite);
				RSI_InverseRSI = new Series<double>(this, MaximumBarsLookBack.Infinite);
				RSI_direction = new Series<int>(this, MaximumBarsLookBack.Infinite);
				
				Fibo_h = new Series<double>(this, MaximumBarsLookBack.Infinite);
				Fibo_l = new Series<double>(this, MaximumBarsLookBack.Infinite);
				Fibo_minL = new Series<double>(this, MaximumBarsLookBack.Infinite);
				Fibo_maxH = new Series<double>(this, MaximumBarsLookBack.Infinite);
				Fibo_Hh = new Series<double>(this, MaximumBarsLookBack.Infinite);
				Fibo_Ll = new Series<double>(this, MaximumBarsLookBack.Infinite);
				Fibo_trend = new Series<double>(this, MaximumBarsLookBack.Infinite);
				Fibo_direction = new Series<int>(this, MaximumBarsLookBack.Infinite);
				Buy = new Series<bool>(this, MaximumBarsLookBack.Infinite);
				Sell = new Series<bool>(this, MaximumBarsLookBack.Infinite);
				
				Strategy_Buy = new Series<bool>(this, MaximumBarsLookBack.Infinite);
				Strategy_Sell = new Series<bool>(this, MaximumBarsLookBack.Infinite);
				
				if (!arrowUpBrush.IsFrozen)
					arrowUpBrush.Freeze();
				if (!arrowDnBrush.IsFrozen)
					arrowDnBrush.Freeze();
				if (!areaBrush.IsFrozen)
					areaBrush.Freeze();						
			}
		}
		
		protected override void OnBarUpdate()
		{
			Trend_Line[0] = macd.Default[0];			
			Trend_Dots[0] = macd.Default[0];
			Trend_Diff[0] = diff.Diff[0];
			Trend_Upper[0] = Bollinger(macd.Default, BB_NumDev, MACD_Smooth).Upper[0];
			Trend_Lower[0] = Bollinger(macd.Default, BB_NumDev, MACD_Smooth).Lower[0];
			Trend_Midline[0] = Bollinger(macd.Default, BB_NumDev, MACD_Smooth).Middle[0];
			
			if(CurrentBar > 0)
			{
				if(Trend_Line[0] > Trend_Line[1] && Trend_Line[0] >= Trend_Upper[0])
				{
					PlotBrushes[0][0] = Brushes.Lime;
					PlotBrushes[1][0] = Brushes.Lime;					
				}
				else if(Trend_Line[0] < Trend_Line[1] && Trend_Line[0] >= Trend_Upper[0])
				{
					PlotBrushes[0][0] = Brushes.DarkGreen;
					PlotBrushes[1][0] = Brushes.DarkGreen;
				}
				else if(Trend_Line[0] < Trend_Line[1] && Trend_Line[0] < Trend_Lower[0])
				{
					PlotBrushes[0][0] = Brushes.Red;
					PlotBrushes[1][0] = Brushes.Red;
				}
				else if(Trend_Line[0] > Trend_Line[1] && Trend_Line[0] < Trend_Lower[0])
				{
					PlotBrushes[0][0] = Brushes.DarkRed;
					PlotBrushes[1][0] = Brushes.DarkRed;
				}
				if(Trend_Diff[0] >= 0)
				{
					if(Trend_Diff[0] > Trend_Diff[1])
						PlotBrushes[2][0] = Brushes.Lime;
					else
						PlotBrushes[2][0] = Brushes.DarkGreen;
				}
				else
				{
					if(Trend_Diff[0] < Trend_Diff[1])
						PlotBrushes[2][0] = Brushes.Red;
					else
						PlotBrushes[2][0] = Brushes.DarkRed;
				}
			}
			
		//--RSI/STOCASTIC/MACD CONFLUENCE COMBO
			bool MACDBB_Buy = macd.Default[0] > Trend_Upper[0];
			bool MACDBB_Sell = macd.Default[0] <= Trend_Lower[0];
			combo_stoch[0] = 100 * (Close[0] - MIN(Low, 14)[0]) / (MAX(High, 14)[0] - MIN(Low, 14)[0]);
			double RSM_StochSlowK = SMA(SMA(combo_stoch, 3), 3)[0];
			bool RSM_rsiGreen = combo_rsm[0] >= 50;
			bool RSM_rsiRed = combo_rsm[0] < 50;
			bool RSM_stochGreen = RSM_StochSlowK >= 50;
			bool RSM_stochRed = RSM_StochSlowK < 50;
			bool RSM_macdGreen = Trend_Diff[0] >= 0;
			bool RSM_macdRed = Trend_Diff[0] < 0;
			bool RSM_Buy = RSM_rsiGreen && RSM_stochGreen && RSM_macdGreen;
			bool RSM_Sell = RSM_rsiRed && RSM_stochRed && RSM_macdRed;
			
		//--Squeeze
			SQZ_Bandwidth[0] = Trend_Upper[0] - Trend_Lower[0];
			SQZ_Low[0] = MIN(SQZ_Bandwidth, SQZ_Period)[0];
			SQZ_Squeeze[0] = SQZ_Bandwidth[0] == SQZ_Low[0];
			bool SQZ_squeeze_signal =  CurrentBar == 0 ? false : !SQZ_Squeeze[1] && SQZ_Squeeze[0];
			
		//--RSI
			RSI_r[0] = rsi[0] - 50;
			double RSI_Avg = EMA(RSI_r, 9)[0];
			RSI_InverseRSI[0] = (Math.Exp(2*RSI_Avg) - 1) / (Math.Exp(2*RSI_Avg) + 1);
			RSI_direction[0] = CurrentBar == 0 ? 0 : (RSI_InverseRSI[1] > 0 && RSI_InverseRSI[0] < 0) ? -1 : (RSI_InverseRSI[0] > 0 && RSI_InverseRSI[1] < 0) ? 1 : RSI_direction[1];
			
		//--Fibonacci SuperTrend
			Fibo_h[0] = Fibo_UseHighLow ? High[0] : Close[0];
			Fibo_l[0] = Fibo_UseHighLow ? Low[0] : Close[0];
			Fibo_minL[0] = MIN(Fibo_l, Fibo_Period)[0];
			Fibo_maxH[0] = MAX(Fibo_h, Fibo_Period)[0];
			Fibo_Hh[0] = CurrentBar == 0 ? 0 : Fibo_h[0] > Fibo_maxH[1] ? Fibo_h[0] : Fibo_Hh[1];
			Fibo_Ll[0] = CurrentBar == 0 ? 0 : Fibo_l[0] < Fibo_minL[1] ? Fibo_l[0] : Fibo_Ll[1];
			Fibo_trend[0] = CurrentBar == 0 ? 0 : Fibo_h[0] > Fibo_maxH[1] ? 1 : Fibo_l[0] < Fibo_minL[1] ? -1 : Fibo_trend[1];;
			Fibo_direction[0] = CurrentBar == 0 ? 0 : Fibo_trend[0] != 1 ? -1 : Fibo_trend[0] == 1 ? 1 : Fibo_direction[1];
			
		//--Strategy
			Buy[0] = (!Include_Fibo || Fibo_direction[0] == 1) && (!Include_RSI || RSI_direction[0] == 1);
			Sell[0] = (!Include_Fibo || Fibo_direction[0] == -1) && (!Include_RSI || RSI_direction[0] == -1);
			
			if(ShowSignalVerticalLines && Buy[0] && !Buy[1])
				Draw.VerticalLine(this, Buy[0].ToString()+CurrentBar.ToString(), 0, Brushes.Lime, DashStyleHelper.Dash, 2, IsOverlay);
			if(ShowSignalVerticalLines && Sell[0] && !Sell[1])
				Draw.VerticalLine(this, Sell[0].ToString()+CurrentBar.ToString(), 0, Brushes.Red, DashStyleHelper.Dash, 2, IsOverlay);
			
			Strategy_Buy[0] = Buy[0] && MACDBB_Buy && RSM_Buy;
			Strategy_Sell[0] = Sell[0] && MACDBB_Sell && RSM_Sell;
			
			bool Strategy_BuySignal = Strategy_Buy[0] && !Strategy_Buy[1];
			bool Strategy_SellSignal = Strategy_Sell[0] && !Strategy_Sell[1];
			
			if(drawSignal)
			{
				if(Strategy_BuySignal)
					Draw.Text(this, "MACDRSIFSTBuy"+CurrentBar.ToString(), false, "5", 0, Low[0] - arrowD*TickSize, 0, arrowUpBrush, new SimpleFont("Webdings", 20), TextAlignment.Center, Brushes.Transparent, areaBrush, opacity);
					//Draw.ArrowLine(this, "MACDRSIFSTBuy"+CurrentBar.ToString(), 0, Low[0] - arrowD*TickSize, 0, Low[0] - 4*TickSize, arrowUpBrush);
				if(Strategy_SellSignal)
					Draw.Text(this, "MACDRSIFSTSell"+CurrentBar.ToString(), false, "6", 0, High[0] + arrowD*TickSize, 0, arrowDnBrush, new SimpleFont("Webdings", 20), TextAlignment.Center, Brushes.Transparent, areaBrush, opacity);
					//Draw.ArrowLine(this, "MACDRSIFSTSell"+CurrentBar.ToString(), 0, High[0] + arrowD*TickSize, 0, High[0] + 4*TickSize, arrowDnBrush);
			}			
			_BuySell[0] = Strategy_BuySignal ? 1 : Strategy_SellSignal ? -1 : 0;
		}
		
		#region Properties
		
		[NinjaScriptProperty]
		[Display(Name="MACD Fast", Description="MACD fast period.", Order=1, GroupName="Parameters")]
		public int _MACD_Fast
		{ 
			get
			{
				return this.MACD_Fast;
			}
			set
			{
				this.MACD_Fast = value;
			}
		}
		
		[NinjaScriptProperty]
		[Display(Name="MACD Slow", Description="MACD slow period.", Order=2, GroupName="Parameters")]
		public int _MACD_Slow
		{ 
			get
			{
				return this.MACD_Slow;
			}
			set
			{
				this.MACD_Slow = value;
			}
		}
		
		[NinjaScriptProperty]
		[Display(Name="MACD Smooth", Description="MACD smoothing.", Order=3, GroupName="Parameters")]
		public int _MACD_Smooth
		{ 
			get
			{
				return this.MACD_Smooth;
			}
			set
			{
				this.MACD_Smooth = value;
			}
		}
		
		[NinjaScriptProperty]
		[Display(Name="Bollinger Std Dev", Description="Bollinger standard deviation.", Order=4, GroupName="Parameters")]
		public double _BB_NumDev
		{ 
			get
			{
				return this.BB_NumDev;
			}
			set
			{
				this.BB_NumDev = value;
			}
		}
		
		[NinjaScriptProperty]
		[Display(Name="Squeeze Period", Description="Squeeze period.", Order=5, GroupName="Parameters")]
		public int _SQZ_Period
		{ 
			get
			{
				return this.SQZ_Period;
			}
			set
			{
				this.SQZ_Period = value;
			}
		}
		
		[NinjaScriptProperty]
		[Display(Name="RSI Period", Description="RSI period.", Order=6, GroupName="Parameters")]
		public int _rsiPeriod
		{ 
			get
			{
				return this.rsiPeriod;
			}
			set
			{
				this.rsiPeriod = value;
			}
		}
		
		[NinjaScriptProperty]
		[Display(Name="RSI Smooth", Description="RSI smoothing.", Order=7, GroupName="Parameters")]
		public int _rsiSmooth
		{ 
			get
			{
				return this.rsiSmooth;
			}
			set
			{
				this.rsiSmooth = value;
			}
		}
		
		[NinjaScriptProperty]
		[Display(Name="Fibonacci Period", Description="Fibonacci period.", Order=8, GroupName="Parameters")]
		public int _Fibo_Period
		{ 
			get
			{
				return this.Fibo_Period;
			}
			set
			{
				this.Fibo_Period = value;
			}
		}
		
		[NinjaScriptProperty]
		[Display(Name="Fibonacci Retrace", Description="Fibonacci retrace.", Order=9, GroupName="Parameters")]
		public double _Fibo_Retrace
		{ 
			get
			{
				return this.Fibo_Retrace;
			}
			set
			{
				this.Fibo_Retrace = value;
			}
		}
		
		[NinjaScriptProperty]
		[Display(Name="Fibonacci Use High/Low", Description="Fibonacci use high or low.", Order=10, GroupName="Parameters")]
		public bool _Fibo_UseHighLow
		{ 
			get
			{
				return this.Fibo_UseHighLow;
			}
			set
			{
				this.Fibo_UseHighLow = value;
			}
		}
		
		//[NinjaScriptProperty]
		[Display(Name="Show Vertical Line", Description="Show vertical line.", Order=1, GroupName="Signal")]
		public bool _ShowSignalVerticalLines
		{ 
			get
			{
				return this.ShowSignalVerticalLines;
			}
			set
			{
				this.ShowSignalVerticalLines = value;
			}
		}
		
		[NinjaScriptProperty]
		[Display(Name="Include Fibonacci", Description="Include Fibonacci in the signal calculation.", Order=2, GroupName="Signal")]
		public bool _Include_Fibo
		{ 
			get
			{
				return this.Include_Fibo;
			}
			set
			{
				this.Include_Fibo = value;
			}
		}
		
		[NinjaScriptProperty]
		[Display(Name="Include RSI", Description="Include RSI in the signal calculation.", Order=3, GroupName="Signal")]
		public bool _Include_RSI
		{ 
			get
			{
				return this.Include_RSI;
			}
			set
			{
				this.Include_RSI = value;
			}
		}
		
		//[NinjaScriptProperty]
		[Display(Name="Draw Arrow", Description="Draw signals as an arrow.", Order=4, GroupName="Signal")]
		public bool _drawSignal
		{ 
			get
			{
				return this.drawSignal;
			}
			set
			{
				this.drawSignal = value;
			}
		}
		
		//[NinjaScriptProperty]
		[XmlIgnore()]
		[Display(Name = "Arrow Up Color", Order = 5, GroupName = "Signal")]
		public Brush ArrowUpBrush
		{ 
			get
			{
				return this.arrowUpBrush;
			}
			set
			{
				this.arrowUpBrush = value;
			}
		}
		
		[Browsable(false)]
		public string ArrowUpBrushS
		{
			get { return Serialize.BrushToString(ArrowUpBrush); }
			set { ArrowUpBrush = Serialize.StringToBrush(value); }
		}
		
		//[NinjaScriptProperty]
		[XmlIgnore()]
		[Display(Name = "Arrow Down Color", Order = 6, GroupName = "Signal")]
		public Brush ArrowDnBrush
		{ 
			get
			{
				return this.arrowDnBrush;
			}
			set
			{
				this.arrowDnBrush = value;
			}
		}
		
		[Browsable(false)]
		public string ArrowDnBrushS
		{
			get { return Serialize.BrushToString(ArrowDnBrush); }
			set { ArrowDnBrush = Serialize.StringToBrush(value); }
		}		
		
		//[NinjaScriptProperty]
		[XmlIgnore()]
		[Display(Name = "Area Color", Order = 7, GroupName = "Signal")]
		public Brush AreaBrush
		{ 
			get
			{
				return this.areaBrush;
			}
			set
			{
				this.areaBrush = value;
			}
		}
		
		[Browsable(false)]
		public string AreaBrushS
		{
			get { return Serialize.BrushToString(AreaBrush); }
			set { AreaBrush = Serialize.StringToBrush(value); }
		}
		
		//[NinjaScriptProperty]
		[Range(8, int.MaxValue)]
		[Display(Name="Area Opacity", Description="", Order=8, GroupName="Signal")]
		public int _opacity
		{ 
			get
			{
				return this.opacity;
			}
			set
			{
				this.opacity = value;
			}
		}
				
		//[NinjaScriptProperty]
		[Range(8, int.MaxValue)]
		[Display(Name="Arrow Distance", Description="", Order=9, GroupName="Signal")]
		public int _arrowD
		{ 
			get
			{
				return this.arrowD;
			}
			set
			{
				this.arrowD = value;
			}
		}
		
		[Browsable(false)]
		[XmlIgnore]
		public Series<double> Trend_Line
		{
			get { return Values[0]; }
		}
		
		[Browsable(false)]
		[XmlIgnore]
		public Series<double> Trend_Dots
		{
			get { return Values[1]; }
		}
		
		[Browsable(false)]
		[XmlIgnore]
		public Series<double> Trend_Diff
		{
			get { return Values[2]; }
		}
		
		[Browsable(false)]
		[XmlIgnore]
		public Series<double> Trend_Upper
		{
			get { return Values[3]; }
		}
		
		[Browsable(false)]
		[XmlIgnore]
		public Series<double> Trend_Lower
		{
			get { return Values[4]; }
		}
		
		[Browsable(false)]
		[XmlIgnore]
		public Series<double> Trend_Midline
		{
			get { return Values[5]; }
		}
		
		[Browsable(false)]
		[XmlIgnore]
		public Series<double> _BuySell
		{
			get { return Values[6]; }
		}
		
		
		#endregion
	}
}

#region NinjaScript generated code. Neither change nor remove.

namespace NinjaTrader.NinjaScript.Indicators
{
	public partial class Indicator : NinjaTrader.Gui.NinjaScript.IndicatorRenderBase
	{
		private VOLAREIndicators.JTNC_MovAvgTrend[] cacheJTNC_MovAvgTrend;
		public VOLAREIndicators.JTNC_MovAvgTrend JTNC_MovAvgTrend(int _mACD_Fast, int _mACD_Slow, int _mACD_Smooth, double _bB_NumDev, int _sQZ_Period, int _rsiPeriod, int _rsiSmooth, int _fibo_Period, double _fibo_Retrace, bool _fibo_UseHighLow, bool _include_Fibo, bool _include_RSI)
		{
			return JTNC_MovAvgTrend(Input, _mACD_Fast, _mACD_Slow, _mACD_Smooth, _bB_NumDev, _sQZ_Period, _rsiPeriod, _rsiSmooth, _fibo_Period, _fibo_Retrace, _fibo_UseHighLow, _include_Fibo, _include_RSI);
		}

		public VOLAREIndicators.JTNC_MovAvgTrend JTNC_MovAvgTrend(ISeries<double> input, int _mACD_Fast, int _mACD_Slow, int _mACD_Smooth, double _bB_NumDev, int _sQZ_Period, int _rsiPeriod, int _rsiSmooth, int _fibo_Period, double _fibo_Retrace, bool _fibo_UseHighLow, bool _include_Fibo, bool _include_RSI)
		{
			if (cacheJTNC_MovAvgTrend != null)
				for (int idx = 0; idx < cacheJTNC_MovAvgTrend.Length; idx++)
					if (cacheJTNC_MovAvgTrend[idx] != null && cacheJTNC_MovAvgTrend[idx]._MACD_Fast == _mACD_Fast && cacheJTNC_MovAvgTrend[idx]._MACD_Slow == _mACD_Slow && cacheJTNC_MovAvgTrend[idx]._MACD_Smooth == _mACD_Smooth && cacheJTNC_MovAvgTrend[idx]._BB_NumDev == _bB_NumDev && cacheJTNC_MovAvgTrend[idx]._SQZ_Period == _sQZ_Period && cacheJTNC_MovAvgTrend[idx]._rsiPeriod == _rsiPeriod && cacheJTNC_MovAvgTrend[idx]._rsiSmooth == _rsiSmooth && cacheJTNC_MovAvgTrend[idx]._Fibo_Period == _fibo_Period && cacheJTNC_MovAvgTrend[idx]._Fibo_Retrace == _fibo_Retrace && cacheJTNC_MovAvgTrend[idx]._Fibo_UseHighLow == _fibo_UseHighLow && cacheJTNC_MovAvgTrend[idx]._Include_Fibo == _include_Fibo && cacheJTNC_MovAvgTrend[idx]._Include_RSI == _include_RSI && cacheJTNC_MovAvgTrend[idx].EqualsInput(input))
						return cacheJTNC_MovAvgTrend[idx];
			return CacheIndicator<VOLAREIndicators.JTNC_MovAvgTrend>(new VOLAREIndicators.JTNC_MovAvgTrend(){ _MACD_Fast = _mACD_Fast, _MACD_Slow = _mACD_Slow, _MACD_Smooth = _mACD_Smooth, _BB_NumDev = _bB_NumDev, _SQZ_Period = _sQZ_Period, _rsiPeriod = _rsiPeriod, _rsiSmooth = _rsiSmooth, _Fibo_Period = _fibo_Period, _Fibo_Retrace = _fibo_Retrace, _Fibo_UseHighLow = _fibo_UseHighLow, _Include_Fibo = _include_Fibo, _Include_RSI = _include_RSI }, input, ref cacheJTNC_MovAvgTrend);
		}
	}
}

namespace NinjaTrader.NinjaScript.MarketAnalyzerColumns
{
	public partial class MarketAnalyzerColumn : MarketAnalyzerColumnBase
	{
		public Indicators.VOLAREIndicators.JTNC_MovAvgTrend JTNC_MovAvgTrend(int _mACD_Fast, int _mACD_Slow, int _mACD_Smooth, double _bB_NumDev, int _sQZ_Period, int _rsiPeriod, int _rsiSmooth, int _fibo_Period, double _fibo_Retrace, bool _fibo_UseHighLow, bool _include_Fibo, bool _include_RSI)
		{
			return indicator.JTNC_MovAvgTrend(Input, _mACD_Fast, _mACD_Slow, _mACD_Smooth, _bB_NumDev, _sQZ_Period, _rsiPeriod, _rsiSmooth, _fibo_Period, _fibo_Retrace, _fibo_UseHighLow, _include_Fibo, _include_RSI);
		}

		public Indicators.VOLAREIndicators.JTNC_MovAvgTrend JTNC_MovAvgTrend(ISeries<double> input , int _mACD_Fast, int _mACD_Slow, int _mACD_Smooth, double _bB_NumDev, int _sQZ_Period, int _rsiPeriod, int _rsiSmooth, int _fibo_Period, double _fibo_Retrace, bool _fibo_UseHighLow, bool _include_Fibo, bool _include_RSI)
		{
			return indicator.JTNC_MovAvgTrend(input, _mACD_Fast, _mACD_Slow, _mACD_Smooth, _bB_NumDev, _sQZ_Period, _rsiPeriod, _rsiSmooth, _fibo_Period, _fibo_Retrace, _fibo_UseHighLow, _include_Fibo, _include_RSI);
		}
	}
}

namespace NinjaTrader.NinjaScript.Strategies
{
	public partial class Strategy : NinjaTrader.Gui.NinjaScript.StrategyRenderBase
	{
		public Indicators.VOLAREIndicators.JTNC_MovAvgTrend JTNC_MovAvgTrend(int _mACD_Fast, int _mACD_Slow, int _mACD_Smooth, double _bB_NumDev, int _sQZ_Period, int _rsiPeriod, int _rsiSmooth, int _fibo_Period, double _fibo_Retrace, bool _fibo_UseHighLow, bool _include_Fibo, bool _include_RSI)
		{
			return indicator.JTNC_MovAvgTrend(Input, _mACD_Fast, _mACD_Slow, _mACD_Smooth, _bB_NumDev, _sQZ_Period, _rsiPeriod, _rsiSmooth, _fibo_Period, _fibo_Retrace, _fibo_UseHighLow, _include_Fibo, _include_RSI);
		}

		public Indicators.VOLAREIndicators.JTNC_MovAvgTrend JTNC_MovAvgTrend(ISeries<double> input , int _mACD_Fast, int _mACD_Slow, int _mACD_Smooth, double _bB_NumDev, int _sQZ_Period, int _rsiPeriod, int _rsiSmooth, int _fibo_Period, double _fibo_Retrace, bool _fibo_UseHighLow, bool _include_Fibo, bool _include_RSI)
		{
			return indicator.JTNC_MovAvgTrend(input, _mACD_Fast, _mACD_Slow, _mACD_Smooth, _bB_NumDev, _sQZ_Period, _rsiPeriod, _rsiSmooth, _fibo_Period, _fibo_Retrace, _fibo_UseHighLow, _include_Fibo, _include_RSI);
		}
	}
}

#endregion
