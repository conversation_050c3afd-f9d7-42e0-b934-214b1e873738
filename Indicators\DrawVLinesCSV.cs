#region Using declarations
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Gui.Tools;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.DrawingTools;
#endregion

//This namespace holds Indicators in this folder and is required. Do not change it. 
namespace NinjaTrader.NinjaScript.Indicators
{
	public class DrawVLinesCSV : Indicator
	{
		#region Variables
		
		private const int LINE_LENGTH_TICKS = 100000;
		private const string TAG_SUFFIX = "_VLine";
		private List<DateTime> lineTimes;
		private DateTime lastProcessedDay;
		
		#endregion
		
		protected override void OnStateChange()
		{
			if (State == State.SetDefaults)
			{
				/*
				Description									= @"Draws a vertical line at a specified time. Same line will appear on all days---designed for intraday charts.";
				Name										= "DrawVLinesCSV";
				Calculate									= Calculate.OnBarClose;
				IsOverlay									= true;
				DisplayInDataBox							= false;
				DrawOnPricePanel							= false;
				DrawHorizontalGridLines						= false;
				DrawVerticalGridLines						= false;
				PaintPriceMarkers							= false;
				ScaleJustification							= NinjaTrader.Gui.Chart.ScaleJustification.Right;
				// Disable this property if your indicator requires custom values that cumulate with each new market data event. 
				// See Help Guide for additional information.
				IsSuspendedWhileInactive					= true;
				*/
				
				Description									= @"Reads time values from a CSV file and draws lines on the chart.";
				Name										= "DrawVLinesCSV";
				Calculate									= Calculate.OnBarClose;
				IsOverlay									= true;
				
				FilePrefix									= "SDI_";
				ColumnDate									= 1;
				ColumnTime									= 4;
				DaysBack									= 30;
				SkipWeekends								= true;

				LineColor									= Brushes.Yellow;
				LineStyle									= DashStyleHelper.Solid;
				LineWidth									= 2;
				LineOpacity									= 50;
			}
			else if (State == State.Configure)
			{
				lineTimes = new List<DateTime>();
				lastProcessedDay = DateTime.MinValue;
				AddDataSeries(BarsPeriodType.Month, 1);
			}
			else if (State == State.Historical)
			{
				// Make sure our object plots behind the chart bars
				if (ZOrder != -1) SetZOrder(-1);
			}
		}

		protected override void OnBarUpdate()
		{
			if (BarsInProgress != 0)
				return;

			// Don't think we need Historical...(?)
			if (State == State.Historical)		return;
			
			string format = "yyyyMM";
			DateTime currentDay = Times[0][0].Date;
			string today = currentDay.ToShortDateString();
			string fileName = FilePrefix + currentDay.ToString(format) + ".csv";
			
			if (currentDay.Month != lastProcessedDay.Month)
			{
				Print($"\n\ncurrentDay ( {currentDay.ToString()} ) != lastProcessedDay ( {lastProcessedDay.ToString()} )");
				Print($"FILE NAME: {fileName}");
				lineTimes.Clear();
				ReadCSVFile(fileName);
				lastProcessedDay = currentDay;
				
				RemoveDrawObjects();
				
				int totalBars = CurrentBars[0];
				int lastBarFound = totalBars - 2;	// skip left-most bar so we can reference Time[j+1]
				int i = 0;
				Print($"\nA: CurrentBars[0] = {CurrentBars[0]}");
				foreach (var time in lineTimes)
				{
					//Print("time value in lineTimes: " + time.ToString());
					if (Times[0][0].Date <= time.Date  &&  Times[0][0].Date >= time.Date.AddDays(-DaysBack))
					{
						bool weekend = (time.DayOfWeek == DayOfWeek.Saturday  ||  time.DayOfWeek == DayOfWeek.Sunday);
						Print($"\nB: weekend = {weekend}, time.DayOfWeek = {time.DayOfWeek}, time = {time.ToString()}");
						if (!SkipWeekends  ||  !weekend)
						{
							// initiate new solid color brush which has an alpha (transparency) value of 100
							//myBrush = new SolidColorBrush(Color.FromArgb(100, 56, 120, 153));
							//myBrush.Freeze();
							
							// Find the exact bar closest to this time
							// We assume lineTimes is sequential; index zero is oldest
							Print($"C: lastBarFound = {lastBarFound}");
							DateTime barTime = time;
							bool found = false;
							for (int j=lastBarFound; j >= 0; j--)
							{
								if (Time[j] == time  ||  Time[j+1] == time)
								{
									Print($"Found exact bar time ({barTime.ToString()}) @ index {j} or {j+1}");
									found = true;
									lastBarFound = j+1;
									break;	// Do nothing; we found exact bar
								}
								else if (Time[j] > time  &&  Time[j+1] < time)
								{
									barTime = Time[j+1];	// Put line on bar before
									Print($"Found closest bar time @ index {j+1} @ {barTime.ToString()}");
									found = true;
									lastBarFound = j+1;
									break;	// Found bar to use for this time, so break
								}
							}
							string tag = time.ToString() + TAG_SUFFIX + "_" + i;
							i++;
							Draw.VerticalLine(this, tag, barTime, LineColor, LineStyle, LineWidth, true);
							Print("Drew Line @ " + time.ToString() + ", with tag: " + tag);
						}
					}
				}
			}
		}

		private void ReadCSVFile(string fileName)
		{
			string filePath = @"S:\Documents\NinjaTrader 8\" + fileName;
			
			if (!File.Exists(filePath))
			{
				Print($"File not found: {filePath}");
				return;
			}
			try
			{
				// Define the date format expected in your CSV file
				string dateFormat = "MM/dd/yyyy";
				
				using (var reader = new StreamReader(filePath))
				{
					while (!reader.EndOfStream)
					{
						var line = reader.ReadLine();
						var values = line.Split(',');
						
						if (values.Length >= ColumnDate && values.Length >= ColumnTime)
						{
							// Parse the date using specified formats
							//if (DateTime.TryParseExact(values[ColumnDate - 1], dateFormat, CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime dateValue) &&
							//	DateTime.TryParse(values[ColumnTime - 1], out DateTime timeValue))
							if (DateTime.TryParseExact(values[ColumnDate - 1], dateFormat, CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime dateValue))
							{
								for (int i=ColumnTime-1; i < values.Length; i++)
								{
									if (DateTime.TryParse(values[i], out DateTime timeValue))
									{
										// Combine date and time into a single DateTime object
										DateTime combinedDateTime = dateValue.Date + timeValue.TimeOfDay;
										lineTimes.Add(combinedDateTime);
									}
								}
							}
						}
					}
				}
			}
			catch (Exception ex)
			{
				Print($"Error reading file: {ex.Message}");
			}
		}

		#region Properties
		
		[NinjaScriptProperty]
		[Display(Name = "FilePrefix", Order = 1, GroupName = "Parameters")]
		public string FilePrefix { get; set; }
		
		[NinjaScriptProperty]
		[Display(Name = "ColumnDate", Order = 2, GroupName = "Parameters")]
		public int ColumnDate { get; set; }
		
		[NinjaScriptProperty]
		[Display(Name = "ColumnTime", Order = 3, GroupName = "Parameters")]
		public int ColumnTime { get; set; }
		
		[NinjaScriptProperty]
		[Display(Name = "DaysBack", Order = 4, GroupName = "Parameters")]
		public int DaysBack { get; set; }
		
		[NinjaScriptProperty]
		[Display(Name = "SkipWeekends", Order = 5, GroupName = "Parameters")]
		public bool SkipWeekends { get; set; }
		
		[NinjaScriptProperty]
		[XmlIgnore]
		[Display(Name="LineColor", Description="Color of the lines", Order=2, GroupName="Line Properties")]
		public Brush LineColor
		{ get; set; }

		[Browsable(false)]
		public string LineColorSerializable
		{
			get { return Serialize.BrushToString(LineColor); }
			set { LineColor = Serialize.StringToBrush(value); }
		}			

		[NinjaScriptProperty]
		[XmlIgnore]
		[Display(Name="LineStyle", Description="Dash style of lines", Order=3, GroupName="Line Properties")]
		public DashStyleHelper LineStyle
		{ get; set; }

		[Browsable(false)]
		public string LineStyleSerializable
		{
			get { return LineStyle.ToString(); }
			set { LineStyle = DeSerializeDashStyle(value); }
		}			

		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name="LineWidth", Description="Thickness of lines", Order=4, GroupName="Line Properties")]
		public int LineWidth
		{ get; set; }

		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name="LineOpacity", Description="Opacity of lines", Order=5, GroupName="Line Properties")]
		public int LineOpacity
		{ get; set; }

		// DashStyle DeSerializer
		public DashStyleHelper DeSerializeDashStyle(string dashStyle)
		{
			if (dashStyle == null) return DashStyleHelper.Solid;
			
			if (dashStyle.Equals("Dash")) return DashStyleHelper.Dash;
			else if (dashStyle.Equals("DashDot")) return DashStyleHelper.DashDot;
			else if (dashStyle.Equals("DashDotDot")) return DashStyleHelper.DashDotDot;
			else if (dashStyle.Equals("Dot")) return DashStyleHelper.Dot;
			else if (dashStyle.Equals("Solid")) return DashStyleHelper.Solid;
			return DashStyleHelper.Solid;	// Deafult if XML is messed up
		}
		#endregion
	}
}

#region NinjaScript generated code. Neither change nor remove.

namespace NinjaTrader.NinjaScript.Indicators
{
	public partial class Indicator : NinjaTrader.Gui.NinjaScript.IndicatorRenderBase
	{
		private DrawVLinesCSV[] cacheDrawVLinesCSV;
		public DrawVLinesCSV DrawVLinesCSV(string filePrefix, int columnDate, int columnTime, int daysBack, bool skipWeekends, Brush lineColor, DashStyleHelper lineStyle, int lineWidth, int lineOpacity)
		{
			return DrawVLinesCSV(Input, filePrefix, columnDate, columnTime, daysBack, skipWeekends, lineColor, lineStyle, lineWidth, lineOpacity);
		}

		public DrawVLinesCSV DrawVLinesCSV(ISeries<double> input, string filePrefix, int columnDate, int columnTime, int daysBack, bool skipWeekends, Brush lineColor, DashStyleHelper lineStyle, int lineWidth, int lineOpacity)
		{
			if (cacheDrawVLinesCSV != null)
				for (int idx = 0; idx < cacheDrawVLinesCSV.Length; idx++)
					if (cacheDrawVLinesCSV[idx] != null && cacheDrawVLinesCSV[idx].FilePrefix == filePrefix && cacheDrawVLinesCSV[idx].ColumnDate == columnDate && cacheDrawVLinesCSV[idx].ColumnTime == columnTime && cacheDrawVLinesCSV[idx].DaysBack == daysBack && cacheDrawVLinesCSV[idx].SkipWeekends == skipWeekends && cacheDrawVLinesCSV[idx].LineColor == lineColor && cacheDrawVLinesCSV[idx].LineStyle == lineStyle && cacheDrawVLinesCSV[idx].LineWidth == lineWidth && cacheDrawVLinesCSV[idx].LineOpacity == lineOpacity && cacheDrawVLinesCSV[idx].EqualsInput(input))
						return cacheDrawVLinesCSV[idx];
			return CacheIndicator<DrawVLinesCSV>(new DrawVLinesCSV(){ FilePrefix = filePrefix, ColumnDate = columnDate, ColumnTime = columnTime, DaysBack = daysBack, SkipWeekends = skipWeekends, LineColor = lineColor, LineStyle = lineStyle, LineWidth = lineWidth, LineOpacity = lineOpacity }, input, ref cacheDrawVLinesCSV);
		}
	}
}

namespace NinjaTrader.NinjaScript.MarketAnalyzerColumns
{
	public partial class MarketAnalyzerColumn : MarketAnalyzerColumnBase
	{
		public Indicators.DrawVLinesCSV DrawVLinesCSV(string filePrefix, int columnDate, int columnTime, int daysBack, bool skipWeekends, Brush lineColor, DashStyleHelper lineStyle, int lineWidth, int lineOpacity)
		{
			return indicator.DrawVLinesCSV(Input, filePrefix, columnDate, columnTime, daysBack, skipWeekends, lineColor, lineStyle, lineWidth, lineOpacity);
		}

		public Indicators.DrawVLinesCSV DrawVLinesCSV(ISeries<double> input , string filePrefix, int columnDate, int columnTime, int daysBack, bool skipWeekends, Brush lineColor, DashStyleHelper lineStyle, int lineWidth, int lineOpacity)
		{
			return indicator.DrawVLinesCSV(input, filePrefix, columnDate, columnTime, daysBack, skipWeekends, lineColor, lineStyle, lineWidth, lineOpacity);
		}
	}
}

namespace NinjaTrader.NinjaScript.Strategies
{
	public partial class Strategy : NinjaTrader.Gui.NinjaScript.StrategyRenderBase
	{
		public Indicators.DrawVLinesCSV DrawVLinesCSV(string filePrefix, int columnDate, int columnTime, int daysBack, bool skipWeekends, Brush lineColor, DashStyleHelper lineStyle, int lineWidth, int lineOpacity)
		{
			return indicator.DrawVLinesCSV(Input, filePrefix, columnDate, columnTime, daysBack, skipWeekends, lineColor, lineStyle, lineWidth, lineOpacity);
		}

		public Indicators.DrawVLinesCSV DrawVLinesCSV(ISeries<double> input , string filePrefix, int columnDate, int columnTime, int daysBack, bool skipWeekends, Brush lineColor, DashStyleHelper lineStyle, int lineWidth, int lineOpacity)
		{
			return indicator.DrawVLinesCSV(input, filePrefix, columnDate, columnTime, daysBack, skipWeekends, lineColor, lineStyle, lineWidth, lineOpacity);
		}
	}
}

#endregion
