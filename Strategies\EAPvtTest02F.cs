//
#region Using declarations
using System;
using System.IO;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.Indicators;
using NinjaTrader.NinjaScript.DrawingTools;
#endregion

// KEN'S VERSION OF MY TepVDS (using <PERSON>'s PVT indicator)
//
// 07/26/23 quickie ea to test pvt
// 08/13/23 fix bug in object look-up
// 08/14/23 add HMA trail stop, EMA and OPL, good test needs a few mods
// 08/15/23 add HMA input,allow 0 on both lgths, only 1 trade per pvt
// 08/16/23 add 2 bar trailing stop (dont like this, save the code and then remove it)
// 08/17/23 switch chart to 15Sec, removed EMA and HMA, added MOM
// 08/18/23 run pvt and chart both in panel 1, remove both MAs, use Mom 1.5x chart size for TrailStop
// 08/19/23 pp pvt-to-pvt no TS no OPL
// 08/20/23 pp pvt-to-pvt w HullMaTrailStop, wait 1 bar for OPL to show dir and all data to paint
// 08/22/23 add specific trading days of week
// 09/15/23 fix days of week inputs
// 11/29/23 add 2S ds for timer for quicker trailingstop, input for quantity
// 02/21/25 fix some bugs
// 03/19/25 fix some bugs
// 03/23/25 add summary text to last page
// 03/31/25 a few fixes
// 04/01/25 add quick exit when lower TF low crosses hullma
// 04/02/25 nop - add trailing stop live pending order, emergency only
// 04/07/25 due to stupid NT load problems, use daysofweek selection to test just that one day 
// 04/14/25 bring pnl graf up to date
// 04/16/25 assure dates into compare are valid, get correct opl 
// 04/29/25 bug fix hull exit required, add exits for MaxLoss, MaxGain, MaxGiveBack
// . . . . .also, object list lookup is delayed for 1 bar to allow time for it to post
// . . . . .and be available for squirlley bar types like renko etc
// 05/01/25 save backup before major mods for trailing stop
// . . . . . TS working good
// 05/02/25 save backup before mods for input to trailing stop
// . . . . . TS offset from prior lo/hi
//
namespace NinjaTrader.NinjaScript.Strategies
{
	public class EAPvtTest02F : Strategy
	{
		private Brush clrDiamond = Brushes.Red;
		// inputs over-ride in properties window
		private bool bBackTest = true;
		//private int iBarsBack = 5;  // trail bars back - not done
		private int iQty1 = 1;
		private int iTPTiks = 0;
		//private int iSLTiks = 0;
		private int iTSTiksAway = 14;
		private int iHullMaPdEn = 0;   // hull ma entry filter
		private int iHullMaPdOt = 21;   // hull ma exit filter
		// added 04/29/25 for controlled quit
		//private double dMaxLoss = 1000;      // enter as positive number
		//private double dMaxGain = 1000;      // enter as positive number
		//private double dMaxGiveBack = 50;    // percentage whole number
		// misc not used
		private int iYYYYtoTest = 0;
		private int iMMtoTest = 0;
		private int iDDtoTest = 0;
		private string sYYYYtoTest = "x";
		private string sMMtoTest = "x";
		private string sDDtoTest = "x";
		
		// default start and end times session 1 - 4
		private int iStr1HHMMSS = 0;
		private int iEnd1HHMMSS = 0;
		private int iStr2HHMMSS = 0;
		private int iEnd2HHMMSS = 0;
		private int iStr3HHMMSS = 0;
		private int iEnd3HHMMSS = 0;
		private int iStr4HHMMSS = 0;
		private int iEnd4HHMMSS = 0;
		
		// default day of week
		private bool bSunOn = false;
		private bool bMonOn = false;
		private bool bTueOn = false;
		private bool bWedOn = false;
		private bool bThuOn = false;
		private bool bFriOn = false;
		private bool bSatOn = false;
		//
		private bool bKeepIt = false;
		private string sDyOfWk = "x";
		private int iResult = 0;
		
		private bool bKillIt = false;
		private bool bRealTim = false;
		
		private string sLineOut = "x";
		private string sTagSearch = "x";
		private string sPvtUpClr = "x";
		private string sPvtDnClr = "x";
		private string sUpDnFndThis = "x";
		private string sUpDnFndPrev = "x";
		private string sFillDir = "x";
		private string sTag = "x";
		
		private double dHmaA0 = 0;
		private double dHmaA1 = 0;
		private double dHmaA2 = 0; 
		
		private double dHmaB0 = 0;
		private double dHmaB1 = 0;
		private double dHmaB2 = 0;
		
		private double dMomA0 = 0;
		private double dMomA1 = 0;
		private double dMomA2 = 0;
		
		private double dFillPri = 0;
		private double dStopPriPrev = 0;
		private double dStopPriNew = 0;
		private double dCloPri = 0;
		private double dPnL = 0;
		private double dComm = 0;
		private double dOPL = 0;
		private double dPriHi = 0;
		private double dPriLo = 0;
		private double dPriCl = 0;
		private DateTime dtPriHiLo;
		private double dPlotPri = 0;
		private double dPlotPriPrev = 0;

		private Account myAccount  = null;
		private Order myBuyMktOrder = null;
		private Order mySelMktOrder = null;
		
		private DateTime dtPVTSrch;
		private DateTime dtPVTSrchPrev;
		
		NinjaTrader.NinjaScript.DrawingTools.Text  xTX;
		
		// added 2/23/25
		private double dSavPri = 0;
		private int iDCThis = 0;
		private int iDCPrev = 0;
		private int iLastNtryMin = 0;
		private int iLastPvtMin = -1;
		private int iBarCnt1 = 0;
		private string sTagDn = "x";
		private string sTagUp = "x";
		private string sTagUp2 = "x";
		private string sTagDn2 = "x";
		private bool bNewPvt = false;
		private int iSaveLastTrdNo = -1;
		private double dPnLLastTrd = 0;
		private double dPnLTotal = 0;
		private double dNetProfitForAll = 0;
		
		// added 03/25/25 for summary stats  chg names later
		private int totalTrades = 0;
		private double dPF = 0;
		private double dDwDn = 0;
		private double totalNetProfit = 0;
		private double dOpenPnL = 0;   // 4/13 for graf
		private double dMaxNet = 0;
		private DateTime dtMaxNet; 
		
		// added 04/05/25 for output txt file 
		private string sPathPart1 = "C:\\$NinjaTrader8_";  // dir part 1 static
		private string sPathPart2 = "NQ_PnL_YYYYMMDD";    // dir part 2 built from data
		private string sPathDir = "x";  //  all the above gets put together for unique dir name
		// file name path is dir plus HrMnSc.txt
		private string sPathFile = "x";
		private string sYYYY = "x";
		private string sMM = "x";
		private string sDD = "x";
		private string sHr = "x";
		private string sMn = "x";
		private string sSc = "x";		
		private bool bPathDirDeleted = false;
		private bool bPathDirCreated = false;
		//private bool bFirstTxtOt = true;
		private StreamWriter sw;
		
		// //////////////////////////////////////////
	
		protected override void OnStateChange()
		{
			if (State == State.SetDefaults)
			{
				//Print("set defaults");
				try
				{
				//Description	= NinjaTrader.Custom.Resource.NinjaScriptStrategyDescriptionSampleMACrossOver;
				Name		= "EAPvtTest02F";
				IsInstantiatedOnEachOptimizationIteration = false;
				IsExitOnSessionCloseStrategy = false;
				//
				IsOverlay = true;  // so hma will plot
				//CALCULATE  for historical
				Calculate = Calculate.OnBarClose;
				//
				//Print("add plots");
    			AddPlot(new Stroke(Brushes.Blue, 3), PlotStyle.Line ,"Plot0");    // hma enter
				AddPlot(new Stroke(Brushes.Red, 3), PlotStyle.Line,"Plot1");      // hma exit   
				//AddPlot(new Stroke(Brushes.Black, 4), PlotStyle.Line,"Plot2");   
				//Find our Sim101 account
     			lock (Account.All)
           			myAccount = Account.All.FirstOrDefault(a => a.Name == "Sim101");
				if(myAccount == null)
				{
					// Derk 19May25 - Commented out so I do not get output in debug window when not even running this
					//Print("acct is null");
					bKillIt = true;
				}
				else
				{
					// Derk 19May25 - Commented out so I do not get output in debug window when not even running this
					//Print("acct nm, " + myAccount.Name);
				// Subscribe to order updates
			    // myAccount.OrderUpdate += OnOrderUpdate;
				}
				}
				catch
				{
					Print("err in set defaults");
				}
			}
			else if (State == State.Configure)
			{
				Print("configure");
				try
				{
				//
				//  ds0 = 1M or bigger Minute
				AddDataSeries(BarsPeriodType.Second, 1);   // ds 1  for quick exit crossing hullma
				AddDataSeries(BarsPeriodType.Second, 15);  // ds 2  for sending net pnl to txt file 
				//  ignore rejected orders	
				//Print("set ignore");
				RealtimeErrorHandling = RealtimeErrorHandling.StopCancelCloseIgnoreRejects;
				//Print("conf done");
				}
				catch (Exception e)
				{
					Print("err in configure, " + e);
				}
			}
			else if (State == State.Historical)
			{
				Print("historical");
			}
			else if (State == State.DataLoaded)
			{
				if( BarsInProgress == 0 ) 
				{ 
					// initialize saved days back for init for date compare
					dtPVTSrch = Time[0].AddDays(-1);
					dtPVTSrchPrev = Time[0].AddDays(-2);
				}
				
				if( true )   //BarsInProgress == 1 )
				{
					Print("dl , ba0 " +  BarsArray[0].Count + ", ba1, " +  BarsArray[1].Count
						+ ", tm00, " + Times[0][0].ToString() 
						+ ", tm10, " + Times[1][0].ToString() ); 
					iBarCnt1 = BarsArray[0].Count;
					Print("dy cnt, " + Bars.DayCount);
					iYYYYtoTest = BarsArray[0].ToDate.Year;
					iMMtoTest = BarsArray[0].ToDate.Month;
					iDDtoTest = BarsArray[0].ToDate.Day;     // only gives day at 12 am
					sYYYYtoTest = iYYYYtoTest.ToString("0000");
					sMMtoTest = iMMtoTest.ToString("00");
					sDDtoTest = iDDtoTest.ToString("00");
					// these dates are not correct on DCs machine
					Print("data loaded, " + Time[0].ToString() + ", mm, " + sMMtoTest
						+ ", dd, " + sDDtoTest + ", yyyy, " + sYYYYtoTest );
				}	
			}
			else if (State == State.Realtime)
			{
				bRealTim = true;
			}
			else if (State == State.Terminated)
			{
				bRealTim = false;
				//if( myAccount != null )  myAccount.OrderUpdate -= OnOrderUpdate;
			}
		}


		// ONBARUPDATE OBU
		protected override void OnBarUpdate()
		{ 
			if( bKillIt )  return;
			if( !bBackTest )
			{
			 	Print("**** ERROR This must run in BACKTEST mode only for now");
				bKillIt = true;
				int zZero = 0;
				int zAns = 0;
				zAns = 2 / zZero;
				return;
			}
			if( BarsInProgress == 0 &&  CurrentBars[0] == (BarsArray[0].Count-3) )
			{
				iBarCnt1 = BarsArray[0].Count;
				//Print("**** ALMOST finished, on bar, " + CurrentBars[0] + ",  of, " + iBarCnt1);
			    // Print out the net profit of all trades
				totalTrades = SystemPerformance.AllTrades.Count;
				totalNetProfit = SystemPerformance.AllTrades.TradesPerformance.Currency.CumProfit;
				dDwDn = SystemPerformance.AllTrades.TradesPerformance.Currency.Drawdown;
				dPF = SystemPerformance.AllTrades.TradesPerformance.ProfitFactor;
				
				//NinjaTrader.Gui.Tools.SimpleFont myFont = new NinjaTrader.Gui.Tools.SimpleFont("Courier New", 12) { Size = 30, Bold = true };
				// font set for DC
				NinjaTrader.Gui.Tools.SimpleFont myFont = 
				 	new NinjaTrader.Gui.Tools.SimpleFont("Courier New", 12) { Size = 20, Bold = false };
					//new NinjaTrader.Gui.Tools.SimpleFont("Courier New", 12) { Size = 20, Bold = true };
				
				// ///////////////////////
				string sMaxNetHr = dtMaxNet.Hour.ToString("00");
				string sMaxNetMn = dtMaxNet.Minute.ToString("00");
				string sChartData = " " + Bars.ToChartString();
				sChartData = sChartData + "\n" + " Net Profit in Period: " + totalNetProfit.ToString("0.00");
				sChartData = sChartData + "\n" + " Profit Factor: " + dPF.ToString("0.00") ;
				sChartData = sChartData + "\n" + " Total Trades: " + totalTrades;
				sChartData = sChartData + "\n" + " Highest Profit in Period: " + dMaxNet.ToString("0.00") + " @ " + sMaxNetHr + ":" + sMaxNetMn;
				sChartData = sChartData + "\n" + " Largest Drawdown in Period: " + dDwDn.ToString("0.00");
				sChartData = sChartData + "\n" + " Backtest End Date and Time: " + Time[0].ToString();
				sChartData = sChartData + "\n";
				Draw.TextFixed(this, "zStats",sChartData,TextPosition.BottomLeft,Brushes.White,myFont,Brushes.Black,Brushes.Black,100,DashStyleHelper.Solid,4,false,null);
				Print("**** stats: " + sChartData);
				// ////////////////////////////////
				bKillIt = true;
				return;
			}
			
			if( BarsInProgress == 0 )
			{ 
				
				if( iHullMaPdEn > 0 )
				{
					dHmaA2 = dHmaA1;
					dHmaA1 = dHmaA0;
					dHmaA0 = HMA(iHullMaPdEn)[0];
					Plot0[0] =  dHmaA0;
				} 
				if( iHullMaPdOt > 0 )
				{
					dHmaB2 = dHmaB1;
					dHmaB1 = dHmaB0;
					dHmaB0 = HMA(iHullMaPdOt)[0];
					Plot1[0] =  dHmaB0;
				} 
			}
			//
			// use dayofweek
			//if( Time[0].Month != iMMtoTest )  return;
			//if( Time[0].Day   != iDDtoTest )  return;
			
			//return;
			
			if( bRealTim )  return;
		
			// Checks to ensure all Bars objects contain enough bars before beginning
    		if( CurrentBars[0] < BarsRequiredToTrade 
				|| CurrentBars[1] < BarsRequiredToTrade 
				|| CurrentBars[2] < BarsRequiredToTrade  
			  )
			{
				return;
			} 
			
			if(iTPTiks > 0) SetProfitTarget(CalculationMode.Ticks, iTPTiks);
			// do not activate sl here, use ts
			//if(iSLTiks > 0) SetStopLoss(CalculationMode.Ticks, iSLTiks); 
			// send pnl to txt file  
			if( BarsInProgress == 2 )   // every x-Sec write pnl txt
			{ 
				if( SystemPerformance.AllTrades.Count == 0 )  return;
				
				// If not flat print our unrealized PnL
			    if (Position.MarketPosition != MarketPosition.Flat) 
				{
					dOpenPnL = Position.GetUnrealizedProfitLoss(PerformanceUnit.Currency, Close[0]); 
					Print("un real PnL, " + dOpenPnL.ToString("0.00")); 
					//return;
				}
				
				if( SystemPerformance.AllTrades.Count == 1 )
				{
					if( !bPathDirDeleted )
					{
						// make sure file dirs are ready
						Trade lastTrade = SystemPerformance.AllTrades[SystemPerformance.AllTrades.Count - 1];
						Execution lastExecEntry = lastTrade.Entry;
						//Execution lastExecExit = lastTrade.Exit;
						sYYYY = lastExecEntry.Time.Year.ToString("0000");  
						sMM = lastExecEntry.Time.Month.ToString("00"); 
						sDD = lastExecEntry.Time.Day.ToString("00"); 
						sPathDir = sPathPart1 + Instrument.MasterInstrument.Name + "_PnL_" + sYYYY + sMM + sDD; 
						//Print("main dir path, " + sPathDir);
						if(Directory.Exists(sPathDir))
						{
							//Print("main dir path exists already, " + sPathDir);
							Directory.Delete(sPathDir,true); 
							//Print("deleted old dir , " + sPathDir);
							bPathDirDeleted = true;
							return;
						} 
					}
				}
				//	
				if( SystemPerformance.AllTrades.Count == 2 )
				{
					if( bPathDirCreated )  return;
					//Print("bip 3, trd cnt 2, ");
					DirectoryInfo di = Directory.CreateDirectory(sPathDir);
					//Print("re-created main dir , " + sPathDir); 
					bPathDirCreated = true;
					return;
				}  
				
				if( SystemPerformance.AllTrades.Count > 2 )
				{
					//Print("bip 3, trd cnt >2, ");
					// 
					//Print("re-created main dir , " + sPathDir);
					// create line in txt file here hr mn sec00
					//Trade lastTrade = SystemPerformance.AllTrades[SystemPerformance.AllTrades.Count - 1];
					//Execution lastExecEntry = lastTrade.Entry;
					//sHr = lastExecEntry.Time.Hour.ToString("00");
					//sMn = lastExecEntry.Time.Minute.ToString("00");  wait trade not closed yet
					// does total net keep running total, or only after trade close  ???
					
					sHr = Time[0].Hour.ToString("00");
					sMn = Time[0].Minute.ToString("00");
					sSc = Time[0].Second.ToString("00");
					sPathFile = sPathDir + "\\" + sHr + sMn + sSc + ".txt";
					Print("file dir, " + sPathFile);
					totalNetProfit = SystemPerformance.AllTrades.TradesPerformance.Currency.CumProfit;
					if (Position.MarketPosition != MarketPosition.Flat) 
					{
						dOpenPnL = Position.GetUnrealizedProfitLoss(PerformanceUnit.Currency, Close[0]); 
						totalNetProfit = totalNetProfit + dOpenPnL;
					}
					sLineOut = "," + totalNetProfit.ToString("0.00") + ",x";
					Print("out line, " + sLineOut);
					sw = File.AppendText(sPathFile);    // Open the path for writing
					sw.WriteLine(sLineOut);             // Append a new line to the file
					sw.Close();                         // Close the file
					return; 
				} 
			}
			
			
	     	// trailing quick exit if short TF crosses hullma
			if( false )  //BarsInProgress == 1 )   // 1-Sec low cross hull
			{
				if( Position.MarketPosition == MarketPosition.Long ) 
				{
					if( iHullMaPdOt > 0  &&  Low[0] < dHmaB0 )
					{
						ExitLong();
						DoCalcPnL();
					}
				} 
			
				if( Position.MarketPosition == MarketPosition.Short )
				{
					if( iHullMaPdOt > 0  &&  High[0] > dHmaB0 )
					{
						ExitShort();
						DoCalcPnL();
					}
				} 
			} 
			//HERE
		    // trailing stop loss
			if( BarsInProgress == 0 ) 
			{
				if( Position.MarketPosition == MarketPosition.Long ) 
				{
					if( true )  //Close[0] > Close[1] )    // rising close
					{
						// calc new stop pri here
						// comp to prev and make sure it is GT
						dStopPriNew = Low[0] - (iTSTiksAway * TickSize);
						if( dStopPriNew > dStopPriPrev )
						{
							SetStopLoss(CalculationMode.Price, dStopPriNew);
							dStopPriPrev = dStopPriNew; 
						} 
						 //from help: paints a red diamond on the current bar 1 tick below the low
						//Draw.Diamond(this, "tag1", true, 0, Low[0] - TickSize, Brushes.Red);
						Draw.Diamond(this,"xStp_"+CurrentBar,false,Time[0],dStopPriPrev,clrDiamond);
					}
				}
						
				if( Position.MarketPosition == MarketPosition.Short )
				{  
					if( true )  //Close[0] < Close[1] )    // dropping close
					{
						// calc new stop pri here
						// comp to prev and make sure it is LT
						dStopPriNew = High[0] + (iTSTiksAway * TickSize);
						if( dStopPriNew < dStopPriPrev )
						{
							SetStopLoss(CalculationMode.Price, dStopPriNew);
							dStopPriPrev = dStopPriNew; 
						} 
						Draw.Diamond(this,"xStp_"+CurrentBar,false,Time[0],dStopPriPrev,clrDiamond);
					} 
				}
			}

			
			if( BarsInProgress == 0 )
			{
				// close any over hullma
				if( true )  
				{
					if( Position.MarketPosition == MarketPosition.Long )
					{
						if( iHullMaPdOt > 0 && Close[0] < dHmaB0 ) 
						{
							ExitLong();
							Print("clo buy, under ts at, " + Time[0].ToString() + ", cl, " + Close[0].ToString() ); 
							DoCalcPnL();
						}
					}
					if( Position.MarketPosition == MarketPosition.Short   )
					{
						if( iHullMaPdOt > 0 && Close[0] > dHmaB0 ) 
						{
							ExitShort();  //("mySelMkt");
							Print("clo sel, over ts at, " + Time[0].ToString() + ", cl, " + Close[0].ToString() ); 
							DoCalcPnL();
						}
					}
				}
			}
			
			
			if( true )  //BarsInProgress == 0 )
			{
				//
				// check for day of week
				//
				bKeepIt = false;
				sDyOfWk = Time[0].DayOfWeek.ToString()  ;
				if( sDyOfWk  ==  "Sunday" && bSunOn )   bKeepIt = true;
				if( sDyOfWk  ==  "Monday" && bMonOn )   bKeepIt = true;
				if( sDyOfWk  ==  "Tuesday" && bTueOn )   bKeepIt = true;
				if( sDyOfWk  ==  "Wednesday" && bWedOn )   bKeepIt = true;
				if( sDyOfWk  ==  "Thursday" && bThuOn )   bKeepIt = true;
				if( sDyOfWk  ==  "Friday" && bFriOn )   bKeepIt = true;
				if( sDyOfWk  ==  "Saturday" && bSatOn )   bKeepIt = true;
				if( bKeepIt == false )  
				{
					ExitLong();
					ExitShort();
					DoCalcPnL();
					return;
				}
				else
				{
					//Print("keep this day, " + sDyOfWk );	
				}
				// chk for time range
				//
				if( (ToTime(Time[0])  >=  iStr1HHMMSS  && ToTime(Time[0]) <  iEnd1HHMMSS )  
				||   (ToTime(Time[0])  >=  iStr2HHMMSS  && ToTime(Time[0]) <  iEnd2HHMMSS )  
				||   (ToTime(Time[0])  >=  iStr3HHMMSS  && ToTime(Time[0]) <  iEnd3HHMMSS )  
				||   (ToTime(Time[0])  >=  iStr4HHMMSS  && ToTime(Time[0]) <  iEnd4HHMMSS )  ) 
				{
					//continue;
				}
				else
				{
					ExitLong();
					ExitShort();
					DoCalcPnL();
					return;  
				} 
			}
				//
				if( BarsInProgress == 0 ) 
				{
					if( true )   //Times[0][0].Minute != iLastPvtMin ) 
					{
						dtPVTSrch = Time[1];  
						iResult = DateTime.Compare(dtPVTSrch,dtPVTSrchPrev);
						if( iResult > 0 ) 
						{
							//Print("go get pvt for tm, " + dtPVTSrch.ToString() );
							DoGetThisPVT(dtPVTSrch);
							if( sUpDnFndThis != "x" ) 
							{
								dOPL = Close[1];
								Print("dir fnd , " + sUpDnFndThis  + ",  tm , " + dtPVTSrch.ToString()
									+ ", OPL, " + dOPL.ToString("0.00") );
								dtPVTSrchPrev = dtPVTSrch;
								sUpDnFndPrev = sUpDnFndThis;
							}
						}
					}
				}
				
				if( sUpDnFndPrev == "x" ) 
				{
					ExitLong(); 
					ExitShort(); 
					DoCalcPnL();
					//return;   //  direction unknown
				}
				
				// close any on new pvt 
				if( sUpDnFndPrev == "u" )  
				{
					if(  Position.MarketPosition == MarketPosition.Short )
					{
						ExitShort(); 
						DoCalcPnL();
						return;
					}
				} 
				if( sUpDnFndPrev == "d")  
				{
					if(  Position.MarketPosition == MarketPosition.Long )
					{
						ExitLong(); 
						DoCalcPnL();
						return;
					}
				} 
		
				

				// open new orders here if meets spec
				//
				//Print("chk for open new ord here");
				if( BarsInProgress != 0)  return; 
				if(  Position.MarketPosition != MarketPosition.Flat )  return;
				//Print("chk for open new ord here, tm, " + Time[0].ToString());
				if( true ) 
				{
					if( !bNewPvt )  return; 
					if( sUpDnFndPrev == "u" && Close[0] > dOPL   )
					{
						if( ( iHullMaPdEn > 0 &&  Close[0] > dHmaA0 )  
							||   ( iHullMaPdEn == 0  ) ) 
						{ 
							dStopPriNew = dOPL - (20.0 * TickSize);
							SetStopLoss(CalculationMode.Price, dStopPriNew);
							myBuyMktOrder = EnterLong(iQty1, "myBuyMkt");  
							Print("went long at pri, " + Close[0].ToString("0.00") 
								+ ",  tm, " + Time[0].ToString() + ",  opl, " + dOPL.ToString("0.00"));
							dStopPriNew = dOPL - (10.0 * TickSize);
							dStopPriPrev = dStopPriNew;
							SetStopLoss(CalculationMode.Price, dStopPriNew);
							Draw.Diamond(this,"xStp_"+CurrentBar,false,Time[0],dStopPriPrev,clrDiamond);
							bNewPvt = false;
							return;
						} 
					} 
				}

						
					if( sUpDnFndPrev == "d" && Close[0] < dOPL   )
					{
						if( ( iHullMaPdEn > 0 &&  Close[0] < dHmaA0 )  
							||   ( iHullMaPdEn == 0  ) ) 
						{ 
							dStopPriNew = dOPL + (20.0 * TickSize);
							SetStopLoss(CalculationMode.Price, dStopPriNew);
							mySelMktOrder = EnterShort(iQty1, "mySelMkt"); 
							Print("went short at pri, " + Close[0].ToString("0.00") 
								+ ",  tm, " + Time[0].ToString() + ",  opl, " + dOPL.ToString("0.00"));
							dStopPriNew = dOPL + (10.0 * TickSize);
							dStopPriPrev = dStopPriNew;
							SetStopLoss(CalculationMode.Price, dStopPriNew);
							Draw.Diamond(this,"xStp_"+CurrentBar,false,Time[0],dStopPriPrev,clrDiamond);
							bNewPvt = false;
							return;
						}
						return;
					}
				return;
			}

		
		
		protected void DoGetThisPVT(DateTime dtPVTSrch2)
		{
					int iRes = 0;
					//Print("Do Get this PVT,,  cb,  " + CurrentBar.ToString()   +  ",  tim, " + dtPVTSrch2.ToString());
					foreach (dynamic xObj in DrawObjects.ToList())
					{
						sUpDnFndThis = "x";
						//bNewPvt = false;
						if(xObj != null) 
						{
							if( xObj.GetType().Name.ToString().Length >= 4   
								&& xObj.GetType().Name.ToString()  == "Text"  )
							{
								// chk to see if this is on current bar
								//if(  CurrentBar.ToString()  !=  xObj.Tag.Substring(2) )  continue;   // no not cb
								//if( dtPVTSrch2  !=  xObj.Anchor.Time )  continue;   // no not cb
								//
								//if( dtPVTSrch2  !=  xObj.Anchor.Time )  continue;   // no not cb
								iRes = DateTime.Compare(dtPVTSrch2,xObj.Anchor.Time);
								if( iRes < 0 ) continue;
								if( iRes > 0 ) continue;
								// the dates must be equal
								if( xObj.DisplayText == "PVT+")
								{
									//Print("UP fnd, tm. " +  xObj.Anchor.Time.ToString());
									//dOPL = xObj.Anchor.Price;   // this price is bogus
									//dOPL = dPriCl;
									sUpDnFndThis = "u";
									bNewPvt = true;
									//iLastPvtMin = Times[0][0].Minute;
									//dtSavTim = xObj.Anchor.Time;
									//dSavPri = xObj.Anchor.Price - (16*TickSize);
									//sTagUp = "newUp" + xObj.Tag;
									//TriangleUp myTriUp  =  Draw.TriangleUp(this,sTagUp,true,dtSavTim,dSavPri,false, "xTUp");	
									//myTriUp.AreaBrush  = Brushes.Blue;
									//sTagUp2 = "newVLUp" + xObj.Tag;
									//Draw.VerticalLine(this, sTagUp2, 0,Brushes.Blue, DashStyleHelper.Solid, iWidth);
									break;
								}
								if( xObj.DisplayText == "PVT-")
								{
									//Print("DN fnd,   tm. " +  xObj.Anchor.Time.ToString());
									//dOPL = xObj.Anchor.Price;
									//dOPL = dPriCl;
									sUpDnFndThis = "d";
									bNewPvt = true;
									//iLastPvtMin = Times[0][0].Minute;
									//dtSavTim = xObj.Anchor.Time;
									//dSavPri = xObj.Anchor.Price + (16*TickSize);
									//sTagDn = "newDn" + xObj.Tag;
									//TriangleDown myTriDn  =  Draw.TriangleDown(this,sTagDn,true,dtSavTim,dSavPri,false, "xTDn");	
									//myTriDn.AreaBrush  = Brushes.Red;
									//sTagDn2 = "newVLDn" + xObj.Tag;
									//Draw.VerticalLine(this, sTagDn2, 0,Brushes.Red, DashStyleHelper.Solid, iWidth);	
									break;
								}
							}
						}
					}
		}
		
		protected void DoGetFirstPVTs()
		{
			return;  // no need for colors
					//Print("Do Get PVTs, " + Time[0].ToString() );
					if( sPvtUpClr != "x"  &&  sPvtDnClr != "x" )  return;
					foreach (dynamic xObj in DrawObjects.ToList())
					{
						if( sPvtUpClr != "x"  &&  sPvtDnClr != "x" )  break;
						if(xObj != null) 
						{
							if( (xObj.GetType().Name.ToString().Length >= 10   
								&& xObj.GetType().Name.ToString()  == "TriangleUp" && xObj.Tag.Substring(0,2) == "up" )
							 ||  (xObj.GetType().Name.ToString().Length>= 12
								&& xObj.GetType().Name.ToString()  == "TriangleDown" 
								&&    xObj.Tag.Substring(0,2) == "dn" )   )
							{
						
								// chk to see if there is a Text obj w/ this meaning PVT	
								sTagSearch = "Ar" + xObj.Tag.Substring(2);
  								if( DrawObjects[sTagSearch] == null )  continue;   //  not a PVT
								if( xObj.Tag.Substring(0,2) == "up" ) 
								{
									sPvtUpClr = xObj.AreaBrush.ToString();
									//Print("up fnd, clr, " + sPvtUpClr + ", tm. " +  xObj.Anchor.Time.ToString());
								}
								else
								{
									sPvtDnClr = xObj.AreaBrush.ToString();
									//Print( "dn fnd, clr, " + sPvtDnClr + ", tm. " +  xObj.Anchor.Time.ToString());
								}
							}
					}
				}
					if( sPvtUpClr == "x"  ||  sPvtDnClr == "x" ) 
					{
						Print("NOTICE -  something is missing in your object list");
						Print("Did not find any Triangles Up or Down for colors");
						return;
 					}
		}
		
		//
		protected override void OnOrderUpdate(Order order, double limitPrice, double stopPrice, int quantity, int filled, double averageFillPrice, OrderState orderState, DateTime time, ErrorCode error, string nativeError)
		{
			if( orderState == OrderState.Submitted )  return;
			if( orderState == OrderState.Accepted )  return;
			if( orderState == OrderState.Working )  return;
			if( orderState == OrderState.Rejected )  return; 
			if( orderState == OrderState.Unknown )  return; 
			
			//Print("HELLO , , ,OOU");
   			// Assign entryOrder in OnOrderUpdate() to ensure the assignment occurs when expected.
    		// This is more reliable than assigning Order objects in OnBarUpdate, as the assignment is not gauranteed to be complete if it is referenced immediately after submitting
			// an order was filled get the fill price
			if (order.Name == "mySelMkt" && orderState == OrderState.Filled)
			{
				dFillPri = order.AverageFillPrice;
				Print("HELLO , , ,OOU");
				Print("sel mkt fil pri, " + dFillPri.ToString("0.00") );
				sFillDir = "d";
				return;
			}
			if (order.Name == "myBuyMkt" && orderState == OrderState.Filled)
			{
				dFillPri = order.AverageFillPrice;
				Print("HELLO , , ,OOU");
				Print("buy mkt fil pri, " + dFillPri.ToString("0.00") );
				sFillDir = "u";
				return;
			}
			if (order.Name == "Buy to cover" && orderState == OrderState.Filled)
			{
				dCloPri = order.AverageFillPrice;
				dPnL = dFillPri - dCloPri;
				DoCalcPnL();
				return;
			}
			if (order.Name == "Sell" && orderState == OrderState.Filled)
			{
				dCloPri = order.AverageFillPrice;
				dPnL = dCloPri - dFillPri;
				DoCalcPnL();
				return;
			}
			//
			// other order and status
			
			if( orderState != OrderState.Filled)  return;
			
			if (order.Name == "Close" && orderState == OrderState.Filled)
			{
				Print("ord nm, " + order.Name + ",  state, " + orderState
					+ ",  filpri,  " + order.AverageFillPrice.ToString("0.00"));
				DoCalcPnL();
				return;
			} 
			if (order.Name == "Stop loss" && orderState == OrderState.Filled)
			{
				//Print("ord nm, " + order.Name + ",  state, " + orderState
				//	+ ",  filpri,  " + order.AverageFillPrice.ToString("0.00"));
				DoCalcPnL();
				return;
			}
			Print("other ord nm, " + order.Name + ",  state, " + orderState );
			DoCalcPnL();
			return;
		} 
		//
		protected void DoCalcPnL() 
		{ 
			if (SystemPerformance.AllTrades.Count > 0)
  			{
	      		Trade lastTrade = SystemPerformance.AllTrades[SystemPerformance.AllTrades.Count - 1];
				if( lastTrade.TradeNumber > iSaveLastTrdNo )
				{
					totalNetProfit = SystemPerformance.AllTrades.TradesPerformance.Currency.CumProfit;
					if( totalNetProfit >= dMaxNet )
					{
						dMaxNet = totalNetProfit;  // keep track of high water mark
						dtMaxNet = Time[0];        // and time it happened
					}
					//
					iSaveLastTrdNo = lastTrade.TradeNumber;
					Execution lastExecEntry = lastTrade.Entry;
					Execution lastExecExit = lastTrade.Exit;
					dPnLLastTrd = lastTrade.ProfitCurrency;
					dComm = 0;  //lastTrade.Commission;
					dPnLTotal = dPnLTotal + dPnLLastTrd - dComm;
					Print(  ", ****  LAST trade no,  " + lastTrade.TradeNumber
						+ "\n , PnL,  " + dPnLLastTrd.ToString("0.00")  
						+ "\n , PnLTotal,  " + dPnLTotal.ToString("0.00")  
					    + "\n, pos, " + lastTrade.Entry.MarketPosition.ToString() 
					 	+ ", qty, " +  lastTrade.Quantity.ToString()
						+ ", ntry tim, " + lastExecEntry.Time.ToString()  );
					// Print out the net profit of all trades
					dNetProfitForAll = SystemPerformance.AllTrades.TradesPerformance.NetProfit;
		 			Print("* docalcpnl,  Net profit is: " + dNetProfitForAll.ToString("0.00")  );
					Print("* docalcpnl,  Max Net: " + dMaxNet.ToString("0.00") 
						+ " at Time: " + dtMaxNet.ToString() );
					dFillPri = 0;
				} 
			}
		}
		
		
		//#region Properties
			
		[Range(1, 1), NinjaScriptProperty]
		[Display(ResourceType = typeof(Custom.Resource), Name = "BackTest", GroupName = "01_Misc 1", Order = 0)]
        public bool a01bBackTest
        {
            get { return bBackTest; }
            set { bBackTest = value; }
        } 
		[Range(1, int.MaxValue), NinjaScriptProperty]
		[Display(ResourceType = typeof(Custom.Resource), Name = "Quantity", GroupName = "01_Misc 1", Order = 1)]
        public int a02iQty1
        {
            get { return iQty1; }
            set { iQty1 = Math.Max(1, value); }
        } 
		
		//[Range(1, int.MaxValue), NinjaScriptProperty]
		//[Display(ResourceType = typeof(Custom.Resource), Name = "Bars Back Trail", GroupName = "01_Misc 1", Order = 2)]
        //public int a03iBarsBack
        //{
        //    get { return iBarsBack; }
        //    set { iBarsBack = Math.Max(1, value); }
        //}
		
		
		// hull ma
		[Range(0, int.MaxValue), NinjaScriptProperty]
		[Display(ResourceType = typeof(Custom.Resource), Name = "Entry Hull MA Period", GroupName = "01_Misc 1", Order = 3)]
        public int a04iHullMaPdEn
        {
            get { return iHullMaPdEn; }
            set { iHullMaPdEn = Math.Max(0, value); }
        } 
		[Range(0, int.MaxValue), NinjaScriptProperty]
		[Display(ResourceType = typeof(Custom.Resource), Name = "Exit Hull MA Period", GroupName = "01_Misc 1", Order = 4)]
        public int a05iHullMaPdOt
        {
            get { return iHullMaPdOt; }
            set { iHullMaPdOt = Math.Max(0, value); }
        }
		
		// tp/sl
		[Range(0, int.MaxValue), NinjaScriptProperty]
		[Display(ResourceType = typeof(Custom.Resource), Name = "Take Profit (Tiks)", GroupName = "01_Misc 1", Order = 5)]
        public int a06iTPTiks
        {
            get { return iTPTiks; }
            set { iTPTiks = Math.Max(0, value); }
        } 
		
		//[Range(0, int.MaxValue), NinjaScriptProperty]
		//[Display(ResourceType = typeof(Custom.Resource), Name = "Stop Loss (Tiks)", GroupName = "01_Misc 1", Order = 6)]
        //public int a07iSLTiks
        //{
        //    get { return iSLTiks; }
        //    set { iSLTiks = Math.Max(0, value); }
        //}  
		// 5/2/25 replace sl w/ trailing stop
		[Range(0, 50), NinjaScriptProperty]
		[Display(ResourceType = typeof(Custom.Resource), Name = "TS (Tiks Away)", GroupName = "01_Misc 1", Order = 6)]
        public int a07iTSTiksAway
        {
            get { return iTSTiksAway; }
            set { iTSTiksAway = Math.Max(0, value); }
        }
		
	/*	
		[Range(0, double.MaxValue), NinjaScriptProperty]
		[Display(ResourceType = typeof(Custom.Resource), Name = "Max Loss ($)", GroupName = "01_Misc 1", Order = 7)]
        public double a08dMaxLoss
        {
            get { return dMaxLoss; }
            set { dMaxLoss = Math.Max(0, value); }
        } 
		[Range(0, double.MaxValue), NinjaScriptProperty]
		[Display(ResourceType = typeof(Custom.Resource), Name = "Max Gain ($)", GroupName = "01_Misc 1", Order = 8)]
        public double a09dMaxGain
        {
            get { return dMaxGain; }
            set { dMaxGain = Math.Max(0, value); }
        } 
		[Range(0, double.MaxValue), NinjaScriptProperty]
		[Display(ResourceType = typeof(Custom.Resource), Name = "Max Give Back (Pct)", GroupName = "01_Misc 1", Order = 9)]
        public double a10dMaxGiveBack
        {
            get { return dMaxGiveBack; }
            set { dMaxGiveBack = Math.Max(0, value); }
        } 
	*/		
		
		
		// / / / / / / / / / / / / /
		// start and end times
		[Range(0, int.MaxValue), NinjaScriptProperty]
		[Display(ResourceType = typeof(Custom.Resource), Name = "Start Time 1 (HHMMSS)", GroupName = "02_Start and End Times", Order = 0)]
        public int aiStr1HHMMSS
        {
            get { return iStr1HHMMSS; }
            set { iStr1HHMMSS = Math.Max(0, value); }
        }
		
		[Range(0, int.MaxValue), NinjaScriptProperty]
		[Display(ResourceType = typeof(Custom.Resource), Name = "End  Time 1 (HHMMSS)", GroupName = "02_Start and End Times", Order = 1)]
        public int biEnd1HHMMSS
        {
            get { return iEnd1HHMMSS; }
            set { iEnd1HHMMSS = Math.Max(0, value); }
		}
        
		[Range(0, int.MaxValue), NinjaScriptProperty]
		[Display(ResourceType = typeof(Custom.Resource), Name = "Start Time 2 (HHMMSS)", GroupName = "02_Start and End Times", Order = 2)]
        public int ciStr2HHMMSS
        {
            get { return iStr2HHMMSS; }
            set { iStr2HHMMSS = Math.Max(0, value); }
        }
		[Range(0, int.MaxValue), NinjaScriptProperty]
		[Display(ResourceType = typeof(Custom.Resource), Name = "End  Time 2 (HHMMSS)", GroupName = "02_Start and End Times", Order = 3)]
        public int diEnd2HHMMSS
        {
            get { return iEnd2HHMMSS; }
            set { iEnd2HHMMSS = Math.Max(0, value); }
        }
		//
       	[Range(0, int.MaxValue), NinjaScriptProperty]
		[Display(ResourceType = typeof(Custom.Resource), Name = "Start Time 3 (HHMMSS)", GroupName = "02_Start and End Times", Order = 4)]
        public int eiStr3HHMMSS
        {
            get { return iStr3HHMMSS; }
            set { iStr3HHMMSS = Math.Max(0, value); }
        }
		[Range(0, int.MaxValue), NinjaScriptProperty]
		[Display(ResourceType = typeof(Custom.Resource), Name = "End  Time 3 (HHMMSS)", GroupName = "02_Start and End Times", Order = 5)]
        public int fiEnd3HHMMSS
        {
            get { return iEnd3HHMMSS; }
            set { iEnd3HHMMSS = Math.Max(0, value); }
        } 
		[Range(0, int.MaxValue), NinjaScriptProperty]
		[Display(ResourceType = typeof(Custom.Resource), Name = "Start Time 4 (HHMMSS)", GroupName = "02_Start and End Times", Order = 6)]
        public int giStr4HHMMSS
        {
            get { return iStr4HHMMSS; }
            set { iStr4HHMMSS = Math.Max(0, value); }
        }
		[Range(0, int.MaxValue), NinjaScriptProperty]
		[Display(ResourceType = typeof(Custom.Resource), Name = "End  Time 4 (HHMMSS)", GroupName = "02_Start and End Times", Order = 7)]
        public int hiEnd4HHMMSS
        {
            get { return iEnd4HHMMSS; }
            set { iEnd4HHMMSS = Math.Max(0, value); }
        }
		///////////////////
		
		// each day of week select
		[Display(ResourceType = typeof(Custom.Resource), Name = "Sun", GroupName = "03_Day of Week", Order = 0)]
        public bool BSunOn
        {
            get { return bSunOn; }
            set { bSunOn = value; }
        }
		[Display(ResourceType = typeof(Custom.Resource), Name = "Mon", GroupName = "03_Day of Week", Order = 1)]
        public bool BMonOn
        {
            get { return bMonOn; }
            set { bMonOn = value; }
        }
		[Display(ResourceType = typeof(Custom.Resource), Name = "Tue", GroupName = "03_Day of Week", Order = 2)]
        public bool BTueOn
        {
            get { return bTueOn; }
            set { bTueOn = value; }
        }
		[Display(ResourceType = typeof(Custom.Resource), Name = "Wed", GroupName = "03_Day of Week", Order = 3)]
        public bool BWedOn
        {
            get { return bWedOn; }
            set { bWedOn = value; }
        }
		[Display(ResourceType = typeof(Custom.Resource), Name = "Thu", GroupName = "03_Day of Week", Order = 4)]
        public bool BThuOn
        {
            get { return bThuOn; }
            set { bThuOn = value; }
        }
		[Display(ResourceType = typeof(Custom.Resource), Name = "Fri", GroupName = "03_Day of Week", Order = 5)]
        public bool BFriOn
        {
            get { return bFriOn; }
            set { bFriOn = value; }
        }
		[Display(ResourceType = typeof(Custom.Resource), Name = "Sat", GroupName = "03_Day of Week", Order = 6)]
        public bool BSatOn
        {
            get { return bSatOn; }
            set { bSatOn = value; }
        }
		
		//
		[Browsable(false)]
		[XmlIgnore()]
		public Series<double> Plot0
		{
			get { return Values[0]; }
		}
		
		[Browsable(false)]
		[XmlIgnore()]
		public Series<double> Plot1
		{
			get { return Values[1]; }
		}
		
		[Browsable(false)]
		[XmlIgnore()]
		public Series<double> Plot2
		{
			get { return Values[2]; }
		}
		//#endregion
	}
}
