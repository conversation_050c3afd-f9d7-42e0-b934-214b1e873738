
// VERSION 2.0.0.3:
// 174 «   175 »  232 Φ   233 Θ   236 ∞   240 ≡   241 ±   242 ≥   243 ≤   248 °   249 ∙
#region USING DECLARATIONS
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Net.Http;			// Added for HttpClient
using System.Net.Http.Headers;	// Added for AuthenticationHeaderValue

using System.Linq;
using System.Runtime.CompilerServices;	// For MemberCallerName
using System.Text;
//using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Gui.Tools;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.Indicators;
using NinjaTrader.NinjaScript.DrawingTools;
using NinjaTrader.NinjaScript.Strategies;
#endregion


#region SUMMARY
/// COUNTING SPOT TESTS
/// • A test of the 26 or 77 occurs coming from at least 8-10 points from above/below.
///   Ideally it comes from the previous level directly
/// • Every tests counts until price carries thru and tests the next level
/// • For 26 that level is the 12 below and 36 above
/// • For 77 that level is the 66 below and the 88 above
/// • After that price need to come back from below/above to count as a new 1st test.
/// 
/// CHEAT SHEET
/// • Back Through (Hook) Entry:
/// In higher volatility (ATR > 20) we can get run over by placing resting orders. This requires a back
/// through technique using a STOP BUY or STOP SELL order as price comes back thru the entry level.
/// • LONG 26 price comes from 22
/// • SHORT 26 price comes from 33
/// • LONG 77 price comes from 72
/// • SHORT 77 price comes from 82
/// • Resting orders or anticipatory orders waiting to “catch price” can be tricky in high volatility.
/// • We want to let price test the level and push thru to the level listed and then put the STOP order in
/// to catch price as it comes back thru the entry level.
/// • If price extends beyond those levels then cancel the order as price may just keep going.
/// 
/// REVISION HISTORY:
/// v2.2.0.0: Undid all the shit with DerkUtils -more hassle than it's worth!! Went back to *******, 
/// or whatever the last version without DerkUtils and multiple inheritance.  Didn't actually revert of 
/// course; I put it all back manually. Github more trouble that it's worth.
#endregion

#region UPDATE LOG
/// *******		Changed session stuff from DateTime to TimeSpan
/// *******		Fixed log file path name; added logFolderName var
#endregion


// This namespace holds Strategies in this folder and is required. Do not change it.
namespace NinjaTrader.NinjaScript.Strategies
{
	// This strategy looks for crosses of the 50-level line (e.g. 19450, 19350, 19250, etc).
	// When it happens, it places a long limit order 14 points down at the 26 level.  If
	// price then rises above the 50-level, it cancels the order and starts over.
	// If instead, the 26-level is reached, the long trade opens, and sets first TP level
	// at the 33 level (28 ticks), and the second TP level back at the 50-level (96 ticks)
	// Move to BreakEven and Trail are optional.
	//
	// Also possible [optional] to trade shorts, just mirror image the entry level.
	// Default StopLoss is 15 points (60 ticks)
	public class FiftyLevel : Strategy
	{
		private const string	VERSION = "*******";

		#region GLOBALS
		public enum HookType
		{
			HookOn,
			HookOff,
			HookAuto
		}

		private double 		dailyProfit = 0;
		private double		cAsk, pAsk = 0;
		private double		cBid, pBid = 0;
		private double		pendingPrice = 0;
		private double		crossPrice = 0;
		private double		prvCrossPrice = 0;
		private double		lastATR = 0;

		private int			mult100 = 0;
		private int			price0 = 0;
		private int			price100 = 0;
		private int			price50 = 0;
		private int			firstEntryBarIdx = -1;
		private int			firstEntryBarsAgo = -1;
		private int			lngQuantityTP2;
		private int			shtQuantityTP2;
		private int			activeSessionNum = 0;
		private int			hookTickGap = 0;
		private int			prvTradeCount = 0;	// For Daily Target

		private string 		lastTradeDir = "None";
		private string		pendingDir = "None";
		private string		activeDir = "None";
		private string		maPriceDir = "None";
		private string		maSlopeDir = "None";
		private string		placeTrade = "None";
		private string		workingOrder = "None";

		private string 		lastDashboard = "";
		private string 		lastCaller = "";
		private string 		lastLogMsg = "";
		private string		lastLogTime;
		private string		chartInstrument;
		private string		logToFileName = "";
		private string		logPath = "";
		private string		logFolderName = "FiftyLevel";

		private bool		longOn = true;
		private bool		shortOn = true;
		private bool 		trailTriggered = false;
		private bool 		dailyLimitHit = false;
		private bool		userRequestedExit = false;
		private bool		inSession = true;
		private bool		inSessionChanged = false;
		private bool		MoveToBEWDone = false;
		private bool		MoveToBELDone = false;
		private bool		pendingPlaced = false;
		private bool		hookOn;
		private bool		prvLongOn = true;
		private bool		prvShortOn = true;
		private bool		previouslyOn = true;
		private bool		completelyLoaded	= false;

		private Order		entryOrder = null;
		private Order		slOrder1 = null;
		private Order		slOrder2 = null;
		private Order		tpOrder1 = null;
		private Order		tpOrder2 = null;
		private string		oco1, oco2;
		private bool		stopsSubmitted = false;

		private HookType	hookMode = HookType.HookAuto;

		private TimeSpan	startTime1, startTime2, startTime3, startTime4, startTime5;
		private TimeSpan	endTime1, endTime2, endTime3, endTime4, endTime5;
		private TimeSpan	closeTime, closeTime1, closeTime2, closeTime3, closeTime4, closeTime5;
		private DateTime	dbg1, dbg2;
		private ATR 		Atr;

		private System.Windows.Controls.Button	shortButton, longButton, exitButton, hookButton;
		private System.Windows.Controls.Grid	myGrid;
		#endregion

		protected override void OnStateChange()
		{
			// Set Input Defaults
			if (State == State.SetDefaults)
			{
				Description						= @"Strategy to automatically trade NQ Fifty Levels";
				Name							= "FiftyLevel";	// This is "Label" input
				LogToFileName					= Name;
				Calculate						= Calculate.OnBarClose;
				EntriesPerDirection				= 1;
				EntryHandling					= EntryHandling.AllEntries;
				IsExitOnSessionCloseStrategy	= true;
				ExitOnSessionCloseSeconds		= 300;		// 45 mins: Exit @ 13:15 PST
				IsFillLimitOnTouch				= false;
				MaximumBarsLookBack				= MaximumBarsLookBack.TwoHundredFiftySix;
				OrderFillResolution				= OrderFillResolution.Standard;
				Slippage						= 0;
				StartBehavior					= StartBehavior.WaitUntilFlat;
				TimeInForce						= TimeInForce.Gtc;
				RealtimeErrorHandling			= RealtimeErrorHandling.StopCancelClose;
				StopTargetHandling				= StopTargetHandling.ByStrategyPosition;
				BarsRequiredToTrade				= 20;
				IsUnmanaged 					= true;
				TraceOrders						= false;

				// Disable this property for performance gains in Strategy Analyzer optimizations
				// See the Help Guide for additional information
				IsInstantiatedOnEachOptimizationIteration	= true;


				// Quantity of contract to trade
				TimeZoneOffset			= -3; 		// Everythig defaulted in east coast time

				TradeLong				= true;
				TradeShort				= true;
				LngQuantity				= 2;
				ShtQuantity				= 2;
				EntryLevel1				= 26;
				EntryLevel2				= 77;
				DisableHook				= false;
				HookGapL1				= 16;
				HookGapL2				= 20;
				HookGapS1				= 28;
				HookGapS2				= 20;
				CancelPoints			= 10;

				// StopLoss & TakeProfit
				LngStopLoss				= 60;
				LngTakeProfit1			= 28;
				LngQuantityTP1			= 1;
				LngTakeProfit2			= 96;
				ShtStopLoss				= 60;
				ShtTakeProfit1			= 28;
				ShtQuantityTP1			= 1;
				ShtTakeProfit2			= 104;

				// Daily Targets
				DailyMinTarget			= 0;
				DailyMaxLoss			= 0;

				// Move to BreakEven
				UseBreakeven			= true;
				BEW_ActivTicks			= 16;
				BEW_Offset				= 1;
				BEL_ActivTicks			= -40;
				BEL_Offset				= -12;

				// Trailing
				UseTrailingStop 		= true;
				LngActivationTicks 		= 60;
				LngTrailTicks			= 30;
				ShtActivationTicks 		= 60;
				ShtTrailTicks			= 30;

				// ATR Filter
				UseATR					= true;
				MinutesATR				= 2;
				PeriodATR				= 5;
				HookPointsATR			= 20;
				DisableOnMaxATR			= 50;

				// Min Volume Filter
				AveVolumePeriod 		= 14;
				MinAveVolume			= 1;

				// Trading session days / hours
				TradeSunday				= true;
				TradeMonday				= true;
				TradeTuesday			= true;
				TradeWednesday			= true;
				TradeThursday			= true;
				TradeFriday				= true;

				UseSession1				= false;
				StartTime1				= DateTime.Parse("10:00 AM", System.Globalization.CultureInfo.InvariantCulture);
				EndTime1				= DateTime.Parse("03:30 PM", System.Globalization.CultureInfo.InvariantCulture);
				UseCloseTime1			= false;
				CloseTime1				= DateTime.Parse("03:50 PM", System.Globalization.CultureInfo.InvariantCulture);

				UseSession2				= false;
				StartTime2				= DateTime.Parse("09:30 AM", System.Globalization.CultureInfo.InvariantCulture);
				EndTime2				= DateTime.Parse("03:30 PM", System.Globalization.CultureInfo.InvariantCulture);
				UseCloseTime2			= false;
				CloseTime2				= DateTime.Parse("03:50 PM", System.Globalization.CultureInfo.InvariantCulture);

				UseSession3				= false;
				StartTime3				= DateTime.Parse("08:00 AM", System.Globalization.CultureInfo.InvariantCulture);
				EndTime3				= DateTime.Parse("09:30 AM", System.Globalization.CultureInfo.InvariantCulture);
				UseCloseTime3			= false;
				CloseTime3				= DateTime.Parse("03:50 PM", System.Globalization.CultureInfo.InvariantCulture);

				UseSession4				= false;
				StartTime4				= DateTime.Parse("10:00 AM", System.Globalization.CultureInfo.InvariantCulture);
				EndTime4				= DateTime.Parse("03:00 PM", System.Globalization.CultureInfo.InvariantCulture);
				UseCloseTime4			= false;
				CloseTime4				= DateTime.Parse("03:50 PM", System.Globalization.CultureInfo.InvariantCulture);

				UseSession5				= false;
				StartTime5				= DateTime.Parse("06:00 PM", System.Globalization.CultureInfo.InvariantCulture);
				EndTime5				= DateTime.Parse("08:00 AM", System.Globalization.CultureInfo.InvariantCulture);
				UseCloseTime5			= false;
				CloseTime5				= DateTime.Parse("03:50 PM", System.Globalization.CultureInfo.InvariantCulture);

				UseCloseTime			= false;
				CloseTime				= DateTime.Parse("03:50 PM", System.Globalization.CultureInfo.InvariantCulture);

				DisplayOCD				= true;
				StartingLine			= 2;
				PlotHist 				= true;
				ApplyDailyToHist		= true;
				StrategyNameOCD			= "FiftyLevel";
				SwapPrices				= true;
				BP						= 8;		// Buffer Points
				LabelMod				= -10;
				DrawLines				= true;
				DisableLogging			= false;
				UseOutput2				= true;
				UniqueID				= "1";
			}

			// Initialize Member Variables
			else if (State == State.Configure)
			{
				chartInstrument = this.Instrument.FullName;
				chartInstrument = chartInstrument.Split(' ')[0];	// Get just the "ES" part in a string like "ES 06-25"

				AddDataSeries(BarsPeriodType.Tick, 1);
				AddDataSeries(BarsPeriodType.Minute, MinutesATR);

				// globals dependent on inputs
				lngQuantityTP2 = LngQuantity - LngQuantityTP1;
				shtQuantityTP2 = ShtQuantity - ShtQuantityTP1;
				dbg1 = new DateTime(2024, 12, 6, 5, 42, 0);
				dbg2 = dbg1.AddMinutes(60);

				PrintTo = UseOutput2 ? PrintTo.OutputTab2 : PrintTo.OutputTab1;
			}

			else if (State == State.DataLoaded)
			{
				// For Log to file
				if (LogToFileName != "")
				{
					// Had to add another (member) variable here initialized to the Strategy name above, because...
					// 'LogToFileName' [can] get changed in inputs, and 'Name' is also an actually the 'label' the 
					// input, which I normally clear to get it to stop from printing it at the top of the chart.
					logPath = NinjaTrader.Core.Globals.UserDataDir + @"log\" + logFolderName + @"\" + LogToFileName + @"Log.txt";
					Print($"   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   logPath = {logPath}   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ");
					if (System.IO.File.Exists(logPath))
					{
						try
						{
							System.IO.File.Delete(logPath);
						}
						catch (Exception ex)
						{
							Print($"Could not delete log file (logPath): {ex.Message}");
						}
					}
				}
				else
					Print(@"   /  /  /  /  /  /  /  /  /  /  /   LogToFileName == null; No Log File will be made  \  \  \  \  \  \  \  \  \  \  \  \");

				Atr			= ATR(BarsArray[2], PeriodATR);
				//AddChartIndicator(Atr);

				longOn		= TradeLong;
				shortOn		= TradeShort;

				// Convert session times to TimeSpan with timezone offset (works for both historical and real-time)
				startTime1	= StartTime1.TimeOfDay.Add(TimeSpan.FromHours(TimeZoneOffset));
				endTime1	= EndTime1.TimeOfDay.Add(TimeSpan.FromHours(TimeZoneOffset));
				closeTime1	= CloseTime1.TimeOfDay.Add(TimeSpan.FromHours(TimeZoneOffset));

				startTime2	= StartTime2.TimeOfDay.Add(TimeSpan.FromHours(TimeZoneOffset));
				endTime2	= EndTime2.TimeOfDay.Add(TimeSpan.FromHours(TimeZoneOffset));
				closeTime2	= CloseTime2.TimeOfDay.Add(TimeSpan.FromHours(TimeZoneOffset));

				startTime3	= StartTime3.TimeOfDay.Add(TimeSpan.FromHours(TimeZoneOffset));
				endTime3	= EndTime3.TimeOfDay.Add(TimeSpan.FromHours(TimeZoneOffset));
				closeTime3	= CloseTime3.TimeOfDay.Add(TimeSpan.FromHours(TimeZoneOffset));

				startTime4	= StartTime4.TimeOfDay.Add(TimeSpan.FromHours(TimeZoneOffset));
				endTime4	= EndTime4.TimeOfDay.Add(TimeSpan.FromHours(TimeZoneOffset));
				closeTime4	= CloseTime4.TimeOfDay.Add(TimeSpan.FromHours(TimeZoneOffset));

				startTime5	= StartTime5.TimeOfDay.Add(TimeSpan.FromHours(TimeZoneOffset));
				endTime5	= EndTime5.TimeOfDay.Add(TimeSpan.FromHours(TimeZoneOffset));
				closeTime5	= CloseTime5.TimeOfDay.Add(TimeSpan.FromHours(TimeZoneOffset));
				closeTime	= CloseTime.TimeOfDay.Add(TimeSpan.FromHours(TimeZoneOffset));
				
				// Handle negative TimeSpan by adding 24 hours if result is negative
				if (startTime1 < TimeSpan.Zero)	startTime1	= startTime1.Add(TimeSpan.FromDays(1));
				if (endTime1   < TimeSpan.Zero)	endTime1	= endTime1.Add(TimeSpan.FromDays(1));
				if (closeTime1 < TimeSpan.Zero)	closeTime1	= closeTime1.Add(TimeSpan.FromDays(1));
				
				if (startTime2 < TimeSpan.Zero)	startTime2	= startTime2.Add(TimeSpan.FromDays(1));
				if (endTime2   < TimeSpan.Zero)	endTime2	= endTime2.Add(TimeSpan.FromDays(1));
				if (closeTime2 < TimeSpan.Zero)	closeTime2	= closeTime2.Add(TimeSpan.FromDays(1));
				
				if (startTime3 < TimeSpan.Zero)	startTime3	= startTime3.Add(TimeSpan.FromDays(1));
				if (endTime3   < TimeSpan.Zero)	endTime3	= endTime3.Add(TimeSpan.FromDays(1));
				if (closeTime3 < TimeSpan.Zero)	closeTime3	= closeTime3.Add(TimeSpan.FromDays(1));
				
				if (startTime4 < TimeSpan.Zero)	startTime4	= startTime4.Add(TimeSpan.FromDays(1));
				if (endTime4   < TimeSpan.Zero)	endTime4	= endTime4.Add(TimeSpan.FromDays(1));
				if (closeTime4 < TimeSpan.Zero)	closeTime4	= closeTime4.Add(TimeSpan.FromDays(1));
				
				if (startTime5 < TimeSpan.Zero)	startTime5	= startTime5.Add(TimeSpan.FromDays(1));
				if (endTime5   < TimeSpan.Zero)	endTime5	= endTime5.Add(TimeSpan.FromDays(1));
				if (closeTime5 < TimeSpan.Zero)	closeTime5	= closeTime5.Add(TimeSpan.FromDays(1));

				if (closeTime  < TimeSpan.Zero)	closeTime	= closeTime.Add(TimeSpan.FromDays(1));

				pAsk = (hookOn  &&  SwapPrices) ? GetCurrentBid() : GetCurrentAsk();
				pBid = (hookOn  &&  SwapPrices) ? GetCurrentAsk() : GetCurrentBid();

				//Print($"pAsk = {pAsk}, pBid = {pBid}; startTime3 = {startTime3.ToString()}, endTime3 = {endTime3.ToString()};      startTime4 = {startTime4.ToString()}, endTime4 = {endTime4.ToString());
				
				ManageOCD(true);
			}

			// Historical is called once the object begins to process historical data. This state is called once
			// when running an object in real-time. This object is called multiple times when running a backtest
			// optimization and the property IsInstantiatedOnEachOptimizationIteration is true (default behavior)
			else if (State == State.Historical)
			{
				if (PlotHist)
					Print($"\n\n   «   «   «   «   «   «   HISTORICAL PROCESSING STARTED @ {DateTime.Now.ToString()}   »   »   »   »   »   »");

				if (!DisableHook  &&  UseATR  &&  HookPointsATR > 0)
					hookMode = HookType.HookAuto;
				else
					hookMode = HookType.HookOff;

				if (ChartControl != null)
				{
					ChartControl.Dispatcher.InvokeAsync(() =>
					{
						CreateButtons();
					});
				}
			}

			// Transition is called once as the object has finished processing historical data but before it starts to process realtime data.
			else if (State == State.Transition)
			{
				dailyProfit = 0;
				dailyLimitHit = false;
			}

			// Realtime is called once when the object begins to process realtime data.
			else if (State == State.Realtime)
			{
				completelyLoaded = true;
				
				// One time only, as we transition from historical to live, convert any old historical
				// order object references to the live order submitted to the real-time account
				// This addresses the error: "Strategy XXX has been disabled because it attempted to
				// modify a historical order that has transitioned to a live order"
				if (entryOrder != null  &&  entryOrder.IsBacktestOrder)		entryOrder = GetRealtimeOrder(entryOrder);
				if (slOrder1 != null  &&  slOrder1.IsBacktestOrder)			slOrder1 = GetRealtimeOrder(slOrder1);
				if (slOrder2 != null  &&  slOrder2.IsBacktestOrder)			slOrder2 = GetRealtimeOrder(slOrder2);
				if (tpOrder1 != null  &&  tpOrder1.IsBacktestOrder)			tpOrder1 = GetRealtimeOrder(tpOrder1);
				if (tpOrder2 != null  &&  tpOrder2.IsBacktestOrder)			tpOrder2 = GetRealtimeOrder(tpOrder2);

				Print("All historical orders converted to live orders.");

				if (PlotHist)
					Print($"\n\n   «   «   «   «   «   «   HISTORICAL PROCESSING ENDED @ {DateTime.Now.ToString()}   »   »   »   »   »   »");

				Print("\n\n\n\n\n-=-  -=-  SWITCHING TO REAL TIME PROCESSING  -=-  -=-  -=-  -=-  -=-  -=-  -=-  -=-  -=-  -=-  -=-  -=-  -=-  -=-  -=-");
				Print("");	Print("");	Print("");	Print("");
			}

			else if (State == State.Terminated)
			{
				if (entryOrder != null)
				{
					if (Position.MarketPosition != MarketPosition.Flat)
					{
						ClosePosition("Terminated");
						CancelAllStops();
						Print($"\n\n\n                           --  State == Terminated: Closed open orders  --");
					}
					else
					{
						CancelEntryOrder();
						Print($"\n\n\n                           --  State == Terminated: Closed pending orders  --");
					}
					entryOrder = null;
				}

				if (ChartControl != null)
				{
					ChartControl.Dispatcher.InvokeAsync(() =>
					{
						DisposeButtons();
					});
				}
			}
		}


		protected override void OnExecutionUpdate(Cbi.Execution execution, string executionId, double price, int ChangePending,
												  Cbi.MarketPosition marketPosition, string orderId, DateTime time)
		{
			bool log = true;
			Log($"Name = {execution.Name}, IsEntryStrategy = {execution.IsEntryStrategy},  OrderAction = {execution.Order.OrderAction},   marketPosition = {marketPosition},   Position.Quantity = {Position.Quantity},   price = {price}");

			// They advise monitoring OnExecution to trigger submission of stop/target orders instead of OnOrderUpdate()
			// since OnExecution() is called after OnOrderUpdate() which ensures your strategy has received the execution
			// which is used for internal signal tracking.

			int sl, tp1, tp2, qty1, qty2;
			double m, prc, avePrice = AveEntryPrice();

			// ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡
			// Check the order state for both entry end exit
			string type = (execution.IsEntryStrategy) ? "        ENTRY" : "        EXIT";

			// If part filled, I do nothing, and hope that this method gets called again with order state == filled.
			// If that never happens, not sure what is the "right" thing to do, or how to tell it never happened...
			if (execution.Order.OrderState == OrderState.PartFilled)
				Log(type + $" ORDER ONLY PARTIALLY FILLED; execution.Order.Filled = {execution.Order.Filled}, Position.Quantity = {Position.Quantity}; WAITING FOR RESLOUTION...");

			// If the order was somehow cancelled, then we need to close the position if partially opened
			else if (execution.Order.OrderState == OrderState.Cancelled  ||  execution.Order.OrderState == OrderState.Rejected)
			{
				if (execution.Order.Filled > 0)
				{
					Log(type + $" ORDER CANCELLED AFTER PARTIAL FILL! dir = {marketPosition.ToString()}, execution.Order.Filled = {execution.Order.Filled}, so closing entire open position");
					ClosePosition();
				}
				else
				{
					Log(type + $" ORDER CANCELLED; dir = {marketPosition.ToString()}, execution.Order.Filled = {execution.Order.Filled}; Doing Nothing");
					lastCaller = "";	// reset so if this method is entered again before other log, reprint the function name
					return;
				}
			}
			else if (execution.Order.OrderState == OrderState.Filled)
				if (log) Log(type + $" ORDER FILLED (execution.Order.OrderState == OrderState.Filled); execution.Order.Filled = {execution.Order.Filled}, Position.Quantity = {Position.Quantity}");


			// ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡
			// IS ENTRY STRATEGY - Handle actions for execution.Order.OrderAction.Buy or SellShort
			if (execution.IsEntryStrategy)
			{
				if (execution.Order.OrderAction == OrderAction.Buy)
				{
					m = 1.0;
					sl = LngStopLoss;
					tp1 = LngTakeProfit1;
					tp2 = LngTakeProfit2;
					qty1 = LngQuantityTP1;
					qty2 = lngQuantityTP2;
				}
				else
				{
					m = -1.0;
					sl = ShtStopLoss;
					tp1 = ShtTakeProfit1;
					tp2 = ShtTakeProfit2;
					qty1 = ShtQuantityTP1;
					qty2 = shtQuantityTP2;
				}

				// If this is the opening of a trade (limit hit), store bar index
				if (firstEntryBarIdx == -1)
				{
					firstEntryBarIdx = CurrentBar;
					if (log) Log($"                                         UNSET, so set  firstEntryBarsIdx = {firstEntryBarIdx}");
				}

				if (sl > 0  ||  tp1 > 0  ||  tp2 > 0)
				{
					// Use a separate SL for each TP; link by OCO
					oco2 = "";
					if (sl > 0)
					{
						//string ocoBase = Times[1][0].ToString() + "_" + CurrentBars[1] + "_";
						string ocoTime = Times[1][0].ToString("yyyy.MM.dd_HH:mm:ss.fffff");
						//Log($"\n\n\n\n\n\n                        ===================================================== >>>>        ocoTime = {ocoTime}");
						if (tp1 > 0)
						{
							if (!stopsSubmitted)
							{
								//if (State == State.Historical)
								//	oco1 = Times[1][0].ToString() + "_" + CurrentBars[1] + "_" + "Exits1";
								//else
								//	oco1 = GetAtmStrategyUniqueId() + "_" + "Exits1";
								oco1 = "Exits1_" + ocoTime;
							}
							else if (slOrder1 != null)
								oco1 = slOrder1.Oco;
							else
								Log($"Could not fetch OCO because slOrder1 == null");
						}
						if (tp2 > 0)
						{
							if (!stopsSubmitted)
							{
								//if (State == State.Historical)
								//	oco2 = Times[1][0].ToString() + "_" + CurrentBars[1] + "_" + "Exits2";
								//else
								//	oco2 = GetAtmStrategyUniqueId() + "_" + "Exits2";
								oco2 = "Exits2_" + ocoTime;
							}
							else if (slOrder2 != null)
								oco2 = slOrder2.Oco;
							else
								Log($"Could not fetch OCO because slOrder2 == null");
						}
					}

					if (entryOrder != null  &&  entryOrder == execution.Order)
					{
						//if (log) Log($"IsEntryStrategy = True, execution.Order.OrderState = {execution.Order.OrderState}");

						if  (execution.Order.OrderState == OrderState.Filled)
						{
							activeDir = (execution.Order.OrderAction == OrderAction.Buy) ? "Long" : "Short";
							pendingPlaced = false;
							pendingDir = "None";

							if (!stopsSubmitted)
							{
								// This used to check that slOrder1 & tpOrders were null, because we were initially setting,
								// but it won't place the stops until order is filled, so no need to check for that.
								var action = (marketPosition == MarketPosition.Long) ? OrderAction.Sell : OrderAction.BuyToCover;

								if (log) Log($"INIT PLACEMENT OF SL & TP; Action = {action.ToString()}");
								if (sl > 0)
								{
									prc = avePrice - m * sl * TickSize;
									Log($"Submitting sl for Qty ( {qty1} + {qty2} ) @ {prc} as sepearate orders");
									SubmitOrderUnmanaged(1, action, OrderType.StopMarket, qty1, 0, prc, oco1, "SL1");
									if (qty2 > 0)
										SubmitOrderUnmanaged(1, action, OrderType.StopMarket, qty2, 0, prc, oco2, "SL2");
								}
								if (tp1 > 0  &&  qty1 > 0)
								{
									prc = avePrice + m * tp1 * TickSize;
									if (log) Log($"Submitting tp1 for Qty {qty1} @ {prc}");
									SubmitOrderUnmanaged(1, action, OrderType.Limit, qty1, prc, 0, oco1, "TP1");
								}
								if (tp2 > 0  &&  qty2 > 0)
								{
									prc = avePrice + m * tp2 * TickSize;
									if (log) Log($"Submitting tp2 for Qty {qty2} @ {prc}");
									SubmitOrderUnmanaged(1, action, OrderType.Limit, qty2, prc, 0, oco2, "TP2");
								}
								stopsSubmitted = true;
							}
							else
								if (log) Log($"execution.Order.OrderState == OrderState.Filled & stops already submitted; DOING NOTHING");
						}
					}
					else
						if (log) Log($"entryOrder = {entryOrder}, execution.Order.OrderState = {execution.Order.OrderState}");
				}
			}

			// ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡   ≡
			// IS EXIT STRATEGY - Handle actions for execution.Order.OrderAction.Sell or BuyToCover
			else
			{
		        // Use SystemPerformance.AllTrades to get the last closed trade
				// We only check when order is filled (not partial).
				//
				// Not sure how this acts on partial fills: if there are 2 lots and it closes 1,
				// then the other, has SystemPerformance.AllTrades.Count gone up by one or two?
				// If it goes up by two, then I guess we DO want to do Filled or PartFilled....
				//
				Log($"\n\n\n\nCHECKING SystemPerformance.AllTrades; Count = {SystemPerformance.AllTrades.Count}, Prev Count = {prvTradeCount}");
				int newTrades = SystemPerformance.AllTrades.Count - prvTradeCount;
		        if (newTrades > 0)
				{
		            // Access trade details
					for (int i = newTrades-1; i >= 0; i--)
					{
						var idx = SystemPerformance.AllTrades.Count - i - 1;
						var lastTrade = SystemPerformance.AllTrades[idx];
//						var lastTrade = SystemPerformance.AllTrades[SystemPerformance.AllTrades.Count - i - 1];
						double pnl = lastTrade.ProfitCurrency;	// includes commission
						dailyProfit += pnl;
						if (log) Log($"Last trade (idx = {idx}) PnL: " + pnl.ToString("C") + " (including " + lastTrade.Commission.ToString("C") + " commission).  Cum. Daily Proft: " + dailyProfit.ToString("C"));
					}
					prvTradeCount = SystemPerformance.AllTrades.Count;
				}
				Log($"dailyProfit = {dailyProfit.ToString("C0")}");
				Log($"\n");
				Log($"\n");

				if (Position.Quantity > 0)
				{
					// Nothing to do here (yet)
				}
				else if (Position.MarketPosition == MarketPosition.Flat)
				{
					// Cancel any stops that are left open. SL & TP2 OCO, but check all to be sure
					if (log) Log($"Position.Quantity == 0; cancelling stops");
					lastTradeDir = activeDir;
					activeDir = "None";
					MoveToBEWDone = MoveToBELDone = false;
					CancelAllStops();

					// Reset some variables
					if (log) Log($"Position.Quantity == 0; RESETTING VARS & checking DailyMaxLoss & DailyMinTarget..");
					stopsSubmitted = false;
					trailTriggered = false;
					firstEntryBarIdx = -1;
					prvCrossPrice = 0;
					entryOrder = slOrder1 = tpOrder1 = tpOrder2 = null;

					// Check for daily profit/loss limits
					if (DailyMaxLoss > 0  ||  DailyMinTarget > 0)
					{
						if (State == State.Realtime  ||  ApplyDailyToHist)
						{
							if (log) Log($"Daily Profit = {dailyProfit.ToString("C0")};    DailyMinTarget = {DailyMinTarget}, DailyMaxLoss = ${DailyMaxLoss}");
//							if (log) Log($"pDaily = (${dailyProfit}), SystemPerformance.RealTimeTrades.TradesPerformance.NetProfit = (${SystemPerformance.RealTimeTrades.TradesPerformance.NetProfit})");
							if (DailyMaxLoss > 0  &&  -dailyProfit > DailyMaxLoss)
							{
								// if (Position.Quantity == 0), Exiting should be unnecessary, but are
								// left in case we are reversing position, or otherwise 'just to be safe'
//								if (log) Log($"pDaily (${dailyProfit}) - NetProfit (${SystemPerformance.AllTrades.TradesPerformance.NetProfit}) > DailyMaxLoss (${DailyMaxLoss})");
								Log($"DAILY LOSS HIT - NEW TRADING DISABLED\n");
								dailyLimitHit = true;
							}
							if (DailyMinTarget > 0  &&  dailyProfit > DailyMinTarget)
							{
//								if (log) Log($"NetProfit (${SystemPerformance.AllTrades.TradesPerformance.NetProfit}) - dailyProfit (${dailyProfit}) > DailyMinTarget (${DailyMinTarget})");
								Log($"DAILY PROFIT HIT - NEW TRADING DISABLED\n");
								dailyLimitHit = true;
							}
						}
					}
				}
			}
		}


		protected override void OnOrderUpdate(Cbi.Order order, double limitPrice, double stopPrice, int quantity,
											  int filled, double averageFillPrice, Cbi.OrderState orderState,
											  DateTime time, Cbi.ErrorCode error, string comment)
		{
			// The normal order for this method being called, for entry order, is State == Submitted, Accepted, Working, Filled
			// For stops, ChangePending, ChangeSubmitted, Accepted, Working, then Filled when the stop is actually hit
			bool log = false;

			if (order == null)
			{
				Log($"ERROR? : SHOULD NOT HAPPEN!!!! Method called with parameter order == null");
			}

			// This is a safety step to prevent errors from transitioning from hist to realtime.
			// The orders are actually transitioned in OnStateChange()
			if (State == State.Realtime  &&  order != null  &&  order.IsBacktestOrder)
			{
				if (log) Log($"[GUARD] Blocked update from historical order '{order.Name}' in Realtime. OrderId = {order.OrderId}");
				return;
			}

			string name = order.Name;
			string action = order.OrderAction.ToString();
			if (log) Log($"order.Name = {name} : OrderState is {order.OrderState}, OrderAction = {action}, OrderType = {order.OrderType}, quantity = {quantity}, limitPrice = {limitPrice}, stopPrice = {stopPrice}");

			if (order.OrderState == OrderState.CancelSubmitted
			||  order.OrderState == OrderState.CancelPending
			||  order.OrderState == OrderState.Cancelled
			||  order.OrderState == OrderState.Rejected
			||  order.OrderState == OrderState.Unknown)
			{
				if (order.OrderState == OrderState.Cancelled  ||  order.OrderState == OrderState.Rejected)
				{
					if (log) Log($"order.Name = {name} : Setting Order variable to null because OrderState is {order.OrderState}, OrderAction = {action}, OrderType = {order.OrderType}, limitPrice = {limitPrice}");
					order = null;
					// Will set corresponding order var to null below
				}
				else
				{
					if (log) Log($"order.Name = {name} : SKIPPING setting Order variable because OrderState is {order.OrderState}, OrderAction = {action}, OrderType = {order.OrderType}, limitPrice = {limitPrice}");
					lastCaller = "";	// reset so if this method is entered again before other log, reprint the function name
					return;
				}
			}

			// Assign Order objects here
			// This is more reliable than assigning Order objects in OnBarUpdate, as the assignment
			// is not guaranteed to be complete if it is referenced immediately after submitting
			if (name == "Entry")
			{
				if (log) Log($"order.Name = {name}; Setting entryOrder, OrderAction = {action}, stopPrice = {stopPrice}, limitPrice = {limitPrice}");
				entryOrder = order;
			}
			else if (name == "SL1")
			{
				if (log) Log($"order.Name = {name}; Setting slOrder1, price = {stopPrice}");
				slOrder1 = order;
			}
			else if (name == "SL2")
			{
				if (log) Log($"order.Name = {name}; Setting slOrder2, price = {stopPrice}");
				slOrder2 = order;
			}
			else if (name == "TP1")
			{
				if (log) Log($"order.Name = {name}; Setting tpOrder1, price = {limitPrice}");
				tpOrder1 = order;
			}
			else if (name == "TP2")
			{
				if (log) Log($"order.Name = {name}; Setting tpOrder2, price = {limitPrice}");
				tpOrder2 = order;
			}
			lastCaller = "";	// reset so if this method is entered again before other log, reprint the function name
		}


		protected override void OnBarUpdate()
		{
			if (CurrentBars[0] < BarsRequiredToTrade  ||  CurrentBars[1] < BarsRequiredToTrade)
			{
				if (State != State.Historical)
					Log($"STATE = {State.ToString()}; Number of bars (BiP[0] = {CurrentBars[0]} & BiP[1] = {CurrentBars[1]}) is less than BarsRequiredToTrade ({BarsRequiredToTrade}); Returning");
				lastCaller = "";	// reset so if this method is entered again before other log, reprint the function name
				return;
			}

			// Don't waste time on OCD for historical
			if (State == State.Historical  &&  !PlotHist)
				return;


			// Effectively change OnBarUpdate to OnPriceChange
			// There is no need to run this function at all if price has not changed.
			// Check Last for now, but convert to Bid/Ask check if I change cAsk/cBid back to Ask & Bid
			//
			// If enabled, swap bid & ask (hook only)
			if (BarsInProgress == 1)
			{
				cAsk = (hookOn  &&  SwapPrices) ? GetCurrentBid() : GetCurrentAsk();
				cBid = (hookOn  &&  SwapPrices) ? GetCurrentAsk() : GetCurrentBid();
				//cBid = cAsk = Closes[1][0];	// Tick price from 1-tick series

				if (cBid == pBid  &&  cAsk == pAsk)
					return;	// Skip duplicate ticks
			}




			// *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *
			// LIMIT HISTORICAL PROCESSING   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *
			DateTime cutoff1 = new DateTime(2025, 4, 8, 0, 0, 0);
			DateTime cutoff2 = new DateTime(2025, 4, 30, 0, 0, 0);
			if (State == State.Historical)
			{
				if (false)
				{
					//SetHook();
					if (Time[0] < cutoff1  ||  Time[0] > cutoff2)
						return;
				}
			}




			//  Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ
			// BEGIN BAR PROCESSING   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ
			// Third data series (only for ATR)
			if (BarsInProgress == 2  ||  lastATR == 0)
			{
				lastATR = Atr[1];
				//Log($"UPDATED lastATR to {lastATR}.   Atr[0] = {Atr[0]}, Atr[1] = {Atr[1]}, Atr[2] = {Atr[2]}");
				SetHook();				// ...when ATR changes
			}

			// Entered on close of each candle of chart TF where strategy is applied (first data series)
			#region CHART SERIES  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
			else if (BarsInProgress == 0)
			{
				// Keep the text labels in the same relative place
				AdjustLabels();

				//Log($"CHART SERIES, Time[0] = {Time[0].ToString()}");
				if (Bars.IsFirstBarOfSession)
				{
					// This is not useful for us because we do not let the EA run 24/5 such that this would trigger at 6pm ET
					// And more importantly, we do not do anthing with historical (yet); that is where this is important:
					// to reset some daily things that will be done with running over historical bars.

					if (Position.MarketPosition != MarketPosition.Flat)
					{
						Log($"\n\n\n\nSESSION STARTED AND POSITION NOT FLAT -CLOSING POSITION\n");
						ClosePosition();
						CancelAllStops();
					}
					else if (entryOrder != null)
					{
						Log($"\n\n\n\nSESSION STARTED AND PENDING OPEN -CANCELLING ORDER\n");
						CancelEntryOrder();
					}
					entryOrder = null;
					trailTriggered = false;
					firstEntryBarIdx = -1;
					pendingDir = "None";
					pendingPrice = 0;
					pAsk = pBid = 0;

					dailyProfit = 0;	//SystemPerformance.AllTrades.TradesPerformance.NetProfit;
					dailyLimitHit = false;
					Log($"\nNEW SESSION (DAY) - RESET EVERYTHING - Time[0] = {Time[0].ToString()}\n");
				}

				// Close all trades at end of user-defined session
				if (IsCloseTime(activeSessionNum))
				{
					Log($"End of Session Close activated @ {Time[0].ToString()}; Closing all open orders");
					if (Position.MarketPosition != MarketPosition.Flat)
					{
						ClosePosition();
						CancelAllStops();
					}
					else if (entryOrder != null)
						CancelEntryOrder();
					entryOrder = null;
					activeSessionNum = 0;
				}

				// Set up how many bars ago was the entry candle
				firstEntryBarsAgo = (firstEntryBarIdx != -1) ? CurrentBar - firstEntryBarIdx : -1;
			}
			#endregion

			// Entered on each tick (second data series)
			#region TICK SERIES - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
			else if (BarsInProgress == 1)
			{
				// Update which line is the zero, 50 and 100 line according to current price
				UpdateLevels();

				// Set whether or not the hook option is enabled
				// Do each tick in case user presses on-chart button
				// Will also disabel/enable trade based on ATR
				SetHook();

				// Check each tick whether we are in-session (when it can place new trades)
				inSessionChanged = inSession;
				inSession = InTradeSession();
				inSessionChanged = (inSessionChanged != inSession);

				// Go no further if daily profit or loss reached - No need to reset lastCaller var
				if (dailyLimitHit  &&  Position.MarketPosition == MarketPosition.Flat)
				{
					ManageOCD();	// Update the On-Chart Display
					return;
				}

				if (!inSession  &&  inSessionChanged)
				{
					string db = (entryOrder != null) ? " - CANCELLING ORDER" : "";
					Log($"NOW OUT OF SESSION{db}");
					if (entryOrder != null)
						if (Position.MarketPosition == MarketPosition.Flat)
							CancelEntryOrder();
				}


				#region ENTRY - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

				//if (Time[0] > dbg1  &&  Time[0] < dbg2)				Log($"     XXXXX      Time[0] = {Time[0].ToString("HH:mm:ss") + ", inSession = {inSession}");

				// Do nothing if already in an active open trade
				if (Position.MarketPosition == MarketPosition.Flat)
				{
					if (inSession  &&  inSessionChanged)
						Log($"Now in session; checking for zero and fifty crosses...");

					else if (!inSession  &&  inSessionChanged)
					{
						Log($"Out of session; not checking for zero and fifty crosses...");
						if (entryOrder != null)
						{
							Log($"entryOrder != null; canceling perding order");
							CancelEntryOrder();
						}
						pendingDir = "None";
					}

					// If not in session, just update prev ask/bid and return
					if (!inSession)
					{
						pAsk = cAsk;
						pBid = cBid;
						ManageOCD();	// Update the On-Chart Display
						return;
					}

					// Get the status of whether we are already in a pending order,
					// and manage possible cancellation of (hook) stop order
					ManageWorkingOrder();

					string logStr = "";
					string sess = (!inSession) ? "  (OutOfSession)" : "";
					double level100;
					bool placePendingOrder = false;
					bool matched = false;

					// =   =   =   =   =   =   =   =   =   =   =   =   =   =   =   =   =   =   =   =   =   =   =   =   =   =   =   =   =   =   =   =   =
					//
					//	NOTE:	Having a lot of issues with line crosses not being detected.  Wrote my own Crossed methon in DerkUtils just to try
					//			to address the problem.  Was seeing issues even then.  So I adopted a different tack, whereby it triggers if in the
					//			'zone', which works fine for the final trigger, but not for the initial cross of the 50/100 line, because for that
					//			[to reset], it really MUST CROSS.  Therefore I adopted 2-part check utilizing Crossed() AND a backup check, hoping
					//			that it will catch all the crosses.  See the Long50,Long100 / Short50,Short100 sections below in both Hook & non-Hook
					//
					// =   =   =   =   =   =   =   =   =   =   =   =   =   =   =   =   =   =   =   =   =   =   =   =   =   =   =   =   =   =   =   =   =
					// This method waits until ask is below 26 level, then places entry order above, at 26 level (for buys)
					if (hookOn)
					{
						bool qualified = false;

						// Which level to use?
						level100 = (price100 - cBid < cBid - price0) ? price100 : price0;

						// -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -
						// First, check for long setup
						if (pendingDir != "Long")
						{
							// Check for crossing the ( 50/100 - buffer ) area, to actually place the pending trade
							// (Actually so long as it was set up, all that needs to happed is price be on correct side of line)
							if (pendingDir == "Long50"  &&  cAsk < price50 - BP)	// Crossed(price50 - BP, "Down", cAsk, pAsk))
							{
								// Set vars to place pending order below
								pendingDir = "Long";
								pendingPrice = price0 + EntryLevel1;
								hookTickGap = HookGapL1;
								qualified = true;
								Log($"\n ===> LONG (HookOn): DOWNWARD CROSS OF ({price50 - BP}) 50-LEVEL to {cAsk} on Candle @ {Time[0].ToString("HH:mm:ss")};  Status: WAITING...");
								Log($" ===> pendingDir = {pendingDir}, pendingPrice = {pendingPrice}, crossPrice = {crossPrice}" + sess);
							}
							else if (pendingDir == "Long100"  &&  cAsk < level100 - BP)	// Crossed(level100 - BP, "Down", cAsk, pAsk))
							{
								// Set vars to place pending order below
								pendingDir = "Long";
								pendingPrice = level100 - (100 - EntryLevel2);
								hookTickGap = HookGapL2;
								qualified = true;
								Log($"\n ===> LONG (HookOn): DOWNWARD CROSS OF ({level100 - BP}) 100-LEVEL to {cAsk} on Candle @ {Time[0].ToString("HH:mm:ss")};  Status: WAITING...");
								Log($" ===> pendingDir = {pendingDir}, pendingPrice = {pendingPrice}, crossPrice = {crossPrice}" + sess);
							}

							if (!qualified)
							{
								if (EntryLevel1 > 0  &&  pendingDir != "Long50")
								{
									// Has it crossed the 50 level downward? i.e. Did it register as actually crossing -OR-
									// Is it in the right zone with the current pending direction one of the short types?
									if (Crossed(price50, "Down", cAsk, pAsk)
									||  (cAsk < price50  &&  cAsk > price50 - BP  &&  pendingDir.StartsWith("Short")))
									{
										pendingDir = "Long50";
										matched = true;
										//if (State == State.Realtime)	Log($"\nLONG (HookOn): DOWNWARD CROSS OF 50-LEVEL ({price50})  cAsk/pAsk: {cAsk}/{pAsk};  pendingDir = {pendingDir}");
									}
								}
								if (!matched  &&  EntryLevel2 > 0  &&  pendingDir != "Long100")
								{
									// Has it crossed the 100 level downward? i.e. Did it register as actually crossing -OR-
									// Is it in the right zone with the current pending direction one of the short types?
									if (Crossed(level100, "Down", cAsk, pAsk)
									||  (cAsk < level100  &&  cAsk > level100 - BP  &&  pendingDir.StartsWith("Short")))
									{
										pendingDir = "Long100";
										//if (State == State.Realtime)	Log($"\nLONG (HookOn): DOWNWARD CROSS OF100-LEVEL ({level100})  cAsk/pAsk: {cAsk}/{pAsk};  pendingDir = {pendingDir}");
									}
								}
							}
							else
							{
								// Condtions met, which means that if we're already in a pending order, we neeed to cancel
								CancelEntryOrder();

								crossPrice = pendingPrice - hookTickGap * TickSize;
							}
						}

						// -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -
						// Now, check for short setup if did not commit to long
						if (pendingDir != "Short"  &&  !qualified)
						{
							//if (Math.Abs(Close[0] - level100) < 5)	if (State != State.Historical)	Log($"     XXXXX      CHECKING FOR SHORT on {level100} LINE: cBid = {cBid}, pBid = {pBid}");

							// Check for crossing the ( 50/100 + buffer ) area, to actually place the pending trade
							// (Actually so long as it was set up, all that needs to happed is price be on correct side of line)
							if (pendingDir == "Short100"  &&  cBid > level100 + BP)		// Crossed(level100 + BP, "Up", cBid, pBid))
							{
								// Set vars to place pending order below
								pendingDir = "Short";
								pendingPrice = level100 + EntryLevel1;
								hookTickGap = HookGapS1;
								qualified = true;
								Log($"\n ===> SHORT (HookOn): UPWARD CROSS OF ({level100 + BP}) 100-LEVEL to {cBid}  on Candle @ {Time[0].ToString("HH:mm:ss")};  Status: WAITING...");
								Log($" ===> pendingDir = {pendingDir}, pendingPrice = {pendingPrice}, crossPrice = {crossPrice}" + sess);
							}
							else if (pendingDir == "Short50"  &&  cBid > price50 + BP)	// Crossed(price50 + BP, "Up", cBid, pBid))
							{
								// Set vars to place pending order below
								pendingDir = "Short";
								pendingPrice = price0 + EntryLevel2;
								hookTickGap = HookGapS2;
								qualified = true;
								Log($"\n ===> SHORT (HookOn): UPWARD CROSS OF ({price50 + BP}) 50-LEVEL to {cBid}  on Candle @ {Time[0].ToString("HH:mm:ss")};  Status: WAITING...");
								Log($" ===> pendingDir = {pendingDir}, pendingPrice = {pendingPrice}, crossPrice = {crossPrice}" + sess);
							}

							if (!qualified)
							{
								if (EntryLevel1 > 0  &&  pendingDir != "Short100")
								{
									// Has it crossed the 100 level upward?i.e. Did it register as actually crossing -OR-
									// Is it in the right zone with the current pending direction one of the long types?
									if (Crossed(level100, "Up", cBid, pBid)
									||  (cBid > level100  &&  cBid < level100 + BP  &&  pendingDir.StartsWith("Long")))
									{
										pendingDir = "Short100";
										//if (State == State.Realtime)	Log($"\nSHORT (HookOn): UPWARD CROSS OF ({level100}) 100-LEVEL  cBid/pBid: {cBid}/{pBid};  pendingDir = {pendingDir}");
									}
								}
								if (!matched  &&  EntryLevel2 > 0  &&  pendingDir != "Short50")
								{
									// Has it crossed the 50 level upward? i.e. Did it register as actually crossing -OR-
									// Is it in the right zone with the current pending direction one of the long types?
									if (Crossed(price50, "Up", cBid, pBid)
									||  (cBid > price50  &&  cBid < price50 + BP  &&  pendingDir.StartsWith("Long")))
									{
										pendingDir = "Short50";
										//if (State == State.Realtime)	Log($"\nSHORT (HookOn): UPWARD CROSS OF ({price50}) 50-LEVEL  cBid/pBid: {cBid}/{pBid};  pendingDir = {pendingDir}");
									}
								}
							}
							else
							{
								// Condtions met, which means that if we're already in a pending order, we neeed to cancel
								CancelEntryOrder();

								crossPrice = pendingPrice + hookTickGap * TickSize;
								Log($"pendingDir = {pendingDir}, pendingPrice = {pendingPrice}, crossPrice = {crossPrice}" + sess);
							}
						}

						// -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -
						// Check for "hook"; has it crossed the crossPrice, where we need to trigger the Stop order?
						if (crossPrice > 0  &&  ((pendingDir == "Long"  &&  longOn)  ||  (pendingDir == "Short"  &&  shortOn)))
						{
							logStr = "";
							if (pendingDir == "Long"  &&  Crossed(crossPrice, "Down", cAsk, pAsk))
							{
								Log($"\n > > > > PENDING DIR IS LONG: DOWNWARD CROSS OF crossPrice ({crossPrice}) to Ask ({cAsk}) on Candle @ {Time[0].ToString("HH:mm:ss")}");
								placePendingOrder = true;
							}
							else if (pendingDir == "Short"  &&  Crossed(crossPrice, "Up", cBid, pBid))
							{
								Log($"\n > > > > PENDING DIR IS SHORT: UPWARD CROSS OF crossPrice ({crossPrice}) to Bid ({cBid}) on Candle @ {Time[0].ToString("HH:mm:ss")}");
								placePendingOrder = true;
							}
							// No need to check if there is already an existing stop order, because with hook on,
							// can't really happen, and anyway, it IS checked in Place entry stop order
						}

						if (placePendingOrder)
						{
							logStr = "";
							if (TradeFiltersOkay(pendingDir))
							{
								int qty = (pendingDir == "Long") ? LngQuantity : ShtQuantity;

								if (hookTickGap == 0)
									logStr = "hookTickGap == 0; PLACING MARKET ORDER";
								else if (pendingDir == "Long"  &&  GetCurrentAsk() >= pendingPrice)
									logStr = "Current price is at/beyond requested Price for Long pending entry; PLACING MARKET ORDER";
								else if (pendingDir == "Short "  &&  GetCurrentBid() <= pendingPrice)
									logStr = "Current price is at/beyond requested Price for Short pending entry; PLACING MARKET ORDER";
								else
									Log($"TradeFiltersOkay, pending order will be placed, pendingPrice = {pendingPrice}");

								bool placedOk = false;
								if (logStr != "")
								{
									Log(logStr);
									pendingPlaced = PlaceMarketOrder(pendingDir, qty);
								}
								else
								{
									Log($"PLACING {pendingDir.ToUpper()} PENDING STOP ORDER @ {pendingPrice}");
									pendingPlaced = PlaceEntryStopOrder(pendingDir, pendingPrice, qty);
								}
								if (pendingPlaced)
								{
									// We need prvCrossPrice for Manage Working Order, but
									// reset crossPrice so we cannot place a duplicate trade
									prvCrossPrice = crossPrice;
									crossPrice = 0;
								}
							}
							else
							{
								// TradeFiltersOkay annouces reason; do not repeat
								//Log($"FAILED TRADE FILTERS - NO ({pendingDir}) STOP ORDER PLACED (HOOK ON)");
								// this does not seem to work
								//Alert("FiltersFailed", Priority.High, pendingDir + " Trade Aborted for Filter", NinjaTrader.Core.Globals.InstallDir+@"\sounds\anticip.wav", 2, Brushes.Black, Brushes.Yellow);
							}
						}
					}

					// =   =   =   =   =   =   =   =   =   =   =   =   =   =   =   =   =   =   =   =   =   =   =   =   =   =   =   =   =   =   =   =   =
					// NO HOOK
					// This method immediately places the limit order at 26 or 77 level when price crosses the 50/100 line
					else //if (!hookOn)
					{
						// Now that we have buy OR sell by crossing the 50 up/down AND the 100 up/down, it is not useful
						// to switch back end forth every time it dips over the line and back.  Therefore, set up a buffer
						// amount by which is has to cross.  Hard code (for now) to 20 ticks, or 5 points.
						//
						// ACTUALLY NO, we don't want to lose the 50/100 line cross.  We need to make it ANOTHER condition, so crossing 50 down makes
						// pendingDir = Long, but then it has to cross the 45 before the pending order is placed.  And of course this all only needs
						// to be done for hook = off.
						string oldDir = "";

						// For Long Limit Order - If this bar crossed the 50 or 100 level downward
						if (pendingDir != "Long"  &&  pAsk != 0)
						{
							// Which level to use?
							level100 = (price100 - cBid < cBid - price0) ? price100 : price0;

							// Check for crossing the ( 50/100 - buffer ) area, to actually place the pending trade
							// (Actually so long as it was set up, all that needs to happed is price be on correct side of line)
							if (pendingDir == "Long100" &&  cAsk < level100 - BP)	// Crossed(level100 - BP, "Down", cAsk, pAsk))
							{
								pendingDir = "Long";
								logStr = $"\n ----> LONG (HookOff): DOWNWARD CROSS OF ({level100 - BP}) 100-LEVEL to {cAsk} on Candle @ {Time[0].ToString("HH:mm:ss")};  pendingDir = {pendingDir} : Place Order";

								// Set vars to place pending order below
								pendingPrice = level100 - (100 - EntryLevel2);
								placePendingOrder = true;
							}
							else if (pendingDir == "Long50"  &&  cAsk < price50 - BP)	// Crossed(price50 - BP, "Down", cAsk, pAsk))
							{
								pendingDir = "Long";
								logStr = $"\n ----> LONG (HookOff): DOWNWARD CROSS OF ({price50 - BP}) 50-LEVEL to {cAsk} on Candle @ {Time[0].ToString("HH:mm:ss")};  pendingDir = {pendingDir} : Place Order";

								// Set vars to place pending order below
								pendingPrice = price0 + EntryLevel1;
								placePendingOrder = true;
							}

							// Set up direction; don't care if it flips back and forth over the line, cause not actually placing pending order
							// (By using this (and 'matched' below), it allows me to break the following conditions into multiple parts for clarity)
							if (!placePendingOrder)
							{
								if (pendingDir != "Long50")
								{
									// Crossed 50 downward?
									if (Crossed(price50, "Down", cAsk, pAsk)
									||  (cAsk < price50  &&  cAsk > price50 - BP  &&  pendingDir.StartsWith("Short")))
									{
										// We will cancel the old order if applicable, regardless...
										matched = true;
										if (pendingDir == "Short")
											oldDir = pendingDir;
										
										// ...But only switch the pending dir if the given entry level is enabled
										if (EntryLevel1 > 0)
										{
											pendingDir = "Long50";
											if (State == State.Realtime)	Log($"\nLONG (HookOff): DOWNWARD CROSS OF ({price50}) 50-LEVEL  cAsk/pAsk: {cAsk}/{pAsk};  pendingDir = {pendingDir}, Status: WAITING...");
										}
										else
										{
											pendingDir = "None";
											if (State == State.Realtime)	Log($"\nLONG (HookOff): DOWNWARD CROSS OF ({price50}) 50-LEVEL, cBid/pBid: {cBid}/{pBid}.  But EntryLevel1 (26) Line is Disabled, so NOT setting 'Long50'");
										}
									}
								}
								if (!matched  &&  pendingDir != "Long100")
								{
									// Crossed 100 downward?
									if (Crossed(level100, "Down", cAsk, pAsk)
									||  (cAsk < level100  &&  cAsk > level100 - BP  &&  pendingDir.StartsWith("Short")))
									{
										// We will cancel the old order if applicable, regardless...
										if (pendingDir == "Short")
											oldDir = pendingDir;
										
										// ...But only switch the pending dir if the given entry level is enabled
										if (EntryLevel2 > 0)
										{
											pendingDir = "Long100";
											if (State == State.Realtime)	Log($"\nLONG (HookOff): DOWNWARD CROSS OF ({level100}) 100-LEVEL  cAsk/pAsk: {cAsk}/{pAsk};  pendingDir = {pendingDir}, Status: WAITING...");
										}
										else
										{
											pendingDir = "None";
											if (State == State.Realtime)	Log($"\nLONG (HookOff): DOWNWARD CROSS OF ({level100}) 100-LEVEL, cBid/pBid: {cBid}/{pBid}.  But EntryLevel2 (77) Line is Disabled, so NOT setting 'Long100'");
										}
									}
								}
							}
						}
						
						// For Short Limit Order - If this bar crossed the 50 or 100 level upward
						if (pendingDir != "Short"  &&  pBid != 0  &&  !placePendingOrder)
						{
							// Which level to use?
							level100 = (price100 - cBid < cBid - price0) ? price100 : price0;

							// Check for crossing the ( 50/100 + buffer ) area, to actually place the pending trade
							// (Actually so long as it was set up, all that needs to happed is price be on correct side of line)
							if (pendingDir == "Short100"  &&  cBid > level100 + BP)		// Crossed(level100 + BP, "Up", cBid, pBid))
							{
								pendingDir = "Short";
								logStr = $"\n ----> SHORT (HookOff): UPWARD CROSS OF ({level100}) 100-LEVEL to {cBid}  on Candle @ {Time[0].ToString("HH:mm:ss")};  pendingDir = {pendingDir} : Place Order";

								// Set vars to place pending order below
								pendingPrice = level100 + EntryLevel1;
								placePendingOrder = true;
							}
							else if (pendingDir == "Short50"  &&  cBid > price50 + BP)	// Crossed(price50 + BP, "Up", cBid, pBid))
							{
								pendingDir = "Short";
								logStr = $"\n ----> SHORT (HookOff): UPWARD CROSS OF ({price50}) 50-LEVEL to {cBid}  on Candle @ {Time[0].ToString("HH:mm:ss")};  pendingDir = {pendingDir} : Place Order";

								// Set vars to place pending order below
								pendingPrice = price0 + EntryLevel2;
								placePendingOrder = true;
							}

							// Set up direction; don't care if it flips back and forth over the line, cause not actually placing pending order
							// (By using this (and 'matched' below), it allows me to break the following conditions into multiple parts for clarity)
							if (!placePendingOrder)
							{
								if (pendingDir != "Short100")
								{
									// Crossed 100 upward?
									if (Crossed(level100, "Up", cBid, pBid)
									||  (cBid > level100  &&  cBid < level100 + BP  &&  pendingDir.StartsWith("Long")))
									{
										// We will cancel the old order if applicable, regardless...
										matched = true;
										if (pendingDir == "Long")
											oldDir = pendingDir;
										
										// ...But only switch the pending dir if the given entry level is enabled
										if (EntryLevel1 > 0)
										{
											pendingDir = "Short100";
											if (State == State.Realtime)	Log($"\nSHORT (HookOff): UPWARD CROSS OF ({level100}) 100-LEVEL  cBid/pBid: {cBid}/{pBid};  pendingDir = {pendingDir}, Status: WAITING...");
										}
										else
										{
											pendingDir = "None";
											if (State == State.Realtime)	Log($"\nSHORT (HookOff): UPWARD CROSS OF ({level100}) 100-LEVEL, cBid/pBid: {cBid}/{pBid}.  But EntryLevel1 (26) Line is Disabled, so NOT setting 'Short100'");
										}
									}
								}
								if (!matched  &&  pendingDir != "Short50")
								{
									// Crossed 50 upward?
									if (Crossed(price50, "Up", cBid, pBid)
									||  (cBid > price50  &&  cBid < price50 + BP  &&  pendingDir.StartsWith("Long")))
									{
										// We will cancel the old order if applicable, regardless...
										if (pendingDir == "Long")
											oldDir = pendingDir;
										
										// ...But only switch the pending dir if the given entry level is enabled
										if (EntryLevel2 > 0)
										{
											pendingDir = "Short50";
											if (State == State.Realtime)	Log($"\nSHORT (HookOff): UPWARD CROSS OF ({price50}) 50-LEVEL  (cBid/pBid: {cBid}/{pBid};  pendingDir = {pendingDir}, Status: WAITING...");
										}
										else
										{
											pendingDir = "None";
											if (State == State.Realtime)	Log($"\nSHORT (HookOff): UPWARD CROSS OF ({price50}) 50-LEVEL, cBid/pBid: {cBid}/{pBid}.  But EntryLevel2 (77) Line is Disabled, so NOT setting 'Short50'");
										}
									}
								}
							}
						}
						
						if (oldDir != ""  &&  entryOrder != null)
						{
							if (State == State.Realtime)
								Log($"pendingDir is {pendingDir}, WAS {oldDir}, and entryrOrder != null, so cancelling entry order");
							CancelEntryOrder();
						}

						// If there is already an order in at the 'pendingPrice', then
						// don't bother with checking (mostly to avoid unnec. logging)
						if (placePendingOrder  &&  entryOrder != null)
						{
							if (entryOrder.OrderType == OrderType.Limit)
							{
								if ((entryOrder.OrderAction == OrderAction.SellShort  &&  pendingDir == "Short")
								||  (entryOrder.OrderAction == OrderAction.Buy  &&  pendingDir == "Long"))
								{
									if (entryOrder.LimitPrice == pendingPrice)
									{
										Log($"     @$$%^%*****%^%$$@     TURNED OFF CALL TO PLACE ({entryOrder.OrderAction}) LIMIT ORDER - ALREADY EXISTS     @$$%^%*****%^%$$@");
										placePendingOrder = false;
									}
								}
							}
							if (placePendingOrder)
							{
								// Cancel any existing limit order, since if exists, it is not the same
								CancelEntryOrder();
								Log(logStr);
							}
						}

						if (placePendingOrder)
						{
							if (TradeFiltersOkay(pendingDir))
							{
								int qty = (pendingDir == "Long") ? LngQuantity : ShtQuantity;
								Log($"PLACING {pendingDir} PENDING LIMIT ORDER @ {pendingPrice}");
								pendingPlaced = PlaceEntryLimitOrder(pendingDir, pendingPrice, qty);
							}
							else
							{
								// TradeFiltersOkay annouces reason; do not repeat
								//Log($"FAILED TRADE FILTERS - NO ({pendingDir}) LIMIT ORDER PLACED (HOOK OFF)");
							}
						}
					}	// end no-hook
				}		// end is Flat
				#endregion

				#region CURRENTLY IN TRADE  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

				// in a trade currently
				else
				{
					// Reset these when live
					pendingDir = "None";		// This is done in OnExecUpdate, but doesn't hurt to do again....
					pendingPrice = 0;

					#region BREAKEVEN (POS or NEG) - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
					int dir = (Position.MarketPosition == MarketPosition.Long) ? 1 : -1;

					double curPrice = (dir == 1) ? GetCurrentBid() : GetCurrentAsk();
					double m = (dir == 1) ? 1.0 : -1.0;

					double newSL, newTP, oldSL1, oldSL2, oldTP1, oldTP2;
					newSL = newTP = oldSL1 = oldSL2 = oldTP1 = oldTP2 = 0;

					// Get existing SL if exists.  The two SL's should always be the same.
					// OrderState.Working seems to not mean 'active', but that it's still in exchange queue. 'Accepted' means it's active.
					if (slOrder1 != null  &&  (slOrder1.OrderState == OrderState.Working  ||  slOrder1.OrderState == OrderState.Accepted))
						oldSL1 = slOrder1.StopPrice;
					if (slOrder2 != null  &&  (slOrder2.OrderState == OrderState.Working  ||  slOrder2.OrderState == OrderState.Accepted))
						oldSL2 = slOrder2.StopPrice;

					// Get existing TP if exists.  It may not, or the two halves may have diff TPs (see below)
					if (tpOrder1 != null  &&  (tpOrder1.OrderState == OrderState.Working  ||  tpOrder1.OrderState == OrderState.Accepted))
						oldTP1 = tpOrder1.LimitPrice;
					if (tpOrder2 != null  &&  (tpOrder2.OrderState == OrderState.Working  ||  tpOrder2.OrderState == OrderState.Accepted))
						oldTP2 = tpOrder2.LimitPrice;

					// ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^
					// Check for positive move-to-breakeven (e.g. when up 7 points, move SL to BE + 1 point)
					if (UseBreakeven  &&  !MoveToBEWDone  &&  BEW_ActivTicks != 0)
					{
						if ((dir ==  1  &&  curPrice >= AveEntryPrice() + BEW_ActivTicks*TickSize)
						||  (dir == -1  &&  curPrice <= AveEntryPrice() - BEW_ActivTicks*TickSize))
						{
							newSL = AveEntryPrice() + m*BEW_Offset*TickSize;
							Log($"POSITIVE BREAKEVEN: m = {m}, Ave Price = {AveEntryPrice()}, curPrice = {curPrice}, set new SL to be {newSL}");

							if (newSL != 0  &&  m*newSL > m*oldSL1)
							{
								if (slOrder1 != null)	ModifyStopLoss(slOrder1, newSL, "+Breakeven");
								if (slOrder2 != null)	ModifyStopLoss(slOrder2, newSL, "+Breakeven");
								MoveToBEWDone = true;
							}
						}
					}

					// ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^   ^
					// Check for negative move-to-breakeven (e.g. when down 10 points, move TP to BE - 3 points)
					if (UseBreakeven  &&  !MoveToBELDone  &&  BEL_ActivTicks != 0)
					{
						if ((dir ==  1  &&  curPrice <= AveEntryPrice() + BEL_ActivTicks*TickSize)
						||  (dir == -1  &&  curPrice >= AveEntryPrice() - BEL_ActivTicks*TickSize))
						{
							newTP = AveEntryPrice() + m*BEL_Offset*TickSize;
							Log($"NEGATIVE BREAKEVEN m = {m}, Ave Price = {AveEntryPrice()}, set new TP to be {newTP}");

							if (newTP != 0)
							{
								// We are only going to modify TakeProfit; if one or both
								// halves do not have an existing TP, they will be skipped
								// HOWEVER, unlike the SL, we do not care what the old TP was...
								if (tpOrder1 != null)	ModifyTakeProfit(tpOrder1, newTP, "-Breakeven");
								if (tpOrder2 != null)	ModifyTakeProfit(tpOrder2, newTP, "-Breakeven");
								MoveToBELDone = true;
							}
						}
					}
					#endregion

					#region TRAIL  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
					if (UseTrailingStop)
					{
						if (!trailTriggered)
						{
							if (dir == 1  &&  curPrice >= AveEntryPrice() + LngActivationTicks*TickSize)
							{
								Log($"                                   TRAILING STOP TRIGGERED cause Bid ({curPrice}) >= Entry Price ({AveEntryPrice()}) + activation ticks ({LngActivationTicks})");
								trailTriggered = true;
							}
							else if (dir == -1  &&  curPrice <= AveEntryPrice() - ShtActivationTicks*TickSize)
							{
								Log($"                                   TRAILING STOP TRIGGERED cause Ask ({curPrice}) <= Entry Price ({AveEntryPrice()}) - activation ticks ({ShtActivationTicks})");
								trailTriggered = true;
							}
						}
						if (trailTriggered)
						{
							// Trail usually when first leg closes, but not nec -we may need to move both stops
							newSL = GetNewTrailPrice(Position.MarketPosition.ToString());
							if (newSL != 0)
							{
								//Log($"                                   MODIFYING TRAILING STOPLOSS TO {newSL}    ( oldSL1 = {oldSL1}, oldSL2 = {oldSL2} )");
								if (oldSL1 > 0  &&  m*newSL > m*oldSL1)
									ModifyStopLoss(slOrder1, newSL, "Trail");
								if (oldSL2 > 0  &&  m*newSL > m*oldSL2)
									ModifyStopLoss(slOrder2, newSL, "Trail");
							}
						}
					}
					#endregion
				}
				#endregion
			}
			#endregion

			// Don't waste time on OCD for historical
			if (State != State.Historical)
				ManageOCD();	// Update the On-Chart Display

			pAsk = cAsk;
			pBid = cBid;
			lastCaller = "";		// reset so if this method is entered again before other log, reprint the function name
		}

		#region TRADE FUNCTIONS (UNMANAGED)  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
		/// <summary>
		/// Places a limit order for entry into a position
		/// </summary>
		/// <param name="dir">Direction of the trade ("Long" or "Short")</param>
		/// <param name="price">Limit price for the order</param>
		/// <param name="qty">Number of contracts/shares to trade</param>
		/// <returns>True if order was placed, false otherwise</returns>
		public bool PlaceEntryLimitOrder(string dir, double price, int qty)
		{
			// Cannot place entry order if market is not flat
			if (Position.MarketPosition != MarketPosition.Flat)
			{
				Log("MarketPosition is not Flat; cannot place new Entry Limit Order", "DerkTM");
				return false;
			}

			// Getting multiple entries sometimes: cannot trust entryOrder to properly
			// reflect the status, so loop through all orders to see if it already exists
			Order openOrder = entryOrder;
			bool placed = false;

			// If we are already in a limit order, cancel or ignore
			if (openOrder != null)
			{
				// If a pending STOP order is already active, we close it...
				if (openOrder.OrderType == OrderType.StopMarket)
					Log("Already in Pending " + dir + " Stop order @ " + openOrder.StopPrice + " - Cancelling");

				else if (openOrder.OrderType == OrderType.Limit)
				{
					if ((openOrder.OrderAction == OrderAction.SellShort && dir == "Short")
					|| (openOrder.OrderAction == OrderAction.Buy && dir == "Long"))
					{
						if (openOrder.LimitPrice == price)
						{
							Log("Already in Pending " + dir + " Limit order @ " + price + " - Doing nothing");
							return false;
						}
						else
							Log("Already in Pending " + dir + " Limit order, but at diff price (" + openOrder.LimitPrice + ", not requested price of " + price + ") - Cancelling old " + openOrder.OrderAction + " pending limit order");
					}
					else
						Log("REVERSAL?: Already in Pending " + openOrder.OrderAction + " Entry Limit order, but " + dir + " requested - Cancelling old order");
				}
				else
					Log("SHOULD NEVER HAPPEN: Already in Pending " + openOrder.OrderAction + " " + openOrder.OrderType + " order - Cancelling old order");

				CancelEntryOrder();
			}

			double curPrice = (dir == "Short") ? GetCurrentBid() : GetCurrentAsk();
			if (curPrice <= price && dir == "Long")
				Log("Current Ask " + curPrice + " <= requested Limit price " + price + "; Cannot place Limit order");
			else if (curPrice >= price && dir == "Short")
				Log("Current Bid " + curPrice + " >= requested Limit price " + price + "; Cannot place Limit order");
			else
			{
				Log("Place limit order @ " + price + ": dir = " + dir + ", qty = " + qty + ", name = Entry");
				var action = (dir == "Long") ? OrderAction.Buy : OrderAction.SellShort;
				SubmitOrderUnmanaged(1, action, OrderType.Limit, qty, price, 0, "", "Entry");
				placed = true;
			}

			return placed;
		}

		/// <summary>
		/// Places a stop order for entry into a position
		/// </summary>
		/// <param name="dir">Direction of the trade ("Long" or "Short")</param>
		/// <param name="price">Stop price for the order</param>
		/// <param name="qty">Number of contracts/shares to trade</param>
		/// <returns>True if order was placed, false otherwise</returns>
		public bool PlaceEntryStopOrder(string dir, double price, int qty)
		{
			// Cannot place entry order if market is not flat
			if (Position.MarketPosition != MarketPosition.Flat)
			{
				Log("MarketPosition is not Flat; cannot place new Entry Stop Order");
				return false;
			}

			// Getting multiple entries sometimes: cannot trust entryOrder to properly
			// reflect the status, so loop through all orders to see if it already exists
			int orderStatus = 0;
			Order openOrder = entryOrder;
			bool placed = false;

			// If we are already in a limit order, cancel or ignore
			if (openOrder != null || orderStatus == 2 || orderStatus == 3)
			{
				// If order exists but in unreliable state, we need to cancel it
				if (orderStatus == 2)
				{
					Log("Cancelling unreliable order to prevent duplicate orders");
					CancelEntryOrder();
				}
				// Special handling for filled orders - can't cancel them
				else if (orderStatus == 3)
				{
					Log("Order already filled with contracts - cannot cancel, but will prevent duplicate orders");
					return false;
				}

				// If a pending LIMIT order is already active, we close it...
				if (openOrder.OrderType == OrderType.Limit)
					Log("Already in Pending " + dir + " Limit order @ " + openOrder.LimitPrice + " - Cancelling");

				else if (openOrder.OrderType == OrderType.StopMarket)
				{
					if ((openOrder.OrderAction == OrderAction.SellShort && dir == "Short")
					|| (openOrder.OrderAction == OrderAction.Buy && dir == "Long"))
					{
						if (openOrder.StopPrice == price)
						{
							Log("Already in Pending " + dir + " Stop order @ " + price + " - Doing nothing");
							return false;
						}
						else
							Log("Already in Pending " + dir + " Stop order, but at diff price (" + openOrder.StopPrice + ", not requested price of " + price + ") - Cancelling old " + openOrder.OrderAction + " pending stop order");
					}
					else
						Log("REVERSAL?: Already in Pending " + openOrder.OrderAction + " StopMarket order, but " + dir + " requested, so cancelling old order");
				}
				else
					Log("SHOULD NEVER HAPPEN: Already in Pending " + openOrder.OrderAction + " " + openOrder.OrderType + " order - Cancelling old order");

				CancelEntryOrder();
			}

			double curPrice = (dir == "Short") ? GetCurrentBid() : GetCurrentAsk();
			if (curPrice >= price && dir == "Long")
				Log(dir + ": Current Ask " + curPrice + " >= requested Limit price " + price + "; Cannot place Stop order");
			else if (curPrice <= price && dir == "Short")
				Log(dir + ": Current Bid " + curPrice + " <= requested Limit price " + price + "; Cannot place Stop order");
			else
			{
				Log("Place stop order @ " + price + ": dir = " + dir + ", qty = " + qty + ", name = Entry");
				var action = (dir == "Long") ? OrderAction.Buy : OrderAction.SellShort;
				SubmitOrderUnmanaged(1, action, OrderType.StopMarket, qty, 0, price, "", "Entry");
				placed = true;
			}

			return placed;
		}

		/// <summary>
		/// Places a market order for immediate execution
		/// </summary>
		/// <param name="dir">Direction of the trade ("Long" or "Short")</param>
		/// <param name="qty">Number of contracts/shares to trade</param>
		/// <param name="name">Name of the order (default: "Entry")</param>
		/// <returns>True if order was placed</returns>
		public bool PlaceMarketOrder(string dir, int qty, string name="Entry")
		{
			Log("Placing market order, dir = " + dir + ", qty = " + qty + ", name = " + name);

			var action = (dir == "Long") ? OrderAction.Buy : OrderAction.SellShort;
			double price = (dir == "Long") ? GetCurrentAsk() : GetCurrentBid();
			Log("Placing " + dir + " Market order named '" + name + "' for " + qty + " contract(s). Current price is: " + price);
			SubmitOrderUnmanaged(1, action, OrderType.Market, qty, 0, 0, "", name);

			return true;	// Always places order (so far -return false if later checks prevent placement)
		}

		/// <summary>
		/// Modifies an existing stop loss order
		/// </summary>
		/// <param name="orderSL">The stop loss order to modify</param>
		/// <param name="newSL">New stop price (0 to keep existing price)</param>
		/// <param name="reason">Reason for the modification (for logging)</param>
		/// <param name="qty">New quantity (0 to use existing quantity)</param>
		public void ModifyStopLoss(Order orderSL, double newSL, string reason, int qty=0)
		{
			if (orderSL == null)
			{
				Log("Cannot move SL because order passed in == null");
				return;
			}

			// Used only for logging
			string dir = Position.MarketPosition.ToString();
			double curPrice = (dir == "Long") ? GetCurrentBid() : GetCurrentAsk();

			// If no newSL specified, use whole position
			if (qty == 0)
				qty = orderSL.Quantity;
			else if (qty > Position.Quantity)
			{
				Log("qty (" + qty + ") > open quantity; setting to " + Position.Quantity);
				qty = Position.Quantity;
			}

			// If no newSL specified, we are just changing quantity
			if (newSL == 0)
			{
				newSL = orderSL.StopPrice;
				Log("No new SL specified; using existing SL (" + newSL + ") w/Qty = " + qty + ". Order State is '" + orderSL.OrderState + "'; Trying to set SL now...");
			}

			try
			{
				if (curPrice <= newSL && dir == "Long")
					Log("Dir is " + dir + ": Current Bid " + curPrice + " <= requested Limit price " + newSL + " - Cannot place Stop order; Aborting");
				else if (curPrice >= newSL && dir == "Short")
					Log("Dir is " + dir + ": Current Ask " + curPrice + " >= requested Limit price " + newSL + " - Cannot modify Stop order; Aborting");
				else
				{
					// If there is no existing SL, we do NOT place a new one; we fail out
					Log("Moving SL for " + qty + " contract(s) to new price @ " + newSL + " (" + reason + ")");

					ChangeOrder(orderSL, qty, 0, newSL);
				}
			}
			catch (Exception ex)
			{
				Log("Error changing SL to " + newSL + ".  Current price = " + curPrice + ". Error: " + ex.Message);
				NinjaScript.Log("Error changing SL to " + newSL + ".  Current price = " + curPrice + ". Error: " + ex.Message, LogLevel.Error);	// Logs to console
			}
		}

		/// <summary>
		/// Modifies an existing take profit order
		/// </summary>
		/// <param name="orderTP">The take profit order to modify</param>
		/// <param name="newTP">New limit price</param>
		/// <param name="reason">Reason for the modification (for logging)</param>
		/// <param name="qty">New quantity (0 to use existing quantity)</param>
		public void ModifyTakeProfit(Order orderTP, double newTP, string reason, int qty=0)
		{
			// An existing TP is required; we do NOT place a new one if it does not exist
			if (orderTP == null)
			{
				Log("Cannot move TP because order passed in == null");
				return;
			}

			// Used only for logging
			string dir = Position.MarketPosition.ToString();
			double curPrice = (dir == "Long") ? GetCurrentBid() : GetCurrentAsk();

			// If no qty specified, use appropriate quantity based on which TP order
			if (qty == 0)
				qty = orderTP.Quantity;
			if (qty > Position.Quantity)
			{
				Log("qty (" + qty + ") > open quantity; setting to " + Position.Quantity);
				qty = Position.Quantity;
			}

			try
			{
				if (curPrice >= newTP && dir == "Long")
					Log("Dir is " + dir + ": Current Ask " + curPrice + " >= requested Limit price " + newTP + " - Cannot modify TP order; Aborting");
				else if (curPrice <= newTP && dir == "Short")
					Log("Dir is " + dir + ": Current Bid " + curPrice + " <= requested Limit price " + newTP + " - Cannot modify TP order; Aborting");
				else
				{
					Log("Moving TP for " + qty + " contract(s) to new price @ " + newTP + " (" + reason + ")");

					ChangeOrder(orderTP, qty, newTP, 0);
				}
			}
			catch (Exception ex)
			{
				Log("Error changing TP to " + newTP + ".  Current price = " + curPrice + ". Error: " + ex.Message);
				NinjaScript.Log("Error changing TP to " + newTP + ".  Current price = " + curPrice + ". Error: " + ex.Message, LogLevel.Error);	// Logs to console
			}
		}

		/// <summary>
		/// Closes an open position with a market order
		/// </summary>
		/// <param name="name">Name of the order (default: "Close")</param>
		/// <param name="dir">Direction of the position to close (default: current position direction)</param>
		/// <param name="qty">Number of contracts/shares to close (default: all)</param>
		public void ClosePosition(string name="Close", string dir="", int qty=0)
		{
			if (Position.MarketPosition == MarketPosition.Flat)
				return;

			if (qty == 0) qty = Position.Quantity;
			if (dir == "") dir = Position.MarketPosition.ToString();
			var action = (dir == "Long") ? OrderAction.Sell : OrderAction.BuyToCover;

			if (qty == Position.Quantity)
			{
				if (slOrder1 != null || tpOrder2 != null)
				{
					Log("Closing all contracts, and slOrder1 != null || tpOrder2 != null, so Cancelling Stops");
					CancelAllStops();
				}
			}

			Log("Closing Position: name = " + name + ", dir = " + dir + ", qty = " + qty);
			SubmitOrderUnmanaged(1, action, OrderType.Market, qty, 0, 0, "", name);
		}

		/// <summary>
		/// Cancels the current entry order if it exists and is not filled
		/// </summary>
		/// <param name="memberName">Name of the calling method (automatically populated)</param>
		public void CancelEntryOrder([CallerMemberName] string memberName = "")
		{
			if (entryOrder == null)
				return;

			if (entryOrder.OrderState == OrderState.Filled)
			{
				Log("Cannot cancel filled order; aborting [from " + memberName + "]");
				return;
			}

			Log("Cancelling entryOrder... [from " + memberName + "]");
			CancelOrder(entryOrder);
			entryOrder = null;
		}

		/// <summary>
		/// Cancels any active SL or TP
		/// </summary>
		private void CancelAllStops()
		{
			if (slOrder1 != null)
			{
				//Log($"Cancelling slOrder1");
				CancelOrder(slOrder1);
				slOrder1 = null;
			}
			if (slOrder2 != null)
			{
				//Log($"Cancelling slOrder2");
				CancelOrder(slOrder2);
				slOrder2 = null;
			}
			if (tpOrder1 != null)
			{
				//Log($"Cancelling tpOrder1");
				CancelOrder(tpOrder1);
				tpOrder1 = null;
			}
			if (tpOrder2 != null)
			{
				//Log($"Cancelling tpOrder2");
				CancelOrder(tpOrder2);
				tpOrder2 = null;
			}
			lastCaller = "";	// reset so if this method is entered again before other log, reprint the function name
		}
		
		/// <summary>
		/// Gets the average entry price of the current position, rounded to the nearest tick
		/// </summary>
		/// <returns>The average entry price</returns>
		public double AveEntryPrice()
		{
			double avePrice = Position.AveragePrice;

			// If ave price does not fall on tick boundary, round up
			int numTicks = (int)(avePrice / TickSize);
			if (numTicks * TickSize != avePrice)
			{
				double prc = Instrument.MasterInstrument.RoundToTickSize(avePrice);
				avePrice = prc;
			}
			return avePrice;
		}
		#endregion
		
		#region HELPER FUNCTIONS - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
		// Update the fifty-level vars
		private void UpdateLevels()
		{
			// Update century prices to work with, when not in any trade
			if (Position.MarketPosition == MarketPosition.Flat)
			{
				mult100 = (int)(Close[0] / 100);
				price0 = 100 * mult100;
				price100 = price0 + 100;
				price50 = price0 + 50;
				double price26 = price0 + EntryLevel1;
				double price126 = price100 + EntryLevel1;
				double price77 = price0 + EntryLevel2;
				double price177 = price100 + EntryLevel2;


				// The reaminder of the code is for drawing lines / labels...

				// Derive offset for moving labels off of line (zero for right on the line)
				double tx = (LabelMod == 0) ? 0 : lastATR / LabelMod;	// Base text offset on ATR
				double M = Math.Round(tx / TickSize);					// Round offset to nearest point
				tx = M * TickSize;

				//Log($"Close[0] = {Close[0]}; price0 = {price0}; price100 = {price100}; price50 = {price50}");
				//Log($"Set mult100 = {mult100}, price0 = {price0}, price100 = {price100}, price50 = {price50}");
				if (DrawLines)
				{
					// Draw main price level lines
					Draw.HorizontalLine(this, "50Line" + price50,  price50,  Brushes.RoyalBlue);
					Draw.HorizontalLine(this, "00Line" + price100, price100, Brushes.MediumSeaGreen);
					Draw.HorizontalLine(this, "00Line" + price0,   price0,   Brushes.MediumSeaGreen);
					Draw.HorizontalLine(this, "26Line" + price126, price126, Brushes.Magenta, DashStyleHelper.DashDotDot, 1, true);
					Draw.HorizontalLine(this, "26Line" + price26,  price26,  Brushes.Magenta, DashStyleHelper.DashDotDot, 1, true);
					Draw.HorizontalLine(this, "77Line" + price177, price177, Brushes.Yellow,  DashStyleHelper.DashDot, 1, true);
					Draw.HorizontalLine(this, "77Line" + price77,  price77,  Brushes.Yellow,  DashStyleHelper.DashDot, 1, true);

					// Add text labels for each price level on the right side of the chart
					SimpleFont labelFont = new SimpleFont("Arial", 12);

					// Position labels on the right edge of the chart and below their respective lines
					// Using barsAgo = -1 to anchor text to the right edge of the chart
					// Using a Y-offset of 3 points to position text below the line
					Draw.Text(this, "50Text" + price50,  false, price50.ToString(),  0, price50 + tx,  0, Brushes.RoyalBlue,      labelFont, TextAlignment.Right, Brushes.Transparent, Brushes.Transparent, 100);
					Draw.Text(this, "00Text" + price100, false, price100.ToString(), 0, price100 + tx, 0, Brushes.MediumSeaGreen, labelFont, TextAlignment.Right, Brushes.Transparent, Brushes.Transparent, 100);
					Draw.Text(this, "00Text" + price0,   false, price0.ToString(),   0, price0 + tx,   0, Brushes.MediumSeaGreen, labelFont, TextAlignment.Right, Brushes.Transparent, Brushes.Transparent, 100);
					Draw.Text(this, "26Text" + price126, false, price126.ToString(), 0, price126 + tx, 0, Brushes.Magenta,        labelFont, TextAlignment.Right, Brushes.Transparent, Brushes.Transparent, 100);
					Draw.Text(this, "26Text" + price26,  false, price26.ToString(),  0, price26 + tx,  0, Brushes.Magenta,        labelFont, TextAlignment.Right, Brushes.Transparent, Brushes.Transparent, 100);
					Draw.Text(this, "77Text" + price177, false, price177.ToString(), 0, price177 + tx, 0, Brushes.Yellow,         labelFont, TextAlignment.Right, Brushes.Transparent, Brushes.Transparent, 100);
					Draw.Text(this, "77Text" + price77,  false, price77.ToString(),  0, price77 + tx,  0, Brushes.Yellow,         labelFont, TextAlignment.Right, Brushes.Transparent, Brushes.Transparent, 100);

					// Draw activation lines -where crossing it will actually place pending order
					Draw.HorizontalLine(this, "50ActTop" + price50, price50 + BP, Brushes.Blue);
					Draw.HorizontalLine(this, "50ActBtm" + price50, price50 - BP, Brushes.Blue);
					Draw.HorizontalLine(this, "00ActTop" + price100, price100 + BP, Brushes.DarkGreen);
					Draw.HorizontalLine(this, "00ActBtm" + price100, price100 - BP, Brushes.DarkGreen);
					Draw.HorizontalLine(this, "00ActTop" + price0, price0 + BP, Brushes.DarkGreen);
					Draw.HorizontalLine(this, "00ActBtm" + price0, price0 - BP, Brushes.DarkGreen);

					// Add text labels for activation lines
					Draw.Text(this, "50ActTopText" + price50,  false, (price50 + BP).ToString(),  0, price50 + BP + tx,  0, Brushes.Blue,      labelFont, TextAlignment.Right, Brushes.Transparent, Brushes.Transparent, 100);
					Draw.Text(this, "50ActBtmText" + price50,  false, (price50 - BP).ToString(),  0, price50 - BP + tx,  0, Brushes.Blue,      labelFont, TextAlignment.Right, Brushes.Transparent, Brushes.Transparent, 100);
					Draw.Text(this, "00ActTopText" + price100, false, (price100 + BP).ToString(), 0, price100 + BP + tx, 0, Brushes.DarkGreen, labelFont, TextAlignment.Right, Brushes.Transparent, Brushes.Transparent, 100);
					Draw.Text(this, "00ActBtmText" + price100, false, (price100 - BP).ToString(), 0, price100 - BP + tx, 0, Brushes.DarkGreen, labelFont, TextAlignment.Right, Brushes.Transparent, Brushes.Transparent, 100);
					Draw.Text(this, "00ActTopText" + price0,   false, (price0 + BP).ToString(),   0, price0 + BP + tx,   0, Brushes.DarkGreen, labelFont, TextAlignment.Right, Brushes.Transparent, Brushes.Transparent, 100);
					Draw.Text(this, "00ActBtmText" + price0,   false, (price0 - BP).ToString(),   0, price0 - BP + tx,   0, Brushes.DarkGreen, labelFont, TextAlignment.Right, Brushes.Transparent, Brushes.Transparent, 100);

					if (hookOn)
					{
						// Draw hook-specific lines
						double gL1 = HookGapL1 * TickSize;
						double gL2 = HookGapL2 * TickSize;
						double gS1 = HookGapS1 * TickSize;
						double gS2 = HookGapS2 * TickSize;
						double price26Limit  = price26  - gL1;
						double price126Limit = price126 - gL1;
						double price77Limit  = price77  - gL2;
						double price177Limit = price177 - gL2;
						double price26Stop   = price26  + gS1;
						double price126Stop  = price126 + gS1;
						double price77Stop   = price77  + gS2;
						double price177Stop  = price177 + gS2;

						Draw.HorizontalLine(this, "26ActLimit" + price26,  price26Limit,  Brushes.BlueViolet, DashStyleHelper.DashDotDot, 1, true);
						Draw.HorizontalLine(this, "26ActLimit" + price126, price126Limit, Brushes.BlueViolet, DashStyleHelper.DashDotDot, 1, true);
						Draw.HorizontalLine(this, "77ActLimit" + price77,  price77Limit,  Brushes.Chocolate,  DashStyleHelper.DashDot, 1, true);
						Draw.HorizontalLine(this, "77ActLimit" + price177, price177Limit, Brushes.Chocolate,  DashStyleHelper.DashDot, 1, true);
						Draw.HorizontalLine(this, "26ActStop" + price26,   price26Stop,   Brushes.BlueViolet, DashStyleHelper.DashDotDot, 1, true);
						Draw.HorizontalLine(this, "26ActStop" + price126,  price126Stop,  Brushes.BlueViolet, DashStyleHelper.DashDotDot, 1, true);
						Draw.HorizontalLine(this, "77ActStop" + price77,   price77Stop,   Brushes.Chocolate,  DashStyleHelper.DashDot, 1, true);
						Draw.HorizontalLine(this, "77ActStop" + price177,  price177Stop,  Brushes.Chocolate,  DashStyleHelper.DashDot, 1, true);

						// Add text labels for hook-specific lines
						Draw.Text(this, "26ActLimitText" + price26,  false, price26Limit.ToString(),  0, price26Limit + tx,  0, Brushes.BlueViolet, labelFont, TextAlignment.Right, Brushes.Transparent, Brushes.Transparent, 100);
						Draw.Text(this, "26ActLimitText" + price126, false, price126Limit.ToString(), 0, price126Limit + tx, 0, Brushes.BlueViolet, labelFont, TextAlignment.Right, Brushes.Transparent, Brushes.Transparent, 100);
						Draw.Text(this, "77ActLimitText" + price77,  false, price77Limit.ToString(),  0, price77Limit + tx,  0, Brushes.Chocolate,  labelFont, TextAlignment.Right, Brushes.Transparent, Brushes.Transparent, 100);
						Draw.Text(this, "77ActLimitText" + price177, false, price177Limit.ToString(), 0, price177Limit + tx, 0, Brushes.Chocolate,  labelFont, TextAlignment.Right, Brushes.Transparent, Brushes.Transparent, 100);
						Draw.Text(this, "26ActStopText" + price26,   false, price26Stop.ToString(),   0, price26Stop + tx,   0, Brushes.BlueViolet, labelFont, TextAlignment.Right, Brushes.Transparent, Brushes.Transparent, 100);
						Draw.Text(this, "26ActStopText" + price126,  false, price126Stop.ToString(),  0, price126Stop + tx,  0, Brushes.BlueViolet, labelFont, TextAlignment.Right, Brushes.Transparent, Brushes.Transparent, 100);
						Draw.Text(this, "77ActStopText" + price77,   false, price77Stop.ToString(),   0, price77Stop + tx,   0, Brushes.Chocolate,  labelFont, TextAlignment.Right, Brushes.Transparent, Brushes.Transparent, 100);
						Draw.Text(this, "77ActStopText" + price177,  false, price177Stop.ToString(),  0, price177Stop + tx,  0, Brushes.Chocolate,  labelFont, TextAlignment.Right, Brushes.Transparent, Brushes.Transparent, 100);
					}
					else
					{
						// Remove the hook-specific lines and text labels
						foreach (IDrawingTool drawingTool in DrawObjects)
						{
							// Remove hook activation lines
							if (drawingTool.Tag.StartsWith("26Act")  ||  drawingTool.Tag.StartsWith("77Act"))
								RemoveDrawObject(drawingTool.Tag);
						}
					}
				}
			}
		}

		private void AdjustLabels()
		{
			if (!DrawLines)
				return;

			// Since we cannot put them on the right side of the chart, move all text labels to Time[0].
			// Unfortunately, this cannot adjust for changing ATR, so old line labels may look a bit off-center.
			// If not good enough, best to delete all lines and redraw....
			foreach (IDrawingTool drawingTool in DrawObjects)
			{
				// Check for hook activation objects
				if (drawingTool.Tag.Contains("Text"))
				{
					// Check if it's a text object
					if (drawingTool is Text textObject)
					{
						// Check if the first text object already has Time[0] as its X value
						// If so, all objects should already be updated (since we update all at once)
						if (textObject.Anchor.Time == Time[0])
							break; // Exit the loop early

						// Try to adjust for current ATR (this will fail badly where y is more than 1 point away from original line)
						double y = textObject.Anchor.Price;

						// Remove the old text object
						RemoveDrawObject(drawingTool.Tag);

						// Create a new text object at Time[0] with the same properties
						Draw.Text(this, drawingTool.Tag, false, textObject.DisplayText, 0, y, 0,
									textObject.TextBrush, textObject.Font, textObject.Alignment,
									textObject.OutlineStroke.Brush, textObject.AreaBrush, textObject.AreaOpacity);
					}
				}
			}
		}

		private void SetHook()
		{
			bool oldValue = hookOn;
			if (hookMode == HookType.HookOn)
				hookOn = true;
			else if (hookMode == HookType.HookOff)
				hookOn = false;
			else if (UseATR  &&  HookPointsATR > 0)
			{
				// If in Auto mode, then we defer to the user's setting of
				// HookPointsATR; if current ATR is over, then we turn on Hook
				hookOn = (lastATR > HookPointsATR);
			}
			else
				Log($"ERROR: No conditions met");

			if (oldValue != hookOn)
				Log($"hookOn changed from {oldValue} to {hookOn}");

			// If ATR is much too large, we disable trade
			if (UseATR  &&  DisableOnMaxATR > 0)
			{
				if (lastATR > DisableOnMaxATR  &&  previouslyOn)
				{
					prvLongOn = longOn;
					prvShortOn = shortOn;
					longOn = shortOn = false;
					Log($"ATR[{PeriodATR}] ({lastATR}) is too large (max {DisableOnMaxATR} points); Disabling all trade");
					if (State == State.Realtime)
						UpdateTradeButtons(false, false);
					previouslyOn = false;
				}
				else if (lastATR <= DisableOnMaxATR  &&  !previouslyOn)
				{
					longOn = prvLongOn;
					shortOn = prvShortOn;
					Log($"ATR[{PeriodATR}] ({lastATR}) now less than max ({DisableOnMaxATR} points); resetting to previous values: longOn = {longOn}, shortOn = {shortOn}");
					if (State == State.Realtime)
						UpdateTradeButtons(longOn, shortOn);
					previouslyOn = true;
				}
			}
		}

		private bool ManageWorkingOrder()
		{
			// Set 'workingOrder' variable, for display on OCD
			bool cancelled = false;
			workingOrder = "None";

			// This is only for PENDING orders.  Set to None if we are in open order
			// The OrderType check below should handle this, but... to be safe...
			if (Position.MarketPosition == MarketPosition.Flat)
			{
				if (entryOrder == null)
					return cancelled;

				//Log($"entryOrder.OrderState = {entryOrder.OrderState}        entryOrder.OrderType = {entryOrder.OrderType}");
				if (entryOrder.OrderState == OrderState.Accepted  ||  entryOrder.OrderState == OrderState.Working)
				{
					// Name the order (for OCD)
					if (entryOrder.OrderType == OrderType.StopMarket)
					{
						if (entryOrder.OrderAction == OrderAction.Buy)				workingOrder = "LongStop";
						else if (entryOrder.OrderAction == OrderAction.SellShort)	workingOrder = "ShortStop";
					}
					else if (entryOrder.OrderType == OrderType.Limit)
					{
						if (entryOrder.OrderAction == OrderAction.Buy)				workingOrder = "LongLimit";
						else if (entryOrder.OrderAction == OrderAction.SellShort)	workingOrder = "ShortLimit";
					}
					if (workingOrder == "None")
						Log($"ERROR?: entryOrder is 'Working', but not a Stop or Limit order");

					// Cancel of the Stop order if price goes too far.   Only hook uses stop orders.
					if (entryOrder.OrderType == OrderType.StopMarket  &&  prvCrossPrice != 0)
					{
						//Log($"entryOrder.OrderType == OrderType.StopMarket");

						// (This runs from the Tick series chart (BiP == 1), so Close[0] is 'last' price)
						// If OrderAction not Buy, we assume it is SellShort
						cancelled = (entryOrder.OrderAction == OrderAction.Buy) ? (Close[0] < prvCrossPrice - CancelPoints)
																				: (Close[0] > prvCrossPrice + CancelPoints);
						if (cancelled)
						{
							Log($"Price ({Close[0]}) moved 'CancelPoints' ({CancelPoints}) beyond Hook entry trigger ({prvCrossPrice}) w/o filling stop order; cancelling order");
							CancelEntryOrder();
						}
					}
				}
			}
			return cancelled;
		}

		
		// Static variables for Crossed()
		private static double prvLvl = 0;
		private static string prvDir = "None"; // "Up" or "Down"
		private static HashSet<string> wasCloseToLevel = new HashSet<string>();
		private const double CLOSE_THRESHOLD = 4.0; // Points to consider "close"

		/// <summary>
		/// Detects price crosses of a specified level with enhanced detection capabilities.
		/// More robust than NinjaTrader's CrossAbove/Below functions as it prevents consecutive
		/// crosses in the same direction and includes "close zone" detection.
		/// </summary>
		/// <param name="level">The price level to check for crosses</param>
		/// <param name="dir">Direction of the cross ("Up" or "Down")</param>
		/// <param name="cPrc">Current price</param>
		/// <param name="pPrc">Previous price</param>
		/// <param name="skipDup">Whether to prevent duplicate crosses in the same direction (default: true)</param>
		/// <returns>True if a valid cross occurred, false otherwise</returns>
		public bool Crossed(double level, string dir, double cPrc, double pPrc, bool skipDup=false)
		{
			// This is an expanded function to check if a price crossed a given price level
			// The simple algo is 'standardCross' shown below, but it has been witnessed to
			// miss crosses (in real time).  This should not be possible (if truly called
			// each tick), yet it occurs.  The solution is to keep track of when the current
			// price is 'nearby' the given level, then if the standardCross is not triggered,
			// we can additionally check that cPrc is now on a different side of the level
			// that it used to be, and therefore know it crossed.  To implement this, we use
			// a hash table with a price/dir combo key.  If it exists, then it was close to
			// crossing.  If no longer valid, we remove the key.
			//
			// Yet I still see this seems to fail.  Either something is wrong with the
			// duplicate check, or.. something else is wrong.  I decided to shelve [the use of]
			// this in FiftyLevel for now and use a diff method.......

			bool didCross = false;

			// Create a unique key for this level+direction combination
			string key = $"{level}_{dir}";

			if (pPrc == 0)
				return false;

			// Standard crossing check
			bool standardCross = (dir == "Down") ? (pPrc >= level && cPrc < level) : (pPrc <= level && cPrc > level);

			// If standard cross detected, clear any "close" state and return true
			if (standardCross)
			{
				wasCloseToLevel.Remove(key);
				//Log($"\n\nStandard {dir} cross of level {level} detected");
				didCross = true;
			}
			else
			{
				// Define the "close zone"
				double closeZoneEdge = (dir == "Down") ? level + CLOSE_THRESHOLD : level - CLOSE_THRESHOLD;

				if (wasCloseToLevel.Contains(key))
				{
					// Did price move away from the 'close zone'?
					if ((cPrc > closeZoneEdge  &&  dir == "Down")  ||  (cPrc < closeZoneEdge  &&  dir == "Up"))
					{
						wasCloseToLevel.Remove(key);
						//Log($"\n\nPrice moved away from close zone for level {level} ({cPrc}), removing tracking");
					}

					// Check for enhanced cross detection
					else if ((cPrc < level  &&  dir == "Down")  ||  (cPrc > level  &&  dir == "Up"))
					{
						wasCloseToLevel.Remove(key);
						didCross = true;
						Log($"\n\nEnhanced {dir} cross of level {level} detected using wasClose");
					}
				}
				else
				{
					// Update "close" state based on price position
					if ((cPrc <= closeZoneEdge  &&  cPrc > level  &&  dir == "Down")
					||  (cPrc >= closeZoneEdge  &&  cPrc < level  &&  dir == "Up"))
					{
						wasCloseToLevel.Add(key);
						//Log($"\n\nPrice is in close zone for level {level} ({cPrc}), marking for potential {dir} cross");
					}
				}
			}

			// If enabled (true by default), do not report two crosses
			// in a row that have the same level and same direction
			if (skipDup  &&  didCross)
			{
				// Check if this is a consecutive cross (same direction, same level)
				if (prvDir == dir  &&  Math.Abs(prvLvl - level) < TickSize)
				{
					Log($"\nPrevented consecutive {dir} cross of level {level}");
					didCross = false;
				}
				else
				{
					prvLvl = level;
					prvDir = dir;
				}
			}
			return didCross;
		}

		
		private double GetNewTrailPrice(string direction)
		{
			// Return new trailing stoploss value, or zero if none/unchanged
			double oldSL = 0;
			double newSL = 0;
			if (Position.MarketPosition.ToString() != direction  ||  !trailTriggered)
				return newSL;

			// Working seems to not mean 'active', but that it's still in exchange queue. Accepted means it's active.
			if (slOrder2 != null  &&  (slOrder2.OrderState == OrderState.Working  ||  slOrder2.OrderState == OrderState.Accepted))
			{
				oldSL = slOrder2.StopPrice;
				//Log($"Old Stop Price = {oldSL}");
			}
			else if (slOrder2 == null)
			{
				Log($"ERROR: slOrder2 == null, so cannot get old SL value");
				return 0;
			}
			else
			{
				Log($"ERROR: slOrder2.OrderState = {slOrder2.OrderState}, so cannot get old SL value; perhaps on next tick....");
				return 0;
			}

			// This is called from tick BiP, not per candle
			double price = (direction == "Short") ? GetCurrentAsk() : GetCurrentBid();
			double ticksToOldSL = 0;
			if (oldSL != 0)
				ticksToOldSL = (direction == "Short") ? (oldSL - price) / TickSize : (price - oldSL) / TickSize;

			// ticksToOldSL is zero if there is no old SL
			int trailBy = 0;
			if (direction == "Long"  &&  ticksToOldSL > LngTrailTicks)
				trailBy = LngTrailTicks;
			else
			if (direction == "Short"  &&  ticksToOldSL > ShtTrailTicks)
				trailBy = ShtTrailTicks;

			if (trailBy > 0)
			{
				double M = (direction == "Short") ? -1.0 : 1.0;
				newSL = price - M*trailBy*TickSize;

				// Make sure its moving in the right direction
				if (direction == "Long"  &&  newSL <= oldSL)
					newSL = 0;
				else
				if (direction == "Short"  &&  newSL >= oldSL)
					newSL = 0;
			}
			//if (newSL != 0)	Log($"Simple trail on {direction} trade: OldSL = {oldSL}, NewSL = {newSL}; trailBy = {trailBy}");

			lastCaller = "";	// reset so if this method is entered again before other log, reprint the function name
			return newSL;
		}

		public void DrawDiamond(string tag, bool isAutoScale, int barsAgo, double y, Brush brush)
		{
			Draw.Diamond(this, tag + CurrentBar, isAutoScale, barsAgo, y, brush);
		}

		private string Bool2YN(bool b)
		{
			return (b) ? "Yes" : "No";
		}
		#endregion

		#region SESSION / FILTER  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
		/// <summary>
		/// Checks if all trade filters are satisfied for entering a trade
		/// </summary>
		/// <param name="dir">Direction of the trade ("Long" or "Short")</param>
		/// <returns>True if all filters pass, false otherwise</returns>
		public bool TradeFiltersOkay(string dir)
		{
			// Enter a trade if all enabled filters are satisfied
			bool passed = true;
			bool log = true;//(State == State.Realtime);
			string logStr = "";

			if (dir != "Long"  &&  dir != "Short")
			{
				logStr = $"Direction passed into TradeFiltersOkay ({dir}) must be 'Long' or 'Short'; Aborting";
				passed = false;
			}

			if (Position.Quantity != 0  ||  Position.MarketPosition != MarketPosition.Flat)
			{
				logStr = $"Cannot enter -already in position: MarketPosition = {Position.MarketPosition.ToString()}, Position.Quantity = {Position.Quantity}";
				passed = false;
			}

			if (!InTradeSession())
			{
				logStr = $"Out of session; Aborting {dir} entry";
				passed = false;
			}

			// Get volume average directly from the strategy
			double aveVol = VOLMA(Closes[0], AveVolumePeriod)[1];
			if (aveVol < MinAveVolume)
			{
				logStr = $"Volume insufficient ({aveVol} vs. {MinAveVolume} minimum); Aborting {dir} entry";
				DrawDiamond("VolFilter", false, 0, 0, Brushes.HotPink);
				passed = false;
			}

			if (dir == "Long")
			{
				if (!longOn)
				{
					logStr = $"Long trade disabled; Aborting entry";
					passed = false;
				}
				else if (Position.MarketPosition == MarketPosition.Long)
				{
					logStr = $"Long trade already open; Aborting entry";
					passed = false;
				}
			}
			else if (dir == "Short")
			{
				if (!shortOn)
				{
					logStr = $"Short trade disabled; Aborting entry";
					passed = false;
				}
				else if (Position.MarketPosition == MarketPosition.Short)
				{
					logStr = $"Short trade already open; Aborting entry";
					passed = false;
				}
			}

			if (log  &&  logStr != "") Log("TRADE FILTER FAILED: " + logStr);
			return passed;
		}

		/// <summary>
		/// Checks if trading is allowed based on current time and configured trading sessions
		/// </summary>
		/// <returns>True if trading is allowed, false otherwise</returns>
		public bool InTradeSession()
		{
			// Check trading sessions / days
			int activeSessionNum = 0;

			switch (Time[0].DayOfWeek)
			{
				case DayOfWeek.Sunday:		if (!TradeSunday)		return false;   else break;
				case DayOfWeek.Monday:		if (!TradeMonday)		return false;   else break;
				case DayOfWeek.Tuesday:		if (!TradeTuesday)		return false;   else break;
				case DayOfWeek.Wednesday:	if (!TradeWednesday)	return false;   else break;
				case DayOfWeek.Thursday:	if (!TradeThursday)		return false;   else break;
				case DayOfWeek.Friday:		if (!TradeFriday)		return false;   else break;
				default:															break;
			}

			if (!(UseSession1  ||  UseSession2  ||  UseSession3  ||  UseSession4  ||  UseSession5))
				return true;

			if (UseSession1)
				if (CheckSession(1, startTime1, endTime1))
					return true;
			if (UseSession2)
				if (CheckSession(2, startTime2, endTime2))
					return true;
			if (UseSession3)
				if (CheckSession(3, startTime3, endTime3))
					return true;
			if (UseSession4)
				if (CheckSession(4, startTime4, endTime4))
					return true;
			if (UseSession5)
				if (CheckSession(5, startTime5, endTime5))
					return true;

			return false;
		}

		/// <summary>
		/// Checks if the current time is within a specific trading session
		/// </summary>
		/// <param name="num">Session number (for logging purposes)</param>
		/// <param name="startTime">Start time of the session</param>
		/// <param name="endTime">End time of the session</param>
		/// <returns>True if current time is within the session, false otherwise</returns>
		private bool CheckSession(int num, TimeSpan startTime, TimeSpan endTime)
		{
			// Check one trading session; return false if out-of-session
			bool okay = false;
			TimeSpan currentTime = (completelyLoaded) ? Times[0][0].TimeOfDay : DateTime.Now.TimeOfDay;
			//TimeSpan currentTime = Times[0][0].TimeOfDay;

			if (startTime < endTime)
			{
				// Same day session (e.g., 9:00 AM to 4:00 PM)
				if (currentTime >= startTime  &&  currentTime < endTime)
					okay = true;
			}
			else if (startTime > endTime)
			{
				// Overnight session (e.g., 6:00 PM to 8:00 AM next day)
				if (currentTime >= startTime  ||  currentTime < endTime)
					okay = true;
			}
			else // (startTime == endTime)
			{
				Log($"Start Time ({startTime.ToString()}) is the same as End Time ({endTime.ToString()}); Trading (always) approved");
				okay = true;
			}
			return okay;
		}

		/// <summary>
		/// Checks if it's time to close positions based on session close times
		/// </summary>
		/// <param name="activeSessionNum">The currently active trading session number (1-5)</param>
		/// <returns>True if positions should be closed, false otherwise</returns>
		public bool IsCloseTime(int activeSessionNum)
		{
			// Check if it's time to close positions based on session close times
			if (Position.MarketPosition == MarketPosition.Flat)
				return false;

			// If we are not using any trading session (trade all times), then just check the main close time
			if (!(UseSession1  ||  UseSession2  ||  UseSession3  ||  UseSession4  ||  UseSession5))
			{
				// Close all trades at end of user-defined session
				if (UseCloseTime  &&  Time[0].TimeOfDay >= closeTime)
					return true;
				return false;
			}

			// Otherwise, check which session is active
			// Close all trades at end of user-defined session
			TimeSpan currentTime = Time[0].TimeOfDay;
			if (activeSessionNum == 1  &&  UseCloseTime1  &&  currentTime >= closeTime1)
				return true;
			if (activeSessionNum == 2  &&  UseCloseTime2  &&  currentTime >= closeTime2)
				return true;
			if (activeSessionNum == 3  &&  UseCloseTime3  &&  currentTime >= closeTime3)
				return true;
			if (activeSessionNum == 4  &&  UseCloseTime4  &&  currentTime >= closeTime4)
				return true;
			if (activeSessionNum == 5  &&  UseCloseTime5  &&  currentTime >= closeTime5)
				return true;
			return false;
		}
		#endregion
		
		#region ON-CHART DISPLAY & LOG - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
		private void ManageOCD(bool shortVersion=false)
		{
			string tmp, text = "";
			for (int i=0; i < StartingLine; i++)
				text += "\n";
			text += " Strategy Name :\t\t" + StrategyNameOCD + "    v" + VERSION;
			if (UniqueID != "")
				text += "    ID: " + UniqueID;

			if (shortVersion)
				inSession = InTradeSession();
			text += "\n Long Trade Enabled :\t" + Bool2YN(longOn);
			text += "\n Short Trade Enabled :\t" + Bool2YN(shortOn);
			text += "\n Trade Session Active :\t" + inSession + "\n";

			if (!shortVersion)
			{
				text += "\n Last Trade Direction :\t" + lastTradeDir;
				text += "\n Pending Direction :\t" + pendingDir;
				text += "\n Working Order :\t\t" + workingOrder;
				text += "\n Active Direction :\t\t" + activeDir;
				if (pendingDir != "None")
				{
					if (pendingPrice != 0)
						text += "\n Pending Price :\t\t" + pendingPrice;
					if (hookOn  &&  crossPrice != 0)
						text += "\n Cross Price :\t\t" + crossPrice;
				}
	
				text += "\n Hook On :\t\t" + Bool2YN(hookOn);
				if (UseATR  &&  (HookPointsATR > 0  ||  DisableOnMaxATR > 0))
				{
					if (HookPointsATR > 0  &&  DisableOnMaxATR > 0)
						text += "\n ATR Auto / Off :\t\t" + HookPointsATR + "/" + DisableOnMaxATR;
					else if (HookPointsATR > 0)
						text += "\n ATR Auto @ :\t\t" + HookPointsATR;
					else if (HookPointsATR > 0  &&  DisableOnMaxATR > 0)
						text += "\n ATR Trade Off @ :\t\t" + DisableOnMaxATR;
				}
	
				if (UseTrailingStop  &&  Position.MarketPosition != MarketPosition.Flat)
					text += "\n\n Trail Triggered :\t\t" + trailTriggered + "\n";
	
				if (DailyMinTarget != 0  ||  DailyMaxLoss != 0)
				{
					text += "\n";
					if (DailyMinTarget > 0)
						text += "\nDaily Min Profit :\t\t" + DailyMinTarget.ToString("C0");
					if (DailyMaxLoss > 0)
						text += "\nDaily Max Loss :\t\t" + DailyMaxLoss.ToString("C0");
					text += "\nDaily Profit :\t\t" + dailyProfit.ToString("C0");
					string pl = (dailyProfit > 0) ? "Profit" : "Loss";
					if (dailyLimitHit)
						text += "\nDaily " + pl + " Hit :\t\tYes - Trading Disabled";
					else
						text += "\nDaily Limit Hit :\t\tNo";
				}
	
				text += "\n\n High Price 100 :\t\t" + price100;
				text += "\n Mid Price 50 :\t\t"   + price50;
				text += "\n Low Price 100 :\t\t"  + price0;
	
				text += "\n\n " + MinutesATR + "-Minute ATR(" + PeriodATR + ") :\t\t"  + lastATR.ToString("F2");
			}
			if (lastDashboard == text)
				return;

			Draw.TextFixed(this, "OCD", text, NinjaTrader.NinjaScript.DrawingTools.TextPosition.TopLeft);
			lastDashboard = text;
		}

		private void Log(string message, [CallerMemberName] string memberName = "")
		{
			if (DisableLogging  ||  lastLogMsg == message)
				return;

			/// Debug time filtering...
			//if (Time[0] < dbg1  ||  Time[0] > dbg2)	return;

			/// Set this scripts Print() calls to the second output tab?
			if (UseOutput2)		PrintTo = PrintTo.OutputTab2;
			else				PrintTo = PrintTo.OutputTab1;

			string dateStr = "";
			string id = StrategyNameOCD;
			if (UniqueID != "")
				id += " " + UniqueID;

			if (lastLogMsg != message)
			{
				DateTime date = (State == State.Historical) ? Time[0] : DateTime.Now;
				dateStr = (State == State.Historical) ? date.ToString() : date.ToString("HH:mm:ss");

				string header = $"\n{id} - {chartInstrument} - [{memberName}]  -  {dateStr}  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -\n";

				/// Output just time if diff time but not new caller
				string output = (lastLogTime != dateStr) ? message + "   ( " + dateStr + " )" : message;

				/// If it is a new caller, write header line
				if (lastCaller != memberName)
					LogToFile(header);

				LogToFile(output);
			}

			lastLogMsg = message;
			lastCaller = memberName;
			lastLogTime = dateStr;
		}

		private void LogToFile(string message)
		{
			/// Always print to the output window as well
			Print(message);

			/// Additionally logging to file is skipped if there is no file name
			if (logPath == "")
				return;
			//Print($"                                                                         logPath = {logPath}         LogToFileName = {LogToFileName}");

			/// Ensure the directory exists
			try
			{
				string folder = System.IO.Path.GetDirectoryName(logPath);
				if (!System.IO.Directory.Exists(folder))
					System.IO.Directory.CreateDirectory(folder);
			}
			catch (Exception ex)
			{
				Print($"LogToFile() error creating folder: {ex.Message}");
				return;
			}

			/// Append to the log file
			try
			{
				System.IO.File.AppendAllText(logPath, "\n" + message);
			}
			catch (Exception ex)
			{
				Print($"LogToFile() error writing file: {ex.Message}");
			}

		}
		#endregion

		#region BUTTONS - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
		private void OnMyButtonClick(object sender, RoutedEventArgs rea)
		{
			System.Windows.Controls.Button button = sender as System.Windows.Controls.Button;

			Log($"\n");
			bool lWasOn = longOn;
			bool sWasOn = shortOn;
			if (button.Name == "longButton")
			{
				if (longOn)
				{
					button.Background = Brushes.Gray;
					button.Content = "Long Disabled";
					longOn = false;
					Log($"\nLONG ENTRIES DISABLED!");
				}
				else
				{
					button.Background = Brushes.Green;
					button.Content = "Long Enabled";
					longOn = true;
					Log($"\nLong entries Enabled!");
				}
			}
			else if (button.Name == "shortButton")
			{
				if (shortOn)
				{
					button.Background = Brushes.Gray;
					button.Content = "Short Disabled";
					shortOn = false;
					Log($"\nSHORT ENTRIES DISABLED!");
				}
				else
				{
					button.Background = Brushes.Crimson;
					button.Content = "Short Enabled";
					shortOn = true;
					Log($"\nShort entries Enabled!");
				}
			}
			else if (button.Name == "exitButton")
			{
				if (Position.MarketPosition == MarketPosition.Flat)
				{
					Log($"\nUser-Instigated Close of All Open Strategy Positions; nothing open...");
					if (entryOrder != null)
					{
						Log($"...but cancelling entryOrder");
						CancelEntryOrder();
						pendingDir = "None";
					}
				}
				else
				{
					Log($"\n\n=====================================================");
					Log($"User-Instigated Close of All Open Strategy Positions");
					Log($"=====================================================\n");
					ClosePosition();
				}
			}
			else if (button.Name == "hookButton")
			{
				string text = "";

				// If not using ATR at all, or just disabling
				// trade on high ATR, then there is no "Auto"
				if (!UseATR  ||  HookPointsATR == 0)
				{
					if (hookMode == HookType.HookOff)
					{
						hookMode = HookType.HookOn;
						text = "Hook On";
					}
					else if (hookMode == HookType.HookOn)
					{
						hookMode = HookType.HookOff;
						text = "Hook Off";
					}
				}
				else
				{
					if (hookMode == HookType.HookOff)
					{
						hookMode = HookType.HookAuto;
						text = "Hook Auto";
					}
					else if (hookMode == HookType.HookOn)
					{
						hookMode = HookType.HookOff;
						text = "Hook Off";
					}
					else if (hookMode == HookType.HookAuto)
					{
						hookMode = HookType.HookOn;
						text = "Hook On";
					}
				}
				hookButton.Content = text;
				Log($"\nSet Hook Mode to '{text}' ({hookMode.ToString()})");
			}

			if (!longOn  &&  !shortOn)
				if (lWasOn  ||  sWasOn)
					Log($"ALL ENTRIES DISABLED!");
			else if (longOn  &&  shortOn)
				if (!lWasOn  ||  !sWasOn)
					Log($"ALL ENTRIES ENABLED!");
			Log($"\n");
		}

		protected void CreateButtons()
		{
			if (UserControlCollection.Contains(myGrid))
				return;

			myGrid = new System.Windows.Controls.Grid
			{
				Name = "MyCustomGrid",
				HorizontalAlignment = HorizontalAlignment.Left,
				VerticalAlignment = VerticalAlignment.Bottom,
			};

			myGrid.ColumnDefinitions.Add(new System.Windows.Controls.ColumnDefinition());
			myGrid.ColumnDefinitions.Add(new System.Windows.Controls.ColumnDefinition());
			myGrid.ColumnDefinitions.Add(new System.Windows.Controls.ColumnDefinition());
			myGrid.ColumnDefinitions.Add(new System.Windows.Controls.ColumnDefinition());
			myGrid.ColumnDefinitions.Add(new System.Windows.Controls.ColumnDefinition());
			myGrid.ColumnDefinitions.Add(new System.Windows.Controls.ColumnDefinition());
			myGrid.ColumnDefinitions.Add(new System.Windows.Controls.ColumnDefinition());
			myGrid.RowDefinitions.Add(new System.Windows.Controls.RowDefinition());
			myGrid.RowDefinitions[0].Height	= new GridLength(20);
			myGrid.ColumnDefinitions[0].Width  = new GridLength(90);	// Long Enable
			myGrid.ColumnDefinitions[1].Width  = new GridLength(2);		// Blank spacer
			myGrid.ColumnDefinitions[2].Width  = new GridLength(90);	// Short Enable
			myGrid.ColumnDefinitions[3].Width  = new GridLength(6);		// Blank spacer
			myGrid.ColumnDefinitions[4].Width  = new GridLength(60);	// Exit All
			myGrid.ColumnDefinitions[5].Width  = new GridLength(6);		// Blank spacer
			myGrid.ColumnDefinitions[6].Width  = new GridLength(90);	// Hook Button

			longButton = new System.Windows.Controls.Button
			{
				Name = "longButton",
				Content = "Long Enabled",
				Foreground = Brushes.White,
				Background = Brushes.Green,
				Height = 25,
				Width = 90,
				FontSize = 12,
				HorizontalAlignment = HorizontalAlignment.Center,
				VerticalAlignment = VerticalAlignment.Center,
				Focusable = false
			};
			// Set initial state based on user input
			if (!longOn)
			{
				longButton.Content = "Long Disabled";
				longButton.Background = Brushes.Gray; // Change appearance to "pressed"
			}

			shortButton = new System.Windows.Controls.Button
			{
				Name = "shortButton",
				Content = "Short Enabled",
				Foreground = Brushes.White,
				Background = Brushes.Crimson,
				Height = 25,
				Width = 90,
				FontSize = 12,
				HorizontalAlignment = HorizontalAlignment.Center,
				VerticalAlignment = VerticalAlignment.Center,
				Focusable = false
			};
			// Set initial state based on user input
			if (!shortOn)
			{
				shortButton.Content = "Short Disabled";
				shortButton.Background = Brushes.Gray; // Change appearance to "pressed"
			}

			exitButton = new System.Windows.Controls.Button
			{
				Name = "exitButton",
				Content = "Exit All",
				Foreground = Brushes.White,
				Background = Brushes.DarkMagenta,
				Height = 25,
				Width = 60,
				FontSize = 12,
				HorizontalAlignment = HorizontalAlignment.Center,
				VerticalAlignment = VerticalAlignment.Center,
				Focusable = false
			};

			// completely disabled, do not show button
			var vis = Visibility.Visible;
			if (DisableHook)
				vis = Visibility.Hidden;

			// Init to correct state:
			string state = hookMode switch
			{
				HookType.HookAuto => "Hook Auto",
				HookType.HookOn => "Hook On",
				_ => "Hook Off"
			};

			hookButton = new System.Windows.Controls.Button
			{
				Name = "hookButton",
				Content = state,
				Foreground = Brushes.White,
				Background = Brushes.Maroon,
				Height = 25,
				Width = 90,
				FontSize = 12,
				HorizontalAlignment = HorizontalAlignment.Center,
				VerticalAlignment = VerticalAlignment.Center,
				Focusable = false,
				Visibility = vis
			};

			longButton.Click += OnMyButtonClick;
			shortButton.Click += OnMyButtonClick;
			exitButton.Click += OnMyButtonClick;
			hookButton.Click += OnMyButtonClick;

			System.Windows.Controls.Grid.SetColumn(longButton, 0);
			System.Windows.Controls.Grid.SetRow(longButton, 0);
			System.Windows.Controls.Grid.SetColumn(shortButton, 2);
			System.Windows.Controls.Grid.SetRow(shortButton, 0);
			System.Windows.Controls.Grid.SetColumn(exitButton, 4);
			System.Windows.Controls.Grid.SetRow(exitButton, 0);
			System.Windows.Controls.Grid.SetColumn(hookButton, 6);
			System.Windows.Controls.Grid.SetRow(hookButton, 0);

			myGrid.Children.Add(longButton);
			myGrid.Children.Add(shortButton);
			myGrid.Children.Add(exitButton);
			myGrid.Children.Add(hookButton);

			UserControlCollection.Add(myGrid);
		}

		public void DisposeButtons()
		{
			if (myGrid != null)
			{
				if (longButton != null)
				{
					myGrid.Children.Remove(longButton);
					longButton.Click -= OnMyButtonClick;
					longButton = null;
				}
				if (shortButton != null)
				{
					myGrid.Children.Remove(shortButton);
					shortButton.Click -= OnMyButtonClick;
					shortButton = null;
				}
				if (exitButton != null)
				{
					myGrid.Children.Remove(exitButton);
					exitButton.Click -= OnMyButtonClick;
					exitButton = null;
				}
				if (hookButton != null)
				{
					myGrid.Children.Remove(hookButton);
					hookButton.Click -= OnMyButtonClick;
					hookButton = null;
				}
			}
		}

		private void UpdateTradeButtons(bool longOn, bool shortOn)
		{
			string stateL = (longOn)  ? " Enabled" : " Disabled";
			string stateS = (shortOn) ? " Enabled" : " Disabled";
			Brush bgLong  = (longOn)  ? Brushes.Green : Brushes.Gray;
			Brush bgShort = (shortOn) ? Brushes.Crimson : Brushes.Gray;

			if (Dispatcher.CheckAccess())
			{
				longButton.Content = "Long" + stateL;
				longButton.Background = bgLong;
				shortButton.Content = "Short" + stateS;
				shortButton.Background = bgShort;
			}
			else
			{
				Dispatcher.Invoke(() => {
					longButton.Content = "Long" + stateL;
					longButton.Background = bgLong;
				});
				Dispatcher.Invoke(() => {
					shortButton.Content = "Short" + stateS;
					shortButton.Background = bgShort;
				});
			}
		}

		#endregion

		#region PROPERTIES - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

		#region TimeZone    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙
		[NinjaScriptProperty]
		[Range(-23, 23)]
		[Display(Name = "Timezone Offset", Description = "Number of hours to add the session times", Order=0, GroupName = "00. TimeZone")]
		public int TimeZoneOffset
		{ get; set; }
		#endregion

		#region Entry  ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙
		[NinjaScriptProperty]
		[Display(Name="Trade Longs", Description = "Trade long direction", Order=3, GroupName = "01. Entry")]
		public bool TradeLong
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name="Trade Short", Description = "Trade short direction", Order=4, GroupName = "01. Entry")]
		public bool TradeShort
		{ get; set; }

		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name="Long Order Quantity", Description = "The initial number of contracts to place per long order", Order=7, GroupName = "01. Entry")]
		public int LngQuantity
		{ get; set; }

		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name="Short Order Quantity", Description = "The initial number of contracts to place per short order", Order=8, GroupName = "01. Entry")]
		public int ShtQuantity
		{ get; set; }

		[NinjaScriptProperty]
		[Range(0, 36)]
		[Display(Name="Entry Level 1", Description = "The price where limit order is set (16-36)", Order=11, GroupName = "01. Entry")]
		public int EntryLevel1
		{ get; set; }

		[NinjaScriptProperty]
		[Range(0, 87)]
		[Display(Name="Entry Level 2", Description = "The price where limit order is set (67-87)", Order=12, GroupName = "01. Entry")]
		public int EntryLevel2
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name="Disable Hook", Description = "Check to prevent from ever using Hook", Order=14, GroupName = "01. Entry")]
		public bool DisableHook
		{ get; set; }

		[NinjaScriptProperty]
		[Range(0, 60)]
		[Display(Name="Hook Gap Ticks Long Level1", Description = "Number of Ticks price must cross Entry Level for Long 'Hook' Entry off Level1", Order=16, GroupName = "01. Entry")]
		public int HookGapL1
		{ get; set; }

		[NinjaScriptProperty]
		[Range(0, 60)]
		[Display(Name="Hook Gap Ticks Long Level2", Description = "Number of Ticks price must cross Entry Level for Long 'Hook' Entry off Level2", Order=18, GroupName = "01. Entry")]
		public int HookGapL2
		{ get; set; }

		[NinjaScriptProperty]
		[Range(0, 60)]
		[Display(Name="Hook Gap Ticks Short Level1", Description = "Number of Ticks price must cross Entry Level for Short 'Hook' Entry off Level1", Order=21, GroupName = "01. Entry")]
		public int HookGapS1
		{ get; set; }

		[NinjaScriptProperty]
		[Range(0, 60)]
		[Display(Name="Hook Gap Ticks Short Level2", Description = "Number of Ticks price must cross Entry Level for Short 'Hook' Entry off Level2", Order=23, GroupName = "01. Entry")]
		public int HookGapS2
		{ get; set; }

		[NinjaScriptProperty]
		[Range(0, 80)]
		[Display(Name="Cancellation Points", Description = "Number of Points price must go beyond Hook trigger to cancel pending stop order.  Zero to disable", Order=26, GroupName = "01. Entry")]
		public int CancelPoints
		{ get; set; }
		#endregion
		#region Long Exits  ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙
		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name="Long Stop Loss", Description = "Initial stop loss in ticks.  Zero to disable", Order=0, GroupName = "02a. Long Exits")]
		public int LngStopLoss
		{ get; set; }

		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name="Long Take Profit 1", Description = "Take profit #1 in ticks.  Zero to disable", Order=3, GroupName = "02a. Long Exits")]
		public int LngTakeProfit1
		{ get; set; }

		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name="Long Exit Qty 1", Description = "Number of contracts to exit on 1st TP target", Order=6, GroupName = "02a. Long Exits")]
		public int LngQuantityTP1
		{ get; set; }

		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name="Long Take Profit 2", Description = "Take profit #2 in ticks.  Zero to disable", Order=9, GroupName = "02a. Long Exits")]
		public int LngTakeProfit2
		{ get; set; }
		#endregion
		#region Short Exits ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙
		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name="Short Stop Loss", Description = "Initial stop loss in ticks.  Zero to disable", Order=0, GroupName = "02b. Short Exits")]
		public int ShtStopLoss
		{ get; set; }

		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name="Short Take Profit 1", Description = "Take profit #1 in ticks.  Zero to disable", Order=3, GroupName = "02b. Short Exits")]
		public int ShtTakeProfit1
		{ get; set; }

		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name="Short Exit Qty 1", Description = "Number of contracts to exit on 1st TP target", Order=6, GroupName = "02b. Short Exits")]
		public int ShtQuantityTP1
		{ get; set; }

		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name="Short Take Profit 2", Description = "Take profit #2 in ticks.  Zero to disable", Order=9, GroupName = "02b. Short Exits")]
		public int ShtTakeProfit2
		{ get; set; }
		#endregion
		#region BreakEven   ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙
		[NinjaScriptProperty]
		[Display(Name = "Use Break Even", Description = "When first TP is triggerd, move remaining stop to Break Even", Order=0, GroupName = "03. Break Even")]
		public bool UseBreakeven
		{ get; set; }

		[NinjaScriptProperty]
		[Range(int.MinValue, int.MaxValue)]
		[Display(Name = "Win Activation Ticks", Description = "Ticks in profit to activate Breakeven", Order=1, GroupName = "03. Break Even")]
		public int BEW_ActivTicks
		{ get; set; }

		[NinjaScriptProperty]
		[Range(int.MinValue, int.MaxValue)]
		[Display(Name="Win BreakEven Offset", Description = "Win Break Even offset from entry price", Order=3, GroupName = "03. Break Even")]
		public int BEW_Offset
		{ get; set; }

		[NinjaScriptProperty]
		[Range(int.MinValue, int.MaxValue)]
		[Display(Name = "Lose Activation Ticks", Description = "Ticks in loss to activate Breakeven", Order=7, GroupName = "03. Break Even")]
		public int BEL_ActivTicks
		{ get; set; }

		[NinjaScriptProperty]
		[Range(int.MinValue, int.MaxValue)]
		[Display(Name="Lose BreakEven Offset", Description = "Lose Break Even offset from entry price", Order=9, GroupName = "03. Break Even")]
		public int BEL_Offset
		{ get; set; }
		#endregion
		#region Trailing Stop    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙
		[NinjaScriptProperty]
		[Display(Name = "Use Trailing Stop", Description = "", Order=0, GroupName = "04. Trailing Stop")]
		public bool UseTrailingStop
		{ get; set; }

		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name = "Long Activation Ticks", Description = "For Long Trade, ticks in profit to activate trail", Order=3, GroupName = "04a. Long Trailing Stop")]
		public int LngActivationTicks
		{ get; set; }

		[NinjaScriptProperty]
		[Range(int.MinValue, int.MaxValue)]
		[Display(Name = "Long Trail Ticks", Description = "Number of ticks to trail by for a Long Trade", Order=6, GroupName = "04a. Long Trailing Stop")]
		public int LngTrailTicks
		{ get; set; }

		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name = "Short Activation Ticks", Description = "For Short Trade, ticks in profit to activate trail", Order=9, GroupName = "04b. Short Trailing Stop")]
		public int ShtActivationTicks
		{ get; set; }

		[NinjaScriptProperty]
		[Range(int.MinValue, int.MaxValue)]
		[Display(Name = "Short Trail Ticks", Description = "Number of ticks to trail by for a Short Trade", Order=12, GroupName = "04b. Short Trailing Stop")]
		public int ShtTrailTicks
		{ get; set; }
		#endregion

		#region Daily Targets    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙
		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name = "Daily Target Min $", Description = "Profit amount at which to stop trading for the day", Order=0, GroupName = "05. Daily Targets")]
		public int DailyMinTarget
		{ get; set; }

		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name = "Daily Loss Max $", Description = "Loss amount at which to stop trading for the day", Order=1, GroupName = "05. Daily Targets")]
		public int DailyMaxLoss
		{ get; set; }
		#endregion

		#region ATR Filter  ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙
		[NinjaScriptProperty]
		[Display(Name = "Use ATR Filter", Description = "Prevent trading when ATR is too large", Order=0, GroupName = "06. ATR Filter")]
		public bool UseATR
		{ get; set; }

		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name = "Minutes Per Bar", Description = "Number of Minutes to use for ATR data series", Order=3, GroupName = "06. ATR Filter")]
		public int MinutesATR
		{ get; set; }

		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name = "ATR Period", Description = "Period parameter to use for Average True Range", Order=6, GroupName = "06. ATR Filter")]
		public int PeriodATR
		{ get; set; }

		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name = "Enable Hook Points", Description = "Minimum ATR Points to enable Hook.  Zero to disable", Order=9, GroupName = "06. ATR Filter")]
		public int HookPointsATR
		{ get; set; }

		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name="Disable Trade Points", Description = "Maximum ATR Points before all trading disabled.  Zero to disable", Order=12, GroupName = "06. ATR Filter")]
		public int DisableOnMaxATR
		{ get; set; }
		#endregion
		#region Minimum Volume   ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙
		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name = "Minimum Average Volume", Description = "Minimum Average Volume to place trade", Order=0, GroupName = "07. Minimum Volume")]
		public int MinAveVolume
		{ get; set; }

		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name = "Average Volume Period", Description = "Period parameter to use for Average Volume", Order=1, GroupName = "07. Minimum Volume")]
		public int AveVolumePeriod
		{ get; set; }
		#endregion

		#region Trading Days     ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙
		[NinjaScriptProperty]
		[Display(Name = "Trade Sunday", Description = "Toggle Sunday Trading", Order=0, GroupName = "08. Trading Days")]
		public bool TradeSunday
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name = "Trade Monday", Description = "Toggle Monday Trading", Order=1, GroupName = "08. Trading Days")]
		public bool TradeMonday
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name = "Trade Tuesday", Description = "Toggle Tuesday Trading", Order=2, GroupName = "08. Trading Days")]
		public bool TradeTuesday
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name = "Trade Wednesday", Description = "Toggle Wednesday Trading", Order=3, GroupName = "08. Trading Days")]
		public bool TradeWednesday
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name = "Trade Thursday", Description = "Toggle Thursday Trading", Order=4, GroupName = "08. Trading Days")]
		public bool TradeThursday
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name = "Trade Friday", Description = "Toggle Friday Trading", Order=5, GroupName = "08. Trading Days")]
		public bool TradeFriday
		{ get; set; }
		#endregion
		#region Session 1 Hours  ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙
		[NinjaScriptProperty]
		[Display(Name = "Use Session 1", Description = "Use Trading Session #1", Order=0, GroupName = "09. Session 1 Hours")]
		public bool UseSession1
		{ get; set; }

		[NinjaScriptProperty]
		[PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
		[Display(Name = "Start Time", Description = "Enter Start Time for Session 1", Order=1, GroupName = "09. Session 1 Hours")]
		public DateTime StartTime1
		{ get; set; }

		[NinjaScriptProperty]
		[PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
		[Display(Name = "End Time", Description = "Enter End Time for Session 1", Order=2, GroupName = "09. Session 1 Hours")]
		public DateTime EndTime1
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name = "Use Close Time", Description = "Enable to close all Session 1 trades at given time", Order=4, GroupName = "09. Session 1 Hours")]
		public bool UseCloseTime1
		{ get; set; }

		[NinjaScriptProperty]
		[PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
		[Display(Name = "Close Time", Description = "Enter Time to Close Session 1 Trades", Order=5, GroupName = "09. Session 1 Hours")]
		public DateTime CloseTime1
		{ get; set; }
		#endregion
		#region Session 2 Hours  ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙
		[NinjaScriptProperty]
		[Display(Name = "Use Session 2", Description = "Use Trading Session #2", Order=0, GroupName = "10. Session 2 Hours")]
		public bool UseSession2
		{ get; set; }

		[NinjaScriptProperty]
		[PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
		[Display(Name = "Start Time", Description = "Enter Start Time for Session 2", Order=1, GroupName = "10. Session 2 Hours")]
		public DateTime StartTime2
		{ get; set; }

		[NinjaScriptProperty]
		[PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
		[Display(Name = "End Time", Description = "Enter End Time for Session 2", Order=2, GroupName = "10. Session 2 Hours")]
		public DateTime EndTime2
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name = "Use Close Time", Description = "Enable to close all Session 2 trades at given time", Order=4, GroupName = "10. Session 2 Hours")]
		public bool UseCloseTime2
		{ get; set; }

		[NinjaScriptProperty]
		[PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
		[Display(Name = "Close Time", Description = "Enter Time to Close Session 2 Trades", Order=5, GroupName = "10. Session 2 Hours")]
		public DateTime CloseTime2
		{ get; set; }
		#endregion
		#region Session 3 Hours  ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙
		[NinjaScriptProperty]
		[Display(Name = "Use Session 3", Description = "Use Trading Session #3", Order=0, GroupName = "11. Session 3 Hours")]
		public bool UseSession3
		{ get; set; }

		[NinjaScriptProperty]
		[PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
		[Display(Name = "Start Time", Description = "Enter Start Time for Session 3", Order=1, GroupName = "11. Session 3 Hours")]
		public DateTime StartTime3
		{ get; set; }

		[NinjaScriptProperty]
		[PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
		[Display(Name = "End Time", Description = "Enter End Time for Session 3", Order=2, GroupName = "11. Session 3 Hours")]
		public DateTime EndTime3
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name = "Use Close Time", Description = "Enable to close all Session 3 trades at given time", Order=4, GroupName = "11. Session 3 Hours")]
		public bool UseCloseTime3
		{ get; set; }

		[NinjaScriptProperty]
		[PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
		[Display(Name = "Close Time", Description = "Enter Time to Close Session 3 Trades", Order=5, GroupName = "11. Session 3 Hours")]
		public DateTime CloseTime3
		{ get; set; }
		#endregion
		#region Session 4 Hours  ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙
		[NinjaScriptProperty]
		[Display(Name = "Use Session 4", Description = "Use Trading Session #4", Order=0, GroupName = "12. Session 4 Hours")]
		public bool UseSession4
		{ get; set; }

		[NinjaScriptProperty]
		[PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
		[Display(Name = "Start Time", Description = "Enter Start Time for Session 4", Order=1, GroupName = "12. Session 4 Hours")]
		public DateTime StartTime4
		{ get; set; }

		[NinjaScriptProperty]
		[PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
		[Display(Name = "End Time", Description = "Enter End Time for Session 4", Order=2, GroupName = "12. Session 4 Hours")]
		public DateTime EndTime4
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name = "Use Close Time", Description = "Enable to close all Session 4 trades at given time", Order=4, GroupName = "12. Session 4 Hours")]
		public bool UseCloseTime4
		{ get; set; }

		[NinjaScriptProperty]
		[PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
		[Display(Name = "Close Time", Description = "Enter Time to Close Session 4 Trades", Order=5, GroupName = "12. Session 4 Hours")]
		public DateTime CloseTime4
		{ get; set; }
		#endregion
		#region Session 5 Hours  ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙
		[NinjaScriptProperty]
		[Display(Name = "Use Session 5", Description = "Use Trading Session #5", Order=0, GroupName = "13. Session 5 Hours")]
		public bool UseSession5
		{ get; set; }

		[NinjaScriptProperty]
		[PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
		[Display(Name = "Start Time", Description = "Enter Start Time for Session 5", Order=1, GroupName = "13. Session 5 Hours")]
		public DateTime StartTime5
		{ get; set; }

		[NinjaScriptProperty]
		[PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
		[Display(Name = "End Time", Description = "Enter End Time for Session 5", Order=2, GroupName = "13. Session 5 Hours")]
		public DateTime EndTime5
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name = "Use Close Time", Description = "Enable to close all Session 5 trades at given time", Order=4, GroupName = "13. Session 5 Hours")]
		public bool UseCloseTime5
		{ get; set; }

		[NinjaScriptProperty]
		[PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
		[Display(Name = "Close Time", Description = "Enter Time to Close Session 5 Trades", Order=5, GroupName = "13. Session 5 Hours")]
		public DateTime CloseTime5
		{ get; set; }
		#endregion
		#region No-Session Close ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙
		[NinjaScriptProperty]
		[Display(Name = "Use Close Time", Description = "Used only if sessions are disabled", Order=0, GroupName = "14. No-Session Close")]
		public bool UseCloseTime
		{ get; set; }

		[NinjaScriptProperty]
		[PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
		[Display(Name = "Close Time", Description = "Enter Time to Close All Open Trades", Order=1, GroupName = "14. No-Session Close")]
		public DateTime CloseTime
		{ get; set; }
		#endregion

		#region Misc / Debug    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙
		[NinjaScriptProperty]
		[Display(Name = "Display OCD", Description = "Show the On-Chart Display", Order=0, GroupName = "15. Misc / Debug")]
		public bool DisplayOCD
		{ get; set; }

		[NinjaScriptProperty]
		[Range(0, 50)]
		[Display(Name = "Starting Line", Description = "How many lines down to space the OCD", Order=3, GroupName = "15. Misc / Debug")]
		public int StartingLine
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name = "Plot Historical", Description = "Plot historical trades from DB", Order=6, GroupName = "15. Misc / Debug")]
		public bool PlotHist
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name = "Daily Targets on Historical", Description = "Apply Daily Profit / Loss settings to historical trades?", Order=9, GroupName = "15. Misc / Debug")]
		public bool ApplyDailyToHist
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name = "Strategy Name", Description = "Name of Strategy", Order=12, GroupName = "15. Misc / Debug")]
		public string StrategyNameOCD
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name = "Swap Bid & Ask on Hook", Description = "For Hook-enabled, use Ask for sells and Bid for buys", Order=15, GroupName = "15. Misc / Debug")]
		public bool SwapPrices
		{ get; set; }

		[NinjaScriptProperty]
		[Range(1, 10)]
		[Display(Name = "Buffer Points", Description = "How many Points on each side of Levels to actually pace pending order (Non-Hook)", Order=18, GroupName = "15. Misc / Debug")]
		public int BP
		{ get; set; }

		[NinjaScriptProperty]
		[Range(-100, 100)]
		[Display(Name = "Label Offset Divisor", Description = "Line labels are offset by dividing ATR by this number.  Negative for below line; positive for above; zero for on-line", Order=21, GroupName = "15. Misc / Debug")]
		public int LabelMod
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name = "Draw Horz Lines", Description = "Draw the 50, 100, and 26 lines", Order=24, GroupName = "15. Misc / Debug")]
		public bool DrawLines
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name = "Disable Logging", Description = "Disable logging to Ninjascript Output windows", Order=27, GroupName = "15. Misc / Debug")]
		public bool DisableLogging
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name = "Use Output 2", Description = "Send logging to second Output window", Order=30, GroupName = "15. Misc / Debug")]
		public bool UseOutput2
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name = "Unique ID (v" + VERSION + ")", Description = "Unique number to identify strategy in Output window", Order=33, GroupName = "15. Misc / Debug")]
		public string UniqueID
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name = "Log to File", Description = "Log to file as well as NinjaScript Output window (name of log file -leave blank for same name as strategy", Order=36, GroupName = "15. Misc / Debug")]
		public string LogToFileName
		{ get; set; }
//		{
//			get { return logToFileName ?? ""; }		// <--- safe fallback
//			set { logToFileName = value; }
//		}
		#endregion

		#endregion
	}
}

