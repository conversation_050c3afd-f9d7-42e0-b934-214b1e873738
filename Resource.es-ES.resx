﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Acceleration" xml:space="preserve">
    <value>Aceleración</value>
  </data>
  <data name="AccelerationMax" xml:space="preserve">
    <value>Aceleración máxima</value>
  </data>
  <data name="AccelerationStep" xml:space="preserve">
    <value>Paso de aceleración</value>
  </data>
  <data name="ADLAD" xml:space="preserve">
    <value>AD</value>
  </data>
  <data name="AlertOnBreak" xml:space="preserve">
    <value>Alerta en la pausa</value>
  </data>
  <data name="AlertOnBreakSound" xml:space="preserve">
    <value>Alerta sobre el sonido de pausa</value>
  </data>
  <data name="AndrewsPitchforkCalculationMethod_ModifiedSchiff" xml:space="preserve">
    <value>Schiff modificado</value>
  </data>
  <data name="AndrewsPitchforkCalculationMethod_Schiff" xml:space="preserve">
    <value>Schiff</value>
  </data>
  <data name="AndrewsPitchforkCalculationMethod_StandardPitchfork" xml:space="preserve">
    <value>Estándar</value>
  </data>
  <data name="AskLineLength" xml:space="preserve">
    <value>Longitud de la línea Ask (% del gráfico)</value>
  </data>
  <data name="AskLineStroke" xml:space="preserve">
    <value>Línea Ask</value>
  </data>
  <data name="AuthDisclosureText1" xml:space="preserve">
    <value>Derechos de autor &lt;sup&gt;©&lt;/sup&gt; {0}. Todos los derechos reservados. NinjaTrader y el logotipo de NinjaTrader. Reg. U.S. Pat. amp; Tm. Apagado.</value>
  </data>
  <data name="AuthDisclosureText2" xml:space="preserve">
    <value>DIVULGACIÓN COMPLETA DEL RIESGO: El comercio de futuros y divisas contiene un riesgo sustancial y no es para todos los inversores. Un inversor podría perder potencialmente todo o más que la inversión inicial. El capital de riesgo es dinero que se puede perder sin poner en peligro la seguridad financiera o el estilo de vida. Solo se debe utilizar capital de riesgo para la negociación y solo aquellos con suficiente capital de riesgo deben considerar la negociación. El rendimiento pasado no es necesariamente indicativo de resultados futuros.</value>
  </data>
  <data name="BandPct" xml:space="preserve">
    <value>Banda de porcentaje</value>
  </data>
  <data name="BarCount" xml:space="preserve">
    <value>Barra de Conteo</value>
  </data>
  <data name="BarDown" xml:space="preserve">
    <value>Barra Abajo</value>
  </data>
  <data name="BarSpacing" xml:space="preserve">
    <value>Bar spacing</value>
  </data>
  <data name="BarsPeriodType" xml:space="preserve">
    <value>Tipos de periodo de barras</value>
  </data>
  <data name="BarsPeriodTypeNameDay" xml:space="preserve">
    <value>Día</value>
  </data>
  <data name="BarsPeriodTypeNameHeikenAshi" xml:space="preserve">
    <value>Heiken-Ashi</value>
  </data>
  <data name="BarsPeriodTypeNameKagi" xml:space="preserve">
    <value>Kagi</value>
  </data>
  <data name="BarsPeriodTypeNameLineBreak" xml:space="preserve">
    <value>Linea de pausa</value>
  </data>
  <data name="BarsPeriodTypeNameMinute" xml:space="preserve">
    <value>Minuto</value>
  </data>
  <data name="BarsPeriodTypeNameMonth" xml:space="preserve">
    <value>Mes</value>
  </data>
  <data name="BarsPeriodTypeNamePointAndFigure" xml:space="preserve">
    <value>Punto y Figura</value>
  </data>
  <data name="BarsPeriodTypeNameRange" xml:space="preserve">
    <value>Rango</value>
  </data>
  <data name="BarsPeriodTypeNameRenko" xml:space="preserve">
    <value>Renko</value>
  </data>
  <data name="BarsPeriodTypeNameSecond" xml:space="preserve">
    <value>Segundo</value>
  </data>
  <data name="BarsPeriodTypeNameTick" xml:space="preserve">
    <value>Marca</value>
  </data>
  <data name="BarsPeriodTypeNameVolume" xml:space="preserve">
    <value>Volumen</value>
  </data>
  <data name="BarsPeriodTypeNameWeek" xml:space="preserve">
    <value>Semana</value>
  </data>
  <data name="BarsPeriodTypeNameYear" xml:space="preserve">
    <value>Año</value>
  </data>
  <data name="BarsPeriodValue" xml:space="preserve">
    <value>Valor de periodo de barras</value>
  </data>
  <data name="BarTimerDisconnectedError" xml:space="preserve">
    <value>Barra temporizadora desactivada por estar desconectado del proveedor de datos.</value>
  </data>
  <data name="BarTimerSessionTimeError" xml:space="preserve">
    <value>Barra temporizadora desactivada desde el tiempo actual de la sesión está fuera de tiempo o el gráfico caduco</value>
  </data>
  <data name="BarTimerTimeBasedError" xml:space="preserve">
    <value>Barra temporizadora sólo trabaja en tiempo intradía basado en intervalos</value>
  </data>
  <data name="BarTimerTimeRemaining" xml:space="preserve">
    <value>Tiempo restante = </value>
  </data>
  <data name="BarTimerWaitingOnDataError" xml:space="preserve">
    <value>Barra temporizadora esperando datos en tiempo real antes de empezar</value>
  </data>
  <data name="BarUp" xml:space="preserve">
    <value>Barra arriba</value>
  </data>
  <data name="BasePeriod" xml:space="preserve">
    <value>Base de periodo</value>
  </data>
  <data name="BidLineLength" xml:space="preserve">
    <value>Longitud de línea de oferta (% del gráfico)</value>
  </data>
  <data name="BidLineStroke" xml:space="preserve">
    <value>Línea de oferta</value>
  </data>
  <data name="BlockTradeSize" xml:space="preserve">
    <value>Tamaño del bloque de trading</value>
  </data>
  <data name="BollingerLowerBand" xml:space="preserve">
    <value>Banda baja</value>
  </data>
  <data name="BollingerMiddleBand" xml:space="preserve">
    <value>Banda media</value>
  </data>
  <data name="BollingerUpperBand" xml:space="preserve">
    <value>Banda alta</value>
  </data>
  <data name="BuySellPressureBuyPressure" xml:space="preserve">
    <value>Presion de comprar</value>
  </data>
  <data name="BuySellPressureSellPressure" xml:space="preserve">
    <value>Presion de vender</value>
  </data>
  <data name="BuySellVolumeBuys" xml:space="preserve">
    <value>Compras</value>
  </data>
  <data name="BuySellVolumeSells" xml:space="preserve">
    <value>Ventas</value>
  </data>
  <data name="CandlestickPatternFound" xml:space="preserve">
    <value>Patrón encontrado</value>
  </data>
  <data name="CCILevel1" xml:space="preserve">
    <value>Nivel 1</value>
  </data>
  <data name="CCILevel2" xml:space="preserve">
    <value>Nivel 2</value>
  </data>
  <data name="CCILevelMinus1" xml:space="preserve">
    <value>Nivel -1</value>
  </data>
  <data name="CCILevelMinus2" xml:space="preserve">
    <value>Nivel -2</value>
  </data>
  <data name="ChartSpan_Day" xml:space="preserve">
    <value>Día 1</value>
  </data>
  <data name="ChartSpan_Min1" xml:space="preserve">
    <value>1 min</value>
  </data>
  <data name="ChartSpan_Min15" xml:space="preserve">
    <value>15 min</value>
  </data>
  <data name="ChartSpan_Min240" xml:space="preserve">
    <value>240 min</value>
  </data>
  <data name="ChartSpan_Min30" xml:space="preserve">
    <value>30 min</value>
  </data>
  <data name="ChartSpan_Min5" xml:space="preserve">
    <value>5 min</value>
  </data>
  <data name="ChartSpan_Min60" xml:space="preserve">
    <value>60 min</value>
  </data>
  <data name="ChartSpan_Month" xml:space="preserve">
    <value>Mes 1</value>
  </data>
  <data name="ChartSpan_Week" xml:space="preserve">
    <value>Semana 1</value>
  </data>
  <data name="ChartSpan_Year" xml:space="preserve">
    <value>Año 1</value>
  </data>
  <data name="ConstantLines1" xml:space="preserve">
    <value>Línea 1</value>
  </data>
  <data name="ConstantLines2" xml:space="preserve">
    <value>Línea 2</value>
  </data>
  <data name="ConstantLines3" xml:space="preserve">
    <value>Línea 3</value>
  </data>
  <data name="ConstantLines4" xml:space="preserve">
    <value>Línea 4</value>
  </data>
  <data name="COT1" xml:space="preserve">
    <value>COT 1</value>
  </data>
  <data name="COT2" xml:space="preserve">
    <value>COT 2</value>
  </data>
  <data name="COT3" xml:space="preserve">
    <value>COT 3</value>
  </data>
  <data name="COT4" xml:space="preserve">
    <value>COT 4</value>
  </data>
  <data name="COT5" xml:space="preserve">
    <value>COT 5</value>
  </data>
  <data name="CotDataError" xml:space="preserve">
    <value>Los datos DE COT no son compatibles con este instrumento</value>
  </data>
  <data name="CotDataStillDownloading" xml:space="preserve">
    <value>Los datos de COT todavía se están descargando. Por favor, actualice el indicador en unos momentos.</value>
  </data>
  <data name="CotDataWarning" xml:space="preserve">
    <value>debe habilitar "Descargar datos de COT al inicio" para recibir los datos COT más recientes</value>
  </data>
  <data name="CountDown" xml:space="preserve">
    <value>Cuenta regresiva</value>
  </data>
  <data name="CountType_Trades" xml:space="preserve">
    <value>Trades</value>
  </data>
  <data name="CountType_Volume" xml:space="preserve">
    <value>Volumen</value>
  </data>
  <data name="CurrentDayOHLError" xml:space="preserve">
    <value>CurrentDayOHL sólo trabaja en intervalos intradía</value>
  </data>
  <data name="CurrentDayOHLHigh" xml:space="preserve">
    <value>Alta actual</value>
  </data>
  <data name="CurrentDayOHLLow" xml:space="preserve">
    <value>Baja actual</value>
  </data>
  <data name="CurrentDayOHLOpen" xml:space="preserve">
    <value>Apertura actual</value>
  </data>
  <data name="CustomWindowAddOnBuyMarket" xml:space="preserve">
    <value>Mercado compra</value>
  </data>
  <data name="CustomWindowAddOnSellMarket" xml:space="preserve">
    <value>Mercado vender</value>
  </data>
  <data name="CustomWindowSampleDescription" xml:space="preserve">
    <value>Descripción de ventana personalizada</value>
  </data>
  <data name="CustomWindowSampleName" xml:space="preserve">
    <value>Muestra de ventana personalizada</value>
  </data>
  <data name="DataBarsTypeDaily" xml:space="preserve">
    <value>Diario</value>
  </data>
  <data name="DataBarsTypeDay" xml:space="preserve">
    <value>{0} Día</value>
  </data>
  <data name="DataBarsTypeMinute" xml:space="preserve">
    <value>{0} Minuto{1}</value>
  </data>
  <data name="DataBarsTypeMonth" xml:space="preserve">
    <value>{0} Mes</value>
  </data>
  <data name="DataBarsTypeMonthly" xml:space="preserve">
    <value>Mensual</value>
  </data>
  <data name="DataBarsTypePointAndFigure" xml:space="preserve">
    <value>{0} punto y cifra</value>
  </data>
  <data name="DataBarsTypeRange" xml:space="preserve">
    <value>{0} Rango{1}</value>
  </data>
  <data name="DataBarsTypeRenko" xml:space="preserve">
    <value>{0} Renko</value>
  </data>
  <data name="DataBarsTypeSecond" xml:space="preserve">
    <value>{0} Segundo</value>
  </data>
  <data name="DataBarsTypeTick" xml:space="preserve">
    <value>{0} Marca{1}</value>
  </data>
  <data name="DataBarsTypeVolume" xml:space="preserve">
    <value>{0} Volumen {1}</value>
  </data>
  <data name="DataBarsTypeWeek" xml:space="preserve">
    <value>{0} Semana</value>
  </data>
  <data name="DataBarsTypeWeekly" xml:space="preserve">
    <value>Semanal</value>
  </data>
  <data name="DataBarsTypeYear" xml:space="preserve">
    <value>{0} Año</value>
  </data>
  <data name="DataBarsTypeYearly" xml:space="preserve">
    <value>Anual</value>
  </data>
  <data name="Day" xml:space="preserve">
    <value>Día</value>
  </data>
  <data name="Days" xml:space="preserve">
    <value>Días</value>
  </data>
  <data name="DeviationType" xml:space="preserve">
    <value>Tipo de desviación</value>
  </data>
  <data name="DeviationValue" xml:space="preserve">
    <value>Valor de desviación</value>
  </data>
  <data name="DMMinusDI" xml:space="preserve">
    <value>-DI</value>
  </data>
  <data name="DMPlusDI" xml:space="preserve">
    <value>+DI</value>
  </data>
  <data name="DonchianChannelMean" xml:space="preserve">
    <value>La media</value>
  </data>
  <data name="DownBarColor" xml:space="preserve">
    <value>Color de barra descendente</value>
  </data>
  <data name="DrawingToolIndicatorDescription" xml:space="preserve">
    <value>El indicador en la teja "herramienta de dibujo" permite crear una teja flotante en el grafico para acceder herriamientas de dibujo mas usadas.</value>
  </data>
  <data name="DrawingToolIndicatorName" xml:space="preserve">
    <value>Teja herramienta de dibujo</value>
  </data>
  <data name="DrawLines" xml:space="preserve">
    <value>Dibuje líneas</value>
  </data>
  <data name="EMA1" xml:space="preserve">
    <value>Periodo EMA1</value>
  </data>
  <data name="EMA2" xml:space="preserve">
    <value>Periodo EMA2</value>
  </data>
  <data name="EmailSignature" xml:space="preserve">
    <value>Enviado por NinjaTrader</value>
  </data>
  <data name="EnvelopePercentage" xml:space="preserve">
    <value>Dotación porcentual</value>
  </data>
  <data name="FacebookServiceName" xml:space="preserve">
    <value>Facebook</value>
  </data>
  <data name="FacebookSignature" xml:space="preserve">
    <value>Enviado por NinjaTrader</value>
  </data>
  <data name="Fast" xml:space="preserve">
    <value>Rápido</value>
  </data>
  <data name="FastLimit" xml:space="preserve">
    <value>Limit rápido</value>
  </data>
  <data name="FastPeriod" xml:space="preserve">
    <value>Periodo rápido</value>
  </data>
  <data name="FibonacciTextAlignment_ExtremeLeft" xml:space="preserve">
    <value>Izquierda extrema</value>
  </data>
  <data name="FibonacciTextAlignment_ExtremeRight" xml:space="preserve">
    <value>Derecha extrema</value>
  </data>
  <data name="FibonacciTextAlignment_Left" xml:space="preserve">
    <value>Izquierda</value>
  </data>
  <data name="FibonacciTextAlignment_Off" xml:space="preserve">
    <value>Desactivado</value>
  </data>
  <data name="FibonacciTextAlignment_Right" xml:space="preserve">
    <value>Derecha</value>
  </data>
  <data name="FileFilterAnyLoadingDialog" xml:space="preserve">
    <value>Algún (*.*)</value>
  </data>
  <data name="FileFilterAnyWinForms" xml:space="preserve">
    <value>Algun (*.*)|*.*</value>
  </data>
  <data name="FileName" xml:space="preserve">
    <value>Nombre de carpeta</value>
  </data>
  <data name="Font" xml:space="preserve">
    <value>Tipo de letra</value>
  </data>
  <data name="Forecast" xml:space="preserve">
    <value>Pronóstico</value>
  </data>
  <data name="GannFanDirection_DownLeft" xml:space="preserve">
    <value>Izquierda inferior</value>
  </data>
  <data name="GannFanDirection_DownRight" xml:space="preserve">
    <value>Derecha inferior</value>
  </data>
  <data name="GannFanDirection_UpLeft" xml:space="preserve">
    <value>Parte superior izquierda</value>
  </data>
  <data name="GannFanDirection_UpRight" xml:space="preserve">
    <value>Parte superior derecha</value>
  </data>
  <data name="GuiAuthorize" xml:space="preserve">
    <value>Autorizar</value>
  </data>
  <data name="GuiChartStyleDojiBrush" xml:space="preserve">
    <value>Color para barras de doji</value>
  </data>
  <data name="HigherHigh" xml:space="preserve">
    <value>Alta superior</value>
  </data>
  <data name="HigherLow" xml:space="preserve">
    <value>Alta inferior</value>
  </data>
  <data name="HighlightVerticalRangeUnit_Currency" xml:space="preserve">
    <value>Moneda</value>
  </data>
  <data name="HighlightVerticalRangeUnit_Percent" xml:space="preserve">
    <value>Porcentaje</value>
  </data>
  <data name="HighlightVerticalRangeUnit_Pips" xml:space="preserve">
    <value>PIPS</value>
  </data>
  <data name="HighlightVerticalRangeUnit_Price" xml:space="preserve">
    <value>Precio</value>
  </data>
  <data name="HighlightVerticalRangeUnit_Ticks" xml:space="preserve">
    <value>Marcas</value>
  </data>
  <data name="HLCCalculationMode" xml:space="preserve">
    <value>Modo de cálculo HLC</value>
  </data>
  <data name="HLCCalculationMode_CalcFromIntradayData" xml:space="preserve">
    <value>Calculado para datos intradía</value>
  </data>
  <data name="HLCCalculationMode_DailyBars" xml:space="preserve">
    <value>Use barras diarias</value>
  </data>
  <data name="HLCCalculationMode_UserDefinedValues" xml:space="preserve">
    <value>Use usuario de valores definidos</value>
  </data>
  <data name="HLCCalculationModeDescription" xml:space="preserve">
    <value>Approach for calculation the prior day HLC values</value>
  </data>
  <data name="Import" xml:space="preserve">
    <value>Importar</value>
  </data>
  <data name="ImportTypeNinjaTraderBeginningOfBar" xml:space="preserve">
    <value>NinjaTrader (marca de tiempo inicio de barras)</value>
  </data>
  <data name="ImportTypeNinjaTraderDateTimeFormatError" xml:space="preserve">
    <value>{0}: Error en línea de Fecha/formato de Tiempo</value>
  </data>
  <data name="ImportTypeNinjaTraderEndOfBar" xml:space="preserve">
    <value>NinjaTrader (fin de la barra de sellos de tiempo)</value>
  </data>
  <data name="ImportTypeNinjaTraderFieldSeparatorNotIdentified" xml:space="preserve">
    <value>{0}: Campo separador de importe no identificado</value>
  </data>
  <data name="ImportTypeNinjaTraderFormatError" xml:space="preserve">
    <value>{0} Error de formato en línea {1}:{2}: {3} </value>
  </data>
  <data name="ImportTypeNinjaTraderInstrumentNotSupported" xml:space="preserve">
    <value>No se puede importar carpetas {0}. El instrumento no es compatible con el repositorio.</value>
  </data>
  <data name="ImportTypeNinjaTraderNumericPriceFormatError" xml:space="preserve">
    <value>{0}: Formato de precio numérico no es soportado</value>
  </data>
  <data name="ImportTypeNinjaTraderUnableReadData" xml:space="preserve">
    <value>No se puede leer los datos de la carpeta {0}; {1}</value>
  </data>
  <data name="ImportTypeNinjaTraderUnexpectedFieldNumber" xml:space="preserve">
    <value>{0}: Cantidad de campos en linea inesperado ´{1}', debería ser 3, 5 or 6</value>
  </data>
  <data name="ImportTypeTickData" xml:space="preserve">
    <value>Tick Data, LLC</value>
  </data>
  <data name="IncrementalPeriod" xml:space="preserve">
    <value>Periodo Incremental</value>
  </data>
  <data name="Intermediate" xml:space="preserve">
    <value>Intermedio</value>
  </data>
  <data name="Interval" xml:space="preserve">
    <value>Intervalo</value>
  </data>
  <data name="KeltnerChannelMidline" xml:space="preserve">
    <value>Línea media</value>
  </data>
  <data name="KeyReversalPlot0" xml:space="preserve">
    <value>Lote 0</value>
  </data>
  <data name="LastLineLength" xml:space="preserve">
    <value>Longitud de la última línea (% del gráfico)</value>
  </data>
  <data name="LastLineStroke" xml:space="preserve">
    <value>Última línea</value>
  </data>
  <data name="LegendLocation" xml:space="preserve">
    <value>Ubicación de la leyenda</value>
  </data>
  <data name="LegendLocation_BottomLeft" xml:space="preserve">
    <value>Izquierda inferior</value>
  </data>
  <data name="LegendLocation_BottomRight" xml:space="preserve">
    <value>Derecha inferior</value>
  </data>
  <data name="LegendLocation_Disabled" xml:space="preserve">
    <value>Deshabilitado</value>
  </data>
  <data name="LegendLocation_TopLeft" xml:space="preserve">
    <value>Izquierda superior</value>
  </data>
  <data name="LegendLocation_TopRight" xml:space="preserve">
    <value>Derecha superior</value>
  </data>
  <data name="Length" xml:space="preserve">
    <value>Longitud</value>
  </data>
  <data name="Line1Value" xml:space="preserve">
    <value>Línea 1 valor</value>
  </data>
  <data name="Line2Value" xml:space="preserve">
    <value>Línea 2 valor</value>
  </data>
  <data name="Line3Value" xml:space="preserve">
    <value>Línea 3 valor</value>
  </data>
  <data name="Line4Value" xml:space="preserve">
    <value>Línea 4 valor</value>
  </data>
  <data name="LineColor" xml:space="preserve">
    <value>Color de línea</value>
  </data>
  <data name="Load" xml:space="preserve">
    <value>Cargar</value>
  </data>
  <data name="Location" xml:space="preserve">
    <value>Localidad</value>
  </data>
  <data name="LowerHigh" xml:space="preserve">
    <value>Baja superior</value>
  </data>
  <data name="LowerLow" xml:space="preserve">
    <value>Baja inferior</value>
  </data>
  <data name="MailCcAddress" xml:space="preserve">
    <value>CC:</value>
  </data>
  <data name="MailCcAddressDescription" xml:space="preserve">
    <value>La dirección de correo electrónico del destinatario de la copia al carbón. Separe varias direcciones con '', o ';'</value>
  </data>
  <data name="MailServiceMailAddress" xml:space="preserve">
    <value>Dirección de correo electrónico</value>
  </data>
  <data name="MailServiceName" xml:space="preserve">
    <value>E-mail</value>
  </data>
  <data name="MailServicePort" xml:space="preserve">
    <value>Puerto de Conexión</value>
  </data>
  <data name="MailServiceSenderDisplayName" xml:space="preserve">
    <value>Departe nombre</value>
  </data>
  <data name="MailServiceServer" xml:space="preserve">
    <value>Servidor de Conexión</value>
  </data>
  <data name="MailServiceSSL" xml:space="preserve">
    <value>Conexión SSL</value>
  </data>
  <data name="MailSubject" xml:space="preserve">
    <value>Sujeto:</value>
  </data>
  <data name="MailSubjectDescription" xml:space="preserve">
    <value>El sujeto de su mensaje e-mail</value>
  </data>
  <data name="MailToAddress" xml:space="preserve">
    <value>Para:</value>
  </data>
  <data name="MailToAddressDescription" xml:space="preserve">
    <value>La dirección de e-mail de su bandeja de entrada, Separar direcciones múltiples ´con´',' o ';'</value>
  </data>
  <data name="MAMAFAMA" xml:space="preserve">
    <value>FAMA</value>
  </data>
  <data name="MAPeriod" xml:space="preserve">
    <value>Periodo de media móvil</value>
  </data>
  <data name="MAType" xml:space="preserve">
    <value>Tipo de media móvil</value>
  </data>
  <data name="MovingAverage" xml:space="preserve">
    <value>Promedio móvil</value>
  </data>
  <data name="MovingAverageRibbonPlot1" xml:space="preserve">
    <value>Promedio móvil 1</value>
  </data>
  <data name="MovingAverageRibbonPlot2" xml:space="preserve">
    <value>Promedio móvil 2</value>
  </data>
  <data name="MovingAverageRibbonPlot3" xml:space="preserve">
    <value>Promedio móvil 3</value>
  </data>
  <data name="MovingAverageRibbonPlot4" xml:space="preserve">
    <value>Promedio móvil 4</value>
  </data>
  <data name="MovingAverageRibbonPlot5" xml:space="preserve">
    <value>Promedio móvil 5</value>
  </data>
  <data name="MovingAverageRibbonPlot6" xml:space="preserve">
    <value>Promedio móvil 6</value>
  </data>
  <data name="MovingAverageRibbonPlot7" xml:space="preserve">
    <value>Promedio móvil 7</value>
  </data>
  <data name="MovingAverageRibbonPlot8" xml:space="preserve">
    <value>Promedio móvil 8</value>
  </data>
  <data name="NBarsDownTrigger" xml:space="preserve">
    <value>Activar</value>
  </data>
  <data name="NegativeColor" xml:space="preserve">
    <value>Color Negativo</value>
  </data>
  <data name="NetChangePosition_BottomLeft" xml:space="preserve">
    <value>Izquierda inferior</value>
  </data>
  <data name="NetChangePosition_BottomRight" xml:space="preserve">
    <value>Derecha inferior</value>
  </data>
  <data name="NetChangePosition_TopLeft" xml:space="preserve">
    <value>Izquierda superior</value>
  </data>
  <data name="NetChangePosition_TopRight" xml:space="preserve">
    <value>Derecha superior</value>
  </data>
  <data name="NinjaScriptBackground" xml:space="preserve">
    <value>Fondo</value>
  </data>
  <data name="NinjaScriptBarsTypeDay" xml:space="preserve">
    <value>Día</value>
  </data>
  <data name="NinjaScriptBarsTypeHeikenAshi" xml:space="preserve">
    <value>Heiken Ashi</value>
  </data>
  <data name="NinjaScriptBarsTypeKagi" xml:space="preserve">
    <value>Kagi</value>
  </data>
  <data name="NinjaScriptBarsTypeKagiReversal" xml:space="preserve">
    <value>Reversión</value>
  </data>
  <data name="NinjaScriptBarsTypeLineBreak" xml:space="preserve">
    <value>Linea de pausa</value>
  </data>
  <data name="NinjaScriptBarsTypeLineBreakLineBreaks" xml:space="preserve">
    <value>Linea de pausas</value>
  </data>
  <data name="NinjaScriptBarsTypeMinute" xml:space="preserve">
    <value>Minuto</value>
  </data>
  <data name="NinjaScriptBarsTypeMonth" xml:space="preserve">
    <value>Mes</value>
  </data>
  <data name="NinjaScriptBarsTypePointAndFigure" xml:space="preserve">
    <value>Punto y Figura</value>
  </data>
  <data name="NinjaScriptBarsTypePointAndFigureBoxSize" xml:space="preserve">
    <value>Dimensión de caja</value>
  </data>
  <data name="NinjaScriptBarsTypePointAndFigureReversal" xml:space="preserve">
    <value>Reversión</value>
  </data>
  <data name="NinjaScriptBarsTypeRange" xml:space="preserve">
    <value>Rango</value>
  </data>
  <data name="NinjaScriptBarsTypeRenko" xml:space="preserve">
    <value>Renko</value>
  </data>
  <data name="NinjaScriptBarsTypeRenkoBrickSize" xml:space="preserve">
    <value>Dimensión de ladrillo</value>
  </data>
  <data name="NinjaScriptBarsTypeSecond" xml:space="preserve">
    <value>Segundo</value>
  </data>
  <data name="NinjaScriptBarsTypeTick" xml:space="preserve">
    <value>Marca</value>
  </data>
  <data name="NinjaScriptBarsTypeVolume" xml:space="preserve">
    <value>Volumen</value>
  </data>
  <data name="NinjaScriptBarsTypeWeek" xml:space="preserve">
    <value>Semana</value>
  </data>
  <data name="NinjaScriptBorder" xml:space="preserve">
    <value>Borde</value>
  </data>
  <data name="NinjaScriptChartStyleBarWidth" xml:space="preserve">
    <value>Amplitud de barra</value>
  </data>
  <data name="NinjaScriptChartStyleBox" xml:space="preserve">
    <value>Cuadro</value>
  </data>
  <data name="NinjaScriptChartStyleBoxDownBarsColor" xml:space="preserve">
    <value>Color de barras descendiente</value>
  </data>
  <data name="NinjaScriptChartStyleBoxDownBarsOutline" xml:space="preserve">
    <value>Esquema de barras descendiente</value>
  </data>
  <data name="NinjaScriptChartStyleBoxUpBarsColor" xml:space="preserve">
    <value>Color para las barras ascendente</value>
  </data>
  <data name="NinjaScriptChartStyleBoxUpBarsOutline" xml:space="preserve">
    <value>Esquema de barras ascendente</value>
  </data>
  <data name="NinjaScriptChartStyleCandleDownBarsColor" xml:space="preserve">
    <value>Color de barras descendiente</value>
  </data>
  <data name="NinjaScriptChartStyleCandleOutline" xml:space="preserve">
    <value>Contorno de la vela</value>
  </data>
  <data name="NinjaScriptChartStyleCandlestick" xml:space="preserve">
    <value>Vela</value>
  </data>
  <data name="NinjaScriptChartStyleCandlestickHollow" xml:space="preserve">
    <value>Vela hueca</value>
  </data>
  <data name="NinjaScriptChartStyleCandleUpBarsColor" xml:space="preserve">
    <value>Color para las barras ascendente</value>
  </data>
  <data name="NinjaScriptChartStyleCandleWick" xml:space="preserve">
    <value>Mecha de vela</value>
  </data>
  <data name="NinjaScriptChartStyleEquivolume" xml:space="preserve">
    <value>Equivolume</value>
  </data>
  <data name="NinjaScriptChartStyleHeikenAshi" xml:space="preserve">
    <value>Heiken Ashi</value>
  </data>
  <data name="NinjaScriptChartStyleKagi" xml:space="preserve">
    <value>Línea Kagi</value>
  </data>
  <data name="NinjaScriptChartStyleKagiThickLine" xml:space="preserve">
    <value>Línea gruesa</value>
  </data>
  <data name="NinjaScriptChartStyleKagiThinLine" xml:space="preserve">
    <value>Línea delgada</value>
  </data>
  <data name="NinjaScriptChartStyleLineOnClose" xml:space="preserve">
    <value>Línea en cierre</value>
  </data>
  <data name="NinjaScriptChartStyleLineOnCloseColor" xml:space="preserve">
    <value>Color</value>
  </data>
  <data name="NinjaScriptChartStyleLineOnCloseWidth" xml:space="preserve">
    <value>Línea Ancha</value>
  </data>
  <data name="NinjaScriptChartStyleLineWidth" xml:space="preserve">
    <value>Línea Ancha</value>
  </data>
  <data name="NinjaScriptChartStyleMountain" xml:space="preserve">
    <value>Montaña</value>
  </data>
  <data name="NinjaScriptChartStyleMountainColor" xml:space="preserve">
    <value>Color</value>
  </data>
  <data name="NinjaScriptChartStyleMountainOutline" xml:space="preserve">
    <value>Esquema</value>
  </data>
  <data name="NinjaScriptChartStyleOHLC" xml:space="preserve">
    <value>OHLC</value>
  </data>
  <data name="NinjaScriptChartStyleOhlcDownBarsColor" xml:space="preserve">
    <value>Color de barras descendiente</value>
  </data>
  <data name="NinjaScriptChartStyleOhlcUpBarsColor" xml:space="preserve">
    <value>Color para las barras ascendente</value>
  </data>
  <data name="NinjaScriptChartStyleOpenClose" xml:space="preserve">
    <value>Abrir/cerrar</value>
  </data>
  <data name="NinjaScriptChartStyleOpenCloseDownBarsColor" xml:space="preserve">
    <value>Color de barras descendiente</value>
  </data>
  <data name="NinjaScriptChartStyleOpenCloseDownBarsOutline" xml:space="preserve">
    <value>Esquema de barras descendiente</value>
  </data>
  <data name="NinjaScriptChartStyleOpenCloseUpBarsColor" xml:space="preserve">
    <value>Color para las barras ascendente</value>
  </data>
  <data name="NinjaScriptChartStyleOpenCloseUpBarsOutline" xml:space="preserve">
    <value>Esquema de barras ascendente</value>
  </data>
  <data name="NinjaScriptChartStylePointAndFigure" xml:space="preserve">
    <value>Punto y Figura</value>
  </data>
  <data name="NinjaScriptChartStylePointAndFigureDownColor" xml:space="preserve">
    <value>Color descendente</value>
  </data>
  <data name="NinjaScriptChartStylePointAndFigureUpColor" xml:space="preserve">
    <value>Color ascendente</value>
  </data>
  <data name="NinjaScriptDrawingToolAnchor" xml:space="preserve">
    <value>Ancla</value>
  </data>
  <data name="NinjaScriptDrawingToolAnchorEnd" xml:space="preserve">
    <value>Final</value>
  </data>
  <data name="NinjaScriptDrawingToolAnchorExtension" xml:space="preserve">
    <value>Extensión</value>
  </data>
  <data name="NinjaScriptDrawingToolAnchorMiddle" xml:space="preserve">
    <value>Medio</value>
  </data>
  <data name="NinjaScriptDrawingToolAnchorStart" xml:space="preserve">
    <value>Iniciar</value>
  </data>
  <data name="NinjaScriptDrawingToolAnchorText" xml:space="preserve">
    <value>Texto</value>
  </data>
  <data name="NinjaScriptDrawingToolAndrewsPitchfork" xml:space="preserve">
    <value>Andrews Pitchfork</value>
  </data>
  <data name="NinjaScriptDrawingToolAndrewsPitchforkCalculationMethod" xml:space="preserve">
    <value>Método de calculo</value>
  </data>
  <data name="NinjaScriptDrawingToolAndrewsPitchforkCategoryStrokes" xml:space="preserve">
    <value>Strokes</value>
  </data>
  <data name="NinjaScriptDrawingToolAndrewsPitchforkDescription" xml:space="preserve">
    <value>Descripción de Andrews Pitchfork</value>
  </data>
  <data name="NinjaScriptDrawingToolAndrewsPitchforkExtendLinesBack" xml:space="preserve">
    <value>Extender las líneas hacia atrás</value>
  </data>
  <data name="NinjaScriptDrawingToolAndrewsPitchforkExtensionStroke" xml:space="preserve">
    <value>Extension de linea stroke</value>
  </data>
  <data name="NinjaScriptDrawingToolAndrewsPitchforkRetracement" xml:space="preserve">
    <value>Retrocesos</value>
  </data>
  <data name="NinjaScriptDrawingToolArc" xml:space="preserve">
    <value>Arco</value>
  </data>
  <data name="NinjaScriptDrawingToolAreaOpacity" xml:space="preserve">
    <value>Opacidad -área (%)</value>
  </data>
  <data name="NinjaScriptDrawingToolArrowLine" xml:space="preserve">
    <value>Linea fletcha</value>
  </data>
  <data name="NinjaScriptDrawingToolBackgroundOpacity" xml:space="preserve">
    <value>Opacidad de fondo (%)</value>
  </data>
  <data name="NinjaScriptDrawingToolEllipse" xml:space="preserve">
    <value>Elipse</value>
  </data>
  <data name="NinjaScriptDrawingToolExtendedLine" xml:space="preserve">
    <value>Línea extendida</value>
  </data>
  <data name="NinjaScriptDrawingToolFibonacciCircle" xml:space="preserve">
    <value>Círculo Fibonacci</value>
  </data>
  <data name="NinjaScriptDrawingToolFibonacciExtensions" xml:space="preserve">
    <value>Extensiones Fibonacci</value>
  </data>
  <data name="NinjaScriptDrawingToolFibonacciLevelsBaseAnchorLineStroke" xml:space="preserve">
    <value>Ancla</value>
  </data>
  <data name="NinjaScriptDrawingToolFibonacciRetracements" xml:space="preserve">
    <value>Retrocesos Fibonacci</value>
  </data>
  <data name="NinjaScriptDrawingToolFibonacciRetracementsExtendLinesLeft" xml:space="preserve">
    <value>Extender líneas a la izquierda</value>
  </data>
  <data name="NinjaScriptDrawingToolFibonacciRetracementsExtendLinesRight" xml:space="preserve">
    <value>Extender líneas a la derecha</value>
  </data>
  <data name="NinjaScriptDrawingToolFibonacciRetracementsTextAlignment" xml:space="preserve">
    <value>Alineamiento de texto</value>
  </data>
  <data name="NinjaScriptDrawingToolFibonacciRetracementsTextLocation" xml:space="preserve">
    <value>Dirección del texto</value>
  </data>
  <data name="NinjaScriptDrawingToolFibonacciTimeCircleDivideTimeSeparately" xml:space="preserve">
    <value>Divida el tiempo/el precio por separado</value>
  </data>
  <data name="NinjaScriptDrawingToolFibonacciTimeExtensions" xml:space="preserve">
    <value>Tiempo de extensiones Fibonacci</value>
  </data>
  <data name="NinjaScriptDrawingToolFibonacciTimeExtensionsShowText" xml:space="preserve">
    <value>Mostrar texto</value>
  </data>
  <data name="NinjaScriptDrawingToolGannFan" xml:space="preserve">
    <value>Gann fan</value>
  </data>
  <data name="NinjaScriptDrawingToolGannFanDisplayText" xml:space="preserve">
    <value>Reproducir Texto</value>
  </data>
  <data name="NinjaScriptDrawingToolGannFanFanDirection" xml:space="preserve">
    <value>Dirección Abanico</value>
  </data>
  <data name="NinjaScriptDrawingToolGannFanPointsPerBar" xml:space="preserve">
    <value>Puntos por barra</value>
  </data>
  <data name="NinjaScriptDrawingToolHorizontalLine" xml:space="preserve">
    <value>Línea horizontal</value>
  </data>
  <data name="NinjaScriptDrawingToolLine" xml:space="preserve">
    <value>Línea</value>
  </data>
  <data name="NinjaScriptDrawingToolPath" xml:space="preserve">
    <value>Ruta</value>
  </data>
  <data name="NinjaScriptDrawingToolPathBegin" xml:space="preserve">
    <value>Comenzar el camino</value>
  </data>
  <data name="NinjaScriptDrawingToolPathEnd" xml:space="preserve">
    <value>Fin de camino</value>
  </data>
  <data name="NinjaScriptDrawingToolPathSegment" xml:space="preserve">
    <value>Segmento</value>
  </data>
  <data name="NinjaScriptDrawingToolPathShowCount" xml:space="preserve">
    <value>Mostrar conteo de</value>
  </data>
  <data name="NinjaScriptDrawingToolPolygon" xml:space="preserve">
    <value>Polígono</value>
  </data>
  <data name="NinjaScriptDrawingToolPriceLevelsOpacity" xml:space="preserve">
    <value>Opacidad de niveles de precio (%)</value>
  </data>
  <data name="NinjaScriptDrawingToolRay" xml:space="preserve">
    <value>Rayo</value>
  </data>
  <data name="NinjaScriptDrawingToolRectangle" xml:space="preserve">
    <value>Rectángulo</value>
  </data>
  <data name="NinjaScriptDrawingToolRegion" xml:space="preserve">
    <value>Región</value>
  </data>
  <data name="NinjaScriptDrawingToolRegionHighlightDirection" xml:space="preserve">
    <value>Dirección</value>
  </data>
  <data name="NinjaScriptDrawingToolRegionHighlightDirectionStroke" xml:space="preserve">
    <value>Dirección Stroke</value>
  </data>
  <data name="NinjaScriptDrawingToolRegionHighlightHorizontalTextFormat" xml:space="preserve">
    <value>{0} tiempo de barras: {1}</value>
  </data>
  <data name="NinjaScriptDrawingToolRegionHighlightVerticalRangeUnit" xml:space="preserve">
    <value>Unidad de rango vertical</value>
  </data>
  <data name="NinjaScriptDrawingToolRegionHighlightVerticalTextFormat" xml:space="preserve">
    <value>Valor del rango: {0} {1}</value>
  </data>
  <data name="NinjaScriptDrawingToolRegionHiglightX" xml:space="preserve">
    <value>Resalta de region x</value>
  </data>
  <data name="NinjaScriptDrawingToolRegionHiglightY" xml:space="preserve">
    <value>Resalta de region y</value>
  </data>
  <data name="NinjaScriptDrawingToolRegressionChannel" xml:space="preserve">
    <value>Canal de regresión</value>
  </data>
  <data name="NinjaScriptDrawingToolRegressionChannelLowerChannel" xml:space="preserve">
    <value>Canal inferior</value>
  </data>
  <data name="NinjaScriptDrawingToolRegressionChannelLowerChannelColor" xml:space="preserve">
    <value>Color del canal inferior</value>
  </data>
  <data name="NinjaScriptDrawingToolRegressionChannelPriceType" xml:space="preserve">
    <value>Tipo de precio</value>
  </data>
  <data name="NinjaScriptDrawingToolRegressionChannelRegressionChannel" xml:space="preserve">
    <value>Regresion</value>
  </data>
  <data name="NinjaScriptDrawingToolRegressionChannelStandardDeviationExtendLeft" xml:space="preserve">
    <value>Extender a la izquierda</value>
  </data>
  <data name="NinjaScriptDrawingToolRegressionChannelStandardDeviationExtendRight" xml:space="preserve">
    <value>Extender a la derecha</value>
  </data>
  <data name="NinjaScriptDrawingToolRegressionChannelStandardDeviationLowerDistance" xml:space="preserve">
    <value>Distancia al canal inferior</value>
  </data>
  <data name="NinjaScriptDrawingToolRegressionChannelStandardDeviationUpperDistance" xml:space="preserve">
    <value>Distancia al canal superior</value>
  </data>
  <data name="NinjaScriptDrawingToolRegressionChannelType" xml:space="preserve">
    <value>Modo</value>
  </data>
  <data name="NinjaScriptDrawingToolRegressionChannelUpperChannel" xml:space="preserve">
    <value>Canal superior</value>
  </data>
  <data name="NinjaScriptDrawingToolRegressionChannelUpperChannelColor" xml:space="preserve">
    <value>Color del canal superior</value>
  </data>
  <data name="NinjaScriptDrawingToolRiskRewardAnchorEntry" xml:space="preserve">
    <value>Ancla de entrada</value>
  </data>
  <data name="NinjaScriptDrawingToolRiskRewardAnchorLineStroke" xml:space="preserve">
    <value>Ancla</value>
  </data>
  <data name="NinjaScriptDrawingToolRiskRewardAnchorReward" xml:space="preserve">
    <value>Ancla de retribucion</value>
  </data>
  <data name="NinjaScriptDrawingToolRiskRewardAnchorRisk" xml:space="preserve">
    <value>Ancla de riesgo</value>
  </data>
  <data name="NinjaScriptDrawingToolRiskRewardCategoryColors" xml:space="preserve">
    <value>Colores</value>
  </data>
  <data name="NinjaScriptDrawingToolRiskRewardDescription" xml:space="preserve">
    <value>Calcule automáticamente su Objetivo basado en un usuario definido como Stop Loss</value>
  </data>
  <data name="NinjaScriptDrawingToolRiskRewardLineStrokeEntry" xml:space="preserve">
    <value>Extensión de entrada</value>
  </data>
  <data name="NinjaScriptDrawingToolRiskRewardLineStrokeReward" xml:space="preserve">
    <value>Extensión de retribucion</value>
  </data>
  <data name="NinjaScriptDrawingToolRiskRewardLineStrokeRisk" xml:space="preserve">
    <value>Extensión de riesgo</value>
  </data>
  <data name="NinjaScriptDrawingToolRiskRewardName" xml:space="preserve">
    <value>retribucion de riesgo</value>
  </data>
  <data name="NinjaScriptDrawingToolRiskRewardRatio" xml:space="preserve">
    <value>Proporcion</value>
  </data>
  <data name="NinjaScriptDrawingToolRuler" xml:space="preserve">
    <value>Regla</value>
  </data>
  <data name="NinjaScriptDrawingToolRulerDaysFormat" xml:space="preserve">
    <value>{0} días</value>
  </data>
  <data name="NinjaScriptDrawingToolRulerNumberBarsText" xml:space="preserve">
    <value># de barras:</value>
  </data>
  <data name="NinjaScriptDrawingToolRulerTimeText" xml:space="preserve">
    <value>Tiempo:</value>
  </data>
  <data name="NinjaScriptDrawingToolRulerYValueDisplayUnit" xml:space="preserve">
    <value>las unidades de visualización del eje de valores Y</value>
  </data>
  <data name="NinjaScriptDrawingToolRulerYValueText" xml:space="preserve">
    <value>Valores del eje Y</value>
  </data>
  <data name="NinjaScriptDrawingTools" xml:space="preserve">
    <value>Herramientas de dibujo</value>
  </data>
  <data name="NinjaScriptDrawingToolsChartArrowDownMarkerName" xml:space="preserve">
    <value>Flecha hacia abajo</value>
  </data>
  <data name="NinjaScriptDrawingToolsChartArrowUpMarkerName" xml:space="preserve">
    <value>Flecha hacia arriba</value>
  </data>
  <data name="NinjaScriptDrawingToolsChartDiamondMarkerName" xml:space="preserve">
    <value>Diamante</value>
  </data>
  <data name="NinjaScriptDrawingToolsChartDotMarkerName" xml:space="preserve">
    <value>Punto</value>
  </data>
  <data name="NinjaScriptDrawingToolsChartSquareMarkerName" xml:space="preserve">
    <value>Cuadrado</value>
  </data>
  <data name="NinjaScriptDrawingToolsChartTriangleDownMarkerName" xml:space="preserve">
    <value>Triángulo Descendiente</value>
  </data>
  <data name="NinjaScriptDrawingToolsChartTriangleUpMarkerName" xml:space="preserve">
    <value>Triángulo Ascendiente</value>
  </data>
  <data name="NinjaScriptDrawingToolsGannAngleRatioX" xml:space="preserve">
    <value>Relación de tiempo</value>
  </data>
  <data name="NinjaScriptDrawingToolsGannAngleRatioY" xml:space="preserve">
    <value>Relación de precio</value>
  </data>
  <data name="NinjaScriptDrawingToolsGannAngles" xml:space="preserve">
    <value>Ángulos de Gann</value>
  </data>
  <data name="NinjaScriptDrawingToolsGannAnglesPrompt" xml:space="preserve">
    <value>1 ángulo de gann|{0} Ángulos de Gann|Añadir Ángulos de Gann..|Editar ángulo de Gann...|Editar ángulos de Gann...</value>
  </data>
  <data name="NinjaScriptDrawingToolShapesAreaBrush" xml:space="preserve">
    <value>Color - area</value>
  </data>
  <data name="NinjaScriptDrawingToolShapesOutlineBrush" xml:space="preserve">
    <value>Color - contorno</value>
  </data>
  <data name="NinjaScriptDrawingToolsPriceLevelIsVisible" xml:space="preserve">
    <value>Visible</value>
  </data>
  <data name="NinjaScriptDrawingToolsPriceLevelLineStroke" xml:space="preserve">
    <value>Línea</value>
  </data>
  <data name="NinjaScriptDrawingToolsPriceLevels" xml:space="preserve">
    <value>Niveles</value>
  </data>
  <data name="NinjaScriptDrawingToolsPriceLevelsPrompt" xml:space="preserve">
    <value>1 nivel de precio|{0} niveles de precio|Añadir niveles de precio..|Editar nivel de precio...|Editar niveles de precio...</value>
  </data>
  <data name="NinjaScriptDrawingToolsPriceLevelUnset" xml:space="preserve">
    <value>Sin establecer</value>
  </data>
  <data name="NinjaScriptDrawingToolsPriceLevelValue" xml:space="preserve">
    <value>Valor (%)</value>
  </data>
  <data name="NinjaScriptDrawingToolStroke" xml:space="preserve">
    <value>Línea</value>
  </data>
  <data name="NinjaScriptDrawingToolText" xml:space="preserve">
    <value>Texto</value>
  </data>
  <data name="NinjaScriptDrawingToolTextAlignment" xml:space="preserve">
    <value>Alineamiento de texto</value>
  </data>
  <data name="NinjaScriptDrawingToolTextBackBrush" xml:space="preserve">
    <value>Pincel en el fondo de texto</value>
  </data>
  <data name="NinjaScriptDrawingToolTextBrush" xml:space="preserve">
    <value>Color - tipo de letra</value>
  </data>
  <data name="NinjaScriptDrawingToolTextFixed" xml:space="preserve">
    <value>Texto fijo</value>
  </data>
  <data name="NinjaScriptDrawingToolTextFixedTextPosition" xml:space="preserve">
    <value>Posición de texto</value>
  </data>
  <data name="NinjaScriptDrawingToolTextFont" xml:space="preserve">
    <value>Tipo de letra</value>
  </data>
  <data name="NinjaScriptDrawingToolTextOutlineStroke" xml:space="preserve">
    <value>Esquema</value>
  </data>
  <data name="NinjaScriptDrawingToolTextOutlineVisible" xml:space="preserve">
    <value>Contorno - permitido</value>
  </data>
  <data name="NinjaScriptDrawingToolTimeCycles" xml:space="preserve">
    <value>Ciclos de tiempo</value>
  </data>
  <data name="NinjaScriptDrawingToolTrendChannel" xml:space="preserve">
    <value>Canal de tendencia</value>
  </data>
  <data name="NinjaScriptDrawingToolTrendChannelDescription" xml:space="preserve">
    <value>Graficar un canal de tendencia usando líneas paralelas</value>
  </data>
  <data name="NinjaScriptDrawingToolTrendChannelEnd1AnchorDisplayName" xml:space="preserve">
    <value>Tendencia Final</value>
  </data>
  <data name="NinjaScriptDrawingToolTrendChannelParallelStroke" xml:space="preserve">
    <value>Paralela</value>
  </data>
  <data name="NinjaScriptDrawingToolTrendChannelStart1AnchorDisplayName" xml:space="preserve">
    <value>Tendencia Inicial</value>
  </data>
  <data name="NinjaScriptDrawingToolTrendChannelStart2AnchorDisplayName" xml:space="preserve">
    <value>Paralela</value>
  </data>
  <data name="NinjaScriptDrawingToolTrendChannelTrendStroke" xml:space="preserve">
    <value>Tendencia</value>
  </data>
  <data name="NinjaScriptDrawingToolTriangle" xml:space="preserve">
    <value>Triángulo</value>
  </data>
  <data name="NinjaScriptDrawingToolVerticalLine" xml:space="preserve">
    <value>Línea vertical</value>
  </data>
  <data name="NinjaScriptGeneral" xml:space="preserve">
    <value>General</value>
  </data>
  <data name="NinjaScriptGeneticOptimizerAveragePerformanceOffsetPercent" xml:space="preserve">
    <value>La compensación del rendimiento promedio (%)</value>
  </data>
  <data name="NinjaScriptGeneticOptimizerConvergenceThreshold" xml:space="preserve">
    <value>Umbral de convergencia</value>
  </data>
  <data name="NinjaScriptGeneticOptimizerCrossoverIndex" xml:space="preserve">
    <value>Índice de cruce</value>
  </data>
  <data name="NinjaScriptGeneticOptimizerCrossoverRatePercent" xml:space="preserve">
    <value>Tarifa de cruce (%)</value>
  </data>
  <data name="NinjaScriptGeneticOptimizerFastGenerations" xml:space="preserve">
    <value>Generaciones rápidas</value>
  </data>
  <data name="NinjaScriptGeneticOptimizerGenerations" xml:space="preserve">
    <value>Generaciones</value>
  </data>
  <data name="NinjaScriptGeneticOptimizerGenerationSize" xml:space="preserve">
    <value>Dimensión de generaciones</value>
  </data>
  <data name="NinjaScriptGeneticOptimizerMinimumPerformance" xml:space="preserve">
    <value>Rendimiento mínimo</value>
  </data>
  <data name="NinjaScriptGeneticOptimizerMutationRatePercent" xml:space="preserve">
    <value>Tarifa de mutación (%)</value>
  </data>
  <data name="NinjaScriptGeneticOptimizerMutationStrengthPercent" xml:space="preserve">
    <value>Fuerza de mutación (%)</value>
  </data>
  <data name="NinjaScriptGeneticOptimizerResetSizePercent" xml:space="preserve">
    <value>Resetear tamaño (%)</value>
  </data>
  <data name="NinjaScriptGeneticOptimizerSlowGenerations" xml:space="preserve">
    <value>Generaciones lentas</value>
  </data>
  <data name="NinjaScriptGeneticOptimizerStabilitySizePercent" xml:space="preserve">
    <value>Dimensión de estabilidad (%)</value>
  </data>
  <data name="NinjaScriptGeneticOptimizerThresholdGenerations" xml:space="preserve">
    <value>Generaciones de umbral</value>
  </data>
  <data name="NinjaScriptIndicator" xml:space="preserve">
    <value>Indicador</value>
  </data>
  <data name="NinjaScriptIndicatorAvg" xml:space="preserve">
    <value>Promedio</value>
  </data>
  <data name="NinjaScriptIndicatorCount" xml:space="preserve">
    <value>Conteo</value>
  </data>
  <data name="NinjaScriptIndicatorDefault" xml:space="preserve">
    <value>Defecto</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionADL" xml:space="preserve">
    <value>El estudio de acumulación y distribución (AD) intenta cuantificar la cantidad de volumen que fluye dentro o fuera de un instrumento mediante la identificación de la posición de cierre del período en relación con la rango de alta/baja de ese período.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionADX" xml:space="preserve">
    <value>El índice promedio direccional mide la fuerza de una tendencia predominante así como si existiese movimiento en el mercado. El ADX se mide en una escala de 0 a 100. Un valor bajo de ADX (generalmente menos de 20) puede indicar un mercado sin tendencia con volúmenes reducidos mientras que una cruz por encima de 20 puede indicar el inicio de una tendencia (arriba o abajo). Si el ADX es mayor de 40 y comienza a caer, puede indicar la desaceleración de una tendencia actual.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionADXR" xml:space="preserve">
    <value>El promedio del Movimiento Direccional cuantifica el cambio de ímpetu del ADX. Este es calculado añadiendo dos valores de ADX (el valor corriente y un valor n de períodos anteriores), luego dividiéndose en dos. Este allanamiento adicional hace que ADXR sea ligeramente menos sensible que ADX. La interpretación es lo mismo como el ADX; más alto el valor, más fuerte la tendencia.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionAPZ" xml:space="preserve">
    <value>El APZ (La zona de precio adaptable) forma un canal estable basado en dobles valores medios móviles exponenciales alisados alrededor del precio medio. Ver S/C, septiembre de 2006, p.28</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionAroon" xml:space="preserve">
    <value>El Indicador Aroon fue desarrollado por Tushar Chande. Consiste de dos complots: una medición del número de períodos desde el x-período más reciente alto (Aroon) y la otra medición del número de períodos desde el x-período más reciente bajo (Aroon Abajo).</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionAroonOscillator" xml:space="preserve">
    <value>El oscilador de Aroon se basa en su indicador Aroon. Tanto como el indicador Aroon, el oscilador de Aroon mide la fuerza de una tendencia.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionATR" xml:space="preserve">
    <value>El rango verdadero promedio (ATR) es una medida de volatilidad. Fue introducido por Welles Wilder en su libro 'Nuevos conceptos en sistemas técnicos comerciales' y desde entonces ha sido utilizado como un componente de muchos indicadores y sistemas comerciales.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionBarTimer" xml:space="preserve">
    <value>Muestra el tiempo restante de la barra de tiempo basado en</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionBlockVolume" xml:space="preserve">
    <value>Bloque de volumen detecta bloquea trades y mostrar cuántos ocurrieron por barra. Esto puede mostrarse como trades o volumen. Datos de la señal histórica es necesaria para trazar históricamente.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionBollinger" xml:space="preserve">
    <value>Las bandas de Bollinger se trazan en los niveles de desviación estándar por encima y por debajo de una media móvil. Puesto que la desviación estándar es una medida de volatilidad, las bandas son autoajustables: ensanchamiento durante la volatilidad de los mercados y la contratación durante períodos de más estabilidad.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionBOP" xml:space="preserve">
    <value>El indicador de equilibrio de poder mide la fuerza de los toros contra los osos tasando la capacidad de cada uno del precio de empuje a un nivel extremo</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionBuySellPressure" xml:space="preserve">
    <value>Indica la compra corriente o venta de presión como un porcentaje. Esto es una marca por el indicador de la marca. Si 'Calcular' se ajusta en 'la barra cercana", los valores de los indicadores siempre serán 100.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionBuySellVolume" xml:space="preserve">
    <value>Traza un volumen de división del histograma entre trading en la venta o más alto y trade en la oferta y más abajo. Sólo los trabajos de datos históricos usando la reproducción de la Marca</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionCamarillaPivots" xml:space="preserve">
    <value>Pivotes de camarilla son un análisis de precio demasiado que genera posibles niveles de soporte y resistencia multiplicando el rango anterior y luego sumando o restando al cierre.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionCandlestickPattern" xml:space="preserve">
    <value>Detecta los patrones comunes de la velas de marca y son identicadas en el gráfico</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionCCI" xml:space="preserve">
    <value>El Commodity Channel Index (CCI) mide la variación del precio de una seguridad de su media estadística. Los Valores elevados muestran que los precios son inusualmente altos en comparación con los precios promedio considerando que los valores bajos indican que los precios son excepcionalmente bajos.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionChaikinMoneyFlow" xml:space="preserve">
    <value>Calcula la cantidad de volumen de flujo de dinero excedente en las barras</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionChaikinOscillator" xml:space="preserve">
    <value>Calcula el impulso de acumulación de la línea de distribución mediante la diferencia entre las dos medias móviles exponenciales.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionChaikinVolatility" xml:space="preserve">
    <value>Compara la diferencia entre un rango de los instrumentos históricos y actuales usando el promedio de las móviles exponenciales.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionChoppinessIndex" xml:space="preserve">
    <value>El índice Choppiness está diseñado para determinar si el mercado es entrecortada (trading hacia un lado) y no entrecortado (comercio dentro de una tendencia en cualquier dirección)</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionCMO" xml:space="preserve">
    <value>El CMO difiere de otros osciladores impulso como el Relative Strength Index (RSI) y Stochastics Usa a ambos datos diarios de arriba abajo en el numerador del cálculo para medir el ímpetu directamente. Principalmente solía buscar condiciones extremas sobrecompradas y sobreestimadas, CMO también puede ser usado para buscar tendencias.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionConstantLines" xml:space="preserve">
    <value>Plot lines at user defined values.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionCorrelation" xml:space="preserve">
    <value>The correlation indicator will plot the correlation of the data series to a desired insturment. Values close to 1 indicate movement in the same direction. Values close to -1 indicate movement in opposite directions. Values near 0 indicate no correlation.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionCOT" xml:space="preserve">
    <value>Compromiso de los comerciantes</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionCurrentDayOHL" xml:space="preserve">
    <value>Traza los valores abiertos, alto, y bajos de la sesión que comienza a partir del día actual</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionDarvas" xml:space="preserve">
    <value>Las cajas Darvas fueron tomadas de las páginas del libro de Nicolas Darvas, cómo hice $2.000.000 en el mercado de valores. Las cajas se utilizan para normalizar una tendencia. Una señal de 'comprar' estaría indicada cuando el precio de la acción supera la parte superior de la caja. Una señal de 'vender' estaría indicada cuando el precio de la acciones cae por debajo de la parte inferior de la caja.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionDEMA" xml:space="preserve">
    <value>El Double Exponential Moving Average (DEMA) es una combinación de un promedio móvil exponencial simple y un promedio móvil exponencial doble</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionDisparityIndex" xml:space="preserve">
    <value>El Disparity Index mide la diferencia entre el precio y la media móvil exponencial. Un valor mayor podría sugerir un impulso alcista, mientras que un valor menor que cero podría sugerir un impulso bajista.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionDM" xml:space="preserve">
    <value>Directional Movement (DM). Este es el mismo indicador como el ADX, incluyendo los dos indicadores de movimiento direccional DI y -DI. DI y -DI miden el impulso hacia arriba y hacia abajo. Una señal de compra se genera cuando DI cruza -DI al alza. Se genera una señal de venta cuando -DI DI se cruza a la baja.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionDMI" xml:space="preserve">
    <value>Directional Movement Index. Directional Movement Index  es muy similar al Índice Welles Wilder's Relative Strength. La diferencia es el DMI utiliza períodos de tiempo variable (de 3 a 30) vs. el RSI de períodos fijos.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionDMIndex" xml:space="preserve">
    <value>El Dynamic Momentum Index es un término variable RSI. El término RSI varía de 3 a 30. El período de tiempo variable hace más sensible a los movimientos a corto plazo del RSI. El precio es el más volátil, el más corto es el período de tiempo. Aunque se interpreta de la misma manera como el RSI, pero proporciona señales antes.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionDonchianChannel" xml:space="preserve">
    <value>El Donchian Channel. El indicador Donchican Channel fue creado por Richard Donchian. Utiliza la más alto y la más bajo de un periodo de tiempo para trazar el canal.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionDoubleStochastics" xml:space="preserve">
    <value>Double Stochastics es una variación de los indicadores Stochastics desarrollados por William Blau</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionEaseOfMovement" xml:space="preserve">
    <value>El indicador Ease of Movement (EMV) enfatiza días en los cuales las acciones se mueve fácilmente y se minimiza los días en los cuales las acciones encuentra difícil moverse. Una señal de la compra es generada cuando el EMV se cruza encima del cero, una señal de la venta cuando se cruza bajo cero. Cuando el EMV se cierne alrededor del cero, entonces hay pequeños movimientos de precios y/o del alto volumen, que debe decir, el precio no se mueve fácilmente.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionEMA" xml:space="preserve">
    <value>El Exponential Moving Average es un indicador que muestra el promedial de un valor de seguridad sobre un periodo de tiempo. Cuando calcule un móvil promedial. las aplicaciones EMA pesan más para precios recientes que los SMA</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionFibonacciPivots" xml:space="preserve">
    <value>Pivotes de Fibonacci son un análisis de precio demasiado que genera apoyo potencial y niveles de resistencia multiplicando el previo van contra los valores Fibonacci entonces agregar o restar de la media del anterior alto, bajo y cierran.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionFisherTransform" xml:space="preserve">
    <value>El Fisher Transform tiene puntos decisivos agudos y distintos puntos de inflexión que ocurren en el momento oportuno.Las oscilaciones máximas que resultan son usadas para identificar inversiones de precios.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionFOSC" xml:space="preserve">
    <value>El Forecast Oscillator (FOSC)  es una extensión de la regresión lineal en función de los indicadores por Tushar Chande. Los trazos del oscilador de previsión de la diferencia entre la proyección del precio (generado por un x-período de regresión lineal) y el precio real. El oscilador está por encima de cero cuando la proyección del precio es mayor que el precio real.El oscilador está por encima de cero cuando el precio del previsión es mayor que el precio real. Por el contrario, es menos de cero si este está debajo. En el raro caso cuando el precio del previsión y el precio real son iguales, el oscilador traza cero. Los precios reales que son constantemente por debajo del precio de previsión sugieren frente a los precios más bajos. Asimismo, los precios reales que son constantemente por encima del precio de previsión sugieren precios más altos adelante. Los Comerciantes a corto plazo deben utilizar más cortos períodos de tiempo y tal vez más cómodos sobre las normas para la longitud necesaria del tiempo por encima o por debajo del precio del previsión. Los Comerciantes a largo plazo deben utilizar períodos más prolongados y tal vez estrictos para la longitud necesaria del tiempo por encima o por debajo del precio de previsión. Chande también sugiere trazar una línea media de disparidad moviendo tres días del oscilador del previsión para generar alertas tempranas de los cambios de tendencia. Cuando el oscilador pasa por debajo del umbral de disparidad, se proponen bajar los precios. Cuando el oscilador pasa por encima del umbral de disparidad, se sugieren los precios más altos.  </value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionHMA" xml:space="preserve">
    <value>El Hull Moving Average (HMA) emplea cálculos de MA cargados para ofrecer el allanamiento superior, y mucho menos retraso, sobre indicadores SMA tradicionales.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionKAMA" xml:space="preserve">
    <value>Desarrollado por Perry Kaufman, este indicador es un EMA utilizando un Efficiency Ratio para modificar la constante de fluidez, que oscila entre un mínimo de longitud a un máximo de longitud. Desde esta media móvil es adaptativo que tiende a seguir los precios mucho más de cerca que de otras MA.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionKeltnerChannel" xml:space="preserve">
    <value>El Keltner Channel es un indicador similar a Bollinger Bands. Aquí la línea media es un valor medio móvil estándar con las bandas superiores e inferiores compensadas por el SMA de la diferencia entre el nivel más alto y bajo de las barras anteriores. El multiplicador de la compensación así como el período SMA es configurable.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionKeyReversalDown" xml:space="preserve">
    <value>Devuelve un valor de 1 cuando el cierre actual es menor que el anterior cierre después de penetrar al nivel más alto de los últimos n barras.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionKeyReversalUp" xml:space="preserve">
    <value>Devuelve un valor de 1 cuando el cierre actual es superior a la del cierre previo después de penetrar en la más baja de los últimos n barras</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionLinReg" xml:space="preserve">
    <value>El Linear Reression es un indicador que "predice" el valor de un precio de cotización.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionLinRegIntercept" xml:space="preserve">
    <value>El Linear Regression Intercept provee el valor de interceptación del valor de la tendencia Linear Regression</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionLinRegSlope" xml:space="preserve">
    <value>El Linear Regression Slope provee el valor de la curva de la tendencia Linear Regaression</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionMACD" xml:space="preserve">
    <value>El MACD (Moving Average Convergence/Divergence) es un indicador que muestra la siguiente tendencia que muestra la relación entre dos valores medios móviles de precios</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionMAEnvelopes" xml:space="preserve">
    <value>Plots % envuelve alrededor de un pormedio movil </value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionMAMA" xml:space="preserve">
    <value>La MAMA (MESA Adaptive Moving Average) fue desarrollado por John Ehlers. Se adapta al movimiento de precio en una nueva y única forma. La adaptación se basa en Hilbert Transform Discriminator. La ventaja de esta caracterizan el método rápido y una media lenta decadencia. La MAMA + la FAMA (Following Adaptive Moving Average) sólo las líneas principales del mercado de cambios.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionMAX" xml:space="preserve">
    <value>The Maximum muestra el máximo de las últimas n barras.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionMcClellanOscillator" xml:space="preserve">
    <value>El Oscilador McClellan es la diferencia entre dos móviles exponenciales promediales  del avance NYSE declina propagación. El Indicador requiere índice de datos ADV y DECL</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionMFI" xml:space="preserve">
    <value>El MFI (Money Flow Index) es un indicador de ímpetu que mide la fuerza del dinero que fluye en y fuera de una seguridad.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionMIN" xml:space="preserve">
    <value>The Minimum muestra el mínimo de las últimas n barras.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionMomentum" xml:space="preserve">
    <value>El indicador Momentum mide la cantidad que el precio de seguridad ha cambiado un período dado.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionMoneyFlowOscillator" xml:space="preserve">
    <value>The Money Flow Oscillator mide la cantidad del volumen del flujo de dinero sobre un periodo específico. Un movimiento entre zonas positivas indica presión de compra mientras que un movimiento dentro de zonas negativas indica presión de venta</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionMovingAverageRibbon" xml:space="preserve">
    <value>The Moving Average Ribbon es una serie de promedios móviles en aumento.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionNBarsDown" xml:space="preserve">
    <value>Este indicador regresa a 1 cuando tenemos n de barras bajas consecutivas, por otra parte regresa a 0. Una barra abajo es definida como una barra donde el final está debajo del abierto y las barras hacen un nivel más alto de la inferior y un nivel más bajo inferior. Puede ajustar los requisitos específicos con las opciones del indicador.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionNBarsUp" xml:space="preserve">
    <value>Este indicador regresa a 1 si tenemos n de barras consecutivas para arriba, de lo contrario regresa 0. Una encima de la barra se define como una barra donde la cercana está por encima del aire libre y las barras hacen que una esté más alta y una baja mayor. Puede ajustar los requerimientos específicos con las opciones del indicador.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionNetChangeDisplay" xml:space="preserve">
    <value>Muestra el cambio neto en el gráfico.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionOBV" xml:space="preserve">
    <value>OBV (On Balance Volume) es un total que corre del volumen. Este muestra si el volumen fluye en o de una seguridad. Cuando la seguridad se cierra más alto que el final anterior, todo el volumen del día se considera el-volumen creciente. Cuando la seguridad se cierra más abajo que el final anterior, todo el volumen del día se considera el volumen decreciente.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionParabolicSAR" xml:space="preserve">
    <value>SAR parabólico según la revista Stocks and Commodities V 11:11 (477-479).</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionPFE" xml:space="preserve">
    <value>El PFE (Polarized Fractal Efficiency) es un indicador que usa la geometría fractal para determinar cómo eficazmente el precio se mueve.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionPivots" xml:space="preserve">
    <value>Los Pivotes (Pivot Points) traza los promedios de la alta, baja y cierre de una sesión anterior o grupo de sesiones anteriores. Esto se basa en los datos históricos proporcionados por su proveedor de alimentación de datos de mercado.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionPPO" xml:space="preserve">
    <value>El PPO (Percentage Price Oscillator) está basado en dos valores medios móviles expresados como un porcentaje. El PPO es encontrado restando a la MA más larga de la MA más corta y luego dividiendo la diferencia por la MA más larga.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionPriceLine" xml:space="preserve">
    <value>Muestra en las últimas líneas de oferta y demanda del gráfico</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionPriceOscillator" xml:space="preserve">
    <value>The Price Oscillator muestra la variación entre dos valores medios móviles por el precio de una seguridad.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionPriorDayOHLC" xml:space="preserve">
    <value>Traza los valores abiertos, altos, bajos y cercanos de la sesión que comienza durante el día previo.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionPsychologicalLine" xml:space="preserve">
    <value>The Psychological Line es la relación entre el número de barras de levantamiento en el número de barras especificado.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionRange" xml:space="preserve">
    <value>Cálculos del rango de una barra.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionRangeCounter" xml:space="preserve">
    <value>Muestra el recuento de rangos de una barra.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionRegressionChannel" xml:space="preserve">
    <value>La regresión lineal es usada para calcular una mejor línea adecuada para los datos de precios. Además un grupo superior e inferior es añadido calculando la desviación estándar de precios de la línea de la regresión.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionRelativeVigorIndex" xml:space="preserve">
    <value>The Relative Vigor Index mide la fuerza de una tendencia comparando un precio de cierre de los instrumentos a su gama de precios. Se basa en el hecho de que los precios tienden a cerrar más alto de lo que se abren en las tendencias, y más cerca de lo que se abren en bajistas.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionRIND" xml:space="preserve">
    <value>RIND (Range Indicator) compara la variedad de un día (alto - bajo) hasta el interdía (cerca - final anterior) variedad. Cuando la variedad de un día sea mayor que la variedad del interdía, el Indicador de la Variedad será un valor alto. Esto señala un final a la tendencia actual. Cuando el Indicador de la Variedad está a un nivel bajo, una nueva tendencia está a punto de comenzar.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionROC" xml:space="preserve">
    <value>El indicador ROC (Rate-of-Change) muestra el cambio porcentual entre el precio actual y los precio hace x-períodos.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionRSI" xml:space="preserve">
    <value>El RSI (Índice de Fuerza Relativo) es un oscilador que sigue el precio que se extiende entre 0 y 100.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionRSquared" xml:space="preserve">
    <value>The R-Squared indicator calcula qué tan bien se aproxima el precio a una línea de regresión lineal. El indicador obtiene su nombre del cálculo, que es, el cuadrado del coeficiente de correlación (referido en las matemáticas por la letra griega Rho, o r). La gama del R-cuadrado es de cero a uno.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionRSS" xml:space="preserve">
    <value>Fuerza de Extensión relativa de la extensión entre dos valores medios móviles. TASC, octubre de 2006, p. 16.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionRVI" xml:space="preserve">
    <value>El RVI (Relative Volatility Index) fue desarrollado por el Dr. Donald Dorsey como un cumplido y una confirmación del impulso  de los indicadores. Cuando se usa para confirmar otras señales, sólo comprar cuando el RVI es de más de 50 y vender sólo cuando el RVI es de menos de 50 años.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionSampleCustomRender" xml:space="preserve">
    <value>Las muestras de script para mostrar funciones OnRender ()</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionSMA" xml:space="preserve">
    <value>El SMA (Simple Moving Average) es un indicador que muestra el valor promedio de un precio de seguridad durante un período de tiempo.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionStdDev" xml:space="preserve">
    <value>Standard Deviation es una medida estadística de la volatilidad. La Standard Deviation es típicamente usada como un componente de otros indicadores, más bien que como un indicador independiente. Por ejemplo, las Bollinger Bands son calculadas añadiendo la Desviación Estándar de una seguridad a un valor medio móvil.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionStdError" xml:space="preserve">
    <value>Standard Error muestra que tan cerca giran los precios de una línea de la regresión lineal. La cercanía de los precios están a la línea de la regresión lineal, la más fuerte es la tendencia.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionStochastics" xml:space="preserve">
    <value>El Stochastic Oscillator se compone de dos líneas que oscilan entre una escala vertical de 0 a 100. El %k es la línea principal y se dibuja como una línea sólida. La segunda es la línea %D y es una media móvil de % K. La línea %D se dibuja como una línea punteada. Utilizar como un generador de señales de compra/venta, comprando cuando se mueve con rapidez sobre lo lento y vendiendo cuando se mueve con rapidez sobre lo lento.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionStochasticsFast" xml:space="preserve">
    <value>El Stochastic Oscillator se compone de dos líneas que oscilan entre una escala vertical de 0 a 100. El %k es la línea principal y se dibuja como una línea sólida. La segunda es la línea %D y es una media móvil de % K. La línea %D se dibuja como una línea punteada. Utilizar como un generador de señales de compra/venta, comprando cuando se mueve con rapidez sobre lo lento y vendiendo cuando se mueve con rapidez sobre lo lento.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionStochRSI" xml:space="preserve">
    <value>StochRSI es un oscilador similar en el cálculo a la medida estocástica, excepto en vez de valores de precios como se hayan introducido, StochRSI usa valores de RSI. StochRSI calcula la situación actual del RSI con relación al nivel más alto y valores de RSI bajos sobre un número especificado de días. La intención de esta medida, diseñada por Tushar Chande y Stanley Kroll, es proveer más información sobre la sobrecompra/sobreestimado la naturaleza del RSI. StochRSI se extiende entre 0.0 y 1.0. Se ve generalmente de los valores encima 0.8 que identifican niveles sobrecomprados y se considera que los valores bajo 0.2 indican condiciones sobreestimadas.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionSUM" xml:space="preserve">
    <value>La Suma muestra la adición de los últimss puntos de datos n.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionSwing" xml:space="preserve">
    <value>El indicador Swing traza líneas que representa la oscilación de los puntos más bajos y culminantes.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionT3" xml:space="preserve">
    <value>El T3 es un tipo de media móvil, o función de alisado. Se basa en el Dema. El T3 toma el cálculo Dema y añade un factorV que está entre cero y 1. La función resultante se denomina GD o dema generalizada. Un GD con factorV de 1 es el mismo que el Dema. Un GD con un factorV de cero es igual que un promedio móvil exponencial. El T3 utiliza típicamente un factorV de 0,7.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionTEMA" xml:space="preserve">
    <value>El TEMA es un indicador alisado. Fue diseñado por Patrick Mulloy y se describe en su artículo en la edición de enero de 1994 del análisis técnico de la revista stocks and commodities.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionTickCounter" xml:space="preserve">
    <value>El conteo de los indicadores de marcas de una barra</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionTMA" xml:space="preserve">
    <value>El TMA (Triangular Moving Average) es un valor medio móvil ponderado. Comparado con el WMA que pone más peso sobre la última barra de precios, el TMA pone más peso sobre los datos en medio del período de tiempo determinado.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionTrendLines" xml:space="preserve">
    <value>When a high swing is followed by a lower high swing a trend line high is automatically plotted. When a low swing is followed by a higher low swing, a trend line low is automatically plotted</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionTRIX" xml:space="preserve">
    <value>El TRIX (Triple Exponential Average) muestra el Precio de cambio (ROC) del porcentaje de un EMA triple. Trix oscila encima y debajo del valor cero. El indicador aplica el allanamiento triple en una tentativa de eliminar movimientos de precios insignificantes dentro de la tendencia que trata de aislar.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionTSF" xml:space="preserve">
    <value>El TSF (Time Series Forecast) calcula valores futuros probables por el precio encajando una línea de la regresión lineal sobre un número dado de barras de precios y después de esa línea adelante en el futuro. Una línea de la regresión lineal es una línea recta que está el más cerca de todos los puntos de precios dados posible. Véase también el indicador Regresión Lineal.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionTSI" xml:space="preserve">
    <value>El TSI (True Strength Index) es un indicador basado en el ímpetu, desarrollado por William Blau. Diseñado para determinar tendencias y sobreventa o condiciones de sobrecompra la TSI  es aplicable a plazos intradía, así como a largo plazo.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionUltimateOscillator" xml:space="preserve">
    <value>El Ultimate Oscillator es la suma ponderada de los tres osciladores de diferentes períodos de tiempo. Los plazos típicos son 7, 14 y 28. Los valores del rango Ultimate oscilador de cero a 100. Los valores mayores de 70 indican condiciones de sobrecompra y valores menores de 30 indican condiciones de sobreventa. También busca acuerdo/divergencia con el precio para confirmar una tendencia o señalar el final de una tendencia.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionVMA" xml:space="preserve">
    <value>El VMA (Variable Moving Average, conocido tambien como VIDYA o Variable Index Dynamic Average) es una media móvil exponencial que se ajusta automáticamente el peso de allanamiento basado en la volatilidad de las series de datos. VMA soluciona un problema con los promedios móviles. En tiempos de baja volatilidad, como cuando los precios son las tendencias, el período de tiempo promedio móvil tendría que ser más corto para ser sensible a la inevitable ruptura en la tendencia. Mientras que, en la más volátil no hay tiempos de tendencias, el período de tiempo promedio móvil debería ser mayor para filtrar los escarceos. VIDYA utiliza el indicador interno OCM de cálculos de volatilidad. Tanto el VMA y la OCM los período son ajustables.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionVOL" xml:space="preserve">
    <value>Volumen es, simplemente, el número de acciones (o contratos) negociados durante un período de tiempo determinado (por ejemplo, la hora, día, semana, mes, etc).</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionVOLMA" xml:space="preserve">
    <value>La VOLMA (Volumen Promedio móvil) representa la media móvil exponencial (EMA) de volumen.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionVolumeCounter" xml:space="preserve">
    <value>Muestra a la cuenta del volumen de cada barra</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionVolumeOscillator" xml:space="preserve">
    <value>El Volume Oscillator mide el volumen calculando la diferencia de un rápido y un lento valor medio móvil del volumen. El oscilador de volumen puede proporcionar la penetración en la fuerza o debilidad de una tendencia de precio. Un valor positivo sugiere que hay bastante apoyo del mercado para seguir conduciendo la actividad de precios en dirección de la tendencia actual. Un valor negativo sugiere que hay una carencia del apoyo, que los precios pueden comenzar a hacerse estancados o inversos.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionVolumeProfile" xml:space="preserve">
    <value>Traza un histograma horizontal del volumen por el precio.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionVolumeUpDown" xml:space="preserve">
    <value>La variación del indicador VOL (Volumen) que colorea el histograma del volumen color diferente depende si la barra actual aumenta o disminuye</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionVolumeZones" xml:space="preserve">
    <value>Las Zonas del volumen trazan un histograma horizontal que reviste un gráfico de precios. Las barras del histograma se extienden de izquierda a derecha empezando en la izquierda del gráfico. La longitud de cada barra es determinada por el total acumulativo de todas las barras del volumen para los períodos durante los cuales el precio se cayó dentro de la variedad vertical de la barra del histograma.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionVortex" xml:space="preserve">
    <value>El indicador Vortex es un oscilador utilizado para identificar tendencias. Una señal alcista se activa cuando la línea VIPlus cruza por encima de la línea de VIMinus. Una señal bajista activa cuando la línea de VIMinus cruza por encima de la línea VIPlus.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionVROC" xml:space="preserve">
    <value>El VROC (Volume Rate-of-Change) muestra si o no una tendencia del volumen está desarrollando hacia arriba o hacia abajo. Es similar al indicador ROC, pero es aplicado al volumen en su lugar</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionVWMA" xml:space="preserve">
    <value>El VWMA ( Volume-Weighted moving Average) devuelve el volumen de media móvil ponderada para el precio especificado y período. El VWMA es similar a una media móvil simple (SMA), pero cada barra de datos es ponderado por la barra de volumen. El VWMA los lugares más significativos sobre los días con el volumen más alto y el menor para los días con el volumen más bajo para el período especifico.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionWilliamsR" xml:space="preserve">
    <value>Williams %R es un indicador de ímpetu que está diseñado para identificar áreas sobrecompradas y sobreestimadas en un mercado sin tendencia.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionWMA" xml:space="preserve">
    <value>El WMA (Weighted Moving Average) es un indicador Moving Average que muestra el valor medio del precio de seguridad por el período del tiempo con el énfasis especial en las porciones más recientes del período de tiempo bajo el análisis a diferencia de la anterior.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionZigZag" xml:space="preserve">
    <value>El indicador ZigZag muestra líneas de tendencia que ignoran los cambios debajo de un nivel definido.</value>
  </data>
  <data name="NinjaScriptIndicatorDescriptionZLEMA" xml:space="preserve">
    <value>El ZLEMA (Zero-Lag Exponential Moving Average) es una variante del EMA que intenta adaptarse al retraso.</value>
  </data>
  <data name="NinjaScriptIndicatorDiff" xml:space="preserve">
    <value>Diferencia</value>
  </data>
  <data name="NinjaScriptIndicatorDisparityLine" xml:space="preserve">
    <value>Línea de disparidad</value>
  </data>
  <data name="NinjaScriptIndicatorDown" xml:space="preserve">
    <value>Abajo</value>
  </data>
  <data name="NinjaScriptIndicatorLower" xml:space="preserve">
    <value>Bajo</value>
  </data>
  <data name="NinjaScriptIndicatorMcClellanOscillatorLine" xml:space="preserve">
    <value>Línea de Oscilador de McClellan</value>
  </data>
  <data name="NinjaScriptIndicatorMiddle" xml:space="preserve">
    <value>Medio</value>
  </data>
  <data name="NinjaScriptIndicatorMoneyFlowLine" xml:space="preserve">
    <value>Money flow line</value>
  </data>
  <data name="NinjaScriptIndicatorNameADL" xml:space="preserve">
    <value>ADL</value>
  </data>
  <data name="NinjaScriptIndicatorNameADX" xml:space="preserve">
    <value>ADX</value>
  </data>
  <data name="NinjaScriptIndicatorNameADXR" xml:space="preserve">
    <value>ADXR</value>
  </data>
  <data name="NinjaScriptIndicatorNameAPZ" xml:space="preserve">
    <value>APZ</value>
  </data>
  <data name="NinjaScriptIndicatorNameAroon" xml:space="preserve">
    <value>Aroon</value>
  </data>
  <data name="NinjaScriptIndicatorNameAroonOscillator" xml:space="preserve">
    <value>Oscilador Aroon</value>
  </data>
  <data name="NinjaScriptIndicatorNameATR" xml:space="preserve">
    <value>ATR</value>
  </data>
  <data name="NinjaScriptIndicatorNameBarTimer" xml:space="preserve">
    <value>Barra temporizadora</value>
  </data>
  <data name="NinjaScriptIndicatorNameBlockVolume" xml:space="preserve">
    <value>Bloque de volumen</value>
  </data>
  <data name="NinjaScriptIndicatorNameBollinger" xml:space="preserve">
    <value>Bollinger</value>
  </data>
  <data name="NinjaScriptIndicatorNameBOP" xml:space="preserve">
    <value>BOP</value>
  </data>
  <data name="NinjaScriptIndicatorNameBuySellPressure" xml:space="preserve">
    <value>Presión compra venta</value>
  </data>
  <data name="NinjaScriptIndicatorNameBuySellVolume" xml:space="preserve">
    <value>Volumen compra venta</value>
  </data>
  <data name="NinjaScriptIndicatorNameCamarillaPivots" xml:space="preserve">
    <value>Pivotes de la camarilla</value>
  </data>
  <data name="NinjaScriptIndicatorNameCandlestickPattern" xml:space="preserve">
    <value>Patrón de vela</value>
  </data>
  <data name="NinjaScriptIndicatorNameCCI" xml:space="preserve">
    <value>CCI</value>
  </data>
  <data name="NinjaScriptIndicatorNameChaikinMoneyFlow" xml:space="preserve">
    <value>Chaikin money flow</value>
  </data>
  <data name="NinjaScriptIndicatorNameChaikinOscillator" xml:space="preserve">
    <value>Oscilador Chaikin</value>
  </data>
  <data name="NinjaScriptIndicatorNameChaikinVolatility" xml:space="preserve">
    <value>Volatilidad Chaikin</value>
  </data>
  <data name="NinjaScriptIndicatorNameChoppinessIndex" xml:space="preserve">
    <value>Indice Choppiness</value>
  </data>
  <data name="NinjaScriptIndicatorNameCMO" xml:space="preserve">
    <value>CMO</value>
  </data>
  <data name="NinjaScriptIndicatorNameConstantLines" xml:space="preserve">
    <value>Líneas constantes</value>
  </data>
  <data name="NinjaScriptIndicatorNameCorrelation" xml:space="preserve">
    <value>Correlación</value>
  </data>
  <data name="NinjaScriptIndicatorNameCOT" xml:space="preserve">
    <value>Compromiso de los comerciantes</value>
  </data>
  <data name="NinjaScriptIndicatorNameCurrentDayOHL" xml:space="preserve">
    <value>Actualidad OHL</value>
  </data>
  <data name="NinjaScriptIndicatorNameDarvas" xml:space="preserve">
    <value>Darvas</value>
  </data>
  <data name="NinjaScriptIndicatorNameDEMA" xml:space="preserve">
    <value>DEMA</value>
  </data>
  <data name="NinjaScriptIndicatorNameDisparityIndex" xml:space="preserve">
    <value>Índice de disparidad</value>
  </data>
  <data name="NinjaScriptIndicatorNameDM" xml:space="preserve">
    <value>DM</value>
  </data>
  <data name="NinjaScriptIndicatorNameDMI" xml:space="preserve">
    <value>DMI</value>
  </data>
  <data name="NinjaScriptIndicatorNameDMIndex" xml:space="preserve">
    <value>Índice DM</value>
  </data>
  <data name="NinjaScriptIndicatorNameDonchianChannel" xml:space="preserve">
    <value>Canal de Dunchian</value>
  </data>
  <data name="NinjaScriptIndicatorNameDoubleStochastics" xml:space="preserve">
    <value>Doble estocásticos</value>
  </data>
  <data name="NinjaScriptIndicatorNameEaseOfMovement" xml:space="preserve">
    <value>Facilidad de movimiento</value>
  </data>
  <data name="NinjaScriptIndicatorNameEMA" xml:space="preserve">
    <value>EMA</value>
  </data>
  <data name="NinjaScriptIndicatorNameFibonacciPivots" xml:space="preserve">
    <value>Pivotes de Fibonacci</value>
  </data>
  <data name="NinjaScriptIndicatorNameFisherTransform" xml:space="preserve">
    <value>Transformacion de Fisher</value>
  </data>
  <data name="NinjaScriptIndicatorNameFOSC" xml:space="preserve">
    <value>FOSC</value>
  </data>
  <data name="NinjaScriptIndicatorNameHMA" xml:space="preserve">
    <value>HMA</value>
  </data>
  <data name="NinjaScriptIndicatorNameKAMA" xml:space="preserve">
    <value>KAMA</value>
  </data>
  <data name="NinjaScriptIndicatorNameKelterChannel" xml:space="preserve">
    <value>Canal de Keltner</value>
  </data>
  <data name="NinjaScriptIndicatorNameKeyReversalDown" xml:space="preserve">
    <value>Inversión clave abajo</value>
  </data>
  <data name="NinjaScriptIndicatorNameKeyReversalUp" xml:space="preserve">
    <value>Inversión clave arriba</value>
  </data>
  <data name="NinjaScriptIndicatorNameLinReg" xml:space="preserve">
    <value>Lin.reg.</value>
  </data>
  <data name="NinjaScriptIndicatorNameLinRegIntercept" xml:space="preserve">
    <value>Interceptar Lin.reg</value>
  </data>
  <data name="NinjaScriptIndicatorNameLinRegSlope" xml:space="preserve">
    <value>Pendiente Lin.reg</value>
  </data>
  <data name="NinjaScriptIndicatorNameMACD" xml:space="preserve">
    <value>MACD</value>
  </data>
  <data name="NinjaScriptIndicatorNameMAEnvelopes" xml:space="preserve">
    <value>MA envelopes</value>
  </data>
  <data name="NinjaScriptIndicatorNameMAMA" xml:space="preserve">
    <value>MAMA</value>
  </data>
  <data name="NinjaScriptIndicatorNameMAX" xml:space="preserve">
    <value>MÁX</value>
  </data>
  <data name="NinjaScriptIndicatorNameMcClellanOscillator" xml:space="preserve">
    <value>Oscilador McClellan</value>
  </data>
  <data name="NinjaScriptIndicatorNameMFI" xml:space="preserve">
    <value>MFI</value>
  </data>
  <data name="NinjaScriptIndicatorNameMIN" xml:space="preserve">
    <value>MIN</value>
  </data>
  <data name="NinjaScriptIndicatorNameMomentum" xml:space="preserve">
    <value>Impulso</value>
  </data>
  <data name="NinjaScriptIndicatorNameMoneyFlowOscillator" xml:space="preserve">
    <value>Oscilador Money flow</value>
  </data>
  <data name="NinjaScriptIndicatorNameMovingAverageRibbon" xml:space="preserve">
    <value>Cinta de promedio móvil</value>
  </data>
  <data name="NinjaScriptIndicatorNameNBarsDown" xml:space="preserve">
    <value>N de barras hacia abajo</value>
  </data>
  <data name="NinjaScriptIndicatorNameNBarsUp" xml:space="preserve">
    <value>N de barra hacia arriba</value>
  </data>
  <data name="NinjaScriptIndicatorNameNetChangeDisplay" xml:space="preserve">
    <value>Muestra de diferencia neta</value>
  </data>
  <data name="NinjaScriptIndicatorNameOBV" xml:space="preserve">
    <value>OBV</value>
  </data>
  <data name="NinjaScriptIndicatorNameParabolicSAR" xml:space="preserve">
    <value>Parábolico SAR</value>
  </data>
  <data name="NinjaScriptIndicatorNamePFE" xml:space="preserve">
    <value>PFE</value>
  </data>
  <data name="NinjaScriptIndicatorNamePivots" xml:space="preserve">
    <value>Pivotes</value>
  </data>
  <data name="NinjaScriptIndicatorNamePPO" xml:space="preserve">
    <value>PPO</value>
  </data>
  <data name="NinjaScriptIndicatorNamePriceLine" xml:space="preserve">
    <value>Línea de precios</value>
  </data>
  <data name="NinjaScriptIndicatorNamePriceOscillator" xml:space="preserve">
    <value>Oscilador de precio</value>
  </data>
  <data name="NinjaScriptIndicatorNamePriorDayOHLC" xml:space="preserve">
    <value>Día previo OHLC</value>
  </data>
  <data name="NinjaScriptIndicatorNamePsychologicalLine" xml:space="preserve">
    <value>Línea psicológica</value>
  </data>
  <data name="NinjaScriptIndicatorNameRange" xml:space="preserve">
    <value>Rango</value>
  </data>
  <data name="NinjaScriptIndicatorNameRangeCounter" xml:space="preserve">
    <value>Contador del rango</value>
  </data>
  <data name="NinjaScriptIndicatorNameRegressionChannel" xml:space="preserve">
    <value>Canal de Regresión</value>
  </data>
  <data name="NinjaScriptIndicatorNameRelativeVigorIndex" xml:space="preserve">
    <value>Índice de vigor relativo</value>
  </data>
  <data name="NinjaScriptIndicatorNameRIND" xml:space="preserve">
    <value>RIND</value>
  </data>
  <data name="NinjaScriptIndicatorNameROC" xml:space="preserve">
    <value>ROC</value>
  </data>
  <data name="NinjaScriptIndicatorNameRSI" xml:space="preserve">
    <value>RSI</value>
  </data>
  <data name="NinjaScriptIndicatorNameRSquared" xml:space="preserve">
    <value>R Cuadrado</value>
  </data>
  <data name="NinjaScriptIndicatorNameRSS" xml:space="preserve">
    <value>RSS</value>
  </data>
  <data name="NinjaScriptIndicatorNameRVI" xml:space="preserve">
    <value>RVI</value>
  </data>
  <data name="NinjaScriptIndicatorNameSampleCustomRender" xml:space="preserve">
    <value>Interpretar muestra personalizada</value>
  </data>
  <data name="NinjaScriptIndicatorNameSMA" xml:space="preserve">
    <value>SMA</value>
  </data>
  <data name="NinjaScriptIndicatorNameStdDev" xml:space="preserve">
    <value>Std. dev.</value>
  </data>
  <data name="NinjaScriptIndicatorNameStdError" xml:space="preserve">
    <value>Std. error</value>
  </data>
  <data name="NinjaScriptIndicatorNameStochastics" xml:space="preserve">
    <value>Estocástico</value>
  </data>
  <data name="NinjaScriptIndicatorNameStochasticsFast" xml:space="preserve">
    <value>Estocástico rápido</value>
  </data>
  <data name="NinjaScriptIndicatorNameStochRSI" xml:space="preserve">
    <value>Stoch RSI</value>
  </data>
  <data name="NinjaScriptIndicatorNameSUM" xml:space="preserve">
    <value>SUM</value>
  </data>
  <data name="NinjaScriptIndicatorNameSwing" xml:space="preserve">
    <value>Swing</value>
  </data>
  <data name="NinjaScriptIndicatorNameT3" xml:space="preserve">
    <value>T3</value>
  </data>
  <data name="NinjaScriptIndicatorNameTEMA" xml:space="preserve">
    <value>TEMA</value>
  </data>
  <data name="NinjaScriptIndicatorNameTickCounter" xml:space="preserve">
    <value>Contador de marcas</value>
  </data>
  <data name="NinjaScriptIndicatorNameTMA" xml:space="preserve">
    <value>TMA</value>
  </data>
  <data name="NinjaScriptIndicatorNameTrendLines" xml:space="preserve">
    <value>Líneas de tendencia</value>
  </data>
  <data name="NinjaScriptIndicatorNameTRIX" xml:space="preserve">
    <value>TRIX</value>
  </data>
  <data name="NinjaScriptIndicatorNameTSF" xml:space="preserve">
    <value>TSF</value>
  </data>
  <data name="NinjaScriptIndicatorNameTSI" xml:space="preserve">
    <value>TSI</value>
  </data>
  <data name="NinjaScriptIndicatorNameUltimateOscillator" xml:space="preserve">
    <value>Oscilador final</value>
  </data>
  <data name="NinjaScriptIndicatorNameVMA" xml:space="preserve">
    <value>VMA</value>
  </data>
  <data name="NinjaScriptIndicatorNameVOL" xml:space="preserve">
    <value>VOL</value>
  </data>
  <data name="NinjaScriptIndicatorNameVOLMA" xml:space="preserve">
    <value>VOLMA</value>
  </data>
  <data name="NinjaScriptIndicatorNameVolumeCounter" xml:space="preserve">
    <value>Contador de Volumen</value>
  </data>
  <data name="NinjaScriptIndicatorNameVolumeOscillator" xml:space="preserve">
    <value>Oscilador de volumen</value>
  </data>
  <data name="NinjaScriptIndicatorNameVolumeProfile" xml:space="preserve">
    <value>Perfil de volumen</value>
  </data>
  <data name="NinjaScriptIndicatorNameVolumesZones" xml:space="preserve">
    <value>Zonas de volumen</value>
  </data>
  <data name="NinjaScriptIndicatorNameVolumeUpDown" xml:space="preserve">
    <value>Volumen arriba hacia abajo</value>
  </data>
  <data name="NinjaScriptIndicatorNameVortex" xml:space="preserve">
    <value>Vortex</value>
  </data>
  <data name="NinjaScriptIndicatorNameVROC" xml:space="preserve">
    <value>VROC</value>
  </data>
  <data name="NinjaScriptIndicatorNameVWMA" xml:space="preserve">
    <value>VWMA</value>
  </data>
  <data name="NinjaScriptIndicatorNameWilliamsR" xml:space="preserve">
    <value>Williams R</value>
  </data>
  <data name="NinjaScriptIndicatorNameWMA" xml:space="preserve">
    <value>WMA</value>
  </data>
  <data name="NinjaScriptIndicatorNameZigZag" xml:space="preserve">
    <value>Zig zag</value>
  </data>
  <data name="NinjaScriptIndicatorNameZLEMA" xml:space="preserve">
    <value>ZLEMA</value>
  </data>
  <data name="NinjaScriptIndicatorNeutral" xml:space="preserve">
    <value>Neutral</value>
  </data>
  <data name="NinjaScriptIndicatorOverbought" xml:space="preserve">
    <value>Sobrecompra</value>
  </data>
  <data name="NinjaScriptIndicatorOverBoughtLine" xml:space="preserve">
    <value>Sobre la línea comprada</value>
  </data>
  <data name="NinjaScriptIndicatorOversold" xml:space="preserve">
    <value>Sobreventa</value>
  </data>
  <data name="NinjaScriptIndicatorOverSoldLine" xml:space="preserve">
    <value>Linea sobre vendida</value>
  </data>
  <data name="NinjaScriptIndicatorRelativeVigorIndex" xml:space="preserve">
    <value>Índice de vigor relativo</value>
  </data>
  <data name="NinjaScriptIndicatorSignal" xml:space="preserve">
    <value>Señal</value>
  </data>
  <data name="NinjaScriptIndicatorUp" xml:space="preserve">
    <value>Arriba</value>
  </data>
  <data name="NinjaScriptIndicatorUpper" xml:space="preserve">
    <value>Superior</value>
  </data>
  <data name="NinjaScriptIndicatorVIMinus" xml:space="preserve">
    <value>VIMinus</value>
  </data>
  <data name="NinjaScriptIndicatorVIPlus" xml:space="preserve">
    <value>VIPlus</value>
  </data>
  <data name="NinjaScriptIndicatorVisualGroup" xml:space="preserve">
    <value>Visual</value>
  </data>
  <data name="NinjaScriptIndicatorZeroLine" xml:space="preserve">
    <value>Línea cero</value>
  </data>
  <data name="NinjaScriptIsVisibleOnlyFocused" xml:space="preserve">
    <value>Visible sólo cuando está en foco</value>
  </data>
  <data name="NinjaScriptLine" xml:space="preserve">
    <value>Línea</value>
  </data>
  <data name="NinjaScriptLines" xml:space="preserve">
    <value>Líneas</value>
  </data>
  <data name="NinjaScriptLoadingData" xml:space="preserve">
    <value>  Cargando datos...{0}</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionAskPrice" xml:space="preserve">
    <value>Precio de venta actual</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionAskSize" xml:space="preserve">
    <value>Tamaño de venta actual</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionAverageDailyVolume" xml:space="preserve">
    <value>Promedio del volumen diario</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionBeta" xml:space="preserve">
    <value>Una medida de la volatilidad o riesgo sistemático, de una seguridad o una carpeta en comparación para el mercado como un todo.</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionBidAskSpread" xml:space="preserve">
    <value>La diferencia entre precios actual de oferta y venta</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionBidPrice" xml:space="preserve">
    <value>Actual precio de oferta</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionBidSize" xml:space="preserve">
    <value>Actual tamaño de oferta</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionCalendarYearHigh" xml:space="preserve">
    <value>Alto precio para el año calendario actual</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionCalendarYearHighDate" xml:space="preserve">
    <value>Fecha de alto precio para el año calendario ocurrido</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionCalendarYearLow" xml:space="preserve">
    <value>Precio bajo para el año calendario actual</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionCalendarYearLowDate" xml:space="preserve">
    <value>Fecha de precio bajo para el año calendario actual</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionChartMini" xml:space="preserve">
    <value>Esta columna del Market Analyzer traza un mini gráfico por las propiedades de entrada.</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionChartNetChange" xml:space="preserve">
    <value>Esta columna del Market Analyzer traza un mini gráfico por las propiedades de entrada.</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionCurrentRatio" xml:space="preserve">
    <value>Activo circulante dividido entre el pasivo circulante</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionDailyHigh" xml:space="preserve">
    <value>Alta de hoy</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionDailyLow" xml:space="preserve">
    <value>Baja de hoy</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionDailyVolume" xml:space="preserve">
    <value>Volumen de hoy</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionDaysUntilRollover" xml:space="preserve">
    <value>Muestra cuántos días de rollover en próximo contrato</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionDescription" xml:space="preserve">
    <value>Descripción de instrumento</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionDividendAmount" xml:space="preserve">
    <value>Cantidad de Dividendo</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionDividendPayDate" xml:space="preserve">
    <value>Fecha de pago de dividendo</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionDividendYield" xml:space="preserve">
    <value>La proporción que muestra cuánto una compañía gasta en dividendos cada año con relación a su precio de las acciones.</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionEarningsPerShare" xml:space="preserve">
    <value>Parte de las ganancias de la compañía asignadas a cada una de sus acciones comunes.</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionFiveYearsGrowthPercentage" xml:space="preserve">
    <value>Porcentanje de crecimiento de cinco años</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionHigh52Weeks" xml:space="preserve">
    <value>Alto de las últimas 52 semanas</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionHigh52WeeksDate" xml:space="preserve">
    <value>Fecha en la que el alto precio se produjeron en la últimas 52 semanas</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionHistoricalVolatility" xml:space="preserve">
    <value>Volatilidad realizada de un instrumento con el tiempo</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionInstrument" xml:space="preserve">
    <value>Nombre de instrumento</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionLastClose" xml:space="preserve">
    <value>Cerrar la última sesión de trading</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionLastPrice" xml:space="preserve">
    <value>Último precio de trading</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionLastSize" xml:space="preserve">
    <value>Último tamaño de trading</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionLow52Weeks" xml:space="preserve">
    <value>Bajo en las últimas 52 semanas</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionLow52WeeksDate" xml:space="preserve">
    <value>Fecha en la que el precio bajo se produjeron en las últimas 52 semanas</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionMarketCap" xml:space="preserve">
    <value>Capitalización de mercado. El valor total de acciones emitidas</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionMarketPrice" xml:space="preserve">
    <value>Precio actual y cambio neto</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionNetChange" xml:space="preserve">
    <value>Precio actual comparado con el último precio de cierre</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionNetChangeMaxDown" xml:space="preserve">
    <value>Corriente baja en comparación con el último precio de cierre</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionNetChangeMaxUp" xml:space="preserve">
    <value>Corriente alta en comparación con el último precio de cierre</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionNextYearsEarningsPerShare" xml:space="preserve">
    <value>Proyección de utilidades por acción</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionNotes" xml:space="preserve">
    <value>Usuario campo definible. Doble clic en columna de notas aplicadas para crear o corregir notas.</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionOpening" xml:space="preserve">
    <value>Precio de apertura para sesión trading actual</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionOpenInterest" xml:space="preserve">
    <value>El número total de opciones y/o contratos de futuros que no están cerrados o entregados en un día en particular</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionPercentHeldByInstitutions" xml:space="preserve">
    <value>Porcentaje de acciones por instituciones</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionPositionAvgPrice" xml:space="preserve">
    <value>Precio de entrada promedio de la situación actual</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionPositionSize" xml:space="preserve">
    <value>Tamaño de la posicion actual</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionPriceEarningsRatio" xml:space="preserve">
    <value>Precio actual de las acciones en comparación con su cuota de ingresos.</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionProfitLoss" xml:space="preserve">
    <value>Total de ganancias y pérdidas no realizadas y realizadas.</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionRealizedProfitLoss" xml:space="preserve">
    <value>Ganancias o perdidas realizadas</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionRevenuePerShare" xml:space="preserve">
    <value>La Proporción de ingresos para compartir precio</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionSettlement" xml:space="preserve">
    <value>Precio de liquidación de hoy</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionSharesOutstanding" xml:space="preserve">
    <value>Número de acciones sobresalientes</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionShortInterest" xml:space="preserve">
    <value>Cantidad de partes comunes que los inversionistas han vendido short, pero todavía no cubrieron o liquidaron.</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionShortInterestRatio" xml:space="preserve">
    <value>Interés Short dividido por volumen diario promedial</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionTimeLastTick" xml:space="preserve">
    <value>Tiempo que se produjo el último trade</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionTradedContracts" xml:space="preserve">
    <value>Los contratos completados de hoy</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionTSTrend" xml:space="preserve">
    <value>Este columndisplays una barra de color que representa las señales entrantes con los mismos colores que usa la ventana de T &amp; S</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionUnrealizedProfitLoss" xml:space="preserve">
    <value>Beneficios o perdidas para la posición actual</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnDescriptionVwap" xml:space="preserve">
    <value>Volumen ponderado del precio promedio</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameAskPrice" xml:space="preserve">
    <value>Precio compra</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameAskSize" xml:space="preserve">
    <value>Ask size</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameAverageDailyVolume" xml:space="preserve">
    <value>Promedio del volumen diario</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameBeta" xml:space="preserve">
    <value>Beta</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameBidAskSpread" xml:space="preserve">
    <value>Bid ask spread</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameBidPrice" xml:space="preserve">
    <value>Precio de oferta</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameBidSize" xml:space="preserve">
    <value>Bid size</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameCalendarYearHigh" xml:space="preserve">
    <value>Altura de año calendario</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameCalendarYearHighDate" xml:space="preserve">
    <value>Fecha de nivel de altura anual</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameCalendarYearLow" xml:space="preserve">
    <value>Nivel bajo anual</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameCalendarYearLowDate" xml:space="preserve">
    <value>Fecha de nivel bajo anual</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameChartMini" xml:space="preserve">
    <value>Grafico - Mini</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameChartNetChange" xml:space="preserve">
    <value>Grafico - cambio neto</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameCurrentRatio" xml:space="preserve">
    <value>Proporción actual</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameDailyHigh" xml:space="preserve">
    <value>Alta diaria</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameDailyLow" xml:space="preserve">
    <value>bajo diario</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameDailyVolume" xml:space="preserve">
    <value>Volumen diario</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameDaysUntilRollover" xml:space="preserve">
    <value>Días hasta el rollover</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameDescription" xml:space="preserve">
    <value>Descripción</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameDividendAmount" xml:space="preserve">
    <value>Cantidad de Dividendo</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameDividendPayDate" xml:space="preserve">
    <value>Fecha de pago de dividendo</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameDividendYield" xml:space="preserve">
    <value>Rentabilidad por dividendo</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameEarningsPerShare" xml:space="preserve">
    <value>Ganancias por acción</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameFiveYearsGrowthPercentage" xml:space="preserve">
    <value>Porcentanje de crecimiento de cinco años</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameHigh52Weeks" xml:space="preserve">
    <value>Altura de 52 semanas</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameHigh52WeeksDate" xml:space="preserve">
    <value>Alta fecha de 52 semanas</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameHistoricalVolatility" xml:space="preserve">
    <value>Hisorial de Volatilidad</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameInstrument" xml:space="preserve">
    <value>Instrumento</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameLastClose" xml:space="preserve">
    <value>El último cierre</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameLastPrice" xml:space="preserve">
    <value>Última oferta</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameLastSize" xml:space="preserve">
    <value>Último tamaño</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameLow52Weeks" xml:space="preserve">
    <value>Bajo de 52 semanas</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameLow52WeeksDate" xml:space="preserve">
    <value>Fecha de bajas de 52 semanas</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameMarketCap" xml:space="preserve">
    <value>Capitalización de mercado</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameMarketPrice" xml:space="preserve">
    <value>Precio de mercado</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameNetChange" xml:space="preserve">
    <value>Cambio neto</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameNetChangeMaxDown" xml:space="preserve">
    <value>Net change max down</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameNetChangeMaxUp" xml:space="preserve">
    <value>Net change max up</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameNextYearsEarningsPerShare" xml:space="preserve">
    <value>El próximo año de Ganancias por acción</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameNotes" xml:space="preserve">
    <value>Notas</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameOpening" xml:space="preserve">
    <value>Apertura</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameOpenInterest" xml:space="preserve">
    <value>Intereses de apertura</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNamePercentHeldByInstitutions" xml:space="preserve">
    <value>Porcentanje detenido por instituto</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNamePositionAvgPrice" xml:space="preserve">
    <value>Precio promedio de posición</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNamePositionSize" xml:space="preserve">
    <value>Tamaño de posición</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNamePriceEarningsRatio" xml:space="preserve">
    <value>Porpocion de ganancias de precio</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameProfitLoss" xml:space="preserve">
    <value>Perdida de ganancias</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameRealizedProfitLoss" xml:space="preserve">
    <value>Perdida de ganancias realizadas</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameRevenuePerShare" xml:space="preserve">
    <value>Ingresos por participación</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameSettlement" xml:space="preserve">
    <value>Precio de liquidación</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameSharesOutstanding" xml:space="preserve">
    <value>Acciones pendientes</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameShortInterest" xml:space="preserve">
    <value>Interes Short</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameShortInterestRatio" xml:space="preserve">
    <value>Interes proporcional Short</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameTimeLastTick" xml:space="preserve">
    <value>Tiempo de ultima marca</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameTradedContracts" xml:space="preserve">
    <value>Contratos comerciales</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameTSTrend" xml:space="preserve">
    <value>Tendencia de T &amp; S</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameUnrealizedProfitLoss" xml:space="preserve">
    <value>Perdida de ganancias no realizadas</value>
  </data>
  <data name="NinjaScriptMarketAnalyzerColumnNameVwap" xml:space="preserve">
    <value>VWAP</value>
  </data>
  <data name="NinjaScriptNumberOfRows" xml:space="preserve">
    <value>Filas</value>
  </data>
  <data name="NinjaScriptOnBarCloseError" xml:space="preserve">
    <value>{0} se basa en la compra/Venta de marcas actualizadas calcular en cada marca</value>
  </data>
  <data name="NinjaScriptOnPriceChangeError" xml:space="preserve">
    <value>{0} se basa en el volumen actualizado esperan calcular 'en cada marca or en barra cerrada</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxAvgMfe" xml:space="preserve">
    <value>Máx. pmd de excursión favorable</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxAvgMfeLong" xml:space="preserve">
    <value>Máx. pmd de excursión favorable(Long)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxAvgMfeShort" xml:space="preserve">
    <value>Máx. pmd de excursión favorable(Short)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxAvgProfit" xml:space="preserve">
    <value>Máx pmd de ganancia</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxAvgProfitLong" xml:space="preserve">
    <value>Máx pmd de ganancia (Long)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxAvgProfitShort" xml:space="preserve">
    <value>Máx pmd de ganancia (Short)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxNetProfit" xml:space="preserve">
    <value>Máx ganancia neta</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxNetProfitLong" xml:space="preserve">
    <value>Máx ganancia neta(Long)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxNetProfitShort" xml:space="preserve">
    <value>Máx ganancia neta( short)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxPercentProfitable" xml:space="preserve">
    <value>Máx % rentable</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxPercentProfitableLong" xml:space="preserve">
    <value>Máx % rentable(Long)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxPercentProfitableShort" xml:space="preserve">
    <value>Máx % rentable(Short)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxProbablity" xml:space="preserve">
    <value>Máx. probabilidad</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxProbablityLong" xml:space="preserve">
    <value>Máx. probabilidad (Long)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxProbablityShort" xml:space="preserve">
    <value>Máx. probabilidad (Short)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxProfitFactor" xml:space="preserve">
    <value>Máx. factor de beneficio</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxProfitFactorLong" xml:space="preserve">
    <value>Máx. factor de beneficio (Long)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxProfitFactorShort" xml:space="preserve">
    <value>Máx. factor de beneficio (Short)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxR2" xml:space="preserve">
    <value>Max. R^2</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxR2Long" xml:space="preserve">
    <value>Max. R^2 (largo)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxR2Short" xml:space="preserve">
    <value>Max. R^2 (Short)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxSharpeRatio" xml:space="preserve">
    <value>Max. Sharpe ratio</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxSharpeRatioLong" xml:space="preserve">
    <value>Max. Sharpe ratio (Long)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxSharpeRatioShort" xml:space="preserve">
    <value>Max. Sharpe ratio (Short)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxSortinoRatio" xml:space="preserve">
    <value>Max. Sortino ratio</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxSortinoRatioLong" xml:space="preserve">
    <value>Max. Sortino ratio (Long)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxSortinoRatioShort" xml:space="preserve">
    <value>Max. Sortino ratio (Short)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxStrength" xml:space="preserve">
    <value>Fuerza Máxima</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxStrengthLong" xml:space="preserve">
    <value>Fuerza Máxima(Long)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxStrengthShort" xml:space="preserve">
    <value>Fuerza Máxima(Short)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxUlcerRatio" xml:space="preserve">
    <value>Max. Ulcer ratio</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxUlcerRatioLong" xml:space="preserve">
    <value>Max. Ulcer ratio (Long)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxUlcerRatioShort" xml:space="preserve">
    <value>Max. Ulcer ratio (Short)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxWinLossRatio" xml:space="preserve">
    <value>Max. gana/pierde ratio</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxWinLossRatioLong" xml:space="preserve">
    <value>Max. win/loss ratio (Long)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMaxWinLossRatioShort" xml:space="preserve">
    <value>Max. win/loss ratio (Short)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMinAvgMae" xml:space="preserve">
    <value>Mín. pmd de excursión adversa</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMinAvgMaeLong" xml:space="preserve">
    <value>Mín. pmd de excursión adversa(Long)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMinAvgMaeShort" xml:space="preserve">
    <value>Mín. pmd de excursión adversa(Short)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMinDrawDown" xml:space="preserve">
    <value>Mín. reducción</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMinDrawDownLong" xml:space="preserve">
    <value>Mín. reducción(Long)</value>
  </data>
  <data name="NinjaScriptOptimizationFitnessNameMinDrawDownShort" xml:space="preserve">
    <value>Mín. reducción(Short)</value>
  </data>
  <data name="NinjaScriptOptimizerDefault" xml:space="preserve">
    <value>Defecto</value>
  </data>
  <data name="NinjaScriptOptimizerGenetic" xml:space="preserve">
    <value>Genético</value>
  </data>
  <data name="NinjaScriptParameters" xml:space="preserve">
    <value>Parámetros</value>
  </data>
  <data name="NinjaScriptStrategyDescriptionSampleATMStrategy" xml:space="preserve">
    <value>Gestión de Muestra de la estrategia de Trading Avanzado. </value>
  </data>
  <data name="NinjaScriptStrategyDescriptionSampleCustomPerformance" xml:space="preserve">
    <value>Muestra para demostrar el uso de rendimiento personalizado</value>
  </data>
  <data name="NinjaScriptStrategyDescriptionSampleFramework" xml:space="preserve">
    <value>Esta estrategia muestra algunas de las capacidades del marco de desarrollo Ninja Trader</value>
  </data>
  <data name="NinjaScriptStrategyDescriptionSampleMACrossOver" xml:space="preserve">
    <value>Simple moving average cross over strategy.</value>
  </data>
  <data name="NinjaScriptStrategyDescriptionSampleMultiInstrument" xml:space="preserve">
    <value>Muestra de estrategia multi-intrumental</value>
  </data>
  <data name="NinjaScriptStrategyDescriptionSampleMultiTimeFrame" xml:space="preserve">
    <value>Muestra de estrategia multi- marco de tiempo</value>
  </data>
  <data name="NinjaScriptStrategyGenerator" xml:space="preserve">
    <value>Generador de estrategia</value>
  </data>
  <data name="NinjaScriptStrategyGeneratorCandleStickPatternPrompt" xml:space="preserve">
    <value>1 patrón de vela|{0} patrones de vela|Añadir patrón de vela...|Configurar patrón de vela...|Configurar patrones de vela...</value>
  </data>
  <data name="NinjaScriptStrategyGeneratorEntries" xml:space="preserve">
    <value>Condiciones de entrada</value>
  </data>
  <data name="NinjaScriptStrategyGeneratorEntriesOrExits" xml:space="preserve">
    <value>Necesitó al menos una orden de entrada con condición de salida</value>
  </data>
  <data name="NinjaScriptStrategyGeneratorIndicatorException" xml:space="preserve">
    <value>Excepción a la expresión: {0} {1}</value>
  </data>
  <data name="NinjaScriptStrategyGeneratorIndicatorsPrompt" xml:space="preserve">
    <value>1 indicador | indicadores de {0} | Añadir indicador... | Configurar indicador... | Configurar indicadores.</value>
  </data>
  <data name="NinjaScriptStrategyGeneratorPeformance" xml:space="preserve">
    <value>Rendimiento para {0} = {1}</value>
  </data>
  <data name="NinjaScriptStrategyGeneratorProperties" xml:space="preserve">
    <value>Propiedades Generadas AI</value>
  </data>
  <data name="NinjaScriptStrategyGeneratorTerminated" xml:space="preserve">
    <value>Generador de estrategia terminada en '{0}' después de generaciones de {1}, puesto que no había ninguna mejora de rendimiento para las generaciones {2}</value>
  </data>
  <data name="NinjaScriptStrategyGeneratorUseCandleStickPattern" xml:space="preserve">
    <value>Esquema vela</value>
  </data>
  <data name="NinjaScriptStrategyGeneratorUseIndicators" xml:space="preserve">
    <value>Indicadores</value>
  </data>
  <data name="NinjaScriptStrategyNameSampleATMStrategy" xml:space="preserve">
    <value>Muestra de estrategia ATM</value>
  </data>
  <data name="NinjaScriptStrategyNameSampleCustomPerformance" xml:space="preserve">
    <value>Muestra de rendimiento personalizado</value>
  </data>
  <data name="NinjaScriptStrategyNameSampleFramework" xml:space="preserve">
    <value>Muestra de marco</value>
  </data>
  <data name="NinjaScriptStrategyNameSampleMACrossOver" xml:space="preserve">
    <value>Muestra de Transición de MA</value>
  </data>
  <data name="NinjaScriptStrategyNameSampleMultiInstrument" xml:space="preserve">
    <value>Muestra multi instrumental</value>
  </data>
  <data name="NinjaScriptStrategyNameSampleMultiTimeFrame" xml:space="preserve">
    <value>Muestra multi marco</value>
  </data>
  <data name="NinjaScriptStrategyParameters" xml:space="preserve">
    <value>Parámetros de estrategia</value>
  </data>
  <data name="NinjaScriptSuperDomColumnApq" xml:space="preserve">
    <value>APQ</value>
  </data>
  <data name="NinjaScriptSuperDomColumnBaseInitializeBarsPoolError" xml:space="preserve">
    <value>Error al cargar las series de barras por {0}/{1}: {2}</value>
  </data>
  <data name="NinjaScriptSuperDomColumnDescriptionApq" xml:space="preserve">
    <value>The Approximate Position In Queue (APQ) le da una estimación conservada de la posición actual en la fila para ordenes que usted ha reemplazado.</value>
  </data>
  <data name="NinjaScriptSuperDomColumnDescriptionNotes" xml:space="preserve">
    <value>La columna de Notes proporciona la entrada de texto en los puntos de precio directamente en el SuperDOM y se puede utilizar para añadir notas por nivel de precio.</value>
  </data>
  <data name="NinjaScriptSuperDomColumnDescriptionPnl" xml:space="preserve">
    <value>La columna Ganancia o perdida (PnL) reproducirá la ganancia y perdida potencial en cada punto de precio una vez que usted esté en un trade.</value>
  </data>
  <data name="NinjaScriptSuperDomColumnDescriptionVolume" xml:space="preserve">
    <value>La columna de Volumen se usará el historial de datos de marca para reproducir el número de contratos de trade en cada nivel de precio. Usted puede colorear opcionalmente las barras basándose si el negocio ocurriera en la compra y venta.</value>
  </data>
  <data name="NinjaScriptSuperDomColumnNotes" xml:space="preserve">
    <value>Notas</value>
  </data>
  <data name="NinjaScriptSuperDomColumnProfitAndLoss" xml:space="preserve">
    <value>PnL</value>
  </data>
  <data name="NinjaScriptSuperDomColumnVolume" xml:space="preserve">
    <value>Volumen</value>
  </data>
  <data name="NinjaScriptTileError" xml:space="preserve">
    <value>Error cargando herramientra de {0} : {1}</value>
  </data>
  <data name="NinjaScriptYOffset" xml:space="preserve">
    <value>Desplazamiento de píxeles en Y</value>
  </data>
  <data name="NumberOfCotPlots" xml:space="preserve">
    <value>Número de parcelas COT</value>
  </data>
  <data name="NumberOfTrendLines" xml:space="preserve">
    <value>Número de líneas de tendencia</value>
  </data>
  <data name="NumStdDev" xml:space="preserve">
    <value>Número de derivaciones estándar</value>
  </data>
  <data name="OffsetMultiplier" xml:space="preserve">
    <value>Multiplicador de compensación</value>
  </data>
  <data name="OldTrendsOpacity" xml:space="preserve">
    <value>Opacidad de las viejas tendencias</value>
  </data>
  <data name="Opacity" xml:space="preserve">
    <value>Opacidad</value>
  </data>
  <data name="PathCapMode_Arrow" xml:space="preserve">
    <value>Flecha</value>
  </data>
  <data name="PathCapMode_Line" xml:space="preserve">
    <value>Línea</value>
  </data>
  <data name="PathToolCapMode_Arrow" xml:space="preserve">
    <value>Flecha</value>
  </data>
  <data name="PathToolCapMode_Line" xml:space="preserve">
    <value>Línea</value>
  </data>
  <data name="PerformanceMetricSampleCumProfit" xml:space="preserve">
    <value>Muestra de acumulación de rendimiento de la ganancia métrica</value>
  </data>
  <data name="Period" xml:space="preserve">
    <value>Periodo</value>
  </data>
  <data name="PeriodD" xml:space="preserve">
    <value>Periodo D</value>
  </data>
  <data name="PeriodK" xml:space="preserve">
    <value>Periodo K</value>
  </data>
  <data name="PeriodQ" xml:space="preserve">
    <value>Periodo Q</value>
  </data>
  <data name="PFEZero" xml:space="preserve">
    <value>Cero</value>
  </data>
  <data name="PiviotsDailyBarsError" xml:space="preserve">
    <value>Intradia o barra diaria debe ser usada para Pivotes</value>
  </data>
  <data name="PiviotsDailyDataError" xml:space="preserve">
    <value>Datos diarios insuficientes para calcular Pivotes</value>
  </data>
  <data name="PiviotsInsufficentDataError" xml:space="preserve">
    <value>Historial de datos insuficientes para calcular pivotes, Incrementar apariencia de cuadro de un periodo de amortización (Días para cargar, Barras para cargar, o Inicio de fecha)</value>
  </data>
  <data name="PiviotsPeriodTypeError" xml:space="preserve">
    <value>Tipo de periodo necesitará ser diario con valor de 1</value>
  </data>
  <data name="PiviotsWeeklyBarsError" xml:space="preserve">
    <value>Barras diarias requiere el uso de rangos de pivotes semanales y mensuales</value>
  </data>
  <data name="PivotRange" xml:space="preserve">
    <value>Rango de Pivote</value>
  </data>
  <data name="PivotRange_Daily" xml:space="preserve">
    <value>Diario</value>
  </data>
  <data name="PivotRange_Monthly" xml:space="preserve">
    <value>Mensual</value>
  </data>
  <data name="PivotRange_Weekly" xml:space="preserve">
    <value>Semanal</value>
  </data>
  <data name="PivotsPP" xml:space="preserve">
    <value>PP</value>
  </data>
  <data name="PivotsR1" xml:space="preserve">
    <value>R1</value>
  </data>
  <data name="PivotsR2" xml:space="preserve">
    <value>R2</value>
  </data>
  <data name="PivotsR3" xml:space="preserve">
    <value>R3</value>
  </data>
  <data name="PivotsR4" xml:space="preserve">
    <value>R4</value>
  </data>
  <data name="PivotsS1" xml:space="preserve">
    <value>S1</value>
  </data>
  <data name="PivotsS2" xml:space="preserve">
    <value>S2</value>
  </data>
  <data name="PivotsS3" xml:space="preserve">
    <value>S3</value>
  </data>
  <data name="PivotsS4" xml:space="preserve">
    <value>S4</value>
  </data>
  <data name="PlotCurrentValue" xml:space="preserve">
    <value>Valor actual de sólo el gráfico</value>
  </data>
  <data name="PositiveColor" xml:space="preserve">
    <value>Color positivo</value>
  </data>
  <data name="PPOSmoothed" xml:space="preserve">
    <value>Lisado</value>
  </data>
  <data name="PriceLinePlotAsk" xml:space="preserve">
    <value>Línea Ask</value>
  </data>
  <data name="PriceLinePlotBid" xml:space="preserve">
    <value>Línea de oferta</value>
  </data>
  <data name="PriceLinePlotLast" xml:space="preserve">
    <value>Última línea</value>
  </data>
  <data name="PriorDayOHLCClose" xml:space="preserve">
    <value>Cierre anterior</value>
  </data>
  <data name="PriorDayOHLCHigh" xml:space="preserve">
    <value>Altura anterior</value>
  </data>
  <data name="PriorDayOHLCIntradayError" xml:space="preserve">
    <value>PriorDayOHLC sólo funciona en intervalos intradía</value>
  </data>
  <data name="PriorDayOHLCLow" xml:space="preserve">
    <value>Baja anterior</value>
  </data>
  <data name="PriorDayOHLCOpen" xml:space="preserve">
    <value>Apertura anterior</value>
  </data>
  <data name="RangeCounterBarError" xml:space="preserve">
    <value>Contador de rango sólo funciona en las barras de rango</value>
  </data>
  <data name="RangeCounterRemaing" xml:space="preserve">
    <value>El rango restante = {0}</value>
  </data>
  <data name="RangerCounterCount" xml:space="preserve">
    <value>Rango de cuenta= {0}</value>
  </data>
  <data name="RangeValue" xml:space="preserve">
    <value>Rango de valor</value>
  </data>
  <data name="RegionHighlightDirection_Horizontal" xml:space="preserve">
    <value>Horizontal</value>
  </data>
  <data name="RegionHighlightDirection_Vertical" xml:space="preserve">
    <value>Vertical</value>
  </data>
  <data name="RegressionChannelType_Segment" xml:space="preserve">
    <value>Segmento</value>
  </data>
  <data name="RegressionChannelType_StandardDeviation" xml:space="preserve">
    <value>Distancia de desviación Estándar</value>
  </data>
  <data name="ROCPeriod" xml:space="preserve">
    <value>Tarifa del periodo de cambio</value>
  </data>
  <data name="RVISignalLine" xml:space="preserve">
    <value>Línea de señal</value>
  </data>
  <data name="SampleAddOnDescription" xml:space="preserve">
    <value>Muestra de Descripción del nombre</value>
  </data>
  <data name="SampleAddOnHiThere" xml:space="preserve">
    <value>Hola!</value>
  </data>
  <data name="SampleAddOnName" xml:space="preserve">
    <value>Muestra del nombre de AddOn</value>
  </data>
  <data name="SampleCumProfit" xml:space="preserve">
    <value>Muestra del beneficio acumulativo</value>
  </data>
  <data name="SampleCumProfitDescription" xml:space="preserve">
    <value>Beneficio acumulativo como una muestra del rendimiento métrica personalizada</value>
  </data>
  <data name="SampleCustomPlotLowerRightCorner" xml:space="preserve">
    <value>Esquina inferior derecha</value>
  </data>
  <data name="SampleCustomPlotUpperLeftCorner" xml:space="preserve">
    <value>Esquina superior izquierda</value>
  </data>
  <data name="SelectPattern" xml:space="preserve">
    <value>Seleccionar patrón</value>
  </data>
  <data name="SelectPatternDescription" xml:space="preserve">
    <value>Elija un patrón para detectar</value>
  </data>
  <data name="SendAlerts" xml:space="preserve">
    <value>Enviar alertas</value>
  </data>
  <data name="SendAlertsDescription" xml:space="preserve">
    <value>Seleccione "verdadero" para enviar menasajes de alerta a la ventana de alertas</value>
  </data>
  <data name="ShareArgsException" xml:space="preserve">
    <value>Hubo un problema llamar a OnShare con argumentos: {0}</value>
  </data>
  <data name="ShareBadGatewayError" xml:space="preserve">
    <value>El proveedor Share devolvió un error de Gateway: {0}</value>
  </data>
  <data name="ShareBadRequestError" xml:space="preserve">
    <value>El proveedor de Share devolvió un error de solicitud: {0}</value>
  </data>
  <data name="ShareException" xml:space="preserve">
    <value>A WebException fue lanzado. Estado : {0} Mensaje: {0}</value>
  </data>
  <data name="ShareFacebookCouldNotRetrieveUser" xml:space="preserve">
    <value>El usuario no puede ser encontrado</value>
  </data>
  <data name="ShareFacebookCouldNotVerifyToken" xml:space="preserve">
    <value>Facebook no puede verificar el símbolo para este usuario</value>
  </data>
  <data name="ShareFacebookNoResult" xml:space="preserve">
    <value>Falló para recibir respuesta de Facebook</value>
  </data>
  <data name="ShareFacebookPermissionDenied" xml:space="preserve">
    <value>Los permisos necesarios de Facebook fueron negados por el usuario</value>
  </data>
  <data name="ShareFacebookScopesNotFound" xml:space="preserve">
    <value>No puede verificar los permisos de Facebook</value>
  </data>
  <data name="ShareFacebookSentSuccessfully" xml:space="preserve">
    <value>{0} - Se envió el correo exitosamente</value>
  </data>
  <data name="ShareForbidden" xml:space="preserve">
    <value>El proveedor Share devolvió un mensaje prohibido: {0}</value>
  </data>
  <data name="ShareGatewayTimeoutError" xml:space="preserve">
    <value>El proveedor devolvió un error de Gateway Timeout: {0}</value>
  </data>
  <data name="ShareImageNoLongerExists" xml:space="preserve">
    <value>La Imagen en la dirección {0} no puede ser encontrada</value>
  </data>
  <data name="ShareInternalServerError" xml:space="preserve">
    <value>El proveedor Share devolvió un error en el servidor interno: {0}</value>
  </data>
  <data name="ShareMailException" xml:space="preserve">
    <value>Hubo un error al enviar un mensaje electrónico: {0}</value>
  </data>
  <data name="ShareMailPreconfiguredAol" xml:space="preserve">
    <value>AOL</value>
  </data>
  <data name="ShareMailPreconfiguredComcast" xml:space="preserve">
    <value>Comcast</value>
  </data>
  <data name="ShareMailPreconfiguredGmail" xml:space="preserve">
    <value>Gmail</value>
  </data>
  <data name="ShareMailPreconfiguredICloud" xml:space="preserve">
    <value>iCloud</value>
  </data>
  <data name="ShareMailPreconfiguredManual" xml:space="preserve">
    <value>Manual</value>
  </data>
  <data name="ShareMailPreconfiguredOutlook" xml:space="preserve">
    <value>Outlook</value>
  </data>
  <data name="ShareMailPreconfiguredYahoo" xml:space="preserve">
    <value>Yahoo</value>
  </data>
  <data name="ShareMailSendError" xml:space="preserve">
    <value>Hubo un error al enviar su mensaje: {0}</value>
  </data>
  <data name="ShareMailSentSuccessfully" xml:space="preserve">
    <value>{0} - el mensaje se envió con éxito</value>
  </data>
  <data name="ShareNonSuccessCode" xml:space="preserve">
    <value>El proveedor de Share devuelve un mensaje de error {0}: '{1}'</value>
  </data>
  <data name="ShareNotAuthorized" xml:space="preserve">
    <value>El proveedor de share devolvió un mensaje no autorizado: {0}</value>
  </data>
  <data name="ShareServiceParameters" xml:space="preserve">
    <value>Credenciales</value>
  </data>
  <data name="ShareServicePassword" xml:space="preserve">
    <value>Contraseña</value>
  </data>
  <data name="ShareServiceSignature" xml:space="preserve">
    <value>Hubo una excepción en el servicio de Share: {0}</value>
  </data>
  <data name="ShareServiceUserName" xml:space="preserve">
    <value>Nombre de Usuario</value>
  </data>
  <data name="ShareStockTwitsNoAccount" xml:space="preserve">
    <value>La cuenta StockTwits no podría ser verificada</value>
  </data>
  <data name="ShareStockTwitsSentSuccessfully" xml:space="preserve">
    <value>{0} - el mensaje se envió con éxito</value>
  </data>
  <data name="ShareTextMessageEmail" xml:space="preserve">
    <value>E-mail</value>
  </data>
  <data name="ShareTextMessageEmailRequired" xml:space="preserve">
    <value>Para configurar el mensaje de texto por correo electrónico compartir servicio que primero debe configurar un servicio de correo electrónico compartido.</value>
  </data>
  <data name="ShareTextMessageErrorOnShare" xml:space="preserve">
    <value>Hubo un error al enviar mensaje vía servicio de correo electrónico {0}: '{1}'</value>
  </data>
  <data name="ShareTextMessageMmsAddress" xml:space="preserve">
    <value>Dirección MMS</value>
  </data>
  <data name="ShareTextMessageName" xml:space="preserve">
    <value>Mensaje de texto vía correo electrónico</value>
  </data>
  <data name="ShareTextMessagePhoneNumber" xml:space="preserve">
    <value>Número de teléfono</value>
  </data>
  <data name="ShareTextMessagePreconfiguredAtt" xml:space="preserve">
    <value>AT&amp;T</value>
  </data>
  <data name="ShareTextMessagePreconfiguredManual" xml:space="preserve">
    <value>Manual</value>
  </data>
  <data name="ShareTextMessagePreconfiguredSprint" xml:space="preserve">
    <value>Sprint</value>
  </data>
  <data name="ShareTextMessagePreconfiguredTMobile" xml:space="preserve">
    <value>T-Mobile</value>
  </data>
  <data name="ShareTextMessagePreconfiguredVerizon" xml:space="preserve">
    <value>Verizon</value>
  </data>
  <data name="ShareTextMessageSentSuccessfully" xml:space="preserve">
    <value>{0} - mensaje de texto enviado</value>
  </data>
  <data name="ShareTextMessageSmsAddress" xml:space="preserve">
    <value>Dirección SMS</value>
  </data>
  <data name="ShareTooManyRequests" xml:space="preserve">
    <value>El proveedor Share devolvió demasiados mensajes de solicitud: {0}</value>
  </data>
  <data name="ShareTwitterSentSuccessfully" xml:space="preserve">
    <value>{0} - Tweet fue enviado exitosamente</value>
  </data>
  <data name="ShowAskLine" xml:space="preserve">
    <value>Mostrar línea de compra</value>
  </data>
  <data name="ShowBidLine" xml:space="preserve">
    <value>Mostrar línea de venta</value>
  </data>
  <data name="ShowClose" xml:space="preserve">
    <value>Mostrar cierre</value>
  </data>
  <data name="ShowHigh" xml:space="preserve">
    <value>Mostrar la altura</value>
  </data>
  <data name="ShowLastLine" xml:space="preserve">
    <value>Mostrar última línea</value>
  </data>
  <data name="ShowLow" xml:space="preserve">
    <value>Mostrar el nivel bajo</value>
  </data>
  <data name="ShowOpen" xml:space="preserve">
    <value>Mostrar la apertura</value>
  </data>
  <data name="ShowPatternCount" xml:space="preserve">
    <value>Mostrar la cuenta patrón</value>
  </data>
  <data name="ShowPatternCountDescription" xml:space="preserve">
    <value>Seleccione "verdadero" para mostrar en la grafica la cantidad de patrones.</value>
  </data>
  <data name="ShowPercent" xml:space="preserve">
    <value>Mostrar porcentaje</value>
  </data>
  <data name="SignalPeriod" xml:space="preserve">
    <value>Periodo de la señal</value>
  </data>
  <data name="Slow" xml:space="preserve">
    <value>Lento</value>
  </data>
  <data name="SlowLimit" xml:space="preserve">
    <value>Limite lento</value>
  </data>
  <data name="SlowPeriod" xml:space="preserve">
    <value>Mostrar periodo</value>
  </data>
  <data name="SmallAreaColor" xml:space="preserve">
    <value>Pequeña area de color</value>
  </data>
  <data name="Smooth" xml:space="preserve">
    <value>Moderar</value>
  </data>
  <data name="Smoothing" xml:space="preserve">
    <value>Parejando</value>
  </data>
  <data name="StochasticsD" xml:space="preserve">
    <value>D</value>
  </data>
  <data name="StochasticsK" xml:space="preserve">
    <value>K</value>
  </data>
  <data name="StockTwitsSentiment" xml:space="preserve">
    <value>Sentimiento:</value>
  </data>
  <data name="StockTwitsSentimentDescription" xml:space="preserve">
    <value>Elija Bearish, Neutral, o Bullish para este mensaje</value>
  </data>
  <data name="StockTwitsServiceName" xml:space="preserve">
    <value>StockTwits</value>
  </data>
  <data name="StockTwitsSignature" xml:space="preserve">
    <value>Envío por NinjaTrader</value>
  </data>
  <data name="Strength" xml:space="preserve">
    <value>Fuerza</value>
  </data>
  <data name="SuperDomColumnException" xml:space="preserve">
    <value>Columna de SuperDOM '{0}': Error en llamar '{1}' método: {2}</value>
  </data>
  <data name="SwingHigh" xml:space="preserve">
    <value>Oscilación alta</value>
  </data>
  <data name="SwingLow" xml:space="preserve">
    <value>Oscilación baja</value>
  </data>
  <data name="SwingSwingHighBarBarsAgoGreaterEqual" xml:space="preserve">
    <value>{0}. SwingHighBar: barsAgo debe ser 0 mayor/igual pero era {1}</value>
  </data>
  <data name="SwingSwingHighBarBarsAgoOutOfRange" xml:space="preserve">
    <value>0}. SwingHighBar: barsAgo fuera del rango valido 0 a través de {1}, fue {2}.</value>
  </data>
  <data name="SwingSwingHighBarInstanceGreaterEqual" xml:space="preserve">
    <value>{0}. SwingHighBar: instancia debe ser mayor/igual 1 pero fue {1}</value>
  </data>
  <data name="SwingSwingLowBarBarsAgoGreaterEqual" xml:space="preserve">
    <value>{0}. SwingLowBar: barsAgo debe ser 0 mayor/igual pero fue {1}</value>
  </data>
  <data name="SwingSwingLowBarBarsAgoOutOfRange" xml:space="preserve">
    <value>{0}. SwingLowBar: barsAgo fuera del rango valido 0 a través de {1}, fue {2}.</value>
  </data>
  <data name="SwingSwingLowBarInstanceGreaterEqual" xml:space="preserve">
    <value>{0}. SwingLowBar: instancia debe ser mayor/igual 1 pero fue {1}</value>
  </data>
  <data name="TCount" xml:space="preserve">
    <value>Cuenta T</value>
  </data>
  <data name="TextColor" xml:space="preserve">
    <value>Color del texto</value>
  </data>
  <data name="TextFont" xml:space="preserve">
    <value>Fuente del texto</value>
  </data>
  <data name="TextFontDescription" xml:space="preserve">
    <value>Seleccione la fuente, el estilo y el tamaño para mostrar en el gráfico</value>
  </data>
  <data name="TextPosition_BottomLeft" xml:space="preserve">
    <value>Izquierda inferior</value>
  </data>
  <data name="TextPosition_BottomRight" xml:space="preserve">
    <value>Derecha inferior</value>
  </data>
  <data name="TextPosition_Center" xml:space="preserve">
    <value>Centro</value>
  </data>
  <data name="TextPosition_TopLeft" xml:space="preserve">
    <value>Izquierda superior</value>
  </data>
  <data name="TextPosition_TopRight" xml:space="preserve">
    <value>Derecha superior</value>
  </data>
  <data name="TickCounterBarError" xml:space="preserve">
    <value>Contador de marcas sólo funciona en las barras construidas con un número determinado de marcas</value>
  </data>
  <data name="TickCounterTickCount" xml:space="preserve">
    <value>Tick Count ='</value>
  </data>
  <data name="TickCounterTicksRemaining" xml:space="preserve">
    <value>Marcas restantes = </value>
  </data>
  <data name="TrendLinesCurrentTrendLine" xml:space="preserve">
    <value>Línea de tendencia actual</value>
  </data>
  <data name="TrendLinesNotVisible" xml:space="preserve">
    <value>El indicador TrendLines no es visible con Strategy Analyzer</value>
  </data>
  <data name="TrendLinesTrendLineBroken" xml:space="preserve">
    <value>{0} roto</value>
  </data>
  <data name="TrendLinesTrendLineHigh" xml:space="preserve">
    <value>Línea de tendencia alta</value>
  </data>
  <data name="TrendLinesTrendLineLow" xml:space="preserve">
    <value>Línea de tendencia de baja</value>
  </data>
  <data name="TrendStrength" xml:space="preserve">
    <value>Fuerza de la tendencia</value>
  </data>
  <data name="TrendStrengthDescription" xml:space="preserve">
    <value>Número de barras necesarios para definir una tendencia cuando un patrón requiere una tendencia predominante. \nA valor de cero desactivará requisito de tendencia.</value>
  </data>
  <data name="TRIXSignal" xml:space="preserve">
    <value>Señal</value>
  </data>
  <data name="TwitterAuthHeader" xml:space="preserve">
    <value>Cuenta autorizada correctamente</value>
  </data>
  <data name="TwitterAuthText1" xml:space="preserve">
    <value>Has autorizado correctamente a {0} a acceder a tu cuenta de Twitter.</value>
  </data>
  <data name="TwitterAuthText2" xml:space="preserve">
    <value>Puede cerrar esta ventana y volver a {0}.</value>
  </data>
  <data name="TwitterServiceName" xml:space="preserve">
    <value>Twitter</value>
  </data>
  <data name="TwitterSignature" xml:space="preserve">
    <value> #NinjaTrader</value>
  </data>
  <data name="Unit" xml:space="preserve">
    <value>Unidad</value>
  </data>
  <data name="UpBarColor" xml:space="preserve">
    <value>Color de barra alta</value>
  </data>
  <data name="UseHighLow" xml:space="preserve">
    <value>Use alto bajo</value>
  </data>
  <data name="UserDefinedClose" xml:space="preserve">
    <value>Cerrado definido por el usuario</value>
  </data>
  <data name="UserDefinedHigh" xml:space="preserve">
    <value>Alto definido por el usuario</value>
  </data>
  <data name="UserDefinedLow" xml:space="preserve">
    <value>Debajo definido por el usuario.</value>
  </data>
  <data name="VFactor" xml:space="preserve">
    <value>Factor V</value>
  </data>
  <data name="VolatilityPeriod" xml:space="preserve">
    <value>Periodo de volatilidad</value>
  </data>
  <data name="VolumeCounterBarError" xml:space="preserve">
    <value>Contador de volumen sólo funciona en volumen basado en intervalos</value>
  </data>
  <data name="VolumeCounterVolumeCount" xml:space="preserve">
    <value>Volumen = </value>
  </data>
  <data name="VolumeCounterVolumeRemaining" xml:space="preserve">
    <value>Volumen restante = </value>
  </data>
  <data name="VolumeDivisor" xml:space="preserve">
    <value>Divisor del volumen</value>
  </data>
  <data name="VolumeDown" xml:space="preserve">
    <value>Volumen bajo</value>
  </data>
  <data name="VolumeDownColor" xml:space="preserve">
    <value>Color de volumen bajo</value>
  </data>
  <data name="VolumeNeutralColor" xml:space="preserve">
    <value>Color de volumen neutral</value>
  </data>
  <data name="VolumeUp" xml:space="preserve">
    <value>Volumen alto</value>
  </data>
  <data name="VolumeUpColor" xml:space="preserve">
    <value>Color de volumen alto</value>
  </data>
  <data name="VOLVolume" xml:space="preserve">
    <value>Volumen</value>
  </data>
  <data name="Width" xml:space="preserve">
    <value>Anchura</value>
  </data>
  <data name="WilliamsPercentR" xml:space="preserve">
    <value>Williams %R</value>
  </data>
  <data name="ZigZagDeviationValueError" xml:space="preserve">
    <value>Zig-zag no puede trazar los valores ya que el valor de la desviación es demasiado grande. Por favor, reducirlo.</value>
  </data>
  <data name="ZigZagHighBarBarsAgoOutOfRange" xml:space="preserve">
    <value>{0}. HighBar: barsAgo fuera del rango valido 0 a través de {1}, {2}</value>
  </data>
  <data name="ZigZagHighBarInstanceGreaterEqual" xml:space="preserve">
    <value>{0}. HighBar: instancia debe ser mayor/igual 1 pero fue {1}</value>
  </data>
  <data name="ZigZagLowBarBarsAgoOutOfRange" xml:space="preserve">
    <value>{0}. LowBar: barsAgo fuera del rango valido 0 a través de {1}, {2}</value>
  </data>
  <data name="ZigZagLowBarInstanceGreaterEqual" xml:space="preserve">
    <value>{0}. LowBar: instancia debe ser mayor/igual 1 pero fue {1}</value>
  </data>
  <data name="ZigZigHighBarBarsAgoGreaterEqual" xml:space="preserve">
    <value>{0}. HighBar: barsAgo debe ser 0 mayor/igual 0 pero fue {1}</value>
  </data>
  <data name="ZigZigLowBarBarsAgoGreaterEqual" xml:space="preserve">
    <value>{0}. LowBar: barsAgo debe ser 0 mayor/igual pero fue {1}</value>
  </data>
  <data name="NinjaScriptRecentColumnAskBackground" xml:space="preserve">
    <value>Pregunte antecedentes</value>
  </data>
  <data name="NinjaScriptRecentColumnAskForeground" xml:space="preserve">
    <value>Preguntar en primer plano</value>
  </data>
  <data name="NinjaScriptRecentColumnBidBackground" xml:space="preserve">
    <value>Antecedentes de la licitación</value>
  </data>
  <data name="NinjaScriptRecentColumnBidForeground" xml:space="preserve">
    <value>Primer plano de la oferta</value>
  </data>
  <data name="PullingStackingResetWhen_NoMoreData" xml:space="preserve">
    <value>Ya no se reciben datos de profundidad</value>
  </data>
  <data name="PullingStackingResetWhen_BidAskChange" xml:space="preserve">
    <value>Cambio de oferta/demanda</value>
  </data>
  <data name="PullingStackingDisplayType_Bid" xml:space="preserve">
    <value>Licitación</value>
  </data>
  <data name="PullingStackingDisplayType_BidAsk" xml:space="preserve">
    <value>Oferta y demanda</value>
  </data>
  <data name="PullingStackingDisplayType_Ask" xml:space="preserve">
    <value>Preguntar</value>
  </data>
  <data name="RecentResetWhen_PriceReturns" xml:space="preserve">
    <value>Devoluciones de precios</value>
  </data>
  <data name="RecentResetWhen_BidAskChange" xml:space="preserve">
    <value>Cambio de oferta/demanda</value>
  </data>
  <data name="RecentDisplayType_Bid" xml:space="preserve">
    <value>Licitación</value>
  </data>
  <data name="RecentDisplayType_BidAsk" xml:space="preserve">
    <value>Oferta y demanda</value>
  </data>
  <data name="RecentDisplayType_Ask" xml:space="preserve">
    <value>Preguntar</value>
  </data>
  <data name="PropertyCategoryVisual" xml:space="preserve">
    <value>Visual</value>
  </data>
  <data name="NinjaScriptSetup" xml:space="preserve">
    <value>Configuración</value>
  </data>
  <data name="NinjaScriptRecentColumnResetWhen" xml:space="preserve">
    <value>Resetear cuando</value>
  </data>
  <data name="NinjaScriptRecentColumnResetTolerance" xml:space="preserve">
    <value>Restablecer tolerancia</value>
  </data>
  <data name="NinjaScriptRecentColumnDiplay" xml:space="preserve">
    <value>monitor</value>
  </data>
  <data name="NinjaScriptDrawingToolPriceMarker" xml:space="preserve">
    <value>Precio de mercado</value>
  </data>
</root>