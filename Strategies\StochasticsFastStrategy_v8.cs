#region Using declarations
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Windows.Media;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.Indicators;
using NinjaTrader.NinjaScript.DrawingTools;
#endregion

namespace NinjaTrader.NinjaScript.Strategies
{
    public class StochasticsFastStrategy_v8 : Strategy
    {
        private StochasticsFast stoch;
        private EMA ema9;
        private EMA ema18;
        private ZLEMA zlema9;
        private ZLEMA zlema18;
        private ADX adx;
        
        // Variables to track profit targets
        private double longTarget1Price;
        private double longTarget2Price;
        private double shortTarget1Price;
        private double shortTarget2Price;
        private bool isTarget1Active;
        private bool isTarget2Active;

        protected override void OnStateChange()
        {
            if (State == State.SetDefaults)
            {
                Description = "Stochastic Fast Strategy with EMA/ZLEMA Crossover and ADX Filter for Futures (Version 8)";
                Name = "StochasticsFastStrategy_v8";
                Calculate = Calculate.OnBarClose;
                EntriesPerDirection = 1;
                EntryHandling = EntryHandling.AllEntries;
                IsExitOnSessionCloseStrategy = true;
                IsFillLimitOnTouch = false;
                MaximumBarsLookBack = MaximumBarsLookBack.TwoHundredFiftySix;
                OrderFillResolution = OrderFillResolution.Standard;
                Slippage = 0;
                StartBehavior = StartBehavior.WaitUntilFlat;
                TimeInForce = TimeInForce.Gtc;
                TraceOrders = false;
                RealtimeErrorHandling = RealtimeErrorHandling.StopCancelClose;
                StopTargetHandling = StopTargetHandling.PerEntryExecution;
                BarsRequiredToTrade = 20;

                // Default parameter values
                PeriodK = 3;         // %K Period
                PeriodD = 3;         // %D Period (SMA of K)
                StopLoss = 20;
                TakeProfit = 0;      // Single profit target - kept for backward compatibility
                TakeProfit1 = 40;    // First profit target in ticks
                TakeProfit2 = 80;    // Second profit target in ticks
                Quantity = 5;        // Default to trading 5 contracts
                QuantityFirstTarget = 3; // Exit 3 contracts at first target
                EmaPeriodShort = 6;  // Short EMA period (6)
                EmaPeriodLong = 10;  // Long EMA period (10)
                UseZLEMA = false;    // Default to standard EMA
                UseMultipleTargets = true; // Use multiple targets by default
                ShowTargetLines = true; // Show target lines on chart
                RequireDAlso = false; // Don't require %D confirmation
                KThreshold = 100.0;  // Upper threshold for K (for long entry)
                DThreshold = 100.0;  // Upper threshold for D (for long entry)

                // Default ADX Parameters
                AdxPeriod = 14;      // Period for ADX calculation
                AdxThreshold = 20;   // Minimum ADX value to allow trades
                UseAdxFilter = true; // Enable ADX filter by default

                // Default time filter values
                StartTime = DateTime.Parse("09:30", System.Globalization.CultureInfo.InvariantCulture);
                EndTime = DateTime.Parse("15:59", System.Globalization.CultureInfo.InvariantCulture);
                UseTimeFilter = true;
                ClosePositionsAtEndTime = true;
            }
            else if (State == State.Configure)
            {
                // Add StochasticsFast indicator
                stoch = StochasticsFast(PeriodK, PeriodD);
                
                // Add EMA indicators
                ema9 = EMA(EmaPeriodShort);
                ema18 = EMA(EmaPeriodLong);
                
                // Add ZLEMA indicators
                zlema9 = ZLEMA(EmaPeriodShort);
                zlema18 = ZLEMA(EmaPeriodLong);
                
                // Add ADX indicator
                adx = ADX(AdxPeriod);
                
                // Set indicator colors
                ema9.Plots[0].Brush = Brushes.Red;
                ema18.Plots[0].Brush = Brushes.Green;
                zlema9.Plots[0].Brush = Brushes.Orange;
                zlema18.Plots[0].Brush = Brushes.DarkBlue;
                adx.Plots[0].Brush = Brushes.Purple;
                
                // Add indicators to chart
                AddChartIndicator(stoch);
                AddChartIndicator(adx);
                
                // Add the selected moving average indicators to chart
                if (UseZLEMA)
                {
                    AddChartIndicator(zlema9);
                    AddChartIndicator(zlema18);
                }
                else
                {
                    AddChartIndicator(ema9);
                    AddChartIndicator(ema18);
                }
                
                // Initialize target variables
                ResetTargetVariables();
            }
            else if (State == State.Terminated)
            {
                // Remove any drawn objects
                RemoveDrawnObjects();
            }
        }
        
        private void ResetTargetVariables()
        {
            longTarget1Price = 0;
            longTarget2Price = 0;
            shortTarget1Price = 0;
            shortTarget2Price = 0;
            isTarget1Active = false;
            isTarget2Active = false;
        }
        
        private void RemoveDrawnObjects()
        {
            RemoveDrawObject("LongTarget1");
            RemoveDrawObject("LongTarget2");
            RemoveDrawObject("ShortTarget1");
            RemoveDrawObject("ShortTarget2");
        }

        protected override void OnBarUpdate()
        {
            if (CurrentBar < BarsRequiredToTrade)
                return;

            // Check if we need to close positions at end time
            if (UseTimeFilter && ClosePositionsAtEndTime && 
                Times[0][0].TimeOfDay >= EndTime.TimeOfDay && 
                Position.MarketPosition != MarketPosition.Flat)
            {
                if (Position.MarketPosition == MarketPosition.Long)
                    ExitLong();
                else if (Position.MarketPosition == MarketPosition.Short)
                    ExitShort();
                
                ResetTargetVariables();
                return;
            }

            // Check if we're within trading hours
            if (UseTimeFilter)
            {
                TimeSpan currentTime = Times[0][0].TimeOfDay;
                if (currentTime < StartTime.TimeOfDay || currentTime >= EndTime.TimeOfDay)
                    return;
            }

            // Determine moving average crossover direction based on selected indicator type
            bool shortAboveLong;
            
            if (UseZLEMA)
                shortAboveLong = zlema9[0] > zlema18[0];
            else
                shortAboveLong = ema9[0] > ema18[0];
                
            // Check for profit targets if we have an open position
            if (Position.MarketPosition == MarketPosition.Long)
            {
                // Check profit targets if using multi-target approach
                if (UseMultipleTargets)
                    CheckLongProfitTargets();
                
                // Exit Long Position when Stochastics K (and optionally D) hits or crosses below threshold
                if (stoch.K[0] <= 0.0 && (!RequireDAlso || stoch.D[0] <= 0.0))
                {
                    ExitLong();
                    ResetTargetVariables();
                }
            }
            else if (Position.MarketPosition == MarketPosition.Short)
            {
                // Check profit targets if using multi-target approach
                if (UseMultipleTargets)
                    CheckShortProfitTargets();
                
                // Exit Short Position when Stochastics K (and optionally D) hits or crosses above threshold
                if (stoch.K[0] >= 100.0 && (!RequireDAlso || stoch.D[0] >= 100.0))
                {
                    ExitShort();
                    ResetTargetVariables();
                }
            }
            else if (Position.MarketPosition == MarketPosition.Flat)
            {
                // Clear any previous targets
                ResetTargetVariables();
                RemoveDrawnObjects();
                
                // Check ADX filter if enabled
                bool adxCondition = !UseAdxFilter || adx[0] >= AdxThreshold;
                
                if (!adxCondition)
                {
                    // Skip entry if ADX filter is not satisfied
                    return;
                }
                
                // Long Entry Logic: Stoch K at K threshold (100 by default) AND D at D threshold (if enabled) AND Short MA > Long MA
                bool kCondition = stoch.K[0] >= KThreshold;
                bool dCondition = !RequireDAlso || stoch.D[0] >= DThreshold;
                
                if (kCondition && dCondition && shortAboveLong)
                {
                    EnterLong(Quantity, "EntryLong");
                    
                    // Log entry conditions
                    Print(string.Format("Long Entry: K={0}, D={1}, ShortMA={2}, LongMA={3}, ADX={4}",
                        stoch.K[0], stoch.D[0], 
                        UseZLEMA ? zlema9[0] : ema9[0], 
                        UseZLEMA ? zlema18[0] : ema18[0],
                        adx[0]));
                    
                    // Set stop loss
                    if (StopLoss > 0)
                        SetStopLoss(CalculationMode.Ticks, StopLoss);
                    
                    // Set up profit targets
                    if (UseMultipleTargets)
                        SetupLongProfitTargets();
                    else if (TakeProfit > 0)
                        SetProfitTarget(CalculationMode.Ticks, TakeProfit);
                }
                
                // Short Entry Logic: Stoch K at 0 or below threshold AND D at or below threshold (if enabled) AND Long MA > Short MA
                kCondition = stoch.K[0] <= 0.0;
                dCondition = !RequireDAlso || stoch.D[0] <= 0.0;
                
                if (kCondition && dCondition && !shortAboveLong)
                {
                    EnterShort(Quantity, "EntryShort");
                    
                    // Log entry conditions
                    Print(string.Format("Short Entry: K={0}, D={1}, ShortMA={2}, LongMA={3}, ADX={4}",
                        stoch.K[0], stoch.D[0], 
                        UseZLEMA ? zlema9[0] : ema9[0], 
                        UseZLEMA ? zlema18[0] : ema18[0],
                        adx[0]));
                    
                    // Set stop loss
                    if (StopLoss > 0)
                        SetStopLoss(CalculationMode.Ticks, StopLoss);
                    
                    // Set up profit targets
                    if (UseMultipleTargets)
                        SetupShortProfitTargets();
                    else if (TakeProfit > 0)
                        SetProfitTarget(CalculationMode.Ticks, TakeProfit);
                }
            }
        }
        
        private void SetupLongProfitTargets()
        {
            // Only proceed if we have valid profit targets
            if (TakeProfit1 <= 0 || Quantity <= 0)
                return;
            
            // Validate target quantities
            if (QuantityFirstTarget >= Quantity)
            {
                Print("Warning: First target quantity must be less than total quantity. Using single target approach.");
                SetProfitTarget(CalculationMode.Ticks, TakeProfit1);
                return;
            }
                
            // Calculate target prices based on entry price
            double entryPrice = Close[0];
            longTarget1Price = entryPrice + (TakeProfit1 * TickSize);
            
            // Draw first target line
            if (ShowTargetLines)
            {
                RemoveDrawObject("LongTarget1");
                Draw.Line(this, "LongTarget1", false, 0, longTarget1Price, 20, longTarget1Price, Brushes.DeepSkyBlue, DashStyleHelper.Solid, 2);
            }
            
            isTarget1Active = true;
            
            Print(String.Format("Long entry at {0}, Target 1 set at {1} (+{2} ticks)", entryPrice, longTarget1Price, TakeProfit1));
            
            // If we have a second target and valid partial quantity
            if (TakeProfit2 > 0)
            {
                longTarget2Price = entryPrice + (TakeProfit2 * TickSize);
                
                // Draw second target line
                if (ShowTargetLines)
                {
                    RemoveDrawObject("LongTarget2");
                    Draw.Line(this, "LongTarget2", false, 0, longTarget2Price, 20, longTarget2Price, Brushes.DarkGreen, DashStyleHelper.Solid, 2);
                }
                
                isTarget2Active = true;
                
                Print(String.Format("Long entry at {0}, Target 2 set at {1} (+{2} ticks)", entryPrice, longTarget2Price, TakeProfit2));
            }
            else
            {
                isTarget2Active = false;
            }
        }
        
        private void SetupShortProfitTargets()
        {
            // Only proceed if we have valid profit targets
            if (TakeProfit1 <= 0 || Quantity <= 0)
                return;
            
            // Validate target quantities
            if (QuantityFirstTarget >= Quantity)
            {
                Print("Warning: First target quantity must be less than total quantity. Using single target approach.");
                SetProfitTarget(CalculationMode.Ticks, TakeProfit1);
                return;
            }
                
            // Calculate target prices based on entry price
            double entryPrice = Close[0];
            shortTarget1Price = entryPrice - (TakeProfit1 * TickSize);
            
            // Draw first target line
            if (ShowTargetLines)
            {
                RemoveDrawObject("ShortTarget1");
                Draw.Line(this, "ShortTarget1", false, 0, shortTarget1Price, 20, shortTarget1Price, Brushes.DeepSkyBlue, DashStyleHelper.Solid, 2);
            }
            
            isTarget1Active = true;
            
            Print(String.Format("Short entry at {0}, Target 1 set at {1} (-{2} ticks)", entryPrice, shortTarget1Price, TakeProfit1));
            
            // If we have a second target and valid partial quantity
            if (TakeProfit2 > 0)
            {
                shortTarget2Price = entryPrice - (TakeProfit2 * TickSize);
                
                // Draw second target line
                if (ShowTargetLines)
                {
                    RemoveDrawObject("ShortTarget2");
                    Draw.Line(this, "ShortTarget2", false, 0, shortTarget2Price, 20, shortTarget2Price, Brushes.DarkGreen, DashStyleHelper.Solid, 2);
                }
                
                isTarget2Active = true;
                
                Print(String.Format("Short entry at {0}, Target 2 set at {1} (-{2} ticks)", entryPrice, shortTarget2Price, TakeProfit2));
            }
            else
            {
                isTarget2Active = false;
            }
        }
        
        private void CheckLongProfitTargets()
        {
            // Only check if we have active targets
            if (!isTarget1Active && !isTarget2Active)
                return;
                
            // First target hit check (High >= target price)
            if (isTarget1Active && High[0] >= longTarget1Price)
            {
                // Take partial profit at Target 1
                if (isTarget2Active && Position.Quantity > QuantityFirstTarget)
                {
                    // Exit part of position at first target
                    ExitLong(QuantityFirstTarget, "Target1Exit", "");
                    
                    Print(String.Format("Long Target 1 hit at {0}, exiting {1} contracts, {2} remaining", 
                        longTarget1Price, QuantityFirstTarget, Position.Quantity - QuantityFirstTarget));
                    
                    // Deactivate first target
                    isTarget1Active = false;
                    
                    // Remove target line
                    if (ShowTargetLines)
                        RemoveDrawObject("LongTarget1");
                }
                else
                {
                    // Exit entire position at first target
                    ExitLong(Position.Quantity, "Target1Exit", "");
                    Print(String.Format("Long Target 1 hit at {0}, exiting all {1} contracts", longTarget1Price, Position.Quantity));
                    
                    // Reset target variables
                    ResetTargetVariables();
                    
                    // Remove target lines
                    if (ShowTargetLines)
                    {
                        RemoveDrawObject("LongTarget1");
                        RemoveDrawObject("LongTarget2");
                    }
                }
            }
            
            // Second target hit check
            if (isTarget2Active && High[0] >= longTarget2Price)
            {
                // Exit remaining position at second target
                ExitLong(Position.Quantity, "Target2Exit", "");
                Print(String.Format("Long Target 2 hit at {0}, exiting all remaining {1} contracts", longTarget2Price, Position.Quantity));
                
                // Reset target variables
                ResetTargetVariables();
                
                // Remove target lines
                if (ShowTargetLines)
                {
                    RemoveDrawObject("LongTarget1");
                    RemoveDrawObject("LongTarget2");
                }
            }
        }
        
        private void CheckShortProfitTargets()
        {
            // Only check if we have active targets
            if (!isTarget1Active && !isTarget2Active)
                return;
                
            // First target hit check (Low <= target price)
            if (isTarget1Active && Low[0] <= shortTarget1Price)
            {
                // Take partial profit at Target 1
                if (isTarget2Active && Position.Quantity > QuantityFirstTarget)
                {
                    // Exit part of position at first target
                    ExitShort(QuantityFirstTarget, "Target1Exit", "");
                    
                    Print(String.Format("Short Target 1 hit at {0}, exiting {1} contracts, {2} remaining", 
                        shortTarget1Price, QuantityFirstTarget, Position.Quantity - QuantityFirstTarget));
                    
                    // Deactivate first target
                    isTarget1Active = false;
                    
                    // Remove target line
                    if (ShowTargetLines)
                        RemoveDrawObject("ShortTarget1");
                }
                else
                {
                    // Exit entire position at first target
                    ExitShort(Position.Quantity, "Target1Exit", "");
                    Print(String.Format("Short Target 1 hit at {0}, exiting all {1} contracts", shortTarget1Price, Position.Quantity));
                    
                    // Reset target variables
                    ResetTargetVariables();
                    
                    // Remove target lines
                    if (ShowTargetLines)
                    {
                        RemoveDrawObject("ShortTarget1");
                        RemoveDrawObject("ShortTarget2");
                    }
                }
            }
            
            // Second target hit check
            if (isTarget2Active && Low[0] <= shortTarget2Price)
            {
                // Exit remaining position at second target
                ExitShort(Position.Quantity, "Target2Exit", "");
                Print(String.Format("Short Target 2 hit at {0}, exiting all remaining {1} contracts", shortTarget2Price, Position.Quantity));
                
                // Reset target variables
                ResetTargetVariables();
                
                // Remove target lines
                if (ShowTargetLines)
                {
                    RemoveDrawObject("ShortTarget1");
                    RemoveDrawObject("ShortTarget2");
                }
            }
        }

        #region Properties
        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name="Period K", Description="The period used for %K calculation", Order=1, GroupName="Stochastic Parameters")]
        public int PeriodK { get; set; }

        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name="Period D", Description="The period used for %D calculation", Order=2, GroupName="Stochastic Parameters")]
        public int PeriodD { get; set; }
        
        [NinjaScriptProperty]
        [Display(Name="Require %D Confirmation", Description="Require %D to also be at extreme levels for signal", Order=3, GroupName="Stochastic Parameters")]
        public bool RequireDAlso { get; set; }
        
        [NinjaScriptProperty]
        [Range(70, 100)]
        [Display(Name="K Upper Threshold", Description="Upper threshold for K (100 for exact, or lower for earlier entries)", Order=4, GroupName="Stochastic Parameters")]
        public double KThreshold { get; set; }
        
        [NinjaScriptProperty]
        [Range(60, 100)]
        [Display(Name="D Upper Threshold", Description="Upper threshold for D (typically 80)", Order=5, GroupName="Stochastic Parameters")]
        public double DThreshold { get; set; }
        
        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name="MA Short Period", Description="The period for short MA (9)", Order=1, GroupName="Moving Average Parameters")]
        public int EmaPeriodShort { get; set; }
        
        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name="MA Long Period", Description="The period for long MA (18)", Order=2, GroupName="Moving Average Parameters")]
        public int EmaPeriodLong { get; set; }
        
        [NinjaScriptProperty]
        [Display(Name="Use ZLEMA", Description="Use ZLEMA instead of standard EMA", Order=3, GroupName="Moving Average Parameters")]
        public bool UseZLEMA { get; set; }

        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name="ADX Period", Description="The period for ADX calculation", Order=1, GroupName="ADX Parameters")]
        public int AdxPeriod { get; set; }

        [NinjaScriptProperty]
        [Range(1, 100)]
        [Display(Name="ADX Threshold", Description="Minimum ADX value to allow trades", Order=2, GroupName="ADX Parameters")]
        public double AdxThreshold { get; set; }

        [NinjaScriptProperty]
        [Display(Name="Use ADX Filter", Description="Filter trades using ADX value", Order=3, GroupName="ADX Parameters")]
        public bool UseAdxFilter { get; set; }

        [NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name="Stop Loss", Description="Stop Loss in ticks (0 to disable)", Order=1, GroupName="Trade Parameters")]
        public int StopLoss { get; set; }

        [NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name="Take Profit", Description="Take Profit in ticks (backward compatibility)", Order=2, GroupName="Trade Parameters")]
        public int TakeProfit { get; set; }
        
        [NinjaScriptProperty]
        [Display(Name="Use Multiple Targets", Description="Enable/disable multi-target exit approach", Order=3, GroupName="Trade Parameters")]
        public bool UseMultipleTargets { get; set; }
        
        [NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name="First Target", Description="First profit target in ticks (0 to disable)", Order=4, GroupName="Trade Parameters")]
        public int TakeProfit1 { get; set; }
        
        [NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name="Second Target", Description="Second profit target in ticks (0 to disable)", Order=5, GroupName="Trade Parameters")]
        public int TakeProfit2 { get; set; }
        
        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name="Total Quantity", Description="Total number of contracts to trade", Order=6, GroupName="Trade Parameters")]
        public int Quantity { get; set; }
        
        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name="First Target Quantity", Description="Number of contracts to exit at first target", Order=7, GroupName="Trade Parameters")]
        public int QuantityFirstTarget { get; set; }
        
        [NinjaScriptProperty]
        [Display(Name="Show Target Lines", Description="Show target lines on chart", Order=8, GroupName="Trade Parameters")]
        public bool ShowTargetLines { get; set; }

        [NinjaScriptProperty]
        [Display(Name="Use Time Filter", Description="Enable/disable time-based trading", Order=1, GroupName="Time Filter")]
        public bool UseTimeFilter { get; set; }

        [NinjaScriptProperty]
        [PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
        [Display(Name="Start Time", Description="Start time for trading", Order=2, GroupName="Time Filter")]
        public DateTime StartTime { get; set; }

        [NinjaScriptProperty]
        [PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
        [Display(Name="End Time", Description="End time for trading", Order=3, GroupName="Time Filter")]
        public DateTime EndTime { get; set; }

        [NinjaScriptProperty]
        [Display(Name="Close Positions at End Time", Description="Close all positions at end time", Order=4, GroupName="Time Filter")]
        public bool ClosePositionsAtEndTime { get; set; }
        #endregion
    }
}