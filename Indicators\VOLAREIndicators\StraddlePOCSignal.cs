#region USING_DECLARATIONS
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.DrawingTools;
using NinjaTrader.NinjaScript.SharedPOC;
#endregion

namespace NinjaTrader.NinjaScript.Indicators.VOLAREIndicators
{
	public class StraddlePOCSignal : Indicator
	{
		private const string VERSION = "*******";
		
		/// Default inputs
		private bool						drawArrow = true;
		private System.Windows.Media.Brush	upSignal = Brushes.Lime;
		private System.Windows.Media.Brush	downSignal = Brushes.Red;
		private int							arrowDistance = 2;
		
		private SignalType					signalType = SignalType.Percentage;
		private double						percentage1 = 50;
		private double						percentage2 = 10;
		private int 						opensX_Ticks = 2;
		private int 						closesX_Ticks = 2;
		
		
		protected override void OnStateChange()
		{
			if (State == State.SetDefaults)
			{
				Description					= @"Indicator for POC Straddle signal generation.";
				Name						= "StraddlePOCSignal";
				Calculate					= Calculate.OnBarClose;
				IsOverlay					= true;
				DisplayInDataBox			= true;
				DrawOnPricePanel			= true;
				DrawHorizontalGridLines		= true;
				DrawVerticalGridLines		= true;
				PaintPriceMarkers			= true;
				ScaleJustification			= NinjaTrader.Gui.Chart.ScaleJustification.Right;
				IsSuspendedWhileInactive	= true;
				
				AddPlot(new Stroke(Brushes.Transparent, 1), PlotStyle.Dot, "Straddle");
			}
		}
	
		protected override void OnBarUpdate()
		{
			if (Bars == null  ||  CurrentBar < 1)
				return;
			
			NinjaTrader.NinjaScript.BarsTypes.VolumetricBarsType barsType = Bars.BarsSeries.BarsType as NinjaTrader.NinjaScript.BarsTypes.VolumetricBarsType;
			if (barsType == null)
				return;
	
			/// Get the POC of the previous bar
			double pocPrice = GetPoc(CurrentBar - 1);
			if (pocPrice == double.NaN)
			{
				Values[0][0] = 0;
				return;
			}
			
			/// Evaluate the current bar's price action against the previous bar's POC
			bool isShort = Opens[0][0] > Closes[0][0];		// Short candle (open > close)
			bool isLong	 = Opens[0][0] < Closes[0][0];		// Long candle (open < close)
			bool CrossGreen	= (pocPrice >= Opens[0][0]  &&  pocPrice <= Closes[0][0])  &&  isLong;	// Straddles POC, long
			bool CrossRed	= (pocPrice <= Opens[0][0]  &&  pocPrice >= Closes[0][0])  &&  isShort;	// Straddles POC, short
			
			Values[0][0] = 0; /// Default no-signal state
			
			if (CrossGreen  ||  CrossRed)
			{
				double p1 = ((Highs[0][0] - Lows[0][0]) / TickSize) * (percentage1 / 100);
				double p2 = ((Highs[0][0] - Lows[0][0]) / TickSize) * (percentage2 / 100);
				int percentTick1 = (int)Math.Round(p1);
				int percentTick2 = (int)Math.Round(p2);
				int openTicks  = (signalType == SignalType.X_Ticks) ? opensX_Ticks : percentTick1;
				int closeTicks = (signalType == SignalType.X_Ticks) ? closesX_Ticks : percentTick2;
				
				bool highToOpenOk = Math.Abs(Highs[0][0] - Opens[0][0]) <= (openTicks * TickSize);
				bool lowToCloseOk = Math.Abs(Closes[0][0] - Lows[0][0]) <= (closeTicks * TickSize);
				bool lowToOpenOk = Math.Abs(Opens[0][0] - Lows[0][0]) <= (openTicks * TickSize);
				bool highToCloseOk = Math.Abs(Highs[0][0] - Closes[0][0]) <= (closeTicks * TickSize);
				
				int bar = CurrentBar;
				
				if (CrossRed  &&  highToOpenOk  &&  lowToCloseOk)
				{
					if (drawArrow)
						Draw.TriangleDown(this, "StraddleDownEntryArrow" + bar.ToString(), false, 0, Highs[0][0] + 5 * TickSize, downSignal);
					Values[0][0] = -1;
				}
				else if (CrossGreen  &&  lowToOpenOk  &&  highToCloseOk)
				{
					if (drawArrow)
						Draw.TriangleUp(this, "StraddleUpEntryArrow" + bar.ToString(), false, 0, Lows[0][0] - 5 * TickSize, upSignal);
					Values[0][0] = 1;
				}
			}
		}
	
		public double GetPoc(int barIndex)
		{
			if (barIndex < 0  ||  barIndex >= Bars.Count)
				return double.NaN;
			
			var barsType = Bars.BarsSeries.BarsType as NinjaTrader.NinjaScript.BarsTypes.VolumetricBarsType;
			if (barsType == null)
				return double.NaN;
			
			var volumeData = barsType.Volumes[barIndex];
			if (volumeData == null)
				return double.NaN;
			
			double maxVolumePrice;
			long maxVolume = volumeData.GetMaximumVolume(null, out maxVolumePrice);
			if (maxVolume <= 0)
				return double.NaN;
			
			List<double> pocPrices = new List<double>();
			for (double price = volumeData.Low; price <= volumeData.High; price += TickSize)
			{
				long bidVolume = volumeData.GetBidVolumeForPrice(price);
				long askVolume = volumeData.GetAskVolumeForPrice(price);
				long totalVolume = bidVolume + askVolume;
				if (totalVolume == maxVolume)
					pocPrices.Add(price);
			}
			return (pocPrices.Count > 0) ? pocPrices.Average() : double.NaN;
		}
	
		#region PROPERTIES
		[Browsable(false)]
		[XmlIgnore()]
		public Series<double> Straddle
		{
			get { Update(); return Values[0]; }
		}
	
		[Display(Name = "Draw Arrow", Description = "Draw arrow.", Order = 0, GroupName = "Arrow Parameters")]
		public bool DrawArrow
		{
			get { return drawArrow; }
			set { drawArrow = value; }
		}
		
		[XmlIgnore]
		[Display(Name = "Arrow Up Color", Description = "Color for arrow up", Order = 1, GroupName = "Arrow Parameters")]
		public System.Windows.Media.Brush UpSignal
		{
			get { return upSignal; }
			set { upSignal = value; }
		}
		
		[Browsable(false)]
		public string UpSignalSerialize
		{
			get { return Serialize.BrushToString(UpSignal); }
			set { UpSignal = Serialize.StringToBrush(value); }
		}
		
		[XmlIgnore]
		[Display(Name = "Arrow Down Color", Description = "Color for arrow down.", Order = 2, GroupName = "Arrow Parameters")]
		public System.Windows.Media.Brush DownSignal
		{
			get { return downSignal; }
			set { downSignal = value; }
		}
		
		[Browsable(false)]
		public string DownSignalSerialize
		{
			get { return Serialize.BrushToString(DownSignal); }
			set { DownSignal = Serialize.StringToBrush(value); }
		}
		
		[Display(Name = "Arrow Distance", Description = "Distance of the arrow from the bar.", Order = 3, GroupName = "Arrow Parameters")]
		public int ArrowDistance
		{
			get { return arrowDistance; }
			set { arrowDistance = value; }
		}
		
		[NinjaScriptProperty]
		[Display(Name = "Signal Type", Description = "Signal type either x ticks or percentage.", Order = 1, GroupName = "Parameters")]
		public SignalType _signal_type
		{
			get { return signalType; }
			set { signalType = value; }
		}
		
		[NinjaScriptProperty]
		[Display(Name = "Open Percentage", Description = "Open percentage for the confirmation of signal.", Order = 2, GroupName = "Parameters")]
		public double _percentage1
		{
			get { return percentage1; }
			set { percentage1 = value; }
		}
		
		[NinjaScriptProperty]
		[Display(Name = "Close Percentage", Description = "Close percentage for the confirmation of signal.", Order = 3, GroupName = "Parameters")]
		public double _percentage2
		{
			get { return percentage2; }
			set { percentage2 = value; }
		}
		
		[NinjaScriptProperty]
		[Display(Name = "X Ticks From the Open", Description = "X ticks from the opening price to the high price of the bar.", Order = 4, GroupName = "Parameters")]
		public int Opens_x_ticks
		{
			get { return opensX_Ticks; }
			set { opensX_Ticks = value; }
		}
		
		[NinjaScriptProperty]
		[Display(Name = "X Ticks From the Close", Description = "X ticks from the closing price to the low price of the bar.", Order = 5, GroupName = "Parameters")]
		public int Close_x_ticks
		{
			get { return closesX_Ticks; }
			set { closesX_Ticks = value; }
		}
		#endregion
	}
}

#region NinjaScript generated code. Neither change nor remove.

namespace NinjaTrader.NinjaScript.Indicators
{
	public partial class Indicator : NinjaTrader.Gui.NinjaScript.IndicatorRenderBase
	{
		private VOLAREIndicators.StraddlePOCSignal[] cacheStraddlePOCSignal;
		public VOLAREIndicators.StraddlePOCSignal StraddlePOCSignal(SignalType _signal_type, double _percentage1, double _percentage2, int opens_x_ticks, int close_x_ticks)
		{
			return StraddlePOCSignal(Input, _signal_type, _percentage1, _percentage2, opens_x_ticks, close_x_ticks);
		}

		public VOLAREIndicators.StraddlePOCSignal StraddlePOCSignal(ISeries<double> input, SignalType _signal_type, double _percentage1, double _percentage2, int opens_x_ticks, int close_x_ticks)
		{
			if (cacheStraddlePOCSignal != null)
				for (int idx = 0; idx < cacheStraddlePOCSignal.Length; idx++)
					if (cacheStraddlePOCSignal[idx] != null && cacheStraddlePOCSignal[idx]._signal_type == _signal_type && cacheStraddlePOCSignal[idx]._percentage1 == _percentage1 && cacheStraddlePOCSignal[idx]._percentage2 == _percentage2 && cacheStraddlePOCSignal[idx].Opens_x_ticks == opens_x_ticks && cacheStraddlePOCSignal[idx].Close_x_ticks == close_x_ticks && cacheStraddlePOCSignal[idx].EqualsInput(input))
						return cacheStraddlePOCSignal[idx];
			return CacheIndicator<VOLAREIndicators.StraddlePOCSignal>(new VOLAREIndicators.StraddlePOCSignal(){ _signal_type = _signal_type, _percentage1 = _percentage1, _percentage2 = _percentage2, Opens_x_ticks = opens_x_ticks, Close_x_ticks = close_x_ticks }, input, ref cacheStraddlePOCSignal);
		}
	}
}

namespace NinjaTrader.NinjaScript.MarketAnalyzerColumns
{
	public partial class MarketAnalyzerColumn : MarketAnalyzerColumnBase
	{
		public Indicators.VOLAREIndicators.StraddlePOCSignal StraddlePOCSignal(SignalType _signal_type, double _percentage1, double _percentage2, int opens_x_ticks, int close_x_ticks)
		{
			return indicator.StraddlePOCSignal(Input, _signal_type, _percentage1, _percentage2, opens_x_ticks, close_x_ticks);
		}

		public Indicators.VOLAREIndicators.StraddlePOCSignal StraddlePOCSignal(ISeries<double> input , SignalType _signal_type, double _percentage1, double _percentage2, int opens_x_ticks, int close_x_ticks)
		{
			return indicator.StraddlePOCSignal(input, _signal_type, _percentage1, _percentage2, opens_x_ticks, close_x_ticks);
		}
	}
}

namespace NinjaTrader.NinjaScript.Strategies
{
	public partial class Strategy : NinjaTrader.Gui.NinjaScript.StrategyRenderBase
	{
		public Indicators.VOLAREIndicators.StraddlePOCSignal StraddlePOCSignal(SignalType _signal_type, double _percentage1, double _percentage2, int opens_x_ticks, int close_x_ticks)
		{
			return indicator.StraddlePOCSignal(Input, _signal_type, _percentage1, _percentage2, opens_x_ticks, close_x_ticks);
		}

		public Indicators.VOLAREIndicators.StraddlePOCSignal StraddlePOCSignal(ISeries<double> input , SignalType _signal_type, double _percentage1, double _percentage2, int opens_x_ticks, int close_x_ticks)
		{
			return indicator.StraddlePOCSignal(input, _signal_type, _percentage1, _percentage2, opens_x_ticks, close_x_ticks);
		}
	}
}

#endregion
