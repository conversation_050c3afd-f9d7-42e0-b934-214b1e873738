#region Using declarations
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Net.Http;			/// Added for HttpClient	
using System.Net.Http.Headers;	/// Added for AuthenticationHeaderValue
using Newtonsoft.Json;			/// Added for automatic parsing of http response
using Npgsql;					/// Added for Benoit new signal get method Sep2024

using System.Linq;
using System.Runtime.CompilerServices;	/// For MemberCallerName
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Gui.Tools;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.Indicators;
using NinjaTrader.NinjaScript.Strategies;
using NinjaTrader.NinjaScript.DrawingTools;
#endregion

/// This namespace holds Strategies in this folder and is required. Do not change it. 
namespace NinjaTrader.NinjaScript.Strategies
{
	public class LadderURL : Strategy
	{
		#region Globals
		
		/// HttpClient is intended to be instantiated once per application, rather than per-use
		private static readonly HttpClient Client = new HttpClient();
		private const string 	URL = "http://mb.almanac.ai:3338/api/signal/gamma";
		private const int 		MIN_LOOKBACK = 61;
		private const int 		ADD_LOOKBACK = 20;
		private const int 		MAX_LOOKBACK = 120;
		private const int		TS_SWING_STRENGTH = 5;

		private double 			dailyProfit = 0;
		private double 			beTriggered1Price = 0;
		private double 			beTriggered2Price = 0;
		private double 			beTriggered3Price = 0;
		private double 			beTriggered4Price = 0;
		private double			lastSigSwingPriceL = 0;
		private double			lastSigSwingPriceS = 0;
		private double			lastSetPnL = 0;
		
		private int 			checkSeconds;
		private int				contTradesTaken = 0;
		private int				maxContTrades;
		private int				firstEntryBarIdx = -1;
		private int				firstEntryBarsAgo = -1;
		private int				urlRetries = 0;
		private int				quantityTP3	= 0;
		
		private string 			signalDir = "None";
		private string 			lastTradeDir = "None";
		private string 			manualSignal = "None";
		private string			positionEMAs = "None";
		private string 			lastDashboard = "";
		private string 			lastLogMsg = "";
		private string 			lastCaller = "";
		private string			lastLogTime = "";
		private string			chartInstrument;
		
		private bool 			entriesEnabled = true;
		private bool 			trailTriggered = false;
		private bool 			dailyLimitHit = false;
		private bool			longOn = true;
		private bool			shortOn = true;
		private bool			firstSignalRecevied = false;
		private bool			contTradesEnabled = false;
		private bool			inContinuationTrade = false;
		private bool			contMaxHit = false;
		private bool			contHadLoss = false;
		private bool			contHad2Loss = false;
		private bool			userRequestedExit = false;
		private bool			logAllSignals = false;
		private bool			inSession = true;
		private bool			noPrevailingToday = false;
		
		/// These variables were taken from NinjaTrader sample called UnmanagedTemplate
		private Order 			entryOrder = null;
		private Order 			slOrder = null;
		private Order 			tpOrder1 = null;
		private Order 			tpOrder2 = null;
		private Order 			tpOrder3 = null;
		private string			oco;
		private bool			stopsSubmitted = false;
		
		//private List<double>	tradeSetsPnL;
		private DateTime		prvSignalTime = DateTime.MinValue;
		private DateTime		signalTime = DateTime.MinValue.AddSeconds(1);
		private DateTime		nextCheckTime = DateTime.MinValue.AddSeconds(1);

		private EMA 			slowEMA, fastEMA;
		
		private System.Windows.Controls.Button	shortButton;
		private System.Windows.Controls.Button	longButton;
		private System.Windows.Controls.Button	exitButton;
		private System.Windows.Controls.Button	logSignalsButton;
		private System.Windows.Controls.Button	manualEnterLong;
		private System.Windows.Controls.Button	manualEnterShort;
		private System.Windows.Controls.Grid	myGrid;
		
		public enum ContTradesModeEnum
		{
			Disabled = 0,
			OnlyBeforeSignal = 1,
			OnlyAfterSignal = 2,
			BeforeAndAfter = 3
		}
		
		public enum ContTradesNumberEnum
		{
			One = 1,
			Two = 2,
			Three = 3,
			Four = 4,
			UntilLoss = 98,
			UntilTwoLoss = 99,
			All = 100
		}
		
		#endregion
		
		#region TODO
		/*
			Figure out a method to detect when market if flip-flopping.  Should be able to look at 2-3 losing sets in a row where all contracts 
			placed exited at a loss.  Resets on new session.  That is the easy part.  The hard part would be how to detect (in same day) that 
			market has chosen direction and we should perhaps restart entering on signals...
		
			Need to exit any open trades at 14:00 PT, or perhaps before.  Perhaps this: We already have it set to take no fresh trades after 12:30.
			I'm thinking that at 13:00 and every 5 minutes thereafter, we reduce the [next] TP by one tick.  So if we are in 3 contracts for 
			TP 16, 24, 48, and we have already closed 16, then we move the 48 down to 24 and redice to 23 at 13:00, 22 at 13:05, etc. If none had 
			reached TP, then reduce all to 16, etc.  Or... we could just close everything at some set time.  Or... we could check the profit of the 
			open trade(s) at 12:45 and if in profit, close.  If not, same check at 13:00, 13:15, 13:30 & 13:45 before closing absolutely at 13:55.
			FOR NOW... This is built into NT8 by IsExitOnSessionCloseStrategy & ExitOnSessionCloseSeconds.
			
			FROM CHAT:
			After that, I will look into methods for determining "trend direction" at market open (basically to guess on what Jacob is showing 
			before there is a signal).	

			After all that, I also want to develop a quantifiable method to determine if Jacob is flip-flipping (probably 2 or 3 losses in a 
			row in a short time).  Then if FlipFlopping, delay entry until candle closes beyond last Swing
		
			Do something in inputs (with header) "Flipping Protection", like:
				bool	UseFlipProtection
				bool	JustStop
				bool	StopUntilAfterWouldHaveWon	// Not sure this would be very useful, and hard to implement
				bool	TakeContinuationBasedOnLast // which would have to be linked to contunation settings
				bool	LetContPlayOut(IgnoreSignals)
			...Or...This shit is complicated, so can just shut off when it hits daily loss
		
		*/
		#endregion
		
		protected override void OnStateChange()
		{
			#region State Change Info
			/// By printing out "State", dicovered this order:
			/*
				OnStateChange(), State = SetDefaults	// 1st clone for listing the strategies available in UI
				OnStateChange(), State = Configure
				OnStateChange(), State = Configure
				OnStateChange(), State = SetDefaults	// 2nd clone for configuring the one selected
				OnStateChange(), State = Terminated
			
				// Then the real one that we care about after we apply the settings:
				OnStateChange(), State = Configure		// Cloned again, so already has defaults set
				OnStateChange(), State = DataLoaded
				OnStateChange(), State = Historical
				OnStateChange(), State = Transition
				OnStateChange(), State = Realtime
				OnStateChange(), State = Terminated		// would be called again when terminated
			*/
			#endregion
			
			/// Set Input Defaults
			if (State == State.SetDefaults)
			{
				Description						= @"Strategy to automatically trade Jacob signals";
				Name							= "LadderURL";
				Calculate						= Calculate.OnBarClose;
				EntriesPerDirection				= 1;
				EntryHandling					= EntryHandling.AllEntries;
				IsExitOnSessionCloseStrategy	= true;
				ExitOnSessionCloseSeconds		= 2700;		// 45 mins: Exit @ 13:15 PST
				IsFillLimitOnTouch				= false;
				MaximumBarsLookBack				= MaximumBarsLookBack.TwoHundredFiftySix;
				OrderFillResolution				= OrderFillResolution.Standard;
				Slippage						= 0;
				StartBehavior					= StartBehavior.WaitUntilFlat; //AdoptAccountPosition;
				TimeInForce						= TimeInForce.Gtc;
				TraceOrders						= true;
				RealtimeErrorHandling			= RealtimeErrorHandling.StopCancelClose;
				//StopTargetHandling			= StopTargetHandling.ByStrategyPosition;	// method from managed Razor version
				StopTargetHandling				= StopTargetHandling.PerEntryExecution;
				BarsRequiredToTrade				= 20;
				IsUnmanaged 					= true;
				IsAdoptAccountPositionAware 	= true;		// Not sure if this is correct/needed
				
				/// Disable this property for performance gains in Strategy Analyzer optimizations
				/// See the Help Guide for additional information
				IsInstantiatedOnEachOptimizationIteration	= true;
				
				
				/// Quantity of contract to trade
				Quantity				= 10;
				
				/// Maximum delay between time stamp and trade entry
				LastTradeDir			= "None";
				CheckSeconds			= 10;
				MaxDelaySeconds			= 300;		// Seems to be ~1-2 min delay built-in....
				TimeZoneOffset			= -3; 		// Signals are posted EST, so we need to subtract 3 to get to PT
				
				/// Take trades before first signal, and/or between signals?
				ContTradesMode			= ContTradesModeEnum.Disabled;
				BiDirectional			= true;
				ContTradesNum			= ContTradesNumberEnum.Two;
				ContStrength			= 3;
				ContReqMAPosition		= true;
				//				bool isCorrect = direction == "Long" ? fastEMA[0] >= slowEMA[0] : fastEMA[0] <= slowEMA[0];
				
				/// StopLoss & TakeProfit
				StopLoss				= 100;
				TakeProfit1				= 10;
				QuantityTP1				= 3;
				TakeProfit2				= 30;
				QuantityTP2				= 3;
				TakeProfit3				= 101;
				
				/// Daily Targets
				DailyMinTarget			= 0;		
				DailyMaxLoss			= 0;
				
				/// Move to BreakEven
				BETrigger1				= 72;
				BEOffset1				= -2;
				BETrigger2				= 0;
				BEOffset2				= 0;
				BETrigger3				= 0;
				BEOffset3				= 0;
				BETrigger4				= 0;
				BEOffset4				= 0;
				
				/// Trailing
				UseTrailingStop 		= false;
				TS_UseSimpleTrail		= false;
				TS_SimpleTicks			= 32;
				TS_UseClose				= true;
				TS_TakeProfitTicks 		= 300;
				TS_OffsetTicks 			= -2;		// Negative for extra room, positive to lock in more
				TS_ActivationTicks 		= 31;
				TS_MinTicks				= 31;
				TS_MaxTicks				= 39;
				TS_ReAdjustAtTicks1 	= 50;
				TS_MinTicks1			= 23;
				TS_MaxTicks1			= 29;
				TS_ReAdjustAtTicks2 	= 80;
				TS_MinTicks2			= 16;
				TS_MaxTicks2			= 20;
				
				/// Min Volume to trade
				AveVolumePeriod 		= 14;
				MinAveVolume			= 50;
				
				LimitTradingHours		= true;
				StartTime				= DateTime.Parse("06:34", System.Globalization.CultureInfo.InvariantCulture);
				EndTime 				= DateTime.Parse("12:45", System.Globalization.CultureInfo.InvariantCulture);
				UseCloseTime			= true;
				CloseTime 				= DateTime.Parse("12:58", System.Globalization.CultureInfo.InvariantCulture);
				/* There really is no need for a second session with Jacob.  
				// But there may be if using different entry signal....
				// Something I could run 24/5 I would want to be able to have a MinDailyProfit 
				// and a MinSessionProfit target, but one-and-the-same for Jacob.
				LimitTradingHours2		= false;	// London, which is useless cause no Jacob signals
				StartTime2				= DateTime.Parse("23:50", System.Globalization.CultureInfo.InvariantCulture);
				EndTime2 				= DateTime.Parse("06:30", System.Globalization.CultureInfo.InvariantCulture);*/
				
				StrategyName			= "Ladder";
				DisplayOCD				= true;
				DisableLogging			= false;
				UseOutput2				= false;
				UniqueID				= "1";
			}
			
			/// Initialize Member Variables
			else if (State == State.Configure)
			{
				checkSeconds = CheckSeconds;
				maxContTrades = (int) ContTradesNum;
				chartInstrument = this.Instrument.FullName;
				quantityTP3 = Quantity - (QuantityTP1 + QuantityTP2);
				//tradeSetsPnL = new List<double>();

				/// Implement PropertyChanged for Dynamic Visibility
				//UpdateContTradesEnabled();

				AddDataSeries(BarsPeriodType.Tick, 1);
			}
			
			else if (State == State.DataLoaded)
			{
				//UpdatePropertyVisibility();
				slowEMA = EMA(SlowEMA_Period);
				fastEMA = EMA(FastEMA_Period);
			}
			
			else if (State == State.Historical)
			{
				// Init user override of lasttradeDir
				// Not sure this is the best place for this, but seems to work
				if (LastTradeDir.ToUpper() == "LONG")		lastTradeDir = "Long";
				else
				if (LastTradeDir.ToUpper() == "SHORT")		lastTradeDir = "Short";
				
				if (ChartControl != null)
				{
					ChartControl.Dispatcher.InvokeAsync(() =>
					{
						CreateButtons();
					});
				}	
			}
			
			else if (State == State.Terminated)
			{
				if (ChartControl != null)
				{
					ChartControl.Dispatcher.InvokeAsync(() =>
					{
						DisposeButtons();
					});
				}
			}
		}

		protected override void OnExecutionUpdate(Cbi.Execution execution, string executionId, double price, int quantity, 
												  Cbi.MarketPosition marketPosition, string orderId, DateTime time)
		{
			Log($"Name = {execution.Name}, IsEntryStrategy = {execution.IsEntryStrategy},  OrderAction = {execution.Order.OrderAction},   marketPosition = {marketPosition},   quantity (parameter) = {quantity},   Position.Quantity = {Position.Quantity}");
			
			// We advise monitoring OnExecution to trigger submission of stop/target orders instead of OnOrderUpdate()
			// since OnExecution() is called after OnOrderUpdate() which ensures your strategy has received the execution
			// which is used for internal signal tracking.
			
			double m = (Position.MarketPosition == MarketPosition.Long) ? 1.0 : -1.0;
			double avePrice = AvePrice();

			/// Handle actions for this execution being an entry order (execution.Order.OrderAction.Buy or SellShort)
			if (execution.IsEntryStrategy)
			{
				if (StopLoss > 0  ||  TakeProfit1 > 0  ||  TakeProfit2 > 0  ||  TakeProfit3 > 0)
				{
					/// [Only] if we are using both stops do we need to link OCO
					oco = "";
					if (StopLoss > 0  &&  TakeProfit3 > 0)
					{
						if (!stopsSubmitted)
						{
							/// TakeProfit is required (if using any TP), TakeProfit 1 & 2 are incidental
							if (State == State.Historical)
								oco = DateTime.Now.ToString() + "_" + CurrentBar + "_" + "Exits";
							else
								oco = GetAtmStrategyUniqueId() + "_" + "Exits";
							//Log($"Set OCO = {oco}");
						}
						else if (slOrder != null)
						{
							oco = slOrder.Oco;
							//Log($"Fetched existing OCO = {oco}");
						}
						else
							Log($"Could not fetch OCO because slOrder == null");
					}
					
					if (entryOrder != null  &&  entryOrder == execution.Order)
					{
						Log($"IsEntryStrategy = True, execution.Order.OrderState = {execution.Order.OrderState}");
						
						// We are not specifically handing this case, so hope it never happens:
						if (execution.Order.OrderState == OrderState.PartFilled)
							Log($"   ENTRY ORDER ONLY PARTIALLY FILLED; execution.Order.Filled = {execution.Order.Filled}");
						
						else if (execution.Order.OrderState == OrderState.Cancelled)
						{
							/// If the order was somehow cancelled, then we need to just and position that partially opened
							Log($"   ENTRY ORDER CANCELLED; dir = {marketPosition.ToString()}, execution.Order.Filled = {execution.Order.Filled}");
							if (execution.Order.Filled > 0)
							{
								Log($"Partial order was filled, so closing now");
								ClosePosition("Partial Fill", marketPosition.ToString(), execution.Order.Filled);
							}
						}
						else if (execution.Order.OrderState == OrderState.Filled)
						{
							if (!stopsSubmitted)
							{
								/// This used to check that slOrder & tpOrders were null, because we were initially setting, 
								/// but it won't place the stops until order is filled, so no need to check for that. 
								var action = (marketPosition == MarketPosition.Long) ? OrderAction.Sell : OrderAction.BuyToCover;

								Log($"INIT PLACEMENT OF SL & TP; Action = {action.ToString()}");
								if (StopLoss > 0)
								{
									Log($"Submitting StopLoss for Qty {Position.Quantity}");
									SubmitOrderUnmanaged(0, action, OrderType.StopMarket, Position.Quantity, 0, avePrice - m * StopLoss * TickSize, oco, "StopLoss");
								}
								if (TakeProfit1 > 0)
								{
									Log($"Submitting TakeProfit1 for Qty {QuantityTP1}");
									SubmitOrderUnmanaged(0, action, OrderType.Limit, QuantityTP1, avePrice + m * TakeProfit1 * TickSize, 0, "", "TakeProfit1");
								}
								if (TakeProfit2 > 0)
								{
									Log($"Submitting TakeProfit2 for Qty {QuantityTP2}");
									SubmitOrderUnmanaged(0, action, OrderType.Limit, QuantityTP2, avePrice + m * TakeProfit2 * TickSize, 0, "", "TakeProfit2");
								}
								if (TakeProfit3 > 0)
								{
									Log($"Submitting TakeProfit3 for Qty {quantityTP3}");
									SubmitOrderUnmanaged(0, action, OrderType.Limit, quantityTP3, avePrice + m * TakeProfit3 * TickSize, 0, oco, "TakeProfit3");
								}
								stopsSubmitted = true;
							}
							else
								Log($"execution.Order.OrderState == OrderState.Filled & stops already submitted; DOING NOTHING");
						}
					}
					else
						Log($"entryOrder = {entryOrder}, execution.Order.OrderState = {execution.Order.OrderState}");
				}
			}
			
			//if (execution.Order.OrderAction == OrderAction.Sell  ||  execution.Order.OrderAction == OrderAction.BuyToCover)
			else	// Is exit strategy
			{
				/// If we did NOT just close *entire* position, update SL quantity
				if (Position.Quantity > 0)
				{
					Log($"Position.Quantity > 0; Changing SL order to Qty ({Position.Quantity}) @ price {avePrice - m * StopLoss * TickSize}");
					ModifyStopLoss(avePrice - m * StopLoss * TickSize);
				}
				else
				{
					/// Cancel any stops that are left open. SL & TP3 OCO, but check all to be sure
					Log($"Position.Quantity == 0; cancelling stops");
					CancelStops();
					
					/// Reset some variables
					Log($"Position.Quantity == 0; RESETTING VARS & checking DailyMaxLoss & DailyMinTarget..");
					stopsSubmitted = false;
					beTriggered1Price = beTriggered2Price = beTriggered3Price = beTriggered4Price = 0;
					trailTriggered = false;
					inContinuationTrade = false;
					firstEntryBarIdx = -1;
					entryOrder = slOrder = tpOrder1 = tpOrder2 = tpOrder3 = null;
					
			        /// Use SystemPerformance.AllTrades to get the last closed trade
			        if (SystemPerformance.AllTrades.Count > 0)
			        {
			            var lastTrade = SystemPerformance.AllTrades[SystemPerformance.AllTrades.Count - 1];
			
			            /// Access trade details
						double pnl = lastTrade.ProfitCurrency;	// includes commission
						Log($"Last trade PnL: ${pnl}");
						//double com = lastTrade.Commission;
						//Log($"Last trade Commission: ${com}");
						
						dailyProfit += pnl;		// we need to save this each time, so we can reload it if we have to restart platform
						
						/// Don't know how to get the last item from the list.  Should be "Item[]" or "Item()", but does not work:
						//lastSetPnL = tradeSetsPnL[tradeSetsPnL.Count-1];
						//tradeSetsPnL.Add(pnl);
						lastSetPnL += pnl;		
			            Log($"Current set PnL: ${lastSetPnL}");
			        }
					
					/// Check for daily profit/loss limits
					Log($"DailyMinTarget = {DailyMinTarget}, DailyMaxLoss = ${DailyMaxLoss}");
					Log($"pDaily = (${dailyProfit}), SystemPerformance.RealTimeTrades.TradesPerformance.NetProfit = (${SystemPerformance.RealTimeTrades.TradesPerformance.NetProfit})");
					if (DailyMaxLoss > 0  &&  -dailyProfit > DailyMaxLoss)
					{
						/// if (Position.Quantity == 0), Exiting should be unnecessary, but are 
						/// left in case we are reversing position, or otherwise 'just to be safe'
						ClosePosition("Daily Loss");
						Log($"pDaily (${dailyProfit}) - NetProfit (${SystemPerformance.AllTrades.TradesPerformance.NetProfit}) > DailyMaxLoss (${DailyMaxLoss}");
						Log($"Daily Loss Hit! Set dailyLimitHit = true\n");
						dailyLimitHit = true;
					}
					if (DailyMinTarget > 0  &&  dailyProfit > DailyMinTarget)
					{
						ClosePosition("Daily Target");
						Log($"NetProfit (${SystemPerformance.AllTrades.TradesPerformance.NetProfit}) - dailyProfit (${dailyProfit}) > DailyMinTarget (${DailyMinTarget}");
						Log($"Daily Profit Hit! Set dailyLimitHit = true\n");
						dailyLimitHit = true;
					}
				}
			}
		}

		
		protected override void OnOrderUpdate(Cbi.Order order, double limitPrice, double stopPrice, int quantity, 
											  int filled, double averageFillPrice, Cbi.OrderState orderState, 
											  DateTime time, Cbi.ErrorCode error, string comment)
        {
			Log($"order.Name = {order.Name}");
			
			/// Assign Order objects here
			/// This is more reliable than assigning Order objects in OnBarUpdate, as the assignment 
			/// is not guaranteed to be complete if it is referenced immediately after submitting
			if (order.Name == "Entry")
			{
				//Log($"Setting entryOrder");
				entryOrder = order;
			}
			else if (order.Name == "StopLoss")
			{
				Log($"Setting slOrder");
				slOrder = order;
			}
			else if (order.Name == "TakeProfit1")
			{
				//Log($"Setting tpOrder1");
				tpOrder1 = order;
			}
			else if (order.Name == "TakeProfit2")
			{
				//Log($"Setting tpOrder2");
				tpOrder2 = order;
			}
			else if (order.Name == "TakeProfit3")
			{
				Log($"Setting tpOrder3");
				tpOrder3 = order;
			}
		}
		
		
		protected override void OnBarUpdate()
		{
			if (CurrentBars[0] < BarsRequiredToTrade  ||  CurrentBars[1] < BarsRequiredToTrade)
				return;
			
			/// No point in trying to do historical trades without the signals
			if (State != State.Historical)
				ManageOCD();	// Update the On-Chart Display
			
			/// Entered on close of each candle of chart TF where strategy is applied (first data series)
			bool highFractal, lowFractal;
			if (BarsInProgress == 0)
			{
				if (Bars.IsFirstBarOfSession)
				{
					/// This is not useful for us because we do not let the EA run 24/5 such that this would trigger at 6pm ET
					/// And more importantly, we do not do anthing with historical (yet); that is where this is important:
					/// to reset some daily things that will be done with running over historical bars.
					dailyProfit = 0;	//SystemPerformance.AllTrades.TradesPerformance.NetProfit;
					dailyLimitHit = false;
					noPrevailingToday = false;
					firstSignalRecevied = false;
					lastTradeDir = "None";
					contTradesTaken = 0;
					contMaxHit = contHadLoss = contHad2Loss = false;
					//tradeSetsPnL.Clear();
					Log($"\nNEW SESSION (DAY) - RESET EVERYTHING\n");
					
					/// This code was in a sample showing how to get all previous profit made 
					/// by this strategy in the past, but SINCE IT WAS APPLIED TO THE CHART
					//int priorTradesCount = SystemPerformance.AllTrades.Count;
					//double priorTradesCumProfit = SystemPerformance.AllTrades.TradesPerformance.Currency.CumProfit;
					/// This is not useful for us because we do not have historical signals [yet] (see comment at top)
				}
				
				/// Close all trades at end of user-defined session
				if (UseCloseTime  &&  Position.MarketPosition != MarketPosition.Flat)
				{
					if (Time[0].TimeOfDay >= CloseTime.AddMinutes(-3).TimeOfDay)
						Log($"UseCloseTime enabled and near EoD.  Position.MarketPosition = {Position.MarketPosition}");
					
					if (Time[0].TimeOfDay >= CloseTime.TimeOfDay)//  &&  Time[1].TimeOfDay < CloseTime.TimeOfDay)
					{
						// This does not seem to be working... Add logs
						Log($"Time[0] ({Time[0].TimeOfDay.ToString()}) >= CloseTime.TimeOfDay ({CloseTime.TimeOfDay.ToString()})");//  &&  Time[1] ({Time[1].TimeOfDay.ToString()}) < CloseTime.TimeOfDay");
						Log($"End of Session Close activated @ {DateTime.Now.ToString()}; Closing all open orders");
						ClosePosition("EoD Close", Position.MarketPosition.ToString());
					}
				}

				
				/// Set up how many bars ago was the entry candle
				firstEntryBarsAgo = (firstEntryBarIdx != -1) ? CurrentBar - firstEntryBarIdx : -1;
				
				/// Need position of EMAs relative to one another for cont entry option
				if (fastEMA[0] == slowEMA[0])		positionEMAs = "None";
				else if (fastEMA[0] > slowEMA[0])	positionEMAs = "Long";
				else if (fastEMA[0] < slowEMA[0])	positionEMAs = "Short";

				/// If not in market and not in-session (or limit hit), no need to go further
				if (!inSession  ||  dailyLimitHit)
					if (Position.MarketPosition == MarketPosition.Flat)
						return;

				#region CONTINUATION ENTRY
				/// NEED TO CHANGE THIS, SO THAT IF IT RECEIVES A REAL (opposite) SIGNAL WHILE IN A CONT TRADE, CLOSE IT AND REVERSE
				if (entriesEnabled  &&  Position.MarketPosition == MarketPosition.Flat  &&  contTradesEnabled)
				{
					string direction = "None";
					string ltd = lastTradeDir;
					
					/// If no lastTradeDir, then we want to check both directions (if BiDirectional enabled)
					if (ltd == "None"  &&  BiDirectional)
					{
						/// If candle is closing above, it'd have to be a long candle...
						if (Close[0] > Open[0])	ltd = "Long";
						else					ltd = "Short";
						//Log($"lastTradeDir == None & BiDirectional = {BiDirectional}; so using lastTradeDir as {ltd}");
					}
					/// Check every candle because any cond above could be false, then there would be a gap...
					if (ltd != "None")
					{
						// Get both high & low each time just to update OCD
						lastSigSwingPriceL = GetLastSignificantSwingPrice("Long");
						lastSigSwingPriceS = GetLastSignificantSwingPrice("Short");
						
						if (ltd == "Long"  &&  lastSigSwingPriceL > 0  &&  Close[0] > lastSigSwingPriceL)
						{
							direction = "Long";
							Log($"No trades open, TradeContinuation enabled, lastTradeDir = {ltd}, and candle closed ({Close[0]}) above previous fractal @ {lastSigSwingPriceL}; Continuation signal = {direction}");
						}
						else if (ltd == "Short"  &&  lastSigSwingPriceS > 0  &&  Close[0] < lastSigSwingPriceS)
						{
							direction = "Short";
							Log($"No trades open, TradeContinuation enabled, lastTradeDir = {ltd}, and candle closed ({Close[0]}) below previous fractal @ {lastSigSwingPriceS}; Continuation signal = {direction}");
						}
						
						// Confirm EMAf/EMAs position
						if (direction != "None"  &&  ContReqMAPosition  &&  positionEMAs == direction)
						{
							Log($"{direction} Continuation trade cancelled because EMA positions do not match");
							direction = "None";
						}
					}
					
					if (direction != "None")
					{
						Log($"\nContinuation Signal Detected @ {DateTime.Now.ToString()}!  Direction = {direction}; checking trade filters...");
						if (TradeFiltersOkay(direction))
						{
							Log($"Trade Filters Okay; ENTERING {direction.ToUpper()} CONTINUATION TRADE");
							PlaceMarketOrder(direction, Quantity);
							contTradesTaken++;
							inContinuationTrade = true;
							lastTradeDir = ltd;
						}
					}
					/// No need to do any trail computations if we were flat a moment ago, or still are
					return;
				}
				#endregion
				
				#region TRAILING STOP
				/// Looking for a better plan for trailing than "fractal + candle".  I really want to prioritize fractals, 
				/// so it seems a better way it to skip input settings and just look for various Swing lows (assume long)
				/// We already have a min & max trail distance, so we'll look for a swing low with strength=x (say we 
				/// hardcode to 5).  If not found in the range between min & max, try 4, then 3, then 2, then 1.  And if 
				/// we don't find any, we can just move to MinTicks.
				if (UseTrailingStop  &&  Position.MarketPosition != MarketPosition.Flat)
				{
					double newSL = GetNewTrailPrice(Position.MarketPosition.ToString());
					if (newSL != 0)
					{
						Log($"MODIFYING TRAILING STOPLOSS TO {newSL}");
						ModifyStopLoss(newSL);
					}
				}
				#endregion
			}

			/// Entered on each tick (2nd data series)
			else if (BarsInProgress == 1)
			{
				// Check each tick whether we are in-session (when it can place new trades)
				inSession = CheckSession();
				
				/// If not in market and not in-session (or limit hit), no need to go further
				if ((!inSession  ||  dailyLimitHit)  &&  manualSignal == "None")
					if (Position.MarketPosition == MarketPosition.Flat)
						return;
				
				/// Assign whether we are active for continuation trades
				#region ENABLE CONTINUATION TRADES?
				/* I don't think I can use new switch syntax because not simple assignemnt
				string contTradesEnabled = ContTradesMode switch
				{
					ContTradesModeEnum.OnlyAfterSignal => true,
					ContTradesModeEnum.OnlyBeforeSignal => false,
					ContTradesNumberEnum.UntilLoss => (lastSetPnL > 0) ? true : false,
					_ => false
				};		//....and this...is not simple:  */
				
				if (!contMaxHit  &&  contTradesTaken >= maxContTrades)
					contMaxHit = true;
				if (lastSetPnL < 0)
				{
					if (!contHadLoss)
					{
						if (maxContTrades == (int) ContTradesNumberEnum.UntilLoss  ||  maxContTrades == (int) ContTradesNumberEnum.UntilTwoLoss)
							contHadLoss = true;
					}
					/// May want to make it two IN A ROW, which would require more coding...
					else if (maxContTrades == (int) ContTradesNumberEnum.UntilTwoLoss)
						contHad2Loss = true;
				}
				
				if (contMaxHit)
					contTradesEnabled = false;
				else if (contHadLoss  &&  maxContTrades == (int) ContTradesNumberEnum.UntilLoss)
					contTradesEnabled = false;
				else if (contHad2Loss  &&  maxContTrades == (int) ContTradesNumberEnum.UntilTwoLoss)
					contTradesEnabled = false;
				else
				{
					switch (ContTradesMode)
					{
						case ContTradesModeEnum.OnlyBeforeSignal:
							contTradesEnabled = !firstSignalRecevied;
							break;
						case ContTradesModeEnum.OnlyAfterSignal:
							contTradesEnabled = firstSignalRecevied;
							break;
						case ContTradesModeEnum.BeforeAndAfter:
							contTradesEnabled = true;
							break;
						default:
						case ContTradesModeEnum.Disabled:
							contTradesEnabled = false;
							break;
					}
				}
				#endregion
				
				#region MAIN SIGNAL ENTRY
				signalDir = "None";
				if (entriesEnabled)
				{
					if (manualSignal == "Long"  ||  manualSignal == "Short")
						signalDir = manualSignal;
					
					// If URL read fails, increase checkSeconds & nextCheckTime time
					if (urlRetries >= 3  &&  checkSeconds == CheckSeconds)
					{
						checkSeconds = 300;
						nextCheckTime = DateTime.Now.AddSeconds(checkSeconds - CheckSeconds);
						Log($"\nurlRetries = {urlRetries} : Trouble getting signal; Changed checkSeconds = {checkSeconds}; nextCheckTime = {nextCheckTime.ToString()}\n");
					}
					else 
					{
						// If URL read *had* failed, now succeeeded, reset checkSeconds
						if (urlRetries < 3  &&  checkSeconds != CheckSeconds)
						{
							checkSeconds = CheckSeconds;
							Log($"\nurlRetries = {urlRetries} : Reset checkSeconds = {checkSeconds}\n");
						}
						if (DateTime.Now >= nextCheckTime  ||  manualSignal != "None")
						{
							//Log($"Checked NOW ( {DateTime.Now.ToString()} ) against nextCheckTime ( {nextCheckTime.ToString()} ) - CALLING CheckSignal()");
							urlRetries = 0;
							if (manualSignal == "None")
								CheckSignalSync();  // This will wait for the async method to complete
							//Log($"Back from CheckSignalSync, signalDir = {signalDir}");

							if (signalDir != "None")
							{
								string extra = (manualSignal == "None") ? "" : "[MANUAL] ";
								Log(extra + $"Jacob Signal Received @ {DateTime.Now.ToString()} - signalDir = {signalDir}; checking trade filters...");
								if (TradeFiltersOkay(signalDir))
								{
									Log($"\nTrade Filters Okay; ENTERING {signalDir.ToUpper()} TRADE");
							
/*										/// Close open position if we are in opposite direction
									/// (May be unnecessary, but seems cleaner)
									string curPos = Position.MarketPosition.ToString();
									if (curPos != "Flat"  &&  curPos != signalDir)
									{
										Log($"(Open position is opposite; closing first)");
										ClosePosition("CloseOnRev", curPos);
									}*/
									PlaceMarketOrder(signalDir, Quantity);
									lastTradeDir = signalDir;
									manualSignal = "None";
								}
							}
						}
					}
				}
				#endregion
				
				/// No need to check anything further if no position open
				if (Position.MarketPosition == MarketPosition.Flat)
					return;
				
				#region BREAKEVEN
		        double newStopPrice = 0;
				double oldPrice = 0;

				/// Get existing stop price if exists
				/// Working seems to not mean 'active', but that it's still in exchange queue. Accepted means it's active.
				if (slOrder != null  &&  (slOrder.OrderState == OrderState.Working  ||  slOrder.OrderState == OrderState.Accepted))
				{
					oldPrice = slOrder.StopPrice;
					//Log("Old Stop Price = " + oldPrice);
				}

				/// Had this issue above in OnExecutionUpdate(); so same here...?
				/// Turns out the marketPosition passed in may not be the same as our actual position direction,
				/// perhaps because this is called in reference to the stop postion(??).  So get correct one
                //var currentMarketPosition = Position.MarketPosition;
				
				double avePrice = AvePrice();
				if (Position.MarketPosition == MarketPosition.Long)
				{
			        /// Calculate new stop price
					if (beTriggered1Price == 0  &&  BETrigger1 != 0  &&  High[0] >= avePrice + BETrigger1*TickSize)
						beTriggered1Price = newStopPrice = avePrice + BEOffset1*TickSize;
					else if (beTriggered2Price == 0  &&  BETrigger2 != 0  &&  High[0] >= avePrice + BETrigger2*TickSize)
						beTriggered2Price = newStopPrice = avePrice + BEOffset2*TickSize;
					else if (beTriggered3Price == 0  &&  BETrigger3 != 0  &&  High[0] >= avePrice + BETrigger3*TickSize)
						beTriggered3Price = newStopPrice = avePrice + BEOffset3*TickSize;
					else if (beTriggered4Price == 0  &&  BETrigger4 != 0  &&  High[0] >= avePrice + BETrigger4*TickSize)
						beTriggered4Price = newStopPrice = avePrice + BEOffset4*TickSize;

					if (newStopPrice != 0)
						Log($"MoveToBE TRIGGERED: newStopPrice set to {newStopPrice}.  Old stop price = {oldPrice}");
					
					/// Abort if new price does not tighten stop
					if (newStopPrice != 0  &&  oldPrice != 0  &&  newStopPrice <= oldPrice)
						newStopPrice = 0;
				}
				else if (Position.MarketPosition == MarketPosition.Short)
				{
			        /// Calculate new stop price
					if (beTriggered1Price == 0  &&  BETrigger1 != 0  &&  Low[0] <= avePrice - BETrigger1*TickSize)
						beTriggered1Price = newStopPrice = avePrice - BEOffset1*TickSize;
					else if (beTriggered2Price == 0  &&  BETrigger2 != 0  &&  Low[0] <= avePrice - BETrigger2*TickSize)
						beTriggered2Price = newStopPrice = avePrice - BEOffset2*TickSize;
					else if (beTriggered3Price == 0  &&  BETrigger3 != 0  &&  Low[0] <= avePrice - BETrigger3*TickSize)
						beTriggered3Price = newStopPrice = avePrice - BEOffset3*TickSize;
					else if (beTriggered4Price == 0  &&  BETrigger4 != 0  &&  Low[0] <= avePrice - BETrigger4*TickSize)
						beTriggered4Price = newStopPrice = avePrice - BEOffset4*TickSize;
					
					if (newStopPrice != 0)
						Log($"MoveToBE TRIGGERED: newStopPrice set to {newStopPrice}.  Old stop price = {oldPrice}");
					
					/// Abort if new price does not tighten stop
					if (newStopPrice != 0  &&  oldPrice != 0  &&  newStopPrice >= oldPrice)
						newStopPrice = 0;
				}
				if (newStopPrice != 0)
				{
					Log($"Calling ModifyStopLoss()");
					ModifyStopLoss(newStopPrice);
				}
				
				#endregion
				
				#region TRIGGER TRAIL
				/// This section just looks at triggering trail, and expanding TP to new value (just once)
				if (!trailTriggered  &&  UseTrailingStop)
				{
					if ((Position.MarketPosition == MarketPosition.Long  &&  High[0] >= AvePrice() + TS_ActivationTicks*TickSize)
					||  (Position.MarketPosition == MarketPosition.Short  &&  Low[0] <= AvePrice() - TS_ActivationTicks*TickSize))
					{
						trailTriggered = true;
						if (TS_TakeProfitTicks > TakeProfit3)
						{
							double m = (Position.MarketPosition == MarketPosition.Long) ? 1.0 : -1.0;
							ModifyTakeProfit3(AvePrice() + m*TS_TakeProfitTicks*TickSize);
						}
					}
				}
				#endregion
			}
		}
		
		#region JACOB SIGNAL
		/// This class needed for JSON parsing
		public class SignalResponse
		{
			public DateTime est_time { get; set; }
		    public int gammaflip { get; set; }
		}
		
		
		/// Synchronous method to call async process
		public void CheckSignalSync()
		{
			try
			{
				/// Use Task.Run to run the async method and Wait to block until it's complete
				Task.Run(async () => await GetSignal()).Wait();
				//Log($"Back from Parse, signalDir = {signalDir}");
			}
			catch (AggregateException ae)
			{
				/// Handle exceptions thrown by the task
				foreach (var e in ae.InnerExceptions)
				{
					Log("Exception: " + e.Message);
				}
			}
		}		
		
		
		/// Get signal from URL
		public async Task GetSignal()
		{
			//-------------------------------------------------------------------------------------------
			// Microsoft help site said HttpWebRequest, and to use HttpClient
			//		See: 
			//		https://learn.microsoft.com/en-us/dotnet/api/system.net.http.httpclient?view=net-8.0
			//		(though ChatGPT got me a long way from this)
			//-------------------------------------------------------------------------------------------
			// Call asynchronous network methods in a try/catch block to handle exceptions.
			try
			{
				/// Set up the next time for checking URL
				nextCheckTime = DateTime.Now.AddSeconds(checkSeconds);
				//Log($"Set nextCheckTime to now + CHECK)SECONDS: {nextCheckTime.ToString()}");
				
				if (urlRetries >= 3)
					return;			// Only try 3 times
				
				/// Get response
				string urlResponse = await Client.GetStringAsync(URL);
				//Log("\n{DateTime.Now.ToString()}  ::  Response = " + urlResponse);
				
				if (urlResponse != "[]")
					ParseResponse(urlResponse);
			}
			catch (HttpRequestException e)
			{
				//Log($"\nException Caught! Retries = {urlRetries}");
				Log($"HTTP Message :{e.Message}");
				urlRetries++;
				
	            /// Retry logic
	            await Task.Delay(5000); // Wait for 5 seconds before retrying
	            GetSignal().Wait();
	        }
	        catch (Exception e)
	        {
	            Log($"\nUnexpected Exception Caught!");
				Log($"Message :{e.Message}");
	        }
		}

		
		/// Parse the response received from GetSignal()
		private void ParseResponse(string response)
		{
			try
			{
				/// Deserialize the JSON response into a list of SignalResponse objects
				List<SignalResponse> signalList = JsonConvert.DeserializeObject<List<SignalResponse>>(response);
				
				/// Check if there are any signals in the list
				if (signalList == null  ||  signalList.Count < 1)
				{
					Log($"No signals found in the response ({response}).  signalList = {signalList}, count = {signalList.Count}");
					return;
				}
				
				/// For debugging, we log all [today's] symbols when button is pressed
				if (logAllSignals)
				{
					Log($"==================================================");
					Log($"  Response from URL Query");
					Log($"==================================================");
					foreach (var sig in signalList)
					{
						if (sig.est_time.Date != DateTime.Now.Date)
							continue;
						if (sig.gammaflip != 0)
						{
							string dir = (sig.gammaflip > 0) ? "Long" : "Short";
							Log($"  Time: {sig.est_time.ToString()}      Direction: {dir}");
						}
					}
					Log($"==================================================");
					logAllSignals = false;
					return;
				}
				
				/// Process the latest signal from the list
				SignalResponse lastSignal = signalList[signalList.Count - 1];
			
				/// If the last signal seen isn't even today, do nothing (wait)
				if (lastSignal.est_time.Date != DateTime.Now.Date)
					return;
				
				/// date time vars used below
				DateTime myTimeZone, delayLimit, currentTime = DateTime.Now;

				/// Check for "prevailing" (pre-market) direction first
				if (lastTradeDir == "None"  &&  !noPrevailingToday  &&  !firstSignalRecevied)
				{
					/// Sometimes we do not actually get 09:30 data until a few minutes later.
					/// The 1st or 2nd entry (09:30 or 09:31) will be "Prevailing"
					/// But we don't know when the EA will be applied; perhaps after a genuine signal is posted.
					/// So to get accurate lastTradeDir, we just need to look for the last signal.  Technically, 
					/// if it as all 4 set and is 09:30 or 09:31, then it's prevailing, and if otherwise, it's just 
					/// lastTradeDir; basically all the same in function though.
					int yy = DateTime.Now.Year;
					int mm = DateTime.Now.Month;
					int dd = DateTime.Now.Day;
					DateTime usOpen0 = new DateTime(yy, mm, dd, 9, 30, 0);	// The signals come in stamped ET
					DateTime usOpen1 = usOpen0.AddMinutes(1);

					/// Iterate backward over signals starting at the end
					for (int i = signalList.Count-1; lastTradeDir == "None"; i--)
					{
						SignalResponse sigResp = signalList[i];
						
						/// If we get back to one which is no longer the current day, we have gone too far
						if (sigResp.est_time.Date != DateTime.Now.Date)
							break;
						
						if (sigResp.gammaflip != 0)
						{
							/// The last non-zero sum is the last direction.
							lastTradeDir = (sigResp.gammaflip == 1) ? "Long" : "Short";
							prvSignalTime = sigResp.est_time;
							Log($"Captured 'Prevailing Dir' as {lastTradeDir} (gammaflip = {sigResp.gammaflip}) @ {sigResp.est_time.ToString()}, and set to lastTradeDir for Continuation trade");
							
							/// We found most recent direction; so long as it is not a currently valid signal, return
							myTimeZone = sigResp.est_time.AddHours(TimeZoneOffset);
							delayLimit = myTimeZone.AddSeconds(MaxDelaySeconds);
							if (MaxDelaySeconds != -1  &&  currentTime > delayLimit)
								return;
						}
					}
					/// If we did not find it, then set a reminder so we don't repeat this every 5 seconds...
					if (lastTradeDir == "None"  &&  lastSignal.est_time > usOpen1)
						noPrevailingToday = true;
				}
				
				/// If signal is zero, then it's just continuation of previous...
				if (lastSignal.gammaflip == 0)
				{
					/// Update prvSignalTime to the current signal's timestamp
					/// (doesn't really matter unless prvSignalTime is zero)
					if (lastSignal.est_time > prvSignalTime)
						prvSignalTime = lastSignal.est_time;
					return;
				}
				
				/// If most recent signal (regardless of validity) is same as current market direction, skip
				/// This is a quick shortcut made in an attempt to reduce redundant debug logging
				if (Position.MarketPosition != MarketPosition.Flat)
				{
					if ((lastSignal.gammaflip ==  1  &&  Position.MarketPosition == MarketPosition.Long)
					|| (lastSignal.gammaflip == -1  &&  Position.MarketPosition == MarketPosition.Short))
						return;
				}
				
				/// Set up some useful datetime vars
				myTimeZone = lastSignal.est_time.AddHours(TimeZoneOffset);
				delayLimit = myTimeZone.AddSeconds(MaxDelaySeconds);
				
				/// Check if the last signal's timestamp is newer than the previous one
				if (lastSignal.est_time > prvSignalTime)
				{
					/// Update prvSignalTime to the current signal's timestamp
					prvSignalTime = lastSignal.est_time;
					
					/// Check that there was not too much delay between the signal post time and now
					if (MaxDelaySeconds == -1)
						Log($"Ignoring delay between Current Time ( {currentTime.ToString()} ) & Adjusted Post Time ( {myTimeZone.ToString()} )");
					else if (currentTime > delayLimit)
					{
						Log($"Rejected: Difference between Current Time ( {currentTime.ToString()} ) & Adjusted Post Time ( {myTimeZone.ToString()} ) Too Large (Max {MaxDelaySeconds} seconds)");
						return;
					}
					
					/// Handle buy/sell signals based on gammaflip value
					signalDir = (lastSignal.gammaflip > 0) ? "Long" : "Short";
					Log($"Signal received: {signalDir} at {lastSignal.est_time}");
					firstSignalRecevied = true;
				}
				else if (lastSignal.est_time < prvSignalTime)
					Log($"SHOULD NOT HAPPEN : Rejected: Post Time ( {lastSignal.est_time.ToString()} ) older than previous signal ( {prvSignalTime.ToString()} )");
				//else Log($"Last signal rejected: Post Time ( {lastSignal.est_time.ToString()} ) is the same as previous signal ( {prvSignalTime.ToString()} )");
		    }
			catch (Exception ex)
			{
				Log("Error parsing JSON response: " + ex.Message);
			}
		}
		#endregion
	
		#region TRADE MANAGEMENT
		private void PlaceMarketOrder(string dir, int qty)
		{
			Log($"Placing market order, dir = {dir}, qty = {qty}");
			if (Position.MarketPosition != MarketPosition.Flat  &&  Position.MarketPosition.ToString() != dir)
			{
				Log($"Existing position is {Position.MarketPosition.ToString()}, so closing position first");
				ClosePosition("CloseOnRev");
				
				// Assume the close worked; need to reset this NOW because it's multi-threaded
				stopsSubmitted = false;
			}
			var action = (dir == "Long") ? OrderAction.Buy : OrderAction.SellShort;
			SubmitOrderUnmanaged(0, action, OrderType.Market, qty, 0, 0, "", "Entry");
		}
		
		private void ClosePosition(string name="Close", string dir="", int qty=0)
		{
			if (Position.MarketPosition == MarketPosition.Flat)
				return;
			
			if (qty == 0)	qty = Position.Quantity;
			if (dir == "")	dir = Position.MarketPosition.ToString();
			var action = (dir == "Long") ? OrderAction.Sell : OrderAction.BuyToCover;
			
			if (qty == Position.Quantity)
			{
				if (slOrder != null  ||  tpOrder3 != null)
				{
					Log($"Closing all contracts, and slOrder != null  ||  tpOrder3 != null, so Cancelling Stops");
					CancelStops();
					inContinuationTrade = false;	// assume it closes
				}
			}
			Log($"Closing Position: name = {name}, dir = {dir}, qty = {qty}");
			SubmitOrderUnmanaged(0, action, OrderType.Market, qty, 0, 0, "", name);
		}

		private void ModifyStopLoss(double newSL)
		{
			/// If there is no existing SL, we must place the order
			if (slOrder == null)
			{
				var action = (Position.MarketPosition == MarketPosition.Long) ? OrderAction.Sell : OrderAction.BuyToCover;
				oco = (tpOrder3 != null) ? tpOrder3.Oco : "";
				Log($"No SL existed on Long position, so placing new SL order, price @ {newSL}");
				SubmitOrderUnmanaged(0, action, OrderType.StopMarket, Position.Quantity, 0, newSL, oco, "StopLoss");
			}
			else
			{
				Log($"Moving SL to new price @ {newSL} (either trail or move to breakeven)");
				ChangeOrder(slOrder, Position.Quantity, 0, newSL);
			}
		}

		private void ModifyTakeProfit3(double newTP)
		{
			/// If there is no existing TP (e.g. No TakeProfit3 set, but TS_TakeProfitTicks set), then we must place the order
			if (tpOrder3 == null)
			{
				var action = (Position.MarketPosition == MarketPosition.Long) ? OrderAction.Sell : OrderAction.BuyToCover;
				oco = (slOrder != null) ? slOrder.Oco : "";
				Log($"No TP existed on Long position, so placing new TP order, price @ {newTP}");
				SubmitOrderUnmanaged(0, action, OrderType.Limit, quantityTP3, newTP, 0, oco, "TakeProfit");
			}
			else
			{
				Log($"Moving TP3 to new price @ {newTP}");
				ChangeOrder(tpOrder3, quantityTP3, newTP, 0);
			}
		}
		
		private void CancelStops()
		{
			if (slOrder != null)
			{
				Log($"Cancelling slOrder");
				CancelOrder(slOrder);
				slOrder = null;
			}
			if (tpOrder1 != null)
			{
				Log($"Cancelling tpOrder1");
				CancelOrder(tpOrder1);
				tpOrder1 = null;
			}
			if (tpOrder2 != null)
			{
				Log($"Cancelling tpOrder2");
				CancelOrder(tpOrder2);
				tpOrder2 = null;
			}
			if (tpOrder3 != null)
			{
				Log($"Cancelling tpOrder3");
				CancelOrder(tpOrder3);
				tpOrder3 = null;
			}
		}
		
		private double AvePrice()
		{
			double avePrice = Position.AveragePrice;
			
			/// If ave price does not fall on tick boundary, round up
			int numTicks = (int)(avePrice / TickSize);
			if (numTicks * TickSize != avePrice)
			{
				double prc = Instrument.MasterInstrument.RoundToTickSize(avePrice);
				Log($"\n      AvePrice between ticks, Rounding, from {avePrice} to {prc}\n\n");
				avePrice = prc;
			}
			return(avePrice);
		}
		#endregion
		
		#region TRAIL & SWING
		/// Return new trailing stoploss value, or zero if none/unchanged
		private double GetNewTrailPrice(string direction)
		{
			double priceHLC = Close[0];
			double oldSL = 0;
			double newSL = 0;
			if (Position.MarketPosition.ToString() != direction  ||  !trailTriggered)
				return newSL;
			
			/// Working seems to not mean 'active', but that it's still in exchange queue. Accepted means it's active.
			if (slOrder != null  &&  (slOrder.OrderState == OrderState.Working  ||  slOrder.OrderState == OrderState.Accepted))
			{
				oldSL = slOrder.StopPrice;
				Log("Old Stop Price = " + oldSL);
			}

			double entryPrice = AvePrice();
			double ticksInProfit = (direction == "Short") ? (entryPrice - Close[0]) / TickSize : (Close[0] - entryPrice) / TickSize;
			double ticksToOldSL = 0;
			priceHLC = (TS_UseClose) ? Close[0] : (direction == "Short") ? Low[0] : High[0];
			if (oldSL != 0)
				ticksToOldSL = (direction == "Short") ? (oldSL - priceHLC) / TickSize : (priceHLC - oldSL) / TickSize;

			/// Set up Min and Max Ticks according to how much we are in profit
			int minDist = TS_MinTicks;
			int maxDist = TS_MaxTicks;
			if (TS_ReAdjustAtTicks2 > 0  &&  ticksInProfit >= TS_ReAdjustAtTicks2)
			{
				minDist = TS_MinTicks2;
				maxDist = TS_MaxTicks2;
			}
			else if (TS_ReAdjustAtTicks1 > 0  &&  ticksInProfit >= TS_ReAdjustAtTicks1)
			{
				minDist = TS_MinTicks1;
				maxDist = TS_MaxTicks1;
			}
				
			if (TS_UseSimpleTrail)
			{
				/// ticksToOldSL is zero if there is no old SL
				if (ticksToOldSL > TS_SimpleTicks)
				{
					if (direction == "Short") 
						newSL = priceHLC + TS_SimpleTicks*TickSize;
					else
						newSL = priceHLC - TS_SimpleTicks*TickSize;
				}
				if (newSL != 0)
					Log("Simple trail: OldSL = {oldSL}, NewSL = {newSL}");
				return newSL;
			}
			
			/// Loop through swing strengths from 5 down to 1, looking for 
			/// the highest strength that fits the min & max requirement
			/// Yes, this seems brute force, but c'est la vie!
			///
			/// No, screw that.  It seems like if we just look at strength=1, 
			/// in the small range between TS_MinTicks & TS_MaxTicks, and use 
			/// the first one we find, it will often actually have a better 
			/// strength.  Lets test it out...
			///
			/// Tried to write it do potentially do the original idea, but 
			/// it's pretty complex, so if this seems to work out fine, 
			/// functionally what we want, I should simplify this.  
			///
			/// And if not, need to make modifications as well...
			int i, j, ss;
			double distToFractal, slPrice;
			for (i=1; newSL == 0  &&  i < firstEntryBarsAgo; i++)
			{
				/// Until proven otherwise, assume the candle we are testing (index i) 
				/// DOES indeed the have the fractal H/L we seek, so start slPrice as H/L
				slPrice = (direction == "Short") ? High[i] - TS_OffsetTicks*TickSize : Low[i] + TS_OffsetTicks*TickSize;
				
				/// If slPrice wouldn't even move the stop, skip to next candle
				if (oldSL != 0)
					if ((direction == "Short") ? (oldSL <= slPrice) : (oldSL >= slPrice))
						continue;

				// Get the distance from the H/L (or Close) to the alleged fractal
				priceHLC = (TS_UseClose) ? Close[0] : (direction == "Short") ? Low[0] : High[0];
				distToFractal = (direction == "Short") ? (slPrice - priceHLC) / TickSize : (priceHLC - slPrice) / TickSize;
				
				// ss = swing strength; here hard-coded to 1
				for (ss=1; newSL == 0  &&  ss > 0; ss--)
				{
					for (j = 1; newSL == 0  &&  j <= ss; j++)
					{
						if (direction == "Short")
						{
							/// Not a Swing High; try next candle
							if (High[i+j] > High[i]  ||  High[i-j] > High[i])
								slPrice = 0;
						}
						else if (direction == "Long")
						{
							/// These aren't the droids you're looking for...
							if (Low[i+j] < Low[i]  ||  Low[i-j] < Low[i])
								slPrice = 0;
						}
						/// Found a LL or HH, so this ain't it; check next smaller SS
						if (slPrice == 0)
							break;	// out of for(j...) loop
						
						/// Found a fractal; see if its far enough and not too far from price
						else if (distToFractal >= minDist  &&  distToFractal <= maxDist)
						{
							if (oldSL == 0 
							|| (direction == "Short"  &&  slPrice < oldSL) 
							|| (direction == "Long"   &&  slPrice > oldSL))
							{
								newSL = slPrice;
								Log($"Found SwingStrength(1) fractal @ {Time[i].ToString()} (index {i}); set newSL = {newSL}  (oldSL = {oldSL})");
							}
						}
					}
				}
			}
			
			/// If we found no fractal, but our current stop is > max, we need to move it
			if (newSL == 0  &&  (ticksToOldSL > maxDist  ||  oldSL == 0))
			{
				Log($"No fractal found since trade opened; checking for max distance");
				priceHLC = (TS_UseClose) ? Close[0] : (direction == "Short") ? Low[0] : High[0];
				if (direction == "Short") 
				{
					newSL = priceHLC + maxDist*TickSize;
					if (newSL >= oldSL)	newSL = 0;
				}
				else
				{
					newSL = priceHLC - maxDist*TickSize;
					if (newSL <= oldSL)	newSL = 0;
				}
				if (newSL != 0)
					Log($"old SL too distant (or absent), so moving to {newSL}");
			}
			return newSL;
		}
		
		/// This is used to get the last swing H/L for a breakout price used for continuation trades
		private double GetLastSignificantSwingPrice(string direction, int min=MIN_LOOKBACK, int add=ADD_LOOKBACK, int max=MAX_LOOKBACK)
		{
			if (direction != "Long"  &&  direction != "Short")
				return -1;
			///
			/// fetch the price of the last most significant swing H/L in the given direction
			/// (we use the term "Fractal" and "Swing H/L" interchangeably
			///

			/// To initialize, we want to use the last Strength(1) SwingHL
			int i, j;
			double initPrice = (direction == "Long") ? High[1] : Low[1];
			double price = (direction == "Long") ? High[1] : Low[1];
			int lastIdx = 1;
			for (i=1; i < 50; i++)
			{
				if (direction == "Short"  &&  Low[i+1] > Low[i]  &&  Low[i-1] > Low[i])
				{
					initPrice = price = Low[i];
					lastIdx = i;
					break;
				}
				else if (direction == "Long"  &&  High[i+1] < High[i]  &&  High[i-1] < High[i])
				{
					initPrice = price = High[i];
					lastIdx = i;
					break;
				}
			}
			//if (i == 50)	Log($"{direction} : Could not find Strength(1) fractal in last 50 bars; using index[1] H/L @ {price}");
			//else			Log($"{direction} : Found Strength(1) fractal at bar[{lastIdx}] ({Time[lastIdx].ToString()}) @ {price}");
			
			
			//Log($"Finding last {direction} fractal price...");
			/// Use hard-coded (for now at least):
			/// We will use an initial lookback of 61 bars, because often there is a volume/price 
			/// spike 60 minutes before 9:30 open.  Then we will keep looking back 20 bars at a 
			/// time until we do not find a more extreme swing price.
			int lookBack = MIN_LOOKBACK;
			int additional = 20;
			if (CurrentBars[0] - ContStrength < lookBack)
			{
				//Log($"GetLastSignificantSwingPrice: There are only {CurrentBars[0]} bars on the chart, so must reduce look back period");
				lookBack = CurrentBars[0] - ContStrength;
			}
			
			int start = ContStrength + 1;
			int lb = lookBack;
			bool furtherFound = true;
			while (furtherFound  &&  lb < MAX_LOOKBACK)
			{
				/// iterate over last lookBack candles seeking fractal levels
				for (j = start; j < lb; j++)
				{
					/// Test candle 'j' to see if it qualifies as Swing
					bool newFound = true;
					for (i = 1; i <= ContStrength; i++)
					{
						if (direction == "Long")
						{
							if (High[j+i] > High[j]  ||  High[j-i] > High[j])	
							{
								newFound = false;
								break;
							}
						}
						else if (direction == "Short")
						{
							if (Low[j+i] < Low[j]  ||  Low[j-i] < Low[j])
							{
								newFound = false;
								break;
							}
						}
					}
					
					/// If it DID qualify and is more extreme than previous, save it
					if (newFound)
					{
						/// Save last swing index, even if it's not a new H/L (see below)
						//Log($"Continuation Fractal Found @ index {j} ({Time[j].ToString()})");
						if (direction == "Long"  &&  High[j] > price)
						{
							lastIdx = j;
							price = High[j];
							//Log($"Fractal @ index {j} ({Time[j].ToString()}) also marks new High @ {price}");
						}
						else if (direction == "Short"  &&  Low[j] < price)
						{
							lastIdx = j;
							price = Low[j];
							//Log($"Fractal @ index {j} ({Time[j].ToString()}) also marks new Low @ {price}");
						}
						/// No need to check the next 'ContStrength' candles; we know they are lower/higher
						j += ContStrength;
					}
				}
				
				/// If the lastIdx was more than (lookBack - additional) back, 
				/// then look back a bit further for a more extreme H/L
				if (lastIdx > lb - additional)
				{
					start = lb;
					lb += additional;
					//Log($"Last fractal index @ {lastIdx} was close to end, so look back {additional} bars further; start = {start}, new limit = {lb}");
				}
				/// If the lastIdx found was more than 20 candles forward 
				/// the earliest one checked, then just use this fractal point
				else
					furtherFound = false;
			}
			/// If whatever fractal found was not higher/lower than H/L[1], then we are 
			/// actively heading up/down and need to wait for at least a 1-bar retrace
			if (direction == "Short")
			{
				if (price > initPrice)
				{
					//Log($"Fractal low found @ Bar[{lastIdx}] ({Time[lastIdx].ToString()}) was {price}, which is higher than last Low Swing(1) @ {initPrice}, so NONE FOUND");
					return -1;
				}
				else if (price > Low[1])
				{
					//Log($"Fractal low found @ Bar[{lastIdx}] ({Time[lastIdx].ToString()}) was {price}, which is higher than Low[1] @ {Low[1]}, so NONE FOUND");
					return -1;
				}
			}
			else	// Long
			{
				if (price < initPrice)
				{
					//Log($"Fractal high found @ Bar[{lastIdx}] ({Time[lastIdx].ToString()}) was {price}, which is lower than last High Swing(1) @ {initPrice}, so NONE FOUND");
					return -1;
				}
				else if (price < High[1])
				{
					//Log($"Fractal high found @ Bar[{lastIdx}] ({Time[lastIdx].ToString()}) was {price}, which is lower than High[1] @ {High[1]}, so NONE FOUND");
					return -1;
				}
			}
			//Log($"Found Fractal price level @ Bar[{lastIdx}] ({Time[lastIdx].ToString()}) = {price}");
			return price;
		}
		#endregion

		#region DEBUG & OCD
		private void ManageOCD()
		{
			string text = $"\n\n Strategy Name            :   {StrategyName}";
			text += $"\n Trade Session Active   :   {inSession}";
			if (lastTradeDir != "None"  &&  !firstSignalRecevied)
				text += $"\n Prevailing Dir @ Open :   {lastTradeDir}";
			else
				text += $"\n Last Signal Direction   :   {lastTradeDir}";

			if (ContTradesMode != ContTradesModeEnum.Disabled  &&  inSession)
			{
				text += $"\n\n Cont Trades Enabled   :   {contTradesEnabled}";
				text += $"\n Continuation Trades    :   {contTradesTaken}";
				text += $"\n Swing Price Long         :   {((lastSigSwingPriceL == -1) ? "None" : lastSigSwingPriceL.ToString("F2"))}";
				text += $"\n Swing Price Short        :   {((lastSigSwingPriceS == -1) ? "None" : lastSigSwingPriceS.ToString("F2"))}";
			}
			
			if (UseTrailingStop)
				text += $"\n Trail Triggered              :   {trailTriggered}";
			
			text += $"\n Daily Limit Hit              :   {dailyLimitHit}";

			if (nextCheckTime != DateTime.MinValue)
			{
				TimeSpan remaining = nextCheckTime - DateTime.Now;
				text += $"\n\n Next Signal Check in    :   {remaining.ToString(@"hh\:mm\:ss")}";
			}

			if (lastDashboard == text)
				return;
			
			Draw.TextFixed(this, "OCD", text, TextPosition.TopLeft);	
			lastDashboard = text;
		}
		
		private void Log(string message, [CallerMemberName] string memberName = "")
		{
			if (DisableLogging  ||  lastLogMsg == message)
				return;

			// Set this scripts Print() calls to the second output tab?
			if (UseOutput2)		PrintTo = PrintTo.OutputTab2;
			else				PrintTo = PrintTo.OutputTab1;
			
			string id = "EA #" + UniqueID;
			
			DateTime date = (State == State.Historical) ? Time[0] : DateTime.Now;
			string dateStr = (State == State.Historical) ? date.ToString() : date.ToString(@"HH\:mm\:ss");
			
			if (lastCaller != memberName)
			{
				Print($"\n{id} - {chartInstrument} - [{memberName}]  -  {dateStr}  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -\n");
				Print(message);
			}
			else if (lastLogMsg != message)
			{
				if (lastLogTime != dateStr)	// Output just time if diff time but not new caller
					Print(message + $"   ( {dateStr} )");
				else
					Print(message);
			}

			lastLogMsg = message;
			lastCaller = memberName;
			lastLogTime = dateStr;
		}		
		
		#endregion
		
		#region FILTERS
		/// Enter a trade [set] if all enabled filters are satisfied
		private bool TradeFiltersOkay(string direction)
		{
			/// Do not filter a manual command to enter a position
			if (manualSignal != "None")
				return(true);
			
			if (direction != "Long"  &&  direction != "Short")
			{
				Log($"Direction passed into TradeFiltersOkay ({direction}) must be 'Long' or 'Short'; Aborting");
				return false;
			}
			
			/// Save the last signal direction (even if actual trade is blocked by filters)
			lastTradeDir = direction;
			
			if (direction == "Long")
			{
				direction = "None";
				if (!longOn)
					Log($"Long trade disabled; Aborting entry");
				else if (Position.MarketPosition == MarketPosition.Long)
					Log($"Long trade already open; Aborting entry");
				else
					direction = "Long";
			}
			else if (direction == "Short")
			{
				direction = "None";
				if (!shortOn)
					Log($"Short trade disabled; Aborting entry");
				else if (Position.MarketPosition == MarketPosition.Short)
					Log($"Short trade already open; Aborting entry");
				else
					direction = "Short";
			}
			
			if (direction == "None")
				return false;

			if (VOLMA(Closes[0], AveVolumePeriod)[1] < MinAveVolume)
			{
				Log($"Volume insufficient ({VOLMA(Closes[0], AveVolumePeriod)[1]} vs. {MinAveVolume} minimum); Aborting {direction} entry");
				return false;
			}
			if (!CheckSession())
			{
				Log($"Out of session; Aborting {direction} entry");
				return false;
			}
			return true;
		}
		
		/// Check trading sessions; return false if out-of-session
		private bool CheckSession()
		{
			if (!LimitTradingHours)
				return true;
			
			bool hoursOk = false; 
			if (LimitTradingHours)
			{
				if (StartTime.TimeOfDay < EndTime.TimeOfDay)
				{
					if (Time[0].TimeOfDay >= StartTime.TimeOfDay  &&  Time[0].TimeOfDay < EndTime.TimeOfDay)
						hoursOk = true;
				}
				else if (StartTime.TimeOfDay > EndTime.TimeOfDay)
				{
					if (Time[0].TimeOfDay >= StartTime.TimeOfDay  ||  Time[0].TimeOfDay < EndTime.TimeOfDay)
						hoursOk = true;
				}
				else // (StartTime1.TimeOfDay == EndTime1.TimeOfDay)
				{
					Log($"Start Time 1 is the same as End Time 1; Trading [always] approved");
						hoursOk = true;
				}
			}
			return hoursOk;
		}
		#endregion
		
		#region BUTTONS
		private void OnMyButtonClick(object sender, RoutedEventArgs rea)
		{
			System.Windows.Controls.Button button = sender as System.Windows.Controls.Button;
			
			Log("\n");
			bool lWasOn = longOn;
			bool sWasOn = shortOn;
			if (button.Name == "longButton")
			{
				if (longOn)
				{
					button.Background = Brushes.Gray;
					button.Content = "Long Disabled";
					longOn = false;
					Log("\nLONG ENTRIES DISABLED!");
				}
				else
				{
					button.Background = Brushes.Green;
					button.Content = "Long Enabled";
					longOn = true;
					Log("\nLong entries Enabled!");
				}
			}
			else if (button.Name == "shortButton")
			{
				if (shortOn)
				{
					button.Background = Brushes.Gray;
					button.Content = "Short Disabled";
					shortOn = false;
					Log("\nSHORT ENTRIES DISABLED!");
				}
				else
				{
					button.Background = Brushes.Crimson;
					button.Content = "Short Enabled";
					shortOn = true;
					Log("\nShort entries Enabled!");
				}
			}
			else if (button.Name == "exitButton")
			{
				if (Position.MarketPosition == MarketPosition.Flat)
				Log("\nUser-Instigated Close of All Open Strategy Positions, but nothing open..");
				else
				{
					Log("\n\n=====================================================");
					Log("User-Instigated Close of All Open Strategy Positions");
					Log("=====================================================\n");
					ClosePosition();
				}
			}
			else if (button.Name == "logSignalsButton")
			{
				logAllSignals = true;
				CheckSignalSync();
			}
			if (button.Name == "manualEnterLong")
			{
				Log($"    ....MANUAL LONG ENTRY ON #{UniqueID}....");
				manualSignal = "Long";
			}
			else if (button.Name == "manualEnterShort")
			{
				Log($"    ....MANUAL SHORT ENTRY ON #{UniqueID}....");
				manualSignal = "Short";
			}
			
			if (!longOn  &&  !shortOn)
				if (lWasOn  ||  sWasOn)
					Log("ALL ENTRIES DISABLED!");
			else if (longOn  &&  shortOn)
				if (!lWasOn  ||  !sWasOn)
					Log("ALL ENTRIES ENABLED!");
			Log("\n");

			entriesEnabled = (longOn  ||  shortOn);
		}
		
		
		protected void CreateButtons()
		{
			if (UserControlCollection.Contains(myGrid))
		        return;
		
			myGrid = new System.Windows.Controls.Grid
	        {
	          Name = "MyCustomGrid",
	          HorizontalAlignment = HorizontalAlignment.Left,
	          VerticalAlignment = VerticalAlignment.Bottom,
	        };
	 	 
	        myGrid.ColumnDefinitions.Add(new System.Windows.Controls.ColumnDefinition());
	        myGrid.ColumnDefinitions.Add(new System.Windows.Controls.ColumnDefinition());
	        myGrid.ColumnDefinitions.Add(new System.Windows.Controls.ColumnDefinition());
			myGrid.RowDefinitions.Add(new System.Windows.Controls.RowDefinition());
			myGrid.RowDefinitions.Add(new System.Windows.Controls.RowDefinition());
			myGrid.ColumnDefinitions[0].Width = new GridLength(80);
			myGrid.ColumnDefinitions[1].Width = new GridLength(80);
			myGrid.ColumnDefinitions[2].Width = new GridLength(80);
			myGrid.RowDefinitions[0].Height = new GridLength(25);
			myGrid.RowDefinitions[1].Height = new GridLength(25);
	 
	        longButton = new System.Windows.Controls.Button
	        {
	          	Name = "longButton",
	          	Content = "Long Enabled",
	          	Foreground = Brushes.White,
	          	Background = Brushes.Green,
				Height = 25,
				Width = 80,
				FontSize = 10,
				HorizontalAlignment = HorizontalAlignment.Center,
				Focusable = false
	        };
			
			shortButton = new System.Windows.Controls.Button
	        {
	          	Name = "shortButton",
	          	Content = "Short Enabled",
	          	Foreground = Brushes.White,
	          	Background = Brushes.Firebrick,
				Height = 25,
				Width = 80,
				FontSize = 10,
				HorizontalAlignment = HorizontalAlignment.Center,
				Focusable = false
	        };
	 
			exitButton = new System.Windows.Controls.Button
	        {
	          	Name = "exitButton",
	          	Content = "Exit All",
	          	Foreground = Brushes.White,
	          	Background = Brushes.DarkMagenta,
				Height = 25,
				Width = 80,
				FontSize = 10,
				HorizontalAlignment = HorizontalAlignment.Center,
				Focusable = false
	        };
	 
			logSignalsButton = new System.Windows.Controls.Button
	        {
	          	Name = "logSignalsButton",
	          	Content = "Log Signals",
	          	Foreground = Brushes.White,
	          	Background = Brushes.Blue,
				Height = 25,
				Width = 80,
				FontSize = 10,
				HorizontalAlignment = HorizontalAlignment.Center,
				Focusable = false
	        };
	 
			manualEnterLong = new System.Windows.Controls.Button
	        {
	          	Name = "manualEnterLong",
	          	Content = "Enter Long",
	          	Foreground = Brushes.Black,
	          	Background = Brushes.Lime,
				Height = 25,
				Width = 80,
				FontSize = 10,
				HorizontalAlignment = HorizontalAlignment.Center,
				Focusable = false
	        };
	 
			manualEnterShort = new System.Windows.Controls.Button
	        {
	          	Name = "manualEnterShort",
	          	Content = "Enter Short",
	          	Foreground = Brushes.White,
	          	Background = Brushes.Crimson,
				Height = 25,
				Width = 80,
				FontSize = 10,
				HorizontalAlignment = HorizontalAlignment.Center,
				Focusable = false
	        };
			
			longButton.Click += OnMyButtonClick;
			shortButton.Click += OnMyButtonClick;
			exitButton.Click += OnMyButtonClick;
			logSignalsButton.Click += OnMyButtonClick;
			manualEnterLong.Click += OnMyButtonClick;
			manualEnterShort.Click += OnMyButtonClick;
			
	        System.Windows.Controls.Grid.SetColumn(shortButton, 0);
			System.Windows.Controls.Grid.SetRow(shortButton, 1);
			System.Windows.Controls.Grid.SetColumn(longButton, 0);
			System.Windows.Controls.Grid.SetRow(longButton, 0);
			System.Windows.Controls.Grid.SetColumn(exitButton, 1);
			System.Windows.Controls.Grid.SetRow(exitButton, 0);
			System.Windows.Controls.Grid.SetColumn(logSignalsButton, 1);
			System.Windows.Controls.Grid.SetRow(logSignalsButton, 1);
			System.Windows.Controls.Grid.SetColumn(manualEnterLong, 2);
			System.Windows.Controls.Grid.SetRow(manualEnterLong, 0);
			System.Windows.Controls.Grid.SetColumn(manualEnterShort, 2);
			System.Windows.Controls.Grid.SetRow(manualEnterShort, 1);
			
	        myGrid.Children.Add(longButton);
			myGrid.Children.Add(shortButton);
			myGrid.Children.Add(exitButton);
			myGrid.Children.Add(logSignalsButton);
			myGrid.Children.Add(manualEnterLong);
			myGrid.Children.Add(manualEnterShort);

			UserControlCollection.Add(myGrid);
		}
		
		
		public void DisposeButtons() 
		{
			if (myGrid != null)
	        {
				if (longButton != null)
				{
					myGrid.Children.Remove(longButton);
					longButton.Click -= OnMyButtonClick;
					longButton = null;
				}
				if (shortButton != null)
				{
					myGrid.Children.Remove(shortButton);
					shortButton.Click -= OnMyButtonClick;
					shortButton = null;
				}
				if (exitButton != null)
				{
					myGrid.Children.Remove(exitButton);
					exitButton.Click -= OnMyButtonClick;
					exitButton = null;
				}
				if (logSignalsButton != null)
				{
					myGrid.Children.Remove(logSignalsButton);
					logSignalsButton.Click -= OnMyButtonClick;
					logSignalsButton = null;
				}
				if (manualEnterLong != null)
				{
					myGrid.Children.Remove(manualEnterLong);
					manualEnterLong.Click -= OnMyButtonClick;
					manualEnterLong = null;
				}
				if (manualEnterShort != null)
				{
					myGrid.Children.Remove(manualEnterShort);
					manualEnterShort.Click -= OnMyButtonClick;
					manualEnterShort = null;
				}
	        }
		}
		#endregion
		
		#region GPT_AttemptToDisableInputs
		/*
		private void UpdatePropertyVisibility()
		{
			bool isEnabled = ContTradesMode != ContTradesModeEnum.Disabled;
			
			var propDescriptor = TypeDescriptor.GetProperties(this)["ContTradesNum"];
			if (propDescriptor != null)
			{
				BrowsableAttribute browsableAttribute = new BrowsableAttribute(isEnabled);
				AttributeCollection attributes = propDescriptor.Attributes;
				TypeDescriptor.AddAttributes(this, browsableAttribute);
			}
			
			NotifyPropertyChanged("ContTradesNum");
		}

		private void NotifyPropertyChanged(string propertyName)
		{
			/// THIS count# SHORTCUT MEANS: If (PropertyChanged != NULL), call PropertyChanged(this, new PropertyChangedEventArgs(propertyName))
			PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
		}

		private void UpdateContTradesEnabled()
		{
			contTradesEnabled = ContTradesMode switch
			{
				ContTradesModeEnum.Disabled => false,
				ContTradesModeEnum.BeforeAndAfter => true,
				ContTradesModeEnum.OnlyAfterSignal => firstSignalRecevied,
				ContTradesModeEnum.OnlyBeforeSignal => !firstSignalRecevied,
				_ => contTradesEnabled
			};
			
			/// Print to check visibility update
			Log($"ContinuationModeChanged: ContTradesMode = {ContTradesMode}, contTradesEnabled = {contTradesEnabled}");
		}
		*/
		#endregion
		
		#region PROPERTIES
		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name = "Order Quantity", Description = "The initial number of contracts to place per order", Order=0, GroupName = "1. Entry")]
		public int Quantity
		{ get; set; }
		
		[NinjaScriptProperty]
        [Display(Name = "Last Trade Direction", Description = "Override for initial Last Trade Direction", Order=3, GroupName = "1. Entry")]
        public string LastTradeDir
        { get; set; }

		[NinjaScriptProperty]
		[Range(1, 300)]
		[Display(Name = "Check URL Interval", Description = "How many seconds between URL signal checks", Order=5, GroupName = "1. Entry")]
		public int CheckSeconds
		{ get; set; }

		[NinjaScriptProperty]
		[Range(1, 300)]
		[Display(Name = "Signal Delay Max Seconds", Description = "Maximum seconds allowed between signal time stamp and entry time", Order=6, GroupName = "1. Entry")]
		public int MaxDelaySeconds
		{ get; set; }

		[NinjaScriptProperty]
		[Range(-23, 23)]
		[Display(Name = "Timezone Offset", Description = "Number of hours to add the signal PostTime to compare with local time", Order=7, GroupName = "1. Entry")]
		public int TimeZoneOffset
		{ get; set; }

		
		
		[NinjaScriptProperty]
		[Display(Name = "Continuation Trades Mode", Description = "How continuation trades are handled", Order=10, GroupName = "2. ReEntry")]
		public ContTradesModeEnum ContTradesMode { get; set; }
		
		[NinjaScriptProperty]
        [Display(Name = "BiDirectional", Description = "If no previous trade / prevailing direction, trade either breakout", Order=11, GroupName = "2. ReEntry")]
        public bool BiDirectional
        { get; set; }
		
		[NinjaScriptProperty]
		[Display(Name = "Continuation Trades Number", Description = "Number of continuation trades to take", Order=12, GroupName = "2. ReEntry")]
		public ContTradesNumberEnum ContTradesNum { get; set; }
		
		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name = "Continuation Swing Strength", Description = "Swing ind setting for H/L on Prevailing & Continuation trades", Order=13, GroupName = "2. ReEntry")]
		public int ContStrength
		{ get; set; }
		
		[NinjaScriptProperty]
        [Display(Name = "Require MA position", Description = "Require fast EMA above slow for long cont. trade and vice versa", Order=15, GroupName = "2. ReEntry")]
        public bool ContReqMAPosition
        { get; set; }
		
        [NinjaScriptProperty]
        [Range(2, int.MaxValue)]
        [Display(Name = "Slow EMA Period", Description = "Period parameter of Slow EMA", Order=16, GroupName = "2. ReEntry")]
        public int SlowEMA_Period
        { get; set; }

        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "Fast EMA Period", Description = "Period parameter of Fast EMA", Order=17, GroupName = "2. ReEntry")]
        public int FastEMA_Period
        { get; set; }


		
		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name = "Stop Loss", Description = "Initial stop loss in ticks", Order=20, GroupName = "3. Exits")]
		public int StopLoss
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name = "1st Take Profit", Description = "Take profit #1 in ticks", Order=21, GroupName = "3. Exits")]
		public int TakeProfit1
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name = "Exit Quantity #1", Description = "Number of contracts to close on TP1", Order=22, GroupName = "3. Exits")]
		public int QuantityTP1
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name = "2nd Take Profit", Description = "Take profit #2 in ticks", Order=23, GroupName = "3. Exits")]
		public int TakeProfit2
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name = "Exit Quantity #2", Description = "Number of contracts to close on TP2", Order=24, GroupName = "3. Exits")]
		public int QuantityTP2
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name = "Final Take Profit", Description = "Final take profit in ticks. If using only one TP, use this one & zero the others", Order=25, GroupName = "3. Exits")]
		public int TakeProfit3
		{ get; set; }

		
		
		[NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name = "Daily Target Min $", Description = "Profit amount at which to stop trading for the day", Order=30, GroupName = "4. Targets")]
        public int DailyMinTarget
        { get; set; }
		
		[NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name = "Daily Loss Max $", Description = "Loss amount at which to stop trading for the day", Order=31, GroupName = "4. Targets")]
        public int DailyMaxLoss
        { get; set; }
		
		
		
		[NinjaScriptProperty]
		[Range(int.MinValue, int.MaxValue)]
		[Display(Name = "BE Trigger #1", Description = "1st profit triger in ticks", Order=40, GroupName = "5. Break Even")]
		public int BETrigger1
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(int.MinValue, int.MaxValue)]
		[Display(Name = "BE Offset #1", Description = "1st move to breakeven offset", Order=41, GroupName = "5. Break Even")]
		public int BEOffset1
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(int.MinValue, int.MaxValue)]
		[Display(Name = "BE Trigger #2", Description = "2nd profit triger in ticks", Order=42, GroupName = "5. Break Even")]
		public int BETrigger2
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(int.MinValue, int.MaxValue)]
		[Display(Name = "BE Offset #2", Description = "2nd move to breakeven offset", Order=43, GroupName = "5. Break Even")]
		public int BEOffset2
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(int.MinValue, int.MaxValue)]
		[Display(Name = "BE Trigger #3", Description = "3rd profit triger in ticks", Order=45, GroupName = "5. Break Even")]
		public int BETrigger3
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(int.MinValue, int.MaxValue)]
		[Display(Name = "BE Offset #3", Description = "3rd move to breakeven offset", Order=46, GroupName = "5. Break Even")]
		public int BEOffset3
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(int.MinValue, int.MaxValue)]
		[Display(Name = "BE Trigger #4", Description = "4th profit triger in ticks", Order=47, GroupName = "5. Break Even")]
		public int BETrigger4
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(int.MinValue, int.MaxValue)]
		[Display(Name = "BE Offset #4", Description = "4th move to breakeven offset", Order=48, GroupName = "5. Break Even")]
		public int BEOffset4
		{ get; set; }

		
		
		[NinjaScriptProperty]
        [Display(Name = "Use Trailing Stop", Description = "", Order=50, GroupName = "6. Trailing Stop")]
        public bool UseTrailingStop
        { get; set; }
		
        [NinjaScriptProperty]
        [Display(Name = "Use Simple Trail", Description = "Trail tick-for-tick", Order=51, GroupName = "6. Trailing Stop")]
        public bool TS_UseSimpleTrail
        { get; set; }
		
        [NinjaScriptProperty]
        [Range(int.MinValue, int.MaxValue)]
        [Display(Name = "Simple Trail Ticks", Description = "Number of ticks offset from Swing or Candle H/L; Negative to allow more cushion", Order=52, GroupName = "6. Trailing Stop")]
        public int TS_SimpleTicks
        { get; set; }
		
        [NinjaScriptProperty]
        [Display(Name = "Use Candle Close", Description = "Use candle close price, not H/L", Order=53, GroupName = "6. Trailing Stop")]
        public bool TS_UseClose
        { get; set; }
		
		[NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name = "Take Profit Ticks", Description = "Expand Take Profit to this when activated", Order=55, GroupName = "6. Trailing Stop")]
        public int TS_TakeProfitTicks
        { get; set; }

        [NinjaScriptProperty]
        [Range(int.MinValue, int.MaxValue)]
        [Display(Name = "Tick Offset", Description = "Number of ticks offset from Swing or Candle H/L; Negative to allow more cushion", Order=56, GroupName = "6. Trailing Stop")]
        public int TS_OffsetTicks
        { get; set; }
		
        [NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name = "Activation Ticks", Description = "Number of ticks in profit to activate trail", Order=57, GroupName = "6. Trailing Stop")]
        public int TS_ActivationTicks
        { get; set; }

        [NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name = "Minimum Tick Distance", Description = "Minimum number of ticks SL can be placed from current price", Order=58, GroupName = "6. Trailing Stop")]
        public int TS_MinTicks
        { get; set; }

        [NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name = "Maximum Tick Distance", Description = "Maximum number of ticks SL can be placed from current price", Order=60, GroupName = "6. Trailing Stop")]
        public int TS_MaxTicks
        { get; set; }

        [NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name = "Adjust Min/Max Ticks #1", Description = "Adjust min/max number of ticks after reaching this profit level (Zero to disable)", Order=61, GroupName = "6. Trailing Stop")]
        public int TS_ReAdjustAtTicks1
        { get; set; }

        [NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name = "Minimum Tick Distance #1", Description = "Minimum number of ticks SL can be placed from current price", Order=62, GroupName = "6. Trailing Stop")]
        public int TS_MinTicks1
        { get; set; }

        [NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name = "Maximum Tick Distance #1", Description = "Maximum number of ticks SL can be placed from current price", Order=63, GroupName = "6. Trailing Stop")]
        public int TS_MaxTicks1
        { get; set; }

        [NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name = "Adjust Min/Max Ticks #2", Description = "Adjust min/max number of ticks after reaching this profit level (Zero to disable)", Order=65, GroupName = "6. Trailing Stop")]
        public int TS_ReAdjustAtTicks2
        { get; set; }

        [NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name = "Minimum Tick Distance #2", Description = "Minimum number of ticks SL can be placed from current price", Order=66, GroupName = "6. Trailing Stop")]
        public int TS_MinTicks2
        { get; set; }

        [NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name = "Maximum Tick Distance #2", Description = "Maximum number of ticks SL can be placed from current price", Order=67, GroupName = "6. Trailing Stop")]
        public int TS_MaxTicks2
        { get; set; }

		
		
		[NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "Average Volume Period", Description = "Period parameter to use for Average Volume", Order=70, GroupName = "7. Volume")]
        public int AveVolumePeriod
        { get; set; }
		
		[NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "Minimum Average Volume", Description = "Minimum Average Volume to place trade", Order=71, GroupName = "7. Volume")]
        public int MinAveVolume
        { get; set; }
		

		
		[NinjaScriptProperty]
        [Display(Name = "Limit Trading Hours", Description = "Use Trading Session #1", Order=80, GroupName = "8. Trading Hours")]
        public bool LimitTradingHours
        { get; set; }

        [NinjaScriptProperty]
        [PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
        [Display(Name = "Start Time", Description = "Enter Start Time for Entries", Order=81, GroupName = "8. Trading Hours")]
        public DateTime StartTime
        { get; set; }

        [NinjaScriptProperty]
        [PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
        [Display(Name = "End Time", Description = "Enter End Time for Entries", Order=82, GroupName = "8. Trading Hours")]
        public DateTime EndTime
        { get; set; }

		[NinjaScriptProperty]
        [Display(Name = "Use Close Time", Description = "Enable to close all trades at given time", Order=83, GroupName = "8. Trading Hours")]
        public bool UseCloseTime
        { get; set; }

        [NinjaScriptProperty]
        [PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
        [Display(Name = "Close Time", Description = "Enter Time to Close All Trades", Order=84, GroupName = "8. Trading Hours")]
        public DateTime CloseTime
        { get; set; }

		
		
		[NinjaScriptProperty]
        [Display(Name = "Display OCD", Description = "Show the On-Chart Display", Order=90, GroupName = "9. Misc / Debug")]
        public bool DisplayOCD
        { get; set; }

		[NinjaScriptProperty]
        [Display(Name = "Strategy Name", Description = "Name of Strategy", Order=91, GroupName = "9. Misc / Debug")]
        public string StrategyName
        { get; set; }

		[NinjaScriptProperty]
        [Display(Name = "Disable Logging", Description = "Disable logging to Ninjascript Output windows", Order=92, GroupName = "9. Misc / Debug")]
        public bool DisableLogging
        { get; set; }

		[NinjaScriptProperty]
        [Display(Name = "Use Output 2", Description = "Send logging to second Output window", Order=93, GroupName = "9. Misc / Debug")]
        public bool UseOutput2
        { get; set; }

		[NinjaScriptProperty]
        [Display(Name = "Unique ID", Description = "Unique number to identify strategy in Output window", Order=94, GroupName = "9. Misc / Debug")]
        public string UniqueID
        { get; set; }
		#endregion
	}
}
