#region Using declarations
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Gui.Tools;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
#endregion

//This namespace holds Bars types in this folder and is required. Do not change it.
namespace NinjaTrader.NinjaScript.BarsTypes
{
	public class rgVolatilityRenko : BarsType
	{
		private double barMax = 0, barMin = 0;

		protected override void OnStateChange()
		{
			if (State == State.SetDefaults)
			{
				Description									= @"Volatility Renko bars with configurable overlap. Body Size controls the bar size in ticks, Overlap controls how many ticks bars will overlap with the previous bar (0 for standard Renko), and Minimum Timespan sets the minimum time between bars in seconds.";
				Name										= "rgVolatilityRenko";
				BarsPeriod									= new BarsPeriod { BarsPeriodType = (BarsPeriodType) 8681314, Value = 1 };
				BuiltFrom									= BarsPeriodType.Tick;
				DaysToLoad									= 5;
				IsIntraday									= true;
			}
			else if (State == State.Configure)
			{
				Properties.Remove(Properties.Find("BaseBarsPeriodType", true));
                Properties.Remove(Properties.Find("PointAndFigurePriceType", true));
                Properties.Remove(Properties.Find("ReversalType", true));

                SetPropertyName("Value", "Body Size (tick)");
                SetPropertyName("Value2", "Overlap (tick)");
                SetPropertyName("BaseBarsPeriodValue", "Minimum Timespan (sec)");


			}
		}

		public override int GetInitialLookBackDays(BarsPeriod barsPeriod, TradingHours tradingHours, int barsBack)
		{
			return 1; //replace with your bars logic
		}

		protected override void OnDataPoint(Bars bars, double open, double high, double low, double close, DateTime time, long volume, bool isBar, double bid, double ask)
		{
			if (SessionIterator == null)
				SessionIterator = new SessionIterator(bars);

			bool isNewSession = SessionIterator.IsNewSession(time, isBar);
			if (isNewSession)
				SessionIterator.CalculateTradingDay(time, isBar);

			int bodySize = bars.BarsPeriod.Value;
			int overlapTicks = bars.BarsPeriod.Value2;

			// Ensure overlap doesn't exceed bodySize-1
			overlapTicks = Math.Min(overlapTicks, bodySize - 1);
			overlapTicks = Math.Max(overlapTicks, 0); // Ensure it's not negative
			int minSeconds = bars.BarsPeriod.BaseBarsPeriodValue;
			double tick = bars.Instrument.MasterInstrument.TickSize;
			
			// Calculate the actual bar size considering overlap
			// The overlap parameter specifies how many ticks to advance (not overlap)
			// So for a 12-tick bar with 3-tick overlap parameter, we want to advance 3 ticks
			double effectiveBarSize = overlapTicks * tick;

			if (bars.Count == 0 || (bars.IsResetOnNewTradingDay && isNewSession))
		    {
		        barMax = close + bodySize*tick;
		        barMin = close - bodySize*tick;

                AddBar(bars, close, close, close, close, time, volume);

				bars.LastPrice = close;
				return;
		    }

			if (bars.Count > 0)
		    {
                double barHigh = bars.GetHigh(bars.Count - 1);
                double barLow = bars.GetLow(bars.Count - 1);
				DateTime barTime = bars.Count<2 ? DateTime.MinValue : bars.GetTime(bars.Count-2);

                bool maxExceeded = bars.Instrument.MasterInstrument.Compare(Math.Max(barHigh,close), barMax) >= 0;
                bool minExceeded = bars.Instrument.MasterInstrument.Compare(Math.Min(barLow,close), barMin) <= 0;
				bool timeExceeded = time.Subtract(barTime).TotalSeconds >= minSeconds;

                //### Defined Range Exceeded?
                if (timeExceeded && (maxExceeded || minExceeded))
                {
                    int barDirection = maxExceeded ? 1 : minExceeded ? -1 : 0;

                    // Calculate the new open price based on the effective bar size (considering overlap)
                    // For an overlap of 3 in a 12-tick bar, we want to move 9 ticks (12-3)
                    // The direction is 1 for up bars, -1 for down bars
                    double fakeOpen;

                    if (overlapTicks > 0)
                    {
                        // For overlapping bars, we want to go back from the close by the effective bar size
                        fakeOpen = close - (effectiveBarSize * barDirection);
                    }
                    else
                    {
                        // For non-overlapping bars, we want to use the standard Renko calculation
                        // which will be handled in the special case below
                        fakeOpen = close - (bodySize * tick * barDirection);
                    }

                    // Debug output
                    System.Diagnostics.Debug.WriteLine($"Body Size: {bodySize}, Overlap: {overlapTicks}, Advance: {bodySize - overlapTicks}, Direction: {barDirection}, Close: {close}, FakeOpen: {fakeOpen}, Movement: {Math.Abs(close - fakeOpen) / tick} ticks");

                    //### Close Current Bar
					double _high = maxExceeded ? close : barHigh;
					double _low = minExceeded ? close : barLow;
                    UpdateBar(bars, _high, _low, close, time, volume);

                    // Store the current boundaries for potential use in zero overlap case
                    double prevBarMax = barMax;
                    double prevBarMin = barMin;

                    //### Add New Bar
                    // Set the boundaries for the next bar
                    // The boundaries should be based on the full body size, not the effective size
                    // This ensures the bar size is always consistent
                    barMax = fakeOpen + bodySize * tick;
                    barMin = fakeOpen - bodySize * tick;

                    // Debug output for boundaries
                    System.Diagnostics.Debug.WriteLine($"New bar boundaries: Min={barMin}, Max={barMax}, Range={bodySize * 2} ticks");

					// Special handling for zero overlap (standard Renko behavior)
					// In standard Renko, the open of the new bar is exactly at the boundary that was exceeded
					if (overlapTicks == 0)
					{
						// For standard Renko, set the open to the boundary that was crossed
						if (maxExceeded)
							fakeOpen = prevBarMax; // Open at the upper boundary that was exceeded
						else if (minExceeded)
							fakeOpen = prevBarMin; // Open at the lower boundary that was exceeded

                        // Recalculate the boundaries based on the new fakeOpen
                        barMax = fakeOpen + bodySize * tick;
                        barMin = fakeOpen - bodySize * tick;
					}

					_high = maxExceeded ? close : fakeOpen;
					_low = minExceeded ? close : fakeOpen;

                    AddBar(bars, fakeOpen, _high, _low, close, time, volume);
                }
                //### Current Bar Still Developing
                else
                {
					double _high = close > barHigh ? close : barHigh;
					double _low = close < barLow ? close : barLow;
                    UpdateBar(bars, _high, _low, close, time, volume);
                }
		    }

			bars.LastPrice = close;
		}

		public override void ApplyDefaultBasePeriodValue(BarsPeriod period)
		{
			//replace with any default base period value logic
		}

		public override void ApplyDefaultValue(BarsPeriod period)
		{
			period.Value = 4;				//### Body Size Value
            period.Value2 = 0;				//### Overlap (tick)
            period.BaseBarsPeriodValue = 1;	//### Minimum Timespan
		}

		public override string ChartLabel(DateTime time)
		{
			return time.ToString("T", Core.Globals.GeneralOptions.CurrentCulture);
		}

		public override double GetPercentComplete(Bars bars, DateTime now)
		{
			return 1.0d; //replace with your bar percent logic
		}




	}
}
