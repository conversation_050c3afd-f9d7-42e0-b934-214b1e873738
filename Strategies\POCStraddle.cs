#region USING_DECLARATIONS
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Runtime.CompilerServices;	/// For MemberCallerName
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Gui.Tools;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.Indicators;
using NinjaTrader.NinjaScript.DrawingTools;
using NinjaTrader.NinjaScript.Indicators.VOLAREIndicators;
using NinjaTrader.NinjaScript.SharedPOC;
#endregion

/// This namespace holds Strategies in this folder and is required. Do not change it. 
namespace NinjaTrader.NinjaScript.Strategies
{
	public class POCStraddle : Strategy
	{
		private const string		VERSION = "*******";
		#region GLOBALS
		private double				slPrice = 0;
		private double				tpPrice = 0;
		private double				ieStopPrice = 0;
		private double				ieLimitPrice = 0;
		private double				dailyProfit = 0;
		private double				straddle1 = 0;
		private double				straddle2 = 0;
		private double				poc = 0;
		private double				percentage1 = 50;
		private double				percentage2 = 10;
		private double				DailyMinTarget = 0;
		private double				DailyMaxLoss = 0;
		
		private int					opens_x_ticks = 2;
		private int					close_x_ticks = 2;
		private int					activeSessionNum = 0;
		private int					lastSignal = 0;
		private int					histBarsCount = -1;
		private int					netWon = -1;
		private int					barRange = -1;
		private int					ticksSL = 0;
		private int					firstTickBar = -1;
		private int					lastTickBar = -1;
		private int					immedTradeDir = 0;

		/// For Log/OCD
		private string 				lastDashboard = "";
		private string 				lastLogMsg = "";
		private string 				lastCaller = "";
		private string				chartInstrument = "";
		private string				lastLogTime;
		private string				tmpDate = "";
		private string				entryName;
		private string				dbgTTText;
		private string				logPath = "";
		
		private bool				inSession = true;
		private bool				inProfit = false;
		private bool				inTrade = false;
		private bool				isLong = false;
		private bool				isShort = false;
		private bool				slChecked = false;
		private bool				longEnabled	= true;
		private bool				shortEnabled = true;
		private bool				justExited = false;
		private bool				enterLong = false;
		private bool				enterShort = false;
		private bool				stopsSubmitted = false;
		private bool				dailyLimitHit = false;
		private bool				isFirstTick = false;
		private bool				isLastTick = false;
		private bool				dataLoadedOk = false;
		
		/// For Sessions
		private DateTime			startTime1, startTime2, startTime3;
		private DateTime			endTime1, endTime2, endTime3;
		private DateTime			closeTime, closeTime1, closeTime2, closeTime3;

		/// For order handling
		private Order				orderEntry = null;
		private Order				orderSL = null;
		private Order				orderTP = null;

		/// For indicators used
		private SignalType			signalType = SignalType.Percentage;
		private StraddlePOCSignal	volare;
		private StochasticsFast		stoch;

		private System.Windows.Controls.Button		shortButton, longButton, exitButton;
		private System.Windows.Controls.Grid		myGrid;
		#endregion
		
		protected override void OnStateChange()
		{
			if (State == State.SetDefaults)
			{
				Description									= @"Enter the description for your new custom Strategy here.";
				Name										= "POCStraddle";
				Calculate									= Calculate.OnBarClose;
				EntriesPerDirection							= 1;
				EntryHandling								= EntryHandling.AllEntries;
				IsExitOnSessionCloseStrategy				= true;
				ExitOnSessionCloseSeconds					= 30;
				IsFillLimitOnTouch							= false;
				MaximumBarsLookBack							= MaximumBarsLookBack.TwoHundredFiftySix;
				OrderFillResolution							= OrderFillResolution.Standard;
				Slippage									= 0;
				StartBehavior								= StartBehavior.WaitUntilFlat;
				TimeInForce									= TimeInForce.Gtc;
				TraceOrders									= false;
				RealtimeErrorHandling						= RealtimeErrorHandling.StopCancelClose;
				StopTargetHandling							= StopTargetHandling.PerEntryExecution;
				BarsRequiredToTrade							= 3;
				IsInstantiatedOnEachOptimizationIteration	= true;
				/// Disable IsInstantiatedOnEachOptimizationIteration for performance gains in 
				/// Strategy Analyzer optimizations. See the Help Guide for additional information
				
				Quantity				= 1;
				TrendTrade				= false;
				ReqStraddlePOC			= true;
				StochLong				= false;
				StochShort				= false;
				EnterBracket			= true;
				PendingTickOffset		= 3;
				NetWinsMax				= 3;

				ExitEndOfBar			= false;
				ExitOnOppositeSignal	= false;
				ExitOnStochFastExtreme	= false;
				
				TicksTP					= 40;
				UsePOC_SL				= false;
				LongStopOffsetTicks		= 10;
				ShortStopOffsetTicks	= 10;
				LongTicksMaxSL			= 40;
				ShortTicksMaxSL			= 40;
				UseTrail				= false;
				TrailTicks				= 4;
				
				PeriodD					= 3;
				PeriodK					= 3;
				UpperLevel				= 80;
				LowerLevel				= 20;

				TimeZoneOffset			= -3; 		/// Everythig defaulted in east coast time

				UseSession1				= false;
				StartTime1				= DateTime.Parse("06:00 AM", System.Globalization.CultureInfo.InvariantCulture);
				EndTime1				= DateTime.Parse("09:00 AM", System.Globalization.CultureInfo.InvariantCulture);
				UseCloseTime1			= false;
				CloseTime1				= DateTime.Parse("09:20 AM", System.Globalization.CultureInfo.InvariantCulture);

				UseSession2				= false;
				StartTime2				= DateTime.Parse("09:30 AM", System.Globalization.CultureInfo.InvariantCulture);
				EndTime2				= DateTime.Parse("01:00 PM", System.Globalization.CultureInfo.InvariantCulture);
				UseCloseTime2			= false;
				CloseTime2				= DateTime.Parse("02:50 PM", System.Globalization.CultureInfo.InvariantCulture);

				UseSession3				= false;
				StartTime3				= DateTime.Parse("05:00 PM", System.Globalization.CultureInfo.InvariantCulture);
				EndTime3				= DateTime.Parse("02:30 AM", System.Globalization.CultureInfo.InvariantCulture);
				UseCloseTime3			= false;
				CloseTime3				= DateTime.Parse("05:50 AM", System.Globalization.CultureInfo.InvariantCulture);

				UseCloseTime			= false;
				CloseTime				= DateTime.Parse("02:50 PM", System.Globalization.CultureInfo.InvariantCulture);
				
				StrategyName			= "POCStraddle";
				DisplayOCD				= true;
				StartingLine			= 0;
				DisableLogging			= false;
				LogToFileName			= Name;
				UseOutput2				= false;
				UniqueID				= "";
				
				/// Non-Input-related stuff
				dataLoadedOk			= false;	/// Reset on init
			}
			else if (State == State.Configure)
			{
				if (LogToFileName != "")
				{
					logPath = NinjaTrader.Core.Globals.UserDataDir + @"log\" + LogToFileName + @"\" + LogToFileName + "Log.txt";
					//Print($"                            logPath = {logPath}");
					if (System.IO.File.Exists(logPath))
					{
						try
						{
							System.IO.File.Delete(logPath);
						}
						catch (Exception ex)
						{
							Print("Could not delete log file (logPath): {ex.Message}");
						}
					}
				}
				chartInstrument = this.Instrument.FullName;
				AddDataSeries(BarsPeriodType.Tick, 1);
			}
			else if (State == State.DataLoaded)
			{				
				/// Minimum bars/ticks for stability (adjust as needed).  There was an issue where the whole 
				/// program crashed when I loaded only 80 bars (which was done to reduce the amount of 
				/// historical processing for troubleshooting).  It took a long time (w/Grok) to find, so I 
				/// have added this code to check there are enough ticks for the volumetric bars, and at 
				/// least ecit gracefully.
				int minRangeBars = Math.Max(PeriodD, PeriodK) + 5;	/// Enough for stoch and volumetric warm-up
				int avgTicksPerBar = 5000;							/// Guess based on brief NQ observation
				int minTicks = minRangeBars * avgTicksPerBar;	    /// Guessing 3000 (ave) per bar is enough
				
				Print($"Bars[0].Count = {BarsArray[0]?.Count ?? -1}, Ticks[1].Count = {BarsArray[1]?.Count ?? -1}");
				
				// Check if BarsArray[0] and BarsArray[1] are fully loaded before accessing GetTime
				if (BarsArray[0] == null  ||  BarsArray[0].Count <= 0  ||  BarsArray[1] == null  ||  BarsArray[1].Count <= 0)
				{
					Print($"Error: BarsArray[0] or BarsArray[1] not fully loaded. Bars[0].Count = {BarsArray[0]?.Count ?? -1}, Ticks[1].Count = {BarsArray[1]?.Count ?? -1}");
					dataLoadedOk = false;
					return;
				}
				
			    // Safely log the time range ONLY if tick data is available
			    if (BarsArray[1] != null && BarsArray[1].Count > 0)
			    {
			        try
			        {
			            Print($"Time range: First bar = {BarsArray[1].GetTime(0)}, Last bar = {BarsArray[1].GetTime(BarsArray[1].Count - 1)}");
			        }
			        catch (Exception ex)
			        {
			            Print($"Error accessing BarsArray[1].GetTime: {ex.Message}");
			            dataLoadedOk = false;
			            return;
			        }
			    }
			    else
			    {
			        Print("Tick data (BarsArray[1]) not loaded.");
			        dataLoadedOk = false;
			        return;
			    }

				if (BarsArray[0].Count < minRangeBars)
				{
					Print($"Error: Insufficient range bars loaded. Required: {minRangeBars}, Loaded: {BarsArray[0].Count}. Increase 'Bars to load' or use days.");
					dataLoadedOk = false;
					return;
				}
				if (BarsArray[1].Count < minTicks)
				{
					Print($"Error: Insufficient tick data loaded. Required: {minTicks}, Loaded: {BarsArray[1].Count}. Increase 'Bars to load' or use days.");
					dataLoadedOk = false;
					return;
				}
				
			    dataLoadedOk = true;
				
				volare	= StraddlePOCSignal(Signal_Type, Percentage1, Percentage2, OpensX_Ticks, ClosesX_Ticks);
				stoch	= StochasticsFast(PeriodD, PeriodK);
				
				startTime1 	= StartTime1.AddHours(TimeZoneOffset);
				endTime1	= EndTime1.AddHours(TimeZoneOffset);
				closeTime1	= CloseTime1.AddHours(TimeZoneOffset);
				
				startTime2 	= StartTime2.AddHours(TimeZoneOffset);
				endTime2	= EndTime2.AddHours(TimeZoneOffset);
				closeTime2	= CloseTime2.AddHours(TimeZoneOffset);
				
				startTime3 	= StartTime3.AddHours(TimeZoneOffset);
				endTime3	= EndTime3.AddHours(TimeZoneOffset);
				closeTime3	= CloseTime3.AddHours(TimeZoneOffset);
				
				closeTime	= CloseTime.AddHours(TimeZoneOffset);
				
			}
			else if (State == State.Historical)
			{
				Print($"    -----    State == State.Historical, MarketPos = {Position.MarketPosition}, justExited = {justExited}\n");
				histBarsCount = Bars.Count;
				if (ChartControl != null)
				{
					ChartControl.Dispatcher.InvokeAsync(() =>
					{
						CreateButtons();
					});
				}
			}
			else if (State == State.Realtime)
			{
				Print($"    -----    State == State.Realtime, MarketPos = {Position.MarketPosition}, justExited = {justExited}\n");
				Print("\n\n\n-=-  -=-  SWITCHING TO REAL TIME PROCESSING  -=-  -=-  -=-  -=-  -=-  -=-  -=-  -=-  -=-  -=-  -=-\n\n\n\n");
			}
		}
		
		protected override void OnExecutionUpdate(Cbi.Execution execution, string executionId, double price, int quantity, 
												  Cbi.MarketPosition marketPosition, string orderId, DateTime time)
		{
			Log($"Name = {execution.Name}, IsEntryStrategy = {execution.IsEntryStrategy},  OrderAction = {execution.Order.OrderAction},   marketPosition = {marketPosition},   quantity (parameter) = {quantity},   Position.Quantity = {Position.Quantity}\n");
			
			/// We advise monitoring OnExecutionUpd() to trigger submission of stop/target orders instead of OnOrderUpd()
			/// since OnExecutionUpd() is called after OnOrderUpd() which ensures your strategy has received the execution
			/// which is used for internal signal tracking.
			
			double m = (Position.MarketPosition == MarketPosition.Long) ? 1.0 : -1.0;
			double avePrice = AvePrice();

			if (orderEntry != null  &&  orderEntry == execution.Order)
			{
				string type = (execution.IsEntryStrategy) ? "        ENTRY" : "        EXIT";
				
				/// If part filled, I do nothing, and hope that this method gets called again with order state == filled. 
				/// If that never happens, not sure what is the "right" thing to do, or how to tell it never happened...
				if (execution.Order.OrderState == OrderState.PartFilled)
					Log($"{type} ORDER ONLY PARTIALLY FILLED; execution.Order.Filled = {execution.Order.Filled}, Position.Quantity = {Position.Quantity}\n");

				else if (execution.Order.OrderState == OrderState.Cancelled)
				{
					/// If the order was somehow cancelled, then we need to just and position that partially opened
					if (execution.IsEntryStrategy)
					{
						if (execution.Order.Filled > 0)
						{
							Log($"{type} ORDER CANCELLED; dir = {marketPosition.ToString()}, execution.Order.Filled = {execution.Order.Filled}, so closing out those parials\n");
							CloseTrade("Partial Fill");
						}
						else
							Log($"{type} ORDER CANCELLED; dir = {marketPosition.ToString()}, execution.Order.Filled = {execution.Order.Filled}; Doing Nothing\n");
					}
					else
						Log($"{type} ORDER CANCELLED; dir = {marketPosition.ToString()}, execution.Order.Filled = {execution.Order.Filled}, so not sure what we should do here\n");
				}
				else if (execution.Order.OrderState == OrderState.Rejected)
				{
					Log($"{type} ORDER REJECTED; execution.Order.Filled = {execution.Order.Filled}; Doing Nothing\n");
				}
				else if (execution.Order.OrderState == OrderState.Filled)
					Log($"{type} ORDER FILLED @ {price} (execution.Order.OrderState == OrderState.Filled); execution.Order.Filled = {execution.Order.Filled}, Position.Quantity = {Position.Quantity}\n");
			}
			else
				Log($"orderEntry = {orderEntry}, execution.Order.OrderState = {execution.Order.OrderState}\n");

			/// IS ENTRY STRATEGY - Handle actions for execution.Order.OrderAction.Buy or SellShort
			if (execution.IsEntryStrategy)
			{
				tpPrice = (TicksTP > 0) ? avePrice + m*TicksTP*TickSize : 0;
				
				if (slPrice > 0  ||  tpPrice > 0)
				{
					if (orderEntry != null  &&  orderEntry == execution.Order)
					{
						if (execution.Order.OrderState == OrderState.Filled)
						{
							if (!stopsSubmitted)
							{
								/// This used to check that orderSL & tpOrders were null, because we were initially setting, 
								/// but it won't place the stops until order is filled, so no need to check for that. 
								//var action = (marketPosition == MarketPosition.Long) ? OrderAction.Sell : OrderAction.BuyToCover;

								Log($"INIT PLACEMENT OF SL & TP...\n");
								if (slPrice > 0)
								{
									Log($"Submitting StopLoss for Qty {Position.Quantity} @ {slPrice}\n");
									SetStopLoss(slPrice);
								}
								if (TicksTP > 0)
								{
									Log($"Submitting TakeProfit for Qty {Position.Quantity} @ {tpPrice}\n");
									SetTakeProfit(tpPrice);
								}
								stopsSubmitted = true;
							}
							else
								Log($"execution.Order.OrderState == OrderState.Filled & stops already submitted; DOING NOTHING\n");
						}
					}
					else
						Log($"orderEntry = {orderEntry}, execution.Order.OrderState = {execution.Order.OrderState}\n");
				}
			}
			
			/// IS EXIT STRATEGY - Handle actions for execution.Order.OrderAction.Sell or BuyToCover
			else
			{
				/// If we did NOT just close *entire* position, update SL quantity
				if (Position.Quantity > 0)
				{
					Log($"EXIT STRATEGY AND (Position.Quantity > 0)   .........   !!! NO BUENO !!!   .................................................................\n");
					if (execution.Order.OrderState == OrderState.Filled)
					{
						Log($"...................................................................... and (execution.Order.OrderState == OrderState.Filled)\n");
					}
				}
				else
				{
					/// Unnecessary with managed approach
					/// Cancel any stops that are left open. SL & TP3 OCO, but check all to be sure
					//if (CancelStops())
					//	Log($"Position.Quantity == 0; cancelled stops\n");
					
					/// Reset some variables
					Log($"Position.Quantity == 0; RESETTING VARS & checking DailyMaxLoss & DailyMinTarget..\n");
					orderEntry = orderSL = orderTP = null;
					stopsSubmitted = false;
					justExited = false;		/// we're flat, so reset this
					//Log($"\n\n\n                         SET justExited = false \n\n\n\n");
					
			        /// Use SystemPerformance.AllTrades to get the last closed trade
			        if (SystemPerformance.AllTrades.Count > 0)
			        {
			            var lastTrade = SystemPerformance.AllTrades[SystemPerformance.AllTrades.Count - 1];
			
			            /// Access trade details
						//double com = lastTrade.Commission;
						//Log($"Last trade Commission: ${com}\n");
						double pnl = lastTrade.ProfitCurrency;	// includes commission
						Log($"Last trade PnL: ${pnl}\n");
						dailyProfit += pnl;		// we need to save this each time, so we can reload it if we have to restart platform
			        }
					
					/// Check for daily profit/loss limits
					//Log($"DailyMinTarget = {DailyMinTarget}, DailyMaxLoss = ${DailyMaxLoss}\n");
					//Log($"pDaily = (${dailyProfit}), SystemPerformance.RealTimeTrades.TradesPerformance.NetProfit = (${SystemPerformance.RealTimeTrades.TradesPerformance.NetProfit})\n");
					if (DailyMaxLoss > 0  &&  -dailyProfit > DailyMaxLoss)
					{
						/// if (Position.Quantity == 0), Exiting should be unnecessary, but are 
						/// left in case we are reversing position, or otherwise 'just to be safe'
						/// This will do nothing if (Position.MarketPosition == MarketPosition.Flat)
						CloseTrade("Daily Loss");
						Log($"pDaily (${dailyProfit}) - NetProfit (${SystemPerformance.AllTrades.TradesPerformance.NetProfit}) > DailyMaxLoss (${DailyMaxLoss}\n");
						Log($"Daily Loss Hit!\n\n");
						dailyLimitHit = true;
					}
					if (DailyMinTarget > 0  &&  dailyProfit > DailyMinTarget)
					{
						CloseTrade("Daily Loss");
						Log($"NetProfit (${SystemPerformance.AllTrades.TradesPerformance.NetProfit}) - dailyProfit (${dailyProfit}) > DailyMinTarget (${DailyMinTarget}\n");
						Log($"Daily Profit Hit!\n\n");
						dailyLimitHit = true;
					}
					
					if (!dailyLimitHit  &&  immedTradeDir != 0)
					{
						Log($"Opening new position from OnExecUpdate(), cause there was an entry signal directly after exit\n");
						OpenPosition(immedTradeDir, ieStopPrice, ieLimitPrice);
						ieStopPrice = ieLimitPrice = immedTradeDir = 0;
					}
				}
			}
			lastCaller = "";	/// reset so if this method is entered again before other log, reprint the function name
		}

		
		protected override void OnOrderUpdate(Cbi.Order order, double limitPrice, double stopPrice, int quantity, 
											  int filled, double averageFillPrice, Cbi.OrderState orderState, 
											  DateTime time, Cbi.ErrorCode error, string comment)
        {
			string name = order.Name;
			string action = order.OrderAction.ToString();
			double stopPrc = order.StopPrice;			
			double limitPrc = order.LimitPrice;	
			
			/// One time only, as we transition from historical to live, convert any old historical 
			/// order object references to the live order submitted to the real-time account
			/// This addresses the error: "Strategy XXX has been disabled because it attempted to 
			/// modify a historical order that has transitioned to a live order"
			/// 
			/// This was really only for pending orders that get pulled over, which I hope to avoid in this EA
			/// If we ARE using pending orders, it would be better to just cancel them...
			//if (orderEntry != null  &&  orderEntry.IsBacktestOrder  &&  State == State.Realtime)
			//	orderEntry = GetRealtimeOrder(orderEntry);
			
			if (order.OrderState == OrderState.Accepted  
			||  order.OrderState == OrderState.Working  
			||  order.OrderState == OrderState.Filled)
			{
				/// Assign Order objects here - This is called first when placing/cencelling/closing/modifying 
				/// an order.  OnExecUpdate is called after it's all done -this  may get called several times 
				/// first; e.g. for cancelling a SL, it will get called for OrderState.CancelSubmitted, then 
				/// OrderState.CancelPending, and finally OrderState.Cancelled.  THEN OnExecUpdate is called...
				if (order.Name == entryName)
				{
					Log($"order.Name = {name}, orderState is {orderState}; Setting orderEntry var, OrderAction = {action}\n");
					orderEntry = order;
				}
				else if (order.Name == "SL")
				{
					Log($"order.Name = {name}, orderState is {orderState}; Setting orderSL var, price = {stopPrc}\n");
					orderSL = order;
				}
				else if (order.Name == "TP")
				{
					Log($"order.Name = {name}, orderState is {orderState}; Setting orderTP var, price = {limitPrc}\n");
					orderTP = order;
				}
			}
			lastCaller = "";	// reset so if this method is entered again before other log, reprint the function name
		}
		
		protected override void OnBarUpdate()
		{
			string log = "";
			bool actionTaken = false;
			
			//Print($"                                                           CurrentBar = {CurrentBar}      BarsRequiredToTrade = {BarsRequiredToTrade}");
			int warmupBars = Math.Max(PeriodK, PeriodD) + 10;
			if (CurrentBars[0] < warmupBars  ||  CurrentBars[1] < warmupBars)
			{
				return;
			}
			
			/// Make sure indicators & data are okay
			if (volare == null  ||  stoch == null  ||  BarsArray[1] == null)
			{
				if (dataLoadedOk)
				{
					Log("Error: Strategy components not initialized. Disabling.");
					dataLoadedOk = false;
				}
			}			
			/// If this just failed, or failed form above, report & abort
			if (!dataLoadedOk)
				return;

			/// Stuff to initialize (wasn't working in DataLoading section)
			if (barRange == -1  &&  CurrentBar > 1  &&  BarsInProgress == 0)	/// No longer using barRange, but may need it later...
				barRange = (int)((Highs[0][1] - Lows[0][1]) / TickSize);
			

			
			
			
			
			/// SHORTEN HISTORICAL PROCESSING   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *
			DateTime cutoff1 = new DateTime(2025, 3, 21, 1, 54, 0);
			DateTime cutoff2 = new DateTime(2025, 3, 27, 3, 30, 0);
			if (State == State.Historical)
				if (false)
					if (Time[0] < cutoff1  ||  Time[0] > cutoff2)
						return;
			/// SHORTEN HISTORICAL PROCESSING   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *   *
			
			
				



				

			#region CHECKED EVERY TIME - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
			/// Close all trades at end of user-defined session
			if (IsCloseTime())
			{
				log += $"End of Session Close activated @ {DateTime.Now.ToString()}; Closing any open orders\n";
				if (Position.MarketPosition == MarketPosition.Long)		ExitLong();
				else
				if (Position.MarketPosition == MarketPosition.Short)	ExitShort();
				activeSessionNum = 0;
			}
			
			//if (Time[0] < cutoff)		log += $"              Time[0] = {Time[0].ToString("HH:mm:ss")},   BarsInProgress == {BarsInProgress},   Position.MarketPosition = {Position.MarketPosition.ToString()}\n";
			
			/// Do this on every run, whether it is a tick, or end of bar
			isLong = (Position.MarketPosition == MarketPosition.Long);
			isShort = (Position.MarketPosition == MarketPosition.Short);
			var ask = GetCurrentAsk();
			var bid = GetCurrentBid();

			if (Position.MarketPosition == MarketPosition.Flat)
				log += $"    -----    Position.MarketPosition == MarketPosition.Flat, MarketPos = {Position.MarketPosition}, justExited = {justExited}\n";
			
			if (isLong)
			{
				inProfit = (bid > Position.AveragePrice);
				inTrade = true;
				//log += $"\n\n\n               LONG trade open now\n\n";
			}
			else if (isShort)
			{
				inProfit = (ask < Position.AveragePrice);
				inTrade = true;
				//log += $"\n\n\n               SHORT trade open now\n\n";
			}
			else if (inTrade)
			{
				/// If we just exited on previous run....
				inTrade = false;
				//wasInTrade = true;
				if (NetWinsMax > 0)
				{
					if (inProfit)	netWon++;
					else			netWon--;
				}
				justExited = false;		/// we're flat, so reset this
				log += $"\n\n\n              !!!!!!!!!!!!!!!!!!!  SET justExited = false\n\n";
			}
			#endregion



			/// Seems to be correct to use zero for historical... not sure if real time should be changed to 1
			var i = (State == State.Historical) ? 0 : 0;




			
			/// Tick series
			/// Need to wait for one bar to close (BiP == 0), so IsFirst/IsLast will work (historical will trigger)
			if (BarsInProgress == 1)
			{
				actionTaken = true;
				log += $"    -----    (BarsInProgress == 1), MarketPos = {Position.MarketPosition}, justExited = {justExited}\n";
				if (inTrade)
				{
					//log += $"    -----    (BarsInProgress == 1), Tick series Time[0] = {Time[0].ToString("HH:mm:ss.fffff")}, Bid = {GetCurrentBid()}, Ask = {GetCurrentAsk()}\n";
					ManageOpenTrades(ask, bid);
				}
			}
			
			#region CHART SERIES ON BAR CLOSE  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
			/// Chart series (Range bars), running Calculate.OnBarClose
			else if (BarsInProgress == 0)
			{
				enterLong = enterShort = false;
				
				log += $"\n==============================   START BiP = 0    &&   Time[0] = {Time[0].ToString()}   ====================================\n";
				
				log += $"    -----    (BarsInProgress == 0) [BEFORE], MarketPos = {Position.MarketPosition}, justExited = {justExited}\n";
				if (inTrade  &&  ExitEndOfBar)
				{
					/// On bar close, if ExitEndOfBar is enabled, then exit the trade.  Set a flag 
					/// whether the trade was in profit so it can be checked on next tick, to tabulate 
					/// for 'netWon' variable, which is checked in Check Additional Entry
					if (!justExited)
					{
						if (isLong)
						{
							inProfit = (bid > Position.AveragePrice);
							justExited = true;
							ExitLong("EoB", entryName);
							log += $"^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n";
							log += $"  Exited Long ({entryName}) on End of Candle: {Time[0].ToString()}; Close[0] = {Close[0]}, bid = {bid} & SET justExited = {justExited}\n";
							log += $"^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n";
						}
						else if (isShort)
						{
							inProfit = (ask < Position.AveragePrice);
							justExited = true;
							ExitShort("EoB", entryName);
							log += $"^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n";
							log += $"  Exited Short ({entryName}) on End of Candle: {Time[0].ToString()}; Close[0] = {Close[0]}, bid = {bid} & SET justExited = {justExited}\n";
							log += $"^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n";
						}
						actionTaken = true;
					}
					log += $"    -----    (BarsInProgress == 0) [AFTER], MarketPos = {Position.MarketPosition}, justExited = {justExited}\n";
				}

				/// Are we primed for an Add-On entry?
				int addOn = CheckAdditionalEntry();
				
				/// Assuming that OnBarClose is same as open of new candle, check for entry
				straddle1 = volare.Straddle[i];
				straddle2 = volare.Straddle[i+1];
				//log += $"\n\n          Time[0] = {Time[0].ToString()}  :  straddle{i} = {straddle1}, straddle{i+1} = {straddle2};  \n";
				
				var barIndex = Bars.GetBar(Time[i]);
				poc = volare.GetPoc(barIndex);

				inSession = TradingOkay();

				if (straddle1 != 0) 
				{
					if (lastSignal != 0  ||  netWon != 0)
					{
						log += $"       ~~~~~ SETTING LAST SIGNAL = 0 AND netWon = 0 BECAUSE OF NEW STRADDLE SIGNAL ({straddle1}) ON CANDLE[1] ~~~~~\n";
						lastSignal = 0;	/// Reset on every new straddle1
						netWon = 0;	/// reset on any new signal		
					}
				}
				
				/// Else, if NOT in any trade, but also out-of-session, do nothing
				if (!inSession)
				{
					log += $"\nOut of Session and no trades open\n";
					actionTaken = true;
				}
				/// Else, we are in session and not in any trade, so check for new entry  =  =  =  =  =  =  =  =  =  =  =  =  =  =  =  =  =  =
				else if (!inTrade  ||  justExited)
				{
					log += $"\n----------------------------------------------------------------------------------------------- START TRADE CHECK \n";
					if (State == State.Historical)
						log += $"Time[0] = {Time[0].ToString()} : straddle{i} = {straddle1}, straddle{i+1} = {straddle2};    stoch.K[0] = {stoch.K[0]}, stoch.K[1] = {stoch.K[1]}, stoch.K[2] = {stoch.K[2]}    MarketPos = {Position.MarketPosition}    i = {i}\n";
					else
						log += $"{Time[0].ToString()} / {DateTime.Now.ToString("HH:mm:ss")}: straddle1 = {straddle1}, straddle2 = {straddle2};    stoch.K[1] = {stoch.K[1]}, stoch.K[2] = {stoch.K[2]}    MarketPos = {Position.MarketPosition}    i = {i}\n";
					
					string tType = "";
					bool addOnLong = false;
					bool addOnShort = false;
					
					/// Stoch has no value on open bar, so we want to check for cross 
					/// in last 2 bars, since it takes two data points to cross a level
					bool stochCrossL = (!StochLong   ||  CrossAbove(stoch.K, LowerLevel, 2));
					bool stochCrossS = (!StochShort  ||  CrossBelow(stoch.K, UpperLevel, 2));
					
					// Check for a VolarePOC signal on closed candle.  If ReqStraddlePOC == false, rather than a 
					// straddle of the POC, it's enough that the bar just close on the 'correct' side of the POC
					bool pocL = (ReqStraddlePOC) ? straddle1 > 0 : Close[i] > poc;
					bool pocS = (ReqStraddlePOC) ? straddle1 < 0 : Close[i] < poc;
					
					/// If Trend Trades enabled, then we use the previous candle
					bool ttL = (ReqStraddlePOC) ? straddle2 > 0 : Close[i+1] > poc;
					bool ttS = (ReqStraddlePOC) ? straddle2 < 0 : Close[i+1] < poc;

					/// If TrendTrade is enabled, then we only take an entry based on the value of addOn
					if (longEnabled)
					{
						if (!TrendTrade)
						{
							if (straddle1 > 0  &&  stochCrossL)
							{
								enterLong = true;
								tType = (EnterBracket) ? "Bracket" : "Market";
								log += $"\n\n     # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # -> NORMAL TRADE: enterLong = TRUE\n\n\n";
							}
						}
						/// Check Trend Trade entry
						else if (straddle2 > 0  &&  stoch.K[i] == 100)
						{
							enterLong = true;
							tType = "Trend";
							log += $"\n\n     <> <> <> <> <> <> <> <> <> <> <> <> <> <> <> <> <> <> <> <> <> <> <> <> <> <> <> <> <> <> <> -> TREND TRADE: enterLong = TRUE\n\n\n";
						}
						if (!enterLong  &&  addOn == 1)
						{
							addOnLong = enterLong = true;
							tType = "AddOn";
							log += $"\n\n     !  !  !  !  !  !  !  !  !  !  !  !  !  !  !  !  !  !  !  !  !  !  !  !  !  !  !  !  !  !  !  ! -> addOnLong = enterLong = true\n\n\n";
						}
					}

					//log += $"\n   -> -> -> POST-Long Check, shortEnabled = {shortEnabled},  enterLong = {enterLong}\n";
					if (shortEnabled  &&  !enterLong)
					{
						if (!TrendTrade)
						{
							if (straddle1 < 0  &&  stochCrossS)
							{
								enterShort = true;
								tType = (EnterBracket) ? "Bracket" : "Market";
								log += $"\n\n     # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # -> NORMAL TRADE: enterShort = TRUE\n\n\n";
							}
						}
						/// Check Trend Trade entry
						else if (straddle2 < 0  &&  stoch.K[i] == 0)
						{
							enterShort = true;
							tType = "Trend";
							log += $"\n\n     <> <> <> <> <> <> <> <> <> <> <> <> <> <> <> <> <> <> <> <> <> <> <> <> <> <> <> <> <> <> <> -> TREND TRADE: enterShort = TRUE\n\n\n";
						}
						if (!enterShort  &&  addOn == -1)
						{
							addOnShort = enterShort = true;
							tType = "AddOn";
							log += $"\n\n     !  !  !  !  !  !  !  !  !  !  !  !  !  !  !  !  !  !  !  !  !  !  !  !  !  !  !  !  !  !  !  ! -> addOnShort = enterShort = true\n\n\n";
						}
					}
					
					//tmpDate = Time[i].ToString();
					//Print($"{tmpDate} : inSession = {inSession}, poc = {poc}, straddle1 = {straddle1}, enterLong = {enterLong}, enterShort = {enterShort}");
					//if (straddle1 == 1)	Print($"{tmpDate} : straddle1 = {straddle1}, inSession = {inSession}, isLong = {isLong}, Cross = {CrossAbove(stoch.K, LowerLevel, 1)}");
					//else
					//if (straddle1 == -1)	Print($"{tmpDate} : straddle1 = {straddle1}, inSession = {inSession}, isShort = {isShort}, Cross = {CrossBelow(stoch.K, UpperLevel, 1)}");
					
					bool enter = false;
					string dbgSL = "";
					double slDist = 0;
					double initSL = 0;
					double limitPrice = 0;
					double stopPrice = 0;
					if (enterLong)
					{
						//log += $"EnterLong Signal at CurrentBar = {CurrentBar}, barIndex = {barIndex}, Time[{i}] = {Time[i].ToString()}\n";
						//log += $"vlare.Straddle[{i}]: {volare.Straddle[i]}, volare.GetPoc() = {poc} Set Long Stop @ {slPrice}\n";
					
						initSL = (UsePOC_SL) ? poc : Low[i];
						slPrice = initSL - LongStopOffsetTicks * TickSize;
						dbgSL = (UsePOC_SL) ? ";    StochX = " + stochCrossL + ";    POC @ Sig Candle = " + poc.ToString("F2") : ";    Low[" + i + "] = " + Low[i];
						//dbgSL = (UsePOC_SL) ? ";    POC @ Sig Candle = " + poc.ToString("F2") : ";    Low[" + i + "] = " + Low[i];
						
						entryName = "Long";
						if (addOnLong  ||  !EnterBracket)
						{
							slDist = (bid - slPrice) / TickSize;		// Ask for EXIT of buy
						}
						else
						{
							/// If placing straddle1 pending orders, we use the same SL price for each, and the same TP ticks for each, but we need to calcualte entry prices
							stopPrice = ask + PendingTickOffset * TickSize;
							limitPrice = poc - PendingTickOffset * TickSize;
							slDist = 1;	/// skip checking max SL for now on pendings
							log += $"Placing Long Pending Bracket,    Stop Price = {stopPrice},    Limit Price = {limitPrice}\n";
						}
						
						if (slDist > 0)
						{
							/// Do not enter on last bar of historical (the open candle)
							//log += $"State = {State.ToString()},       histBarsCount = {histBarsCount},       CurrentBar = {CurrentBar}\n";
							if (State == State.Historical  &&  CurrentBar == histBarsCount-1)
							{
								log += $"Cannot enter trade on open candle from Historical run\n";
							}
							else
							{
								enter = true;
								lastSignal = 1;
								if (slDist > LongTicksMaxSL)
								{
									log += $"Ticks to SL ({slDist}) > max ({LongTicksMaxSL}); setting to max, Position.MarketPosition = {Position.MarketPosition.ToString()}\n";
									slDist = LongTicksMaxSL;
									slPrice = ask - slDist * TickSize;
								}
								int j = (tType == "Trend") ? i + 1 : i;
								var strad = (tType == "Trend") ? straddle2 : straddle1;
								log += $"Long [{tType}] : Straddle ({strad}) at Time[{j}] = {Time[j].ToString()}{dbgSL};    Enter ({enter}) & Set Long Stop @ {slPrice} ({slDist} Ticks)\n";
							}
						}
						else
						{
							log += $"Cannot set SL because calculated price is ({slPrice}) >= Bid ({bid}); Aborting Entry\n";
						}
					}
					else if (enterShort)
					{
						//log += $"EnterShort Signal at CurrentBar = {CurrentBar}, barIndex = {barIndex}, Time[{i}] = {Time[i].ToString()}\n";
						//log += $"volare.Straddle[{i}]: {volare.Straddle[i]}, volare.GetPoc() = {poc} Set Short Stop @ {slPrice}\n";
	
						initSL = (UsePOC_SL) ? poc : High[i];
						slPrice = initSL + ShortStopOffsetTicks * TickSize;
						//log += $"slPrice : initSL ({initSL}) + ShortStopOffsetTicks ({ShortStopOffsetTicks}) * TickSize = {slPrice}\n";
						dbgSL = (UsePOC_SL) ? ";    StochX = " + stochCrossS + ";    POC @ Sig Candle = " + poc.ToString("F2") : ";    High[" + i + "] = " + High[i];
						//dbgSL = (UsePOC_SL) ? ";    POC @ Sig Candle = " + poc.ToString("F2") : ";    High[" + i + "] = " + High[i];

						entryName = "Short";
						if (addOnShort  ||  !EnterBracket)
						{
							slDist = (slPrice - ask) / TickSize;		/// Ask for EXIT of sell
						}
						else
						{
							/// If placing straddle1 pending orders, we use the same SL price for each, and the same TP ticks for each, but we need to calcualte entry prices
							stopPrice = bid - PendingTickOffset * TickSize;
							limitPrice = poc + PendingTickOffset * TickSize;
							slDist = 1;	/// skip checking max SL for now on pendings
							log += $"Placing Short Pending Bracket, Stop Price = {stopPrice}, Limit Price = {limitPrice}\n";
						}
						
						if (slDist > 0)
						{
							/// Do not enter on last bar of historical (the open candle)
							//log += $"State = {State.ToString()},       histBarsCount = {histBarsCount},       CurrentBar = {CurrentBar}\n";
							if (false)//(State == State.Historical  &&  CurrentBar == histBarsCount-1)//  &&  (Open[0] - bid)*TickSize > 1)
							{
								log += $"Cannot enter trade on open candle from Historical run\n";
							}
							else
							{
								enter = true;
								lastSignal = -1;
								if (slDist > ShortTicksMaxSL)
								{
									log += $"Ticks to SL ({slDist}) > max ({ShortTicksMaxSL}); setting to max, Position.MarketPosition = {Position.MarketPosition.ToString()}\n";
									slDist = ShortTicksMaxSL;
									slPrice = bid + slDist * TickSize;
								}
								int j = (tType == "Trend") ? i + 1 : i;
								var strad = (tType == "Trend") ? straddle2 : straddle1;
								log += $"Short [{tType}] : Straddle ({strad}) at Time[{j}] = {Time[j].ToString()}{dbgSL};    Enter ({enter}) & Set Long Stop @ {slPrice} ({slDist} Ticks)\n";
							}
						}
						else
						{
							log += $"Cannot set SL because calculated price is ({slPrice}) <= Ask ({ask}); Aborting Entry\n";
						}					
					}
					else
					{
						//if (State == State.Realtime)	Log($" ------ NO ENTRY ------  inSession = {inSession}, isLong = {isLong}, isShort = {isShort}, stochCrossL = {stochCrossL}, stochCrossS = {stochCrossS}, poc[{Time[i].ToString("HH:mm:ss")}] = {poc}");
					}
					
					if (enter)
					{
						actionTaken = true;
						entryName = entryName + " " + tType;
						if (justExited)
						{
							immedTradeDir = (enterLong) ? 1 : -1;	/// flag used to take new trade in OnExecUpdate
							log += $"just exited, so setting up parameters to enter on next OnExecUpdate, immedTradeDir = {immedTradeDir}";
							ieStopPrice = stopPrice;
							ieLimitPrice = limitPrice;
						}
						else
						{
							int tradeDir = (enterLong) ? 1 : -1;
							log += $"Opening position....";
							OpenPosition(tradeDir, stopPrice, limitPrice);
						}
					}
					
					/// Set at end of block, so Check Additional Entry had time to reference it
					/// If we just entered a trade, it will be set again on nest tick
					inProfit = false;
					log += $"\n----------------------------------------------------------------------------------------------- END TRADE CHECK \n";
				}
			}
			#endregion
			
//			if (actionTaken  &&  log != "")
			{
				//Print($"\n\n\n\n\n*****  BEGIN OnBarUpdate() PRINT LOG  *****************************************************************************************************************************");
				Log(log);
				//Print($"*****  END OnBarUpdate() PRINTING LOG  ***************************************************************************************************************************\n\n\n");
			}
			lastCaller = "";	// reset so if this method is entered again before other log, reprint the function name
			
			if (State == State.Realtime)
				ManageOCD();
		}
		
		#region SUPPORTING METHODS - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
		private void ManageOpenTrades(double ask, double bid)
		{
			/// If this flag set, then we already sent a command to exit the trade, so do nothing
			if (justExited)
				return;
			
//			log += $"{Position.MarketPosition.ToString()} Position Open - Managing...\n";
			
			/// Check for exits / trail if already in a trade
			if (isLong)
			{
				inProfit = (bid > Position.AveragePrice);

				/// Modify the SL if it is too large, now that the trade is open
				if (EnterBracket  &&  !slChecked)
				{
					slChecked = true;
					if ((Position.AveragePrice - slPrice) * TickSize > LongTicksMaxSL)
					{
						slPrice = Position.AveragePrice + LongTicksMaxSL / TickSize;
						ExitLongStopMarket(slPrice, "MaxSL", entryName);
					}
				}
				
				if (ExitOnOppositeSignal  &&  volare.Straddle[1] < 0)
				{
					Log($"Exiting Long on Opposite Signal\n");
					ExitLong("OppSig", entryName);
				}
				else if (ExitOnStochFastExtreme  &&  stoch.K[1] == 100)
				{
					Log($"Exiting Long on Stoch Fast Extreme\n");
					ExitLong("StochEx", entryName);
				}
				else if (UseTrail  &&  poc - TrailTicks*TickSize > slPrice)
				{
					/// Running only on first tick of new bar, Close[0] will be that open price
					slPrice = poc - TrailTicks * TickSize;
					if (Close[0] <= slPrice)
					{
						Log($"Exiting Long on Trail\n");
						ExitLong("Trail", entryName);
					}
					else 
					{
						Log($"Trail moved SL up to {slPrice}\n");
						ExitLongStopMarket(slPrice, "Trail", entryName);
					}
				}
			}
			else if (isShort)
			{
				inProfit = (ask < Position.AveragePrice);
				
				/// Modify the SL if it is too large, now that the trade is open
				if (EnterBracket  &&  !slChecked)
				{
					slChecked = true;
					if ((slPrice - Position.AveragePrice) * TickSize > ShortTicksMaxSL)
					{
						slPrice = Position.AveragePrice + ShortTicksMaxSL / TickSize;
						ExitShortStopMarket(slPrice, "MaxSL", entryName);
					}
				}
				
				if (ExitOnOppositeSignal  &&  volare.Straddle[1] > 0)
				{
					Log($"Exiting Short on Opposite Signal\n");
					ExitShort("OppSig", entryName);
				}
				else if (ExitOnStochFastExtreme  &&  stoch.K[1] == 0)
				{
					Log($"Exiting Short on Stoch Fast Extreme\n");
					ExitShort("StochEx", entryName);
				}
				else if (UseTrail  &&  poc + TrailTicks*TickSize < slPrice)
				{
					/// Running only on first tick of new bar, Close[0] will be that open price
					slPrice = poc + TrailTicks * TickSize;
					if (Close[0] >= slPrice)
					{
						Log($"Exiting Short on Trail\n");
						ExitShort("Trail", entryName);
					}
					else
					{
						Log($"Trail moved SL down to {slPrice}\n");
						ExitShortStopMarket(slPrice, "Trail", entryName);
					}
				}
			}
			lastCaller = "";	// reset so if this method is entered again before other log, reprint the function name
		}
		
		private int CheckAdditionalEntry()
		{
			if (NetWinsMax == 0)
				return 0;
			if (lastSignal == 0)
				return 0;
				
			if (netWon >= NetWinsMax)
			{
				Log($"Already won {netWon} add-on trades; halting additional entries");
				return 0;
			}
			//Log($"NET Add Ons so far: {netWon}");
			Log($"lastSignal = {lastSignal}, stoch.K[1] = {stoch.K[1]}, volare.Straddle[2] = {volare.Straddle[2]}");
			var stoc = stoch.K[0];
			
			/// If this is set, it means we want to enter on stoch entreme 
			/// but only if there was a straddle signal 2 candles back
			int dirToTrade;
			if (lastSignal == 1  &&  stoc == 100)
			{
				dirToTrade = 1;
				//Log($"Stoch == 1000; Additional Entry LONG");
			}
			else if (lastSignal == -1  &&  stoc == 0)
			{
				dirToTrade = -1;
				//Log($"Stoch == 0; Additional Entry SHORT");
			}
			else
			{
				lastSignal = dirToTrade = netWon = 0;		/// reset netWon on stoch no longer 0/100
				dbgTTText = "";
				Log($"       SET LAST SIGNAL == 0 AFTER NO ADDTIONAL ENTRY");
			}			

			lastCaller = "";	// reset so if this method is entered again before other log, reprint the function name
			return (dirToTrade);
		}
		#endregion
		
		#region ORDER METHODS  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
		private void OpenPosition(int dir, double stopPrice=0, double limitPrice=0) 
		{
			if (EnterBracket)
			{
				if (stopPrice == 0  ||  limitPrice == 0)
				{
					Log($"OpenPosition() ERROR: EnterBracket is enabled, but no stop &| limit price specified");
					return;
				}
				
				/// Place pending straddle1 order
				if (dir == 1)	
				{
					EnterLongStopMarket(Quantity, stopPrice, entryName);
					EnterLongLimit(Quantity, limitPrice, entryName);
				}
				else
				{
					EnterShortStopMarket(Quantity, stopPrice, entryName);
					EnterShortLimit(Quantity, limitPrice, entryName);
				}
			}
			else
			{
				if (dir == 1)	EnterLong(Quantity, entryName);
				else			EnterShort(Quantity, entryName);
			}
		}

		private void CloseTrade(string name="Close")
		{
			if (Position.MarketPosition == MarketPosition.Long)
				ExitLong(name, entryName);
			else if (Position.MarketPosition == MarketPosition.Short)
				ExitShort(name, entryName);
		}

		private void SetStopLoss(double price, string name="SL")
		{
			Log($"Sending SL order @ {price}, for {entryName}; Order name = {name}");
			if (Position.MarketPosition == MarketPosition.Long)
			{
				ExitLongStopMarket(price, name, entryName);
			}
			else if (Position.MarketPosition == MarketPosition.Short)
			{
				ExitShortStopMarket(price, name, entryName);
			}
		}
		
		private void SetTakeProfit(double price, string name="TP")
		{
			Log($"Sending TP order @ {price}, for {entryName}; Order name = {name}");
			if (Position.MarketPosition == MarketPosition.Long)
			{
				ExitLongLimit(price, name, entryName);
			}
			else if (Position.MarketPosition == MarketPosition.Short)
			{
				ExitShortLimit(price, name, entryName);
			}
		}
		
		private double AvePrice()
		{
			double avePrice = Position.AveragePrice;
			
			/// If ave price does not fall on tick boundary, round up
			int numTicks = (int)(avePrice / TickSize);
			if (numTicks * TickSize != avePrice)
			{
				double prc = Instrument.MasterInstrument.RoundToTickSize(avePrice);
				avePrice = prc;
			}
			return(avePrice);
		}
		#endregion
		
		#region DEBUG & OCD  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
		private void ManageOCD()
		{
			if (!DisplayOCD)
				return;
			
			string text = "";
			string state = (inSession) ? "Active" : "Inactive";
			for (int i=0; i < StartingLine; i++)
				text += "\n";
			text += " Strategy :\t" + StrategyName + " v" + VERSION + ", " + UniqueID;
			text += "\n Session :\t" + state + "\n";
			if (!longEnabled  &&  !shortEnabled)
				text += "\n ALL TRADING DISABLED" + "\n";
			else if (!longEnabled)
				text += "\n LONG TRADING DISABLED" + "\n";
			else if (!shortEnabled)
				text += "\n SHORT TRADING DISABLED" + "\n";
			
			text += "\n Stoch[1] :\t" + stoch.K[1];
			text += "\n Stoch[2] :\t" + stoch.K[2] + "\n";
			
			text += "\n justExited :\t" + justExited;
			//if (UseTrailing  &&  Position.MarketPosition != MarketPosition.Flat)			text += "\n Trail Triggered :\t\t" + trailTriggered + "\n";

			/*
			if (DailyMinTarget != 0  ||  DailyMaxLoss != 0)
			{
				text += "\n";
				if (DailyMinTarget > 0)
					text += "Daily Min Profit :\t\t\t\t" + DailyMinTarget.ToString("C0") + "\n";
				if (DailyMaxLoss > 0)
					text += "Daily Max Loss :\t\t\t\t" + DailyMaxLoss.ToString("C0") + "\n";
				text += "Daily Profit :\t\t\t\t\t" + dailyProfit.ToString("C0") + "\n";
				if (dailyLimitHit)
					text += "Daily Limit Hit :\t\t\t\tYes - Trading Disabled\n";
				else
					text += "Daily Limit Hit :\t\t\t\tNo\n";
			}
			*/
			
			if (lastDashboard == text)
				return;
			
			Draw.TextFixed(this, "OCD", text, TextPosition.TopLeft);	
			lastDashboard = text;
		}
		
		private void Log(string message, [CallerMemberName] string memberName = "")
		{
			if (DisableLogging  ||  lastLogMsg == message)
				return;

			/// Debug time filtering...
			//if (Time[0] < dbg1  ||  Time[0] > dbg2)	return;
			
			/// Set this scripts Print() calls to the second output tab?
			if (UseOutput2)		PrintTo = PrintTo.OutputTab2;
			else				PrintTo = PrintTo.OutputTab1;
			
			string dateStr = "";
			string id = StrategyName + " " + UniqueID;
			
			if (lastLogMsg != message)
			{
				DateTime date = (State == State.Historical) ? Time[0] : DateTime.Now;
				dateStr = (State == State.Historical) ? date.ToString() : date.ToString("HH:mm:ss");
				
				string header = $"\n{id} - {chartInstrument} - [{memberName}]  -  {dateStr}  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -\n";
			
				/// Output just time if diff time but not new caller
				string output = (lastLogTime != dateStr) ? message + "   ( " + dateStr + " )" : message;
					
				/// If it is a new caller, write header line
				if (lastCaller != memberName)
					LogToFile(header);
	
				LogToFile(output);
			}
			
			lastLogMsg = message;
			lastCaller = memberName;
			lastLogTime = dateStr;
		}
		
		private void LogToFile(string message)
		{
			/// Always print to the output window as well
			Print(message);
			
			/// Additionally logging to file is skipped if there is no file name
			if (logPath == "")
				return;
			//Print($"                                                                         logPath = {logPath}         LogToFileName = {LogToFileName}");

			/// Ensure the directory exists
			try
			{
				string folder = System.IO.Path.GetDirectoryName(logPath);
				if (!System.IO.Directory.Exists(folder))
					System.IO.Directory.CreateDirectory(folder);
			}
			catch (Exception ex)
			{
				Print($"LogToFile() error creating folder: {ex.Message}");
				return;
			}
			
			/// Append to the log file
			try
			{
				System.IO.File.AppendAllText(logPath, "\n" + message);
			}
			catch (Exception ex)
			{
				Print($"LogToFile() error writing file: {ex.Message}");
			}

		}
		#endregion
		
		#region SESSION CHECK  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
		/// Check trading sessions / days; 
		private bool TradingOkay()
		{
			activeSessionNum = 0;
			
			//Log($"UseSession1 = {UseSession1}     UseSession2 = {UseSession2}     UseSession3 = {UseSession3}\n";
			if (!(UseSession1  ||  UseSession2  ||  UseSession3))
				return true;
			
			if (UseSession1)
			{
				//Log($"Checking time {startTime1.TimeOfDay.ToString()} to {endTime1.TimeOfDay.ToString()}\n";
				if (CheckSession(1, startTime1, endTime1))
					return(true);
				//Log($"OUT OF SESSION\n";
			}
			if (UseSession2)
			{
				if (CheckSession(2, startTime2, endTime2))
					return(true);
			}
			if (UseSession3)
			{
				if (CheckSession(3, startTime3, endTime3))
					return(true);
			}
			return false;
		}
		
		/// Check one trading sessions; return false if out-of-session
		private bool CheckSession(int num, DateTime startTime, DateTime endTime)
		{
			bool okay = false; 
			if (startTime.TimeOfDay < endTime.TimeOfDay)
			{
				if (Time[0].TimeOfDay >= startTime.TimeOfDay  &&  Time[0].TimeOfDay < endTime.TimeOfDay)
					okay = true;
			}
			else if (startTime.TimeOfDay > endTime.TimeOfDay)
			{
				if (Time[0].TimeOfDay >= startTime.TimeOfDay  ||  Time[0].TimeOfDay < endTime.TimeOfDay)
					okay = true;
			}
			else /// (startTime.TimeOfDay == endTime.TimeOfDay)
			{
				Log("Start Time ({startTime.TimeOfDay.ToString()}) is the same as End Time (endTime.TimeOfDay.ToString()); Trading (always) approved");
				okay = true;
			}
			
			if (okay)	activeSessionNum = num;
			return okay;
		}
		
		private bool IsCloseTime()
		{
			if (Position.MarketPosition == MarketPosition.Flat)
				return false;

			/// If we are not using any trading session (trade all times), then just check the main close time
			if (!(UseSession1  ||  UseSession2  ||  UseSession3))
			{
				/// Close all trades at end of user-defined session				///  Use if we want to make sure this only fires once, RIGHT AT close time?
				if (UseCloseTime  &&  Time[0].TimeOfDay >= CloseTime.TimeOfDay) //  &&  Time[1].TimeOfDay < CloseTime1.TimeOfDay)
					return true;
				return false;
			}
			
			/// Otherwise, check be which session is active 
			/// Close all trades at end of user-defined session
			if (activeSessionNum == 1  &&  UseCloseTime1  &&  Time[0].TimeOfDay >= closeTime1.TimeOfDay)
				return true;
			if (activeSessionNum == 2  &&  UseCloseTime2  &&  Time[0].TimeOfDay >= closeTime2.TimeOfDay)
				return true;
			if (activeSessionNum == 3  &&  UseCloseTime3  &&  Time[0].TimeOfDay >= closeTime3.TimeOfDay)
				return true;
			return false;
		}
		#endregion
		
		#region BUTTONS - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
		private void OnMyButtonClick(object sender, RoutedEventArgs rea)
		{
			System.Windows.Controls.Button button = sender as System.Windows.Controls.Button;
			
			string log = "\n\n";
			bool lWasOn = longEnabled;
			bool sWasOn = shortEnabled;
			if (button.Name == "longButton")
			{
				if (longEnabled)
				{
					button.Background = Brushes.Gray;
					button.Content = "Long Disabled";
					longEnabled = false;
					log += "\nLONG ENTRIES DISABLED!\n";
				}
				else
				{
					button.Background = Brushes.Green;
					button.Content = "Long Enabled";
					longEnabled = true;
					log += "\nLong entries Enabled!\n";
				}
			}
			else if (button.Name == "shortButton")
			{
				if (shortEnabled)
				{
					button.Background = Brushes.Gray;
					button.Content = "Short Disabled";
					shortEnabled = false;
					log += "\nSHORT ENTRIES DISABLED!\n";
				}
				else
				{
					button.Background = Brushes.Crimson;
					button.Content = "Short Enabled";
					shortEnabled = true;
					log += "\nShort entries Enabled!\n";
				}
			}
			else if (button.Name == "exitButton")
			{
				if (Position.MarketPosition == MarketPosition.Flat)
					log += "\nUser-Instigated Close of All Open Strategy Positions, but nothing open...\n";
				else 
				{
					log += "\n\n=====================================================\n";
					log += "User-Instigated Close of All Open Strategy Positions\n";
					log += "=====================================================\n\n";
					if (Position.MarketPosition == MarketPosition.Long)
						ExitLong("Manual", entryName);
					else if (Position.MarketPosition == MarketPosition.Short)
						ExitShort("Manual", entryName);
				}
			}
			
			if (!longEnabled  &&  !shortEnabled)
				if (lWasOn  ||  sWasOn)
					log += "ALL ENTRIES DISABLED!\n";
			else if (longEnabled  &&  shortEnabled)
				if (!lWasOn  ||  !sWasOn)
					log += "ALL ENTRIES ENABLED!\n";
			log += "\n\n";
			Log(log);
		}
		
		
		protected void CreateButtons()
		{
			if (UserControlCollection.Contains(myGrid))
		        return;
		
			myGrid = new System.Windows.Controls.Grid
			{
				Name = "MyCustomGrid",
				HorizontalAlignment = HorizontalAlignment.Left,
				VerticalAlignment = VerticalAlignment.Bottom,
			};
	 	 
	        myGrid.ColumnDefinitions.Add(new System.Windows.Controls.ColumnDefinition());
	        myGrid.ColumnDefinitions.Add(new System.Windows.Controls.ColumnDefinition());
	        myGrid.ColumnDefinitions.Add(new System.Windows.Controls.ColumnDefinition());
			myGrid.RowDefinitions.Add(new System.Windows.Controls.RowDefinition());
			myGrid.ColumnDefinitions[0].Width = new GridLength(80);
			myGrid.ColumnDefinitions[1].Width = new GridLength(80);
			myGrid.ColumnDefinitions[2].Width = new GridLength(60);
			myGrid.RowDefinitions[0].Height = new GridLength(20);

	        longButton = new System.Windows.Controls.Button
			{
				Name = "longButton",
				Content = "Long Enabled",
				Foreground = Brushes.White,
				Background = Brushes.Green,
				Height = 25,
				Width = 80,
				FontSize = 10,
				HorizontalAlignment = HorizontalAlignment.Center,
				Focusable = false
			};
			
			shortButton = new System.Windows.Controls.Button
			{
				Name = "shortButton",
				Content = "Short Enabled",
				Foreground = Brushes.White,
				Background = Brushes.Crimson,
				Height = 25,
				Width = 80,
				FontSize = 10,
				HorizontalAlignment = HorizontalAlignment.Center,
				Focusable = false
			};
			/// Set initial state based on user input
			if (!shortEnabled)
			{
				shortButton.Content = "Short Disabled";
				shortButton.Background = Brushes.Gray; /// Change appearance to "pressed"
			}
			
			exitButton = new System.Windows.Controls.Button
			{
				Name = "exitButton",
				Content = "Exit All",
				Foreground = Brushes.White,
				Background = Brushes.DarkMagenta,
				Height = 25,
				Width = 80,
				FontSize = 10,
				HorizontalAlignment = HorizontalAlignment.Center,
				Focusable = false
			};
	 
			longButton.Click += OnMyButtonClick;
			shortButton.Click += OnMyButtonClick;
			exitButton.Click += OnMyButtonClick;
	 
	        System.Windows.Controls.Grid.SetColumn(shortButton, 1);
			System.Windows.Controls.Grid.SetRow(shortButton, 0);
			System.Windows.Controls.Grid.SetColumn(longButton, 0);
			System.Windows.Controls.Grid.SetRow(longButton, 0);
			System.Windows.Controls.Grid.SetColumn(exitButton, 2);
			System.Windows.Controls.Grid.SetRow(exitButton, 0);
	 
	        myGrid.Children.Add(longButton);
			myGrid.Children.Add(shortButton);
			myGrid.Children.Add(exitButton);
	 
	        UserControlCollection.Add(myGrid);
		}
		
		
		public void DisposeButtons() 
		{
			if (myGrid != null)
			{
				if (longButton != null)
				{
					myGrid.Children.Remove(longButton);
					longButton.Click -= OnMyButtonClick;
					longButton = null;
				}
				if (shortButton != null)
				{
					myGrid.Children.Remove(shortButton);
					shortButton.Click -= OnMyButtonClick;
					shortButton = null;
				}
				if (exitButton != null)
				{
					myGrid.Children.Remove(exitButton);
					exitButton.Click -= OnMyButtonClick;
					exitButton = null;
				}
			}
		}
		#endregion
		
		#region PROPERTIES - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
		
		#region TimeZone
		[NinjaScriptProperty]
		[Range(-23, 23)]
		[Display(Name = "Timezone Offset", Description = "Number of hours to add the signal PostTime to compare with local time", Order=0, GroupName = "06. Session 1 Hours")]
		public int TimeZoneOffset
		{ get; set; }
		#endregion
		
		#region Entry Parameters
		[NinjaScriptProperty]
		[Range(0, 1000)]
		[Display(Name="Contract Size", Description="Number of crontracts to trade", Order=4, GroupName = "01. Entry Parameters")]
		public int Quantity
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name="Trend Trade", Description="Stochastic 0/100 entry, with Straddle 2 candles back", Order=6, GroupName = "01. Entry Parameters")]
		public bool TrendTrade
		{ get; set; }
		
		[NinjaScriptProperty]
		[Display(Name="Require POC Straddle", Description="Require POC Straddle for Entry", Order=8, GroupName = "01. Entry Parameters")]
		public bool ReqStraddlePOC
		{ get; set; }
		
		[NinjaScriptProperty]
		[Display(Name="Stoch Filter Long", Description="Use Stochastic filter for Long entry", Order=10, GroupName = "01. Entry Parameters")]
		public bool StochLong
		{ get; set; }
		
		[NinjaScriptProperty]
		[Display(Name="Stoch Filter Short", Description="Use Stochastic filter for Short entry", Order=11, GroupName = "01. Entry Parameters")]
		public bool StochShort
		{ get; set; }
		
		[NinjaScriptProperty]
		[Display(Name="Use Stop/Limit Bracket", Description="Don't Enter immediately; place a stop and limit bracketing entry order", Order=14, GroupName = "01. Entry Parameters")]
		public bool EnterBracket
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(0, 1000)]
		[Display(Name="Tick Offset for Bracket", Description="Number of ticks to offset from Open/POC", Order=15, GroupName = "01. Entry Parameters")]
		public int PendingTickOffset
		{ get; set; }

		[NinjaScriptProperty]
		[Range(0, 1000)]
		[Display(Name="Max Net Wins", Description="Quit add-on trades after winning this many", Order=18, GroupName = "01. Entry Parameters")]
		public int NetWinsMax
		{ get; set; }
		#endregion
		
		#region Exit Parameters
		[NinjaScriptProperty]
		[Display(Name="Exit End of Candle", Description="Exit any trade at end of candle", Order=0, GroupName = "02. Exit Parameters")]
		public bool ExitEndOfBar
		{ get; set; }
		
		[NinjaScriptProperty]
		[Display(Name="Exit On Opposite Signal", Description="Exit long on short signal and vice versa", Order=0, GroupName = "02. Exit Parameters")]
		public bool ExitOnOppositeSignal
		{ get; set; }
		
		[NinjaScriptProperty]
		[Display(Name="Exit On Stoch Fast Extreme", Description="Exit on extreme fast Stochastic", Order=1, GroupName = "02. Exit Parameters")]
		public bool ExitOnStochFastExtreme
		{ get; set; }
		#endregion
		
		#region SL, TP & Trail
		[NinjaScriptProperty]
		[Range(0, 1000)]
		[Display(Name="Take Profit Ticks", Description="Ticks to use for profit target", Order=0, GroupName = "03. SL, TP & Trail")]
		public int TicksTP
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name="Use POC for SL", Description="Use offset from the relevant POC price as SL", Order=3, GroupName = "03. SL, TP & Trail")]
		public bool UsePOC_SL
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(0, 100)]
		[Display(Name="SL Offset Ticks Long", Description="Extra ticks to be added to base SL price for Long trades", Order=4, GroupName = "03. SL, TP & Trail")]
		public int LongStopOffsetTicks
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(0, 100)]
		[Display(Name="SL Offset Ticks Short", Description="Extra ticks to be added to base SL price for Short trades", Order=5, GroupName = "03. SL, TP & Trail")]
		public int ShortStopOffsetTicks
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(0, 1000)]
		[Display(Name="Max Ticks SL Long", Description="Maximum ticks for calculated SL for Long trades", Order=6, GroupName = "03. SL, TP & Trail")]
		public int LongTicksMaxSL
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(0, 1000)]
		[Display(Name="Max Ticks SL Short", Description="Maximum ticks for calculated SL for Short trades", Order=7, GroupName = "03. SL, TP & Trail")]
		public int ShortTicksMaxSL
		{ get; set; }
		
		[NinjaScriptProperty]
		[Display(Name="Use Trail", Description="Use trailing stop", Order=8, GroupName = "03. SL, TP & Trail")]
		public bool UseTrail
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(0, 100)]
		[Display(Name="Trail Ticks", Description="Offset ticks for trailing stop callculated value", Order=9, GroupName = "03. SL, TP & Trail")]
		public int TrailTicks
		{ get; set; }
		#endregion
		
		#region Stochastic
		[Range(1, int.MaxValue), NinjaScriptProperty]
		[Display(Name="Period D", Description="Stochastic parameter Period D", Order=0, GroupName = "04. Stochastic")]
		public int PeriodD
		{ get; set; }
		
		[Range(1, int.MaxValue), NinjaScriptProperty]
		[Display(Name = "Period K", Description="Stochastic parameter Period K", Order = 1, GroupName = "04. Stochastic")]
		public int PeriodK
		{ get; set; }

		[Range(0, 100), NinjaScriptProperty]
		[Display(Name = "Upper Level", Description="Stochastic upper level setting (50-100)", Order = 5, GroupName = "04. Stochastic")]
		public double UpperLevel
		{ get; set; }
		
		[Range(0, 100), NinjaScriptProperty]
		[Display(Name = "Lower Level", Description="Stochastic lower level setting (0-50)", Order = 6, GroupName = "04. Stochastic")]
		public double LowerLevel
		{ get; set; }
		#endregion
		
		#region VolarePOC
		[NinjaScriptProperty]
		[Display(Name="Signal Type", Description="Signal type either x ticks or percetage.", Order=0, GroupName = "05. Volare POC Straddle")]
		public SignalType Signal_Type
		{
			get { return signalType; }
			set { signalType = value; }
		}
		
		[NinjaScriptProperty]
		[Display(Name="Open Percentage", Description="Open percentage for the confirmation of signal.", Order=3, GroupName = "05. Volare POC Straddle")]
		public double Percentage1
		{
			get { return percentage1; }
			set { percentage1 = value; }
		}
		
		[NinjaScriptProperty]
		[Display(Name="Close Percentage", Description="Close percentage for the confirmation of signal.", Order=6, GroupName = "05. Volare POC Straddle")]
		public double Percentage2
		{
			get { return percentage2; }
			set { percentage2 = value; }
		}
		
		[NinjaScriptProperty]
		[Display(Name="X Ticks From the Open", Description="X ticks from the opening price to the high price of the bar.", Order=9, GroupName = "05. Volare POC Straddle")]
		public int OpensX_Ticks
		{
			get { return opens_x_ticks; }
			set { opens_x_ticks = value; }
		}
		
		[NinjaScriptProperty]
		[Display(Name="X Ticks From the Close", Description="X ticks from the closing price to the low price of the bar.", Order=12, GroupName = "05. Volare POC Straddle")]
		public int ClosesX_Ticks
		{
			get { return close_x_ticks; }
			set { close_x_ticks = value; }
		}
		#endregion
		
		#region Sessions
		#region SessionTimes1
		[NinjaScriptProperty]
        [Display(Name = "Use Session 1", Description = "Use Trading Session #1", Order=0, GroupName = "06. Session 1 Hours")]
        public bool UseSession1
		{ get; set; }

        [NinjaScriptProperty]
        [PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
        [Display(Name = "Start Time", Description = "Enter Start Time for Session 1", Order=1, GroupName = "06. Session 1 Hours")]
        public DateTime StartTime1
		{ get; set; }

        [NinjaScriptProperty]
        [PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
        [Display(Name = "End Time", Description = "Enter End Time for Session 1", Order=2, GroupName = "06. Session 1 Hours")]
        public DateTime EndTime1
		{ get; set; }

		[NinjaScriptProperty]
        [Display(Name = "Use Close Time", Description = "Enable to close all Session 1 trades at given time", Order=4, GroupName = "06. Session 1 Hours")]
        public bool UseCloseTime1
		{ get; set; }

        [NinjaScriptProperty]
        [PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
        [Display(Name = "Close Time", Description = "Enter Time to Close Session 1 Trades", Order=5, GroupName = "06. Session 1 Hours")]
        public DateTime CloseTime1
		{ get; set; }
		#endregion
		#region SessionTimes2
		[NinjaScriptProperty]
        [Display(Name = "Use Session 2", Description = "Use Trading Session #2", Order=0, GroupName = "07. Session 2 Hours")]
        public bool UseSession2
		{ get; set; }

        [NinjaScriptProperty]
        [PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
        [Display(Name = "Start Time", Description = "Enter Start Time for Session 2", Order=1, GroupName = "07. Session 2 Hours")]
        public DateTime StartTime2
		{ get; set; }

        [NinjaScriptProperty]
        [PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
        [Display(Name = "End Time", Description = "Enter End Time for Session 2", Order=2, GroupName = "07. Session 2 Hours")]
        public DateTime EndTime2
		{ get; set; }

		[NinjaScriptProperty]
        [Display(Name = "Use Close Time", Description = "Enable to close all Session 2 trades at given time", Order=4, GroupName = "07. Session 2 Hours")]
        public bool UseCloseTime2
		{ get; set; }

        [NinjaScriptProperty]
        [PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
        [Display(Name = "Close Time", Description = "Enter Time to Close Session 2 Trades", Order=5, GroupName = "07. Session 2 Hours")]
        public DateTime CloseTime2
		{ get; set; }
		#endregion
		#region SessionTimes3
		[NinjaScriptProperty]
        [Display(Name = "Use Session 3", Description = "Use Trading Session #3", Order=0, GroupName = "08. Session 3 Hours")]
        public bool UseSession3
		{ get; set; }

        [NinjaScriptProperty]
        [PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
        [Display(Name = "Start Time", Description = "Enter Start Time for Session 3", Order=1, GroupName = "08. Session 3 Hours")]
        public DateTime StartTime3
		{ get; set; }

        [NinjaScriptProperty]
        [PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
        [Display(Name = "End Time", Description = "Enter End Time for Session 3", Order=2, GroupName = "08. Session 3 Hours")]
        public DateTime EndTime3
		{ get; set; }

		[NinjaScriptProperty]
        [Display(Name = "Use Close Time", Description = "Enable to close all Session 3 trades at given time", Order=4, GroupName = "08. Session 3 Hours")]
        public bool UseCloseTime3
		{ get; set; }

        [NinjaScriptProperty]
        [PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
        [Display(Name = "Close Time", Description = "Enter Time to Close Session 3 Trades", Order=5, GroupName = "08. Session 3 Hours")]
        public DateTime CloseTime3
		{ get; set; }
		#endregion
		#region CloseTime
		[NinjaScriptProperty]
        [Display(Name = "Use Close Time", Description = "Used only if sessions are disabled", Order=0, GroupName = "09. No Session Close")]
        public bool UseCloseTime
		{ get; set; }

        [NinjaScriptProperty]
        [PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
        [Display(Name = "Close Time", Description = "Enter Time to Close All Open Trades", Order=1, GroupName = "09. No Session Close")]
        public DateTime CloseTime
		{ get; set; }
		#endregion
		#endregion

		#region Misc / Debug
		[NinjaScriptProperty]
        [Display(Name = "Display OCD", Description = "Show the On-Chart Display", Order=0, GroupName = "15. Misc / Debug")]
        public bool DisplayOCD
		{ get; set; }

		[NinjaScriptProperty]
		[Range(0, 50)]
        [Display(Name = "Starting Line", Description = "How many lines down to space the OCD", Order=3, GroupName = "15. Misc / Debug")]
        public int StartingLine
		{ get; set; }

		[NinjaScriptProperty]
        [Display(Name = "Strategy Name", Description = "Name of Strategy", Order=12, GroupName = "15. Misc / Debug")]
        public string StrategyName
		{ get; set; }

		[NinjaScriptProperty]
        [Display(Name = "Disable Logging", Description = "Disable logging to Ninjascript Output windows", Order=15, GroupName = "15. Misc / Debug")]
        public bool DisableLogging
		{ get; set; }

		[NinjaScriptProperty]
        [Display(Name = "Use Output 2", Description = "Send logging to second Output window", Order=18, GroupName = "15. Misc / Debug")]
        public bool UseOutput2
		{ get; set; }

		[NinjaScriptProperty]
        [Display(Name = "Unique ID (v" + VERSION + ")", Description = "Unique number to identify strategy in Output window", Order=20, GroupName = "15. Misc / Debug")]
        public string UniqueID
		{ get; set; }

		[NinjaScriptProperty]
        [Display(Name = "Log to File", Description = "Log to file as well as NinjaScript Output window (name of log file -leave blank for same name as strategy", Order=21, GroupName = "15. Misc / Debug")]
        public string LogToFileName
		{ get; set; }
		#endregion
		#endregion
	}
}
