#region Using declarations
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Gui.Tools;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;

#endregion



#region NinjaScript generated code. Neither change nor remove.

namespace NinjaTrader.NinjaScript.Indicators
{
	public partial class Indicator : NinjaTrader.Gui.NinjaScript.IndicatorRenderBase
	{
		
		private TradingIndicators.DivergenceCloudAI[] cacheDivergenceCloudAI;
		private TradingIndicators.OneTwoThreeStrikeAI[] cacheOneTwoThreeStrikeAI;
		private TradingIndicators.VolatilityCrusherAI[] cacheVolatilityCrusherAI;

		
		public TradingIndicators.DivergenceCloudAI DivergenceCloudAI(int longPartialExits, int shortPartialExits, bool useTrailingStop, bool usePartialExits, TradingApproach_DIVERGENCE_CLOUD_AI tradingApproach, bool tradingStyleByNumber, int tradingStyleNumber, bool showMaxSize, double maxRisk, int optimizationLookback, int reOptimizationInterval, bool reOptimizeAutomatically, ReOptimizationMode_DIVERGENCE_CLOUD_AI reOptimizationMode, bool autoOptimizeParameters, OptimizationTarget_DIVERGENCE_CLOUD_AI optimizationTarget, OptimizationMode_DIVERGENCE_CLOUD_AI optimizationMode, int aTRLookback, int divergenceLookback, double multipleOfAtr, bool showIET, double panelOpacity, double globalOpacity, bool showZoneGlow, int glowOpacity, bool showTriggerLines, bool showEntryExitLines, bool colorPriceBars)
		{
			return DivergenceCloudAI(Input, longPartialExits, shortPartialExits, useTrailingStop, usePartialExits, tradingApproach, tradingStyleByNumber, tradingStyleNumber, showMaxSize, maxRisk, optimizationLookback, reOptimizationInterval, reOptimizeAutomatically, reOptimizationMode, autoOptimizeParameters, optimizationTarget, optimizationMode, aTRLookback, divergenceLookback, multipleOfAtr, showIET, panelOpacity, globalOpacity, showZoneGlow, glowOpacity, showTriggerLines, showEntryExitLines, colorPriceBars);
		}

		public TradingIndicators.OneTwoThreeStrikeAI OneTwoThreeStrikeAI(int longPartialExits, int shortPartialExits, bool useTrailingStop, bool usePartialExits, TradingApproach_123STRIKEAI tradingApproach, bool tradingStyleByNumber, int tradingStyleNumber, bool showMaxSize, double maxRisk, int optimizationLookback, int reOptimizationInterval, bool reOptimizeAutomatically, ReOptimizationMode_123STRIKEAI reOptimizationMode, bool autoOptimizeParameters, OptimizationTarget_123STRIKEAI optimizationTarget, OptimizationMode_123STRIKEAI optimizationMode, bool showIET, double panelOpacity, double globalOpacity, bool colorPriceBars, int volatilityPeriod, int volumePeriod, int fastMAPeriod, int slowMAPeriod, bool exitOnHVR)
		{
			return OneTwoThreeStrikeAI(Input, longPartialExits, shortPartialExits, useTrailingStop, usePartialExits, tradingApproach, tradingStyleByNumber, tradingStyleNumber, showMaxSize, maxRisk, optimizationLookback, reOptimizationInterval, reOptimizeAutomatically, reOptimizationMode, autoOptimizeParameters, optimizationTarget, optimizationMode, showIET, panelOpacity, globalOpacity, colorPriceBars, volatilityPeriod, volumePeriod, fastMAPeriod, slowMAPeriod, exitOnHVR);
		}

		public TradingIndicators.VolatilityCrusherAI VolatilityCrusherAI(bool requireVolume, TradingApproach_VOLATILITY_CRUSHER_AI tradingApproach, bool tradingStyleByNumber, int tradingStyleNumber, bool showMaxSize, double maxRisk, int optimizationLookback, int reOptimizationInterval, bool reOptimizeAutomatically, ReOptimizationMode_VOLATILITY_CRUSHER_AI reOptimizationMode, bool autoOptimizeParameters, OptimizationTarget_VOLATILITY_CRUSHER_AI optimizationTarget, OptimizationMode_VOLATILITY_CRUSHER_AI optimizationMode, int compressionPeriod, int normalityPeriod, double stdDeviationsMultiple, int fastTrendPeriod, int slowTrendPeriod, int volumeMAPeriod, double volumeMultiple, double target1RetracementLevel, double target2RetracementLevel, double stopRetracementLevel, bool showIET, double panelOpacity, double globalOpacity, bool colorBars, bool showCompressionZones, int compressionZoneFillOpacity)
		{
			return VolatilityCrusherAI(Input, requireVolume, tradingApproach, tradingStyleByNumber, tradingStyleNumber, showMaxSize, maxRisk, optimizationLookback, reOptimizationInterval, reOptimizeAutomatically, reOptimizationMode, autoOptimizeParameters, optimizationTarget, optimizationMode, compressionPeriod, normalityPeriod, stdDeviationsMultiple, fastTrendPeriod, slowTrendPeriod, volumeMAPeriod, volumeMultiple, target1RetracementLevel, target2RetracementLevel, stopRetracementLevel, showIET, panelOpacity, globalOpacity, colorBars, showCompressionZones, compressionZoneFillOpacity);
		}


		
		public TradingIndicators.DivergenceCloudAI DivergenceCloudAI(ISeries<double> input, int longPartialExits, int shortPartialExits, bool useTrailingStop, bool usePartialExits, TradingApproach_DIVERGENCE_CLOUD_AI tradingApproach, bool tradingStyleByNumber, int tradingStyleNumber, bool showMaxSize, double maxRisk, int optimizationLookback, int reOptimizationInterval, bool reOptimizeAutomatically, ReOptimizationMode_DIVERGENCE_CLOUD_AI reOptimizationMode, bool autoOptimizeParameters, OptimizationTarget_DIVERGENCE_CLOUD_AI optimizationTarget, OptimizationMode_DIVERGENCE_CLOUD_AI optimizationMode, int aTRLookback, int divergenceLookback, double multipleOfAtr, bool showIET, double panelOpacity, double globalOpacity, bool showZoneGlow, int glowOpacity, bool showTriggerLines, bool showEntryExitLines, bool colorPriceBars)
		{
			if (cacheDivergenceCloudAI != null)
				for (int idx = 0; idx < cacheDivergenceCloudAI.Length; idx++)
					if (cacheDivergenceCloudAI[idx].LongPartialExits == longPartialExits && cacheDivergenceCloudAI[idx].ShortPartialExits == shortPartialExits && cacheDivergenceCloudAI[idx].UseTrailingStop == useTrailingStop && cacheDivergenceCloudAI[idx].UsePartialExits == usePartialExits && cacheDivergenceCloudAI[idx].tradingApproach == tradingApproach && cacheDivergenceCloudAI[idx].TradingStyleByNumber == tradingStyleByNumber && cacheDivergenceCloudAI[idx].TradingStyleNumber == tradingStyleNumber && cacheDivergenceCloudAI[idx].ShowMaxSize == showMaxSize && cacheDivergenceCloudAI[idx].MaxRisk == maxRisk && cacheDivergenceCloudAI[idx].OptimizationLookback == optimizationLookback && cacheDivergenceCloudAI[idx].ReOptimizationInterval == reOptimizationInterval && cacheDivergenceCloudAI[idx].ReOptimizeAutomatically == reOptimizeAutomatically && cacheDivergenceCloudAI[idx].ReOptimizationMode == reOptimizationMode && cacheDivergenceCloudAI[idx].AutoOptimizeParameters == autoOptimizeParameters && cacheDivergenceCloudAI[idx].OptimizationTarget == optimizationTarget && cacheDivergenceCloudAI[idx].OptimizationMode == optimizationMode && cacheDivergenceCloudAI[idx].ATRLookback == aTRLookback && cacheDivergenceCloudAI[idx].DivergenceLookback == divergenceLookback && cacheDivergenceCloudAI[idx].MultipleOfAtr == multipleOfAtr && cacheDivergenceCloudAI[idx].ShowIET == showIET && cacheDivergenceCloudAI[idx].PanelOpacity == panelOpacity && cacheDivergenceCloudAI[idx].GlobalOpacity == globalOpacity && cacheDivergenceCloudAI[idx].ShowZoneGlow == showZoneGlow && cacheDivergenceCloudAI[idx].GlowOpacity == glowOpacity && cacheDivergenceCloudAI[idx].ShowTriggerLines == showTriggerLines && cacheDivergenceCloudAI[idx].ShowEntryExitLines == showEntryExitLines && cacheDivergenceCloudAI[idx].ColorPriceBars == colorPriceBars && cacheDivergenceCloudAI[idx].EqualsInput(input))
						return cacheDivergenceCloudAI[idx];
			return CacheIndicator<TradingIndicators.DivergenceCloudAI>(new TradingIndicators.DivergenceCloudAI(){ LongPartialExits = longPartialExits, ShortPartialExits = shortPartialExits, UseTrailingStop = useTrailingStop, UsePartialExits = usePartialExits, tradingApproach = tradingApproach, TradingStyleByNumber = tradingStyleByNumber, TradingStyleNumber = tradingStyleNumber, ShowMaxSize = showMaxSize, MaxRisk = maxRisk, OptimizationLookback = optimizationLookback, ReOptimizationInterval = reOptimizationInterval, ReOptimizeAutomatically = reOptimizeAutomatically, ReOptimizationMode = reOptimizationMode, AutoOptimizeParameters = autoOptimizeParameters, OptimizationTarget = optimizationTarget, OptimizationMode = optimizationMode, ATRLookback = aTRLookback, DivergenceLookback = divergenceLookback, MultipleOfAtr = multipleOfAtr, ShowIET = showIET, PanelOpacity = panelOpacity, GlobalOpacity = globalOpacity, ShowZoneGlow = showZoneGlow, GlowOpacity = glowOpacity, ShowTriggerLines = showTriggerLines, ShowEntryExitLines = showEntryExitLines, ColorPriceBars = colorPriceBars }, input, ref cacheDivergenceCloudAI);
		}

		public TradingIndicators.OneTwoThreeStrikeAI OneTwoThreeStrikeAI(ISeries<double> input, int longPartialExits, int shortPartialExits, bool useTrailingStop, bool usePartialExits, TradingApproach_123STRIKEAI tradingApproach, bool tradingStyleByNumber, int tradingStyleNumber, bool showMaxSize, double maxRisk, int optimizationLookback, int reOptimizationInterval, bool reOptimizeAutomatically, ReOptimizationMode_123STRIKEAI reOptimizationMode, bool autoOptimizeParameters, OptimizationTarget_123STRIKEAI optimizationTarget, OptimizationMode_123STRIKEAI optimizationMode, bool showIET, double panelOpacity, double globalOpacity, bool colorPriceBars, int volatilityPeriod, int volumePeriod, int fastMAPeriod, int slowMAPeriod, bool exitOnHVR)
		{
			if (cacheOneTwoThreeStrikeAI != null)
				for (int idx = 0; idx < cacheOneTwoThreeStrikeAI.Length; idx++)
					if (cacheOneTwoThreeStrikeAI[idx].LongPartialExits == longPartialExits && cacheOneTwoThreeStrikeAI[idx].ShortPartialExits == shortPartialExits && cacheOneTwoThreeStrikeAI[idx].UseTrailingStop == useTrailingStop && cacheOneTwoThreeStrikeAI[idx].UsePartialExits == usePartialExits && cacheOneTwoThreeStrikeAI[idx].tradingApproach == tradingApproach && cacheOneTwoThreeStrikeAI[idx].TradingStyleByNumber == tradingStyleByNumber && cacheOneTwoThreeStrikeAI[idx].TradingStyleNumber == tradingStyleNumber && cacheOneTwoThreeStrikeAI[idx].ShowMaxSize == showMaxSize && cacheOneTwoThreeStrikeAI[idx].MaxRisk == maxRisk && cacheOneTwoThreeStrikeAI[idx].OptimizationLookback == optimizationLookback && cacheOneTwoThreeStrikeAI[idx].ReOptimizationInterval == reOptimizationInterval && cacheOneTwoThreeStrikeAI[idx].ReOptimizeAutomatically == reOptimizeAutomatically && cacheOneTwoThreeStrikeAI[idx].ReOptimizationMode == reOptimizationMode && cacheOneTwoThreeStrikeAI[idx].AutoOptimizeParameters == autoOptimizeParameters && cacheOneTwoThreeStrikeAI[idx].OptimizationTarget == optimizationTarget && cacheOneTwoThreeStrikeAI[idx].OptimizationMode == optimizationMode && cacheOneTwoThreeStrikeAI[idx].ShowIET == showIET && cacheOneTwoThreeStrikeAI[idx].PanelOpacity == panelOpacity && cacheOneTwoThreeStrikeAI[idx].GlobalOpacity == globalOpacity && cacheOneTwoThreeStrikeAI[idx].ColorPriceBars == colorPriceBars && cacheOneTwoThreeStrikeAI[idx].VolatilityPeriod == volatilityPeriod && cacheOneTwoThreeStrikeAI[idx].VolumePeriod == volumePeriod && cacheOneTwoThreeStrikeAI[idx].FastMAPeriod == fastMAPeriod && cacheOneTwoThreeStrikeAI[idx].SlowMAPeriod == slowMAPeriod && cacheOneTwoThreeStrikeAI[idx].ExitOnHVR == exitOnHVR && cacheOneTwoThreeStrikeAI[idx].EqualsInput(input))
						return cacheOneTwoThreeStrikeAI[idx];
			return CacheIndicator<TradingIndicators.OneTwoThreeStrikeAI>(new TradingIndicators.OneTwoThreeStrikeAI(){ LongPartialExits = longPartialExits, ShortPartialExits = shortPartialExits, UseTrailingStop = useTrailingStop, UsePartialExits = usePartialExits, tradingApproach = tradingApproach, TradingStyleByNumber = tradingStyleByNumber, TradingStyleNumber = tradingStyleNumber, ShowMaxSize = showMaxSize, MaxRisk = maxRisk, OptimizationLookback = optimizationLookback, ReOptimizationInterval = reOptimizationInterval, ReOptimizeAutomatically = reOptimizeAutomatically, ReOptimizationMode = reOptimizationMode, AutoOptimizeParameters = autoOptimizeParameters, OptimizationTarget = optimizationTarget, OptimizationMode = optimizationMode, ShowIET = showIET, PanelOpacity = panelOpacity, GlobalOpacity = globalOpacity, ColorPriceBars = colorPriceBars, VolatilityPeriod = volatilityPeriod, VolumePeriod = volumePeriod, FastMAPeriod = fastMAPeriod, SlowMAPeriod = slowMAPeriod, ExitOnHVR = exitOnHVR }, input, ref cacheOneTwoThreeStrikeAI);
		}

		public TradingIndicators.VolatilityCrusherAI VolatilityCrusherAI(ISeries<double> input, bool requireVolume, TradingApproach_VOLATILITY_CRUSHER_AI tradingApproach, bool tradingStyleByNumber, int tradingStyleNumber, bool showMaxSize, double maxRisk, int optimizationLookback, int reOptimizationInterval, bool reOptimizeAutomatically, ReOptimizationMode_VOLATILITY_CRUSHER_AI reOptimizationMode, bool autoOptimizeParameters, OptimizationTarget_VOLATILITY_CRUSHER_AI optimizationTarget, OptimizationMode_VOLATILITY_CRUSHER_AI optimizationMode, int compressionPeriod, int normalityPeriod, double stdDeviationsMultiple, int fastTrendPeriod, int slowTrendPeriod, int volumeMAPeriod, double volumeMultiple, double target1RetracementLevel, double target2RetracementLevel, double stopRetracementLevel, bool showIET, double panelOpacity, double globalOpacity, bool colorBars, bool showCompressionZones, int compressionZoneFillOpacity)
		{
			if (cacheVolatilityCrusherAI != null)
				for (int idx = 0; idx < cacheVolatilityCrusherAI.Length; idx++)
					if (cacheVolatilityCrusherAI[idx].RequireVolume == requireVolume && cacheVolatilityCrusherAI[idx].tradingApproach == tradingApproach && cacheVolatilityCrusherAI[idx].TradingStyleByNumber == tradingStyleByNumber && cacheVolatilityCrusherAI[idx].TradingStyleNumber == tradingStyleNumber && cacheVolatilityCrusherAI[idx].ShowMaxSize == showMaxSize && cacheVolatilityCrusherAI[idx].MaxRisk == maxRisk && cacheVolatilityCrusherAI[idx].OptimizationLookback == optimizationLookback && cacheVolatilityCrusherAI[idx].ReOptimizationInterval == reOptimizationInterval && cacheVolatilityCrusherAI[idx].ReOptimizeAutomatically == reOptimizeAutomatically && cacheVolatilityCrusherAI[idx].ReOptimizationMode == reOptimizationMode && cacheVolatilityCrusherAI[idx].AutoOptimizeParameters == autoOptimizeParameters && cacheVolatilityCrusherAI[idx].OptimizationTarget == optimizationTarget && cacheVolatilityCrusherAI[idx].OptimizationMode == optimizationMode && cacheVolatilityCrusherAI[idx].CompressionPeriod == compressionPeriod && cacheVolatilityCrusherAI[idx].NormalityPeriod == normalityPeriod && cacheVolatilityCrusherAI[idx].StdDeviationsMultiple == stdDeviationsMultiple && cacheVolatilityCrusherAI[idx].FastTrendPeriod == fastTrendPeriod && cacheVolatilityCrusherAI[idx].SlowTrendPeriod == slowTrendPeriod && cacheVolatilityCrusherAI[idx].VolumeMAPeriod == volumeMAPeriod && cacheVolatilityCrusherAI[idx].VolumeMultiple == volumeMultiple && cacheVolatilityCrusherAI[idx].Target1RetracementLevel == target1RetracementLevel && cacheVolatilityCrusherAI[idx].Target2RetracementLevel == target2RetracementLevel && cacheVolatilityCrusherAI[idx].StopRetracementLevel == stopRetracementLevel && cacheVolatilityCrusherAI[idx].ShowIET == showIET && cacheVolatilityCrusherAI[idx].PanelOpacity == panelOpacity && cacheVolatilityCrusherAI[idx].GlobalOpacity == globalOpacity && cacheVolatilityCrusherAI[idx].ColorBars == colorBars && cacheVolatilityCrusherAI[idx].ShowCompressionZones == showCompressionZones && cacheVolatilityCrusherAI[idx].CompressionZoneFillOpacity == compressionZoneFillOpacity && cacheVolatilityCrusherAI[idx].EqualsInput(input))
						return cacheVolatilityCrusherAI[idx];
			return CacheIndicator<TradingIndicators.VolatilityCrusherAI>(new TradingIndicators.VolatilityCrusherAI(){ RequireVolume = requireVolume, tradingApproach = tradingApproach, TradingStyleByNumber = tradingStyleByNumber, TradingStyleNumber = tradingStyleNumber, ShowMaxSize = showMaxSize, MaxRisk = maxRisk, OptimizationLookback = optimizationLookback, ReOptimizationInterval = reOptimizationInterval, ReOptimizeAutomatically = reOptimizeAutomatically, ReOptimizationMode = reOptimizationMode, AutoOptimizeParameters = autoOptimizeParameters, OptimizationTarget = optimizationTarget, OptimizationMode = optimizationMode, CompressionPeriod = compressionPeriod, NormalityPeriod = normalityPeriod, StdDeviationsMultiple = stdDeviationsMultiple, FastTrendPeriod = fastTrendPeriod, SlowTrendPeriod = slowTrendPeriod, VolumeMAPeriod = volumeMAPeriod, VolumeMultiple = volumeMultiple, Target1RetracementLevel = target1RetracementLevel, Target2RetracementLevel = target2RetracementLevel, StopRetracementLevel = stopRetracementLevel, ShowIET = showIET, PanelOpacity = panelOpacity, GlobalOpacity = globalOpacity, ColorBars = colorBars, ShowCompressionZones = showCompressionZones, CompressionZoneFillOpacity = compressionZoneFillOpacity }, input, ref cacheVolatilityCrusherAI);
		}

	}
}

namespace NinjaTrader.NinjaScript.MarketAnalyzerColumns
{
	public partial class MarketAnalyzerColumn : MarketAnalyzerColumnBase
	{
		
		public Indicators.TradingIndicators.DivergenceCloudAI DivergenceCloudAI(int longPartialExits, int shortPartialExits, bool useTrailingStop, bool usePartialExits, TradingApproach_DIVERGENCE_CLOUD_AI tradingApproach, bool tradingStyleByNumber, int tradingStyleNumber, bool showMaxSize, double maxRisk, int optimizationLookback, int reOptimizationInterval, bool reOptimizeAutomatically, ReOptimizationMode_DIVERGENCE_CLOUD_AI reOptimizationMode, bool autoOptimizeParameters, OptimizationTarget_DIVERGENCE_CLOUD_AI optimizationTarget, OptimizationMode_DIVERGENCE_CLOUD_AI optimizationMode, int aTRLookback, int divergenceLookback, double multipleOfAtr, bool showIET, double panelOpacity, double globalOpacity, bool showZoneGlow, int glowOpacity, bool showTriggerLines, bool showEntryExitLines, bool colorPriceBars)
		{
			return indicator.DivergenceCloudAI(Input, longPartialExits, shortPartialExits, useTrailingStop, usePartialExits, tradingApproach, tradingStyleByNumber, tradingStyleNumber, showMaxSize, maxRisk, optimizationLookback, reOptimizationInterval, reOptimizeAutomatically, reOptimizationMode, autoOptimizeParameters, optimizationTarget, optimizationMode, aTRLookback, divergenceLookback, multipleOfAtr, showIET, panelOpacity, globalOpacity, showZoneGlow, glowOpacity, showTriggerLines, showEntryExitLines, colorPriceBars);
		}

		public Indicators.TradingIndicators.OneTwoThreeStrikeAI OneTwoThreeStrikeAI(int longPartialExits, int shortPartialExits, bool useTrailingStop, bool usePartialExits, TradingApproach_123STRIKEAI tradingApproach, bool tradingStyleByNumber, int tradingStyleNumber, bool showMaxSize, double maxRisk, int optimizationLookback, int reOptimizationInterval, bool reOptimizeAutomatically, ReOptimizationMode_123STRIKEAI reOptimizationMode, bool autoOptimizeParameters, OptimizationTarget_123STRIKEAI optimizationTarget, OptimizationMode_123STRIKEAI optimizationMode, bool showIET, double panelOpacity, double globalOpacity, bool colorPriceBars, int volatilityPeriod, int volumePeriod, int fastMAPeriod, int slowMAPeriod, bool exitOnHVR)
		{
			return indicator.OneTwoThreeStrikeAI(Input, longPartialExits, shortPartialExits, useTrailingStop, usePartialExits, tradingApproach, tradingStyleByNumber, tradingStyleNumber, showMaxSize, maxRisk, optimizationLookback, reOptimizationInterval, reOptimizeAutomatically, reOptimizationMode, autoOptimizeParameters, optimizationTarget, optimizationMode, showIET, panelOpacity, globalOpacity, colorPriceBars, volatilityPeriod, volumePeriod, fastMAPeriod, slowMAPeriod, exitOnHVR);
		}

		public Indicators.TradingIndicators.VolatilityCrusherAI VolatilityCrusherAI(bool requireVolume, TradingApproach_VOLATILITY_CRUSHER_AI tradingApproach, bool tradingStyleByNumber, int tradingStyleNumber, bool showMaxSize, double maxRisk, int optimizationLookback, int reOptimizationInterval, bool reOptimizeAutomatically, ReOptimizationMode_VOLATILITY_CRUSHER_AI reOptimizationMode, bool autoOptimizeParameters, OptimizationTarget_VOLATILITY_CRUSHER_AI optimizationTarget, OptimizationMode_VOLATILITY_CRUSHER_AI optimizationMode, int compressionPeriod, int normalityPeriod, double stdDeviationsMultiple, int fastTrendPeriod, int slowTrendPeriod, int volumeMAPeriod, double volumeMultiple, double target1RetracementLevel, double target2RetracementLevel, double stopRetracementLevel, bool showIET, double panelOpacity, double globalOpacity, bool colorBars, bool showCompressionZones, int compressionZoneFillOpacity)
		{
			return indicator.VolatilityCrusherAI(Input, requireVolume, tradingApproach, tradingStyleByNumber, tradingStyleNumber, showMaxSize, maxRisk, optimizationLookback, reOptimizationInterval, reOptimizeAutomatically, reOptimizationMode, autoOptimizeParameters, optimizationTarget, optimizationMode, compressionPeriod, normalityPeriod, stdDeviationsMultiple, fastTrendPeriod, slowTrendPeriod, volumeMAPeriod, volumeMultiple, target1RetracementLevel, target2RetracementLevel, stopRetracementLevel, showIET, panelOpacity, globalOpacity, colorBars, showCompressionZones, compressionZoneFillOpacity);
		}


		
		public Indicators.TradingIndicators.DivergenceCloudAI DivergenceCloudAI(ISeries<double> input , int longPartialExits, int shortPartialExits, bool useTrailingStop, bool usePartialExits, TradingApproach_DIVERGENCE_CLOUD_AI tradingApproach, bool tradingStyleByNumber, int tradingStyleNumber, bool showMaxSize, double maxRisk, int optimizationLookback, int reOptimizationInterval, bool reOptimizeAutomatically, ReOptimizationMode_DIVERGENCE_CLOUD_AI reOptimizationMode, bool autoOptimizeParameters, OptimizationTarget_DIVERGENCE_CLOUD_AI optimizationTarget, OptimizationMode_DIVERGENCE_CLOUD_AI optimizationMode, int aTRLookback, int divergenceLookback, double multipleOfAtr, bool showIET, double panelOpacity, double globalOpacity, bool showZoneGlow, int glowOpacity, bool showTriggerLines, bool showEntryExitLines, bool colorPriceBars)
		{
			return indicator.DivergenceCloudAI(input, longPartialExits, shortPartialExits, useTrailingStop, usePartialExits, tradingApproach, tradingStyleByNumber, tradingStyleNumber, showMaxSize, maxRisk, optimizationLookback, reOptimizationInterval, reOptimizeAutomatically, reOptimizationMode, autoOptimizeParameters, optimizationTarget, optimizationMode, aTRLookback, divergenceLookback, multipleOfAtr, showIET, panelOpacity, globalOpacity, showZoneGlow, glowOpacity, showTriggerLines, showEntryExitLines, colorPriceBars);
		}

		public Indicators.TradingIndicators.OneTwoThreeStrikeAI OneTwoThreeStrikeAI(ISeries<double> input , int longPartialExits, int shortPartialExits, bool useTrailingStop, bool usePartialExits, TradingApproach_123STRIKEAI tradingApproach, bool tradingStyleByNumber, int tradingStyleNumber, bool showMaxSize, double maxRisk, int optimizationLookback, int reOptimizationInterval, bool reOptimizeAutomatically, ReOptimizationMode_123STRIKEAI reOptimizationMode, bool autoOptimizeParameters, OptimizationTarget_123STRIKEAI optimizationTarget, OptimizationMode_123STRIKEAI optimizationMode, bool showIET, double panelOpacity, double globalOpacity, bool colorPriceBars, int volatilityPeriod, int volumePeriod, int fastMAPeriod, int slowMAPeriod, bool exitOnHVR)
		{
			return indicator.OneTwoThreeStrikeAI(input, longPartialExits, shortPartialExits, useTrailingStop, usePartialExits, tradingApproach, tradingStyleByNumber, tradingStyleNumber, showMaxSize, maxRisk, optimizationLookback, reOptimizationInterval, reOptimizeAutomatically, reOptimizationMode, autoOptimizeParameters, optimizationTarget, optimizationMode, showIET, panelOpacity, globalOpacity, colorPriceBars, volatilityPeriod, volumePeriod, fastMAPeriod, slowMAPeriod, exitOnHVR);
		}

		public Indicators.TradingIndicators.VolatilityCrusherAI VolatilityCrusherAI(ISeries<double> input , bool requireVolume, TradingApproach_VOLATILITY_CRUSHER_AI tradingApproach, bool tradingStyleByNumber, int tradingStyleNumber, bool showMaxSize, double maxRisk, int optimizationLookback, int reOptimizationInterval, bool reOptimizeAutomatically, ReOptimizationMode_VOLATILITY_CRUSHER_AI reOptimizationMode, bool autoOptimizeParameters, OptimizationTarget_VOLATILITY_CRUSHER_AI optimizationTarget, OptimizationMode_VOLATILITY_CRUSHER_AI optimizationMode, int compressionPeriod, int normalityPeriod, double stdDeviationsMultiple, int fastTrendPeriod, int slowTrendPeriod, int volumeMAPeriod, double volumeMultiple, double target1RetracementLevel, double target2RetracementLevel, double stopRetracementLevel, bool showIET, double panelOpacity, double globalOpacity, bool colorBars, bool showCompressionZones, int compressionZoneFillOpacity)
		{
			return indicator.VolatilityCrusherAI(input, requireVolume, tradingApproach, tradingStyleByNumber, tradingStyleNumber, showMaxSize, maxRisk, optimizationLookback, reOptimizationInterval, reOptimizeAutomatically, reOptimizationMode, autoOptimizeParameters, optimizationTarget, optimizationMode, compressionPeriod, normalityPeriod, stdDeviationsMultiple, fastTrendPeriod, slowTrendPeriod, volumeMAPeriod, volumeMultiple, target1RetracementLevel, target2RetracementLevel, stopRetracementLevel, showIET, panelOpacity, globalOpacity, colorBars, showCompressionZones, compressionZoneFillOpacity);
		}
	
	}
}

namespace NinjaTrader.NinjaScript.Strategies
{
	public partial class Strategy : NinjaTrader.Gui.NinjaScript.StrategyRenderBase
	{
		
		public Indicators.TradingIndicators.DivergenceCloudAI DivergenceCloudAI(int longPartialExits, int shortPartialExits, bool useTrailingStop, bool usePartialExits, TradingApproach_DIVERGENCE_CLOUD_AI tradingApproach, bool tradingStyleByNumber, int tradingStyleNumber, bool showMaxSize, double maxRisk, int optimizationLookback, int reOptimizationInterval, bool reOptimizeAutomatically, ReOptimizationMode_DIVERGENCE_CLOUD_AI reOptimizationMode, bool autoOptimizeParameters, OptimizationTarget_DIVERGENCE_CLOUD_AI optimizationTarget, OptimizationMode_DIVERGENCE_CLOUD_AI optimizationMode, int aTRLookback, int divergenceLookback, double multipleOfAtr, bool showIET, double panelOpacity, double globalOpacity, bool showZoneGlow, int glowOpacity, bool showTriggerLines, bool showEntryExitLines, bool colorPriceBars)
		{
			return indicator.DivergenceCloudAI(Input, longPartialExits, shortPartialExits, useTrailingStop, usePartialExits, tradingApproach, tradingStyleByNumber, tradingStyleNumber, showMaxSize, maxRisk, optimizationLookback, reOptimizationInterval, reOptimizeAutomatically, reOptimizationMode, autoOptimizeParameters, optimizationTarget, optimizationMode, aTRLookback, divergenceLookback, multipleOfAtr, showIET, panelOpacity, globalOpacity, showZoneGlow, glowOpacity, showTriggerLines, showEntryExitLines, colorPriceBars);
		}

		public Indicators.TradingIndicators.OneTwoThreeStrikeAI OneTwoThreeStrikeAI(int longPartialExits, int shortPartialExits, bool useTrailingStop, bool usePartialExits, TradingApproach_123STRIKEAI tradingApproach, bool tradingStyleByNumber, int tradingStyleNumber, bool showMaxSize, double maxRisk, int optimizationLookback, int reOptimizationInterval, bool reOptimizeAutomatically, ReOptimizationMode_123STRIKEAI reOptimizationMode, bool autoOptimizeParameters, OptimizationTarget_123STRIKEAI optimizationTarget, OptimizationMode_123STRIKEAI optimizationMode, bool showIET, double panelOpacity, double globalOpacity, bool colorPriceBars, int volatilityPeriod, int volumePeriod, int fastMAPeriod, int slowMAPeriod, bool exitOnHVR)
		{
			return indicator.OneTwoThreeStrikeAI(Input, longPartialExits, shortPartialExits, useTrailingStop, usePartialExits, tradingApproach, tradingStyleByNumber, tradingStyleNumber, showMaxSize, maxRisk, optimizationLookback, reOptimizationInterval, reOptimizeAutomatically, reOptimizationMode, autoOptimizeParameters, optimizationTarget, optimizationMode, showIET, panelOpacity, globalOpacity, colorPriceBars, volatilityPeriod, volumePeriod, fastMAPeriod, slowMAPeriod, exitOnHVR);
		}

		public Indicators.TradingIndicators.VolatilityCrusherAI VolatilityCrusherAI(bool requireVolume, TradingApproach_VOLATILITY_CRUSHER_AI tradingApproach, bool tradingStyleByNumber, int tradingStyleNumber, bool showMaxSize, double maxRisk, int optimizationLookback, int reOptimizationInterval, bool reOptimizeAutomatically, ReOptimizationMode_VOLATILITY_CRUSHER_AI reOptimizationMode, bool autoOptimizeParameters, OptimizationTarget_VOLATILITY_CRUSHER_AI optimizationTarget, OptimizationMode_VOLATILITY_CRUSHER_AI optimizationMode, int compressionPeriod, int normalityPeriod, double stdDeviationsMultiple, int fastTrendPeriod, int slowTrendPeriod, int volumeMAPeriod, double volumeMultiple, double target1RetracementLevel, double target2RetracementLevel, double stopRetracementLevel, bool showIET, double panelOpacity, double globalOpacity, bool colorBars, bool showCompressionZones, int compressionZoneFillOpacity)
		{
			return indicator.VolatilityCrusherAI(Input, requireVolume, tradingApproach, tradingStyleByNumber, tradingStyleNumber, showMaxSize, maxRisk, optimizationLookback, reOptimizationInterval, reOptimizeAutomatically, reOptimizationMode, autoOptimizeParameters, optimizationTarget, optimizationMode, compressionPeriod, normalityPeriod, stdDeviationsMultiple, fastTrendPeriod, slowTrendPeriod, volumeMAPeriod, volumeMultiple, target1RetracementLevel, target2RetracementLevel, stopRetracementLevel, showIET, panelOpacity, globalOpacity, colorBars, showCompressionZones, compressionZoneFillOpacity);
		}


		
		public Indicators.TradingIndicators.DivergenceCloudAI DivergenceCloudAI(ISeries<double> input , int longPartialExits, int shortPartialExits, bool useTrailingStop, bool usePartialExits, TradingApproach_DIVERGENCE_CLOUD_AI tradingApproach, bool tradingStyleByNumber, int tradingStyleNumber, bool showMaxSize, double maxRisk, int optimizationLookback, int reOptimizationInterval, bool reOptimizeAutomatically, ReOptimizationMode_DIVERGENCE_CLOUD_AI reOptimizationMode, bool autoOptimizeParameters, OptimizationTarget_DIVERGENCE_CLOUD_AI optimizationTarget, OptimizationMode_DIVERGENCE_CLOUD_AI optimizationMode, int aTRLookback, int divergenceLookback, double multipleOfAtr, bool showIET, double panelOpacity, double globalOpacity, bool showZoneGlow, int glowOpacity, bool showTriggerLines, bool showEntryExitLines, bool colorPriceBars)
		{
			return indicator.DivergenceCloudAI(input, longPartialExits, shortPartialExits, useTrailingStop, usePartialExits, tradingApproach, tradingStyleByNumber, tradingStyleNumber, showMaxSize, maxRisk, optimizationLookback, reOptimizationInterval, reOptimizeAutomatically, reOptimizationMode, autoOptimizeParameters, optimizationTarget, optimizationMode, aTRLookback, divergenceLookback, multipleOfAtr, showIET, panelOpacity, globalOpacity, showZoneGlow, glowOpacity, showTriggerLines, showEntryExitLines, colorPriceBars);
		}

		public Indicators.TradingIndicators.OneTwoThreeStrikeAI OneTwoThreeStrikeAI(ISeries<double> input , int longPartialExits, int shortPartialExits, bool useTrailingStop, bool usePartialExits, TradingApproach_123STRIKEAI tradingApproach, bool tradingStyleByNumber, int tradingStyleNumber, bool showMaxSize, double maxRisk, int optimizationLookback, int reOptimizationInterval, bool reOptimizeAutomatically, ReOptimizationMode_123STRIKEAI reOptimizationMode, bool autoOptimizeParameters, OptimizationTarget_123STRIKEAI optimizationTarget, OptimizationMode_123STRIKEAI optimizationMode, bool showIET, double panelOpacity, double globalOpacity, bool colorPriceBars, int volatilityPeriod, int volumePeriod, int fastMAPeriod, int slowMAPeriod, bool exitOnHVR)
		{
			return indicator.OneTwoThreeStrikeAI(input, longPartialExits, shortPartialExits, useTrailingStop, usePartialExits, tradingApproach, tradingStyleByNumber, tradingStyleNumber, showMaxSize, maxRisk, optimizationLookback, reOptimizationInterval, reOptimizeAutomatically, reOptimizationMode, autoOptimizeParameters, optimizationTarget, optimizationMode, showIET, panelOpacity, globalOpacity, colorPriceBars, volatilityPeriod, volumePeriod, fastMAPeriod, slowMAPeriod, exitOnHVR);
		}

		public Indicators.TradingIndicators.VolatilityCrusherAI VolatilityCrusherAI(ISeries<double> input , bool requireVolume, TradingApproach_VOLATILITY_CRUSHER_AI tradingApproach, bool tradingStyleByNumber, int tradingStyleNumber, bool showMaxSize, double maxRisk, int optimizationLookback, int reOptimizationInterval, bool reOptimizeAutomatically, ReOptimizationMode_VOLATILITY_CRUSHER_AI reOptimizationMode, bool autoOptimizeParameters, OptimizationTarget_VOLATILITY_CRUSHER_AI optimizationTarget, OptimizationMode_VOLATILITY_CRUSHER_AI optimizationMode, int compressionPeriod, int normalityPeriod, double stdDeviationsMultiple, int fastTrendPeriod, int slowTrendPeriod, int volumeMAPeriod, double volumeMultiple, double target1RetracementLevel, double target2RetracementLevel, double stopRetracementLevel, bool showIET, double panelOpacity, double globalOpacity, bool colorBars, bool showCompressionZones, int compressionZoneFillOpacity)
		{
			return indicator.VolatilityCrusherAI(input, requireVolume, tradingApproach, tradingStyleByNumber, tradingStyleNumber, showMaxSize, maxRisk, optimizationLookback, reOptimizationInterval, reOptimizeAutomatically, reOptimizationMode, autoOptimizeParameters, optimizationTarget, optimizationMode, compressionPeriod, normalityPeriod, stdDeviationsMultiple, fastTrendPeriod, slowTrendPeriod, volumeMAPeriod, volumeMultiple, target1RetracementLevel, target2RetracementLevel, stopRetracementLevel, showIET, panelOpacity, globalOpacity, colorBars, showCompressionZones, compressionZoneFillOpacity);
		}

	}
}

#endregion
