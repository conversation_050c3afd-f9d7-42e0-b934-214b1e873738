﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace NinjaTrader.Custom {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class Resource {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resource() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("NinjaTrader.Custom.Resource", typeof(Resource).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Acceleration.
        /// </summary>
        public static string Acceleration {
            get {
                return ResourceManager.GetString("Acceleration", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Acceleration max.
        /// </summary>
        public static string AccelerationMax {
            get {
                return ResourceManager.GetString("AccelerationMax", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Acceleration step.
        /// </summary>
        public static string AccelerationStep {
            get {
                return ResourceManager.GetString("AccelerationStep", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AD.
        /// </summary>
        public static string ADLAD {
            get {
                return ResourceManager.GetString("ADLAD", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Alert on break.
        /// </summary>
        public static string AlertOnBreak {
            get {
                return ResourceManager.GetString("AlertOnBreak", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Alert on break sound.
        /// </summary>
        public static string AlertOnBreakSound {
            get {
                return ResourceManager.GetString("AlertOnBreakSound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Modified Schiff.
        /// </summary>
        public static string AndrewsPitchforkCalculationMethod_ModifiedSchiff {
            get {
                return ResourceManager.GetString("AndrewsPitchforkCalculationMethod_ModifiedSchiff", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Schiff.
        /// </summary>
        public static string AndrewsPitchforkCalculationMethod_Schiff {
            get {
                return ResourceManager.GetString("AndrewsPitchforkCalculationMethod_Schiff", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Standard.
        /// </summary>
        public static string AndrewsPitchforkCalculationMethod_StandardPitchfork {
            get {
                return ResourceManager.GetString("AndrewsPitchforkCalculationMethod_StandardPitchfork", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ask line length (% of chart).
        /// </summary>
        public static string AskLineLength {
            get {
                return ResourceManager.GetString("AskLineLength", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ask line.
        /// </summary>
        public static string AskLineStroke {
            get {
                return ResourceManager.GetString("AskLineStroke", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Copyright &lt;sup&gt;©&lt;/sup&gt; {0}. All rights reserved. NinjaTrader and the NinjaTrader logo. Reg. U.S. Pat. &amp;amp; Tm. Off..
        /// </summary>
        public static string AuthDisclosureText1 {
            get {
                return ResourceManager.GetString("AuthDisclosureText1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FULL RISK DISCLOSURE: Futures and forex trading contains substantial risk and is not for every investor. An investor could potentially lose all or more than the initial investment. Risk capital is money that can be lost without jeopardizing ones financial security or lifestyle. Only risk capital should be used for trading and only those with sufficient risk capital should consider trading. Past performance is not necessarily indicative of future results..
        /// </summary>
        public static string AuthDisclosureText2 {
            get {
                return ResourceManager.GetString("AuthDisclosureText2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Band percent.
        /// </summary>
        public static string BandPct {
            get {
                return ResourceManager.GetString("BandPct", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bar count.
        /// </summary>
        public static string BarCount {
            get {
                return ResourceManager.GetString("BarCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bar down.
        /// </summary>
        public static string BarDown {
            get {
                return ResourceManager.GetString("BarDown", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bar spacing.
        /// </summary>
        public static string BarSpacing {
            get {
                return ResourceManager.GetString("BarSpacing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bars period type.
        /// </summary>
        public static string BarsPeriodType {
            get {
                return ResourceManager.GetString("BarsPeriodType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Day.
        /// </summary>
        public static string BarsPeriodTypeNameDay {
            get {
                return ResourceManager.GetString("BarsPeriodTypeNameDay", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Heiken-Ashi.
        /// </summary>
        public static string BarsPeriodTypeNameHeikenAshi {
            get {
                return ResourceManager.GetString("BarsPeriodTypeNameHeikenAshi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kagi.
        /// </summary>
        public static string BarsPeriodTypeNameKagi {
            get {
                return ResourceManager.GetString("BarsPeriodTypeNameKagi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Line Break.
        /// </summary>
        public static string BarsPeriodTypeNameLineBreak {
            get {
                return ResourceManager.GetString("BarsPeriodTypeNameLineBreak", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Minute.
        /// </summary>
        public static string BarsPeriodTypeNameMinute {
            get {
                return ResourceManager.GetString("BarsPeriodTypeNameMinute", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Month.
        /// </summary>
        public static string BarsPeriodTypeNameMonth {
            get {
                return ResourceManager.GetString("BarsPeriodTypeNameMonth", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Point and Figure.
        /// </summary>
        public static string BarsPeriodTypeNamePointAndFigure {
            get {
                return ResourceManager.GetString("BarsPeriodTypeNamePointAndFigure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Range.
        /// </summary>
        public static string BarsPeriodTypeNameRange {
            get {
                return ResourceManager.GetString("BarsPeriodTypeNameRange", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Renko.
        /// </summary>
        public static string BarsPeriodTypeNameRenko {
            get {
                return ResourceManager.GetString("BarsPeriodTypeNameRenko", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Second.
        /// </summary>
        public static string BarsPeriodTypeNameSecond {
            get {
                return ResourceManager.GetString("BarsPeriodTypeNameSecond", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tick.
        /// </summary>
        public static string BarsPeriodTypeNameTick {
            get {
                return ResourceManager.GetString("BarsPeriodTypeNameTick", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Volume.
        /// </summary>
        public static string BarsPeriodTypeNameVolume {
            get {
                return ResourceManager.GetString("BarsPeriodTypeNameVolume", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Week.
        /// </summary>
        public static string BarsPeriodTypeNameWeek {
            get {
                return ResourceManager.GetString("BarsPeriodTypeNameWeek", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Year.
        /// </summary>
        public static string BarsPeriodTypeNameYear {
            get {
                return ResourceManager.GetString("BarsPeriodTypeNameYear", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bars period value.
        /// </summary>
        public static string BarsPeriodValue {
            get {
                return ResourceManager.GetString("BarsPeriodValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bar timer disabled since you are currently disconnected from a data provider.
        /// </summary>
        public static string BarTimerDisconnectedError {
            get {
                return ResourceManager.GetString("BarTimerDisconnectedError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bar timer disabled since the current time is outside session time or chart end date.
        /// </summary>
        public static string BarTimerSessionTimeError {
            get {
                return ResourceManager.GetString("BarTimerSessionTimeError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bar timer only works on intraday time based intervals.
        /// </summary>
        public static string BarTimerTimeBasedError {
            get {
                return ResourceManager.GetString("BarTimerTimeBasedError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Time remaining = .
        /// </summary>
        public static string BarTimerTimeRemaining {
            get {
                return ResourceManager.GetString("BarTimerTimeRemaining", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BarTimer waiting for realtime data before starting.
        /// </summary>
        public static string BarTimerWaitingOnDataError {
            get {
                return ResourceManager.GetString("BarTimerWaitingOnDataError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bar up.
        /// </summary>
        public static string BarUp {
            get {
                return ResourceManager.GetString("BarUp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Base period.
        /// </summary>
        public static string BasePeriod {
            get {
                return ResourceManager.GetString("BasePeriod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bid line length (% of chart).
        /// </summary>
        public static string BidLineLength {
            get {
                return ResourceManager.GetString("BidLineLength", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bid line.
        /// </summary>
        public static string BidLineStroke {
            get {
                return ResourceManager.GetString("BidLineStroke", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Block trade size.
        /// </summary>
        public static string BlockTradeSize {
            get {
                return ResourceManager.GetString("BlockTradeSize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lower band.
        /// </summary>
        public static string BollingerLowerBand {
            get {
                return ResourceManager.GetString("BollingerLowerBand", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Middle band.
        /// </summary>
        public static string BollingerMiddleBand {
            get {
                return ResourceManager.GetString("BollingerMiddleBand", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Upper band.
        /// </summary>
        public static string BollingerUpperBand {
            get {
                return ResourceManager.GetString("BollingerUpperBand", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Buy pressure.
        /// </summary>
        public static string BuySellPressureBuyPressure {
            get {
                return ResourceManager.GetString("BuySellPressureBuyPressure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sell pressure.
        /// </summary>
        public static string BuySellPressureSellPressure {
            get {
                return ResourceManager.GetString("BuySellPressureSellPressure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Buys.
        /// </summary>
        public static string BuySellVolumeBuys {
            get {
                return ResourceManager.GetString("BuySellVolumeBuys", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sells.
        /// </summary>
        public static string BuySellVolumeSells {
            get {
                return ResourceManager.GetString("BuySellVolumeSells", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pattern found.
        /// </summary>
        public static string CandlestickPatternFound {
            get {
                return ResourceManager.GetString("CandlestickPatternFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Level 1.
        /// </summary>
        public static string CCILevel1 {
            get {
                return ResourceManager.GetString("CCILevel1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Level 2.
        /// </summary>
        public static string CCILevel2 {
            get {
                return ResourceManager.GetString("CCILevel2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Level -1.
        /// </summary>
        public static string CCILevelMinus1 {
            get {
                return ResourceManager.GetString("CCILevelMinus1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Level -2.
        /// </summary>
        public static string CCILevelMinus2 {
            get {
                return ResourceManager.GetString("CCILevelMinus2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 1 Day.
        /// </summary>
        public static string ChartSpan_Day {
            get {
                return ResourceManager.GetString("ChartSpan_Day", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 1 min.
        /// </summary>
        public static string ChartSpan_Min1 {
            get {
                return ResourceManager.GetString("ChartSpan_Min1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 15 min.
        /// </summary>
        public static string ChartSpan_Min15 {
            get {
                return ResourceManager.GetString("ChartSpan_Min15", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 240 min.
        /// </summary>
        public static string ChartSpan_Min240 {
            get {
                return ResourceManager.GetString("ChartSpan_Min240", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 30 min.
        /// </summary>
        public static string ChartSpan_Min30 {
            get {
                return ResourceManager.GetString("ChartSpan_Min30", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 5 min.
        /// </summary>
        public static string ChartSpan_Min5 {
            get {
                return ResourceManager.GetString("ChartSpan_Min5", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 60 min.
        /// </summary>
        public static string ChartSpan_Min60 {
            get {
                return ResourceManager.GetString("ChartSpan_Min60", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 1 Month.
        /// </summary>
        public static string ChartSpan_Month {
            get {
                return ResourceManager.GetString("ChartSpan_Month", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 1 Week.
        /// </summary>
        public static string ChartSpan_Week {
            get {
                return ResourceManager.GetString("ChartSpan_Week", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 1 Year.
        /// </summary>
        public static string ChartSpan_Year {
            get {
                return ResourceManager.GetString("ChartSpan_Year", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Line 1.
        /// </summary>
        public static string ConstantLines1 {
            get {
                return ResourceManager.GetString("ConstantLines1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Line 2.
        /// </summary>
        public static string ConstantLines2 {
            get {
                return ResourceManager.GetString("ConstantLines2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Line 3.
        /// </summary>
        public static string ConstantLines3 {
            get {
                return ResourceManager.GetString("ConstantLines3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Line 4.
        /// </summary>
        public static string ConstantLines4 {
            get {
                return ResourceManager.GetString("ConstantLines4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to COT 1.
        /// </summary>
        public static string COT1 {
            get {
                return ResourceManager.GetString("COT1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to COT 2.
        /// </summary>
        public static string COT2 {
            get {
                return ResourceManager.GetString("COT2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to COT 3.
        /// </summary>
        public static string COT3 {
            get {
                return ResourceManager.GetString("COT3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to COT 4.
        /// </summary>
        public static string COT4 {
            get {
                return ResourceManager.GetString("COT4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to COT 5.
        /// </summary>
        public static string COT5 {
            get {
                return ResourceManager.GetString("COT5", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to COT data is not supported for this instrument.
        /// </summary>
        public static string CotDataError {
            get {
                return ResourceManager.GetString("CotDataError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to COT data is still being downloaded. Please refresh the indicator in few moments..
        /// </summary>
        public static string CotDataStillDownloading {
            get {
                return ResourceManager.GetString("CotDataStillDownloading", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &quot;Download COT data at startup&quot; must be enabled in Settings to receive the latest data.
        /// </summary>
        public static string CotDataWarning {
            get {
                return ResourceManager.GetString("CotDataWarning", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Count down.
        /// </summary>
        public static string CountDown {
            get {
                return ResourceManager.GetString("CountDown", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Trades.
        /// </summary>
        public static string CountType_Trades {
            get {
                return ResourceManager.GetString("CountType_Trades", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Volume.
        /// </summary>
        public static string CountType_Volume {
            get {
                return ResourceManager.GetString("CountType_Volume", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CurrentDayOHL only works on intraday intervals.
        /// </summary>
        public static string CurrentDayOHLError {
            get {
                return ResourceManager.GetString("CurrentDayOHLError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current high.
        /// </summary>
        public static string CurrentDayOHLHigh {
            get {
                return ResourceManager.GetString("CurrentDayOHLHigh", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current low.
        /// </summary>
        public static string CurrentDayOHLLow {
            get {
                return ResourceManager.GetString("CurrentDayOHLLow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current open.
        /// </summary>
        public static string CurrentDayOHLOpen {
            get {
                return ResourceManager.GetString("CurrentDayOHLOpen", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Buy Market.
        /// </summary>
        public static string CustomWindowAddOnBuyMarket {
            get {
                return ResourceManager.GetString("CustomWindowAddOnBuyMarket", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sell Market.
        /// </summary>
        public static string CustomWindowAddOnSellMarket {
            get {
                return ResourceManager.GetString("CustomWindowAddOnSellMarket", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Custom Window Description.
        /// </summary>
        public static string CustomWindowSampleDescription {
            get {
                return ResourceManager.GetString("CustomWindowSampleDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Custom Window Sample.
        /// </summary>
        public static string CustomWindowSampleName {
            get {
                return ResourceManager.GetString("CustomWindowSampleName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Daily.
        /// </summary>
        public static string DataBarsTypeDaily {
            get {
                return ResourceManager.GetString("DataBarsTypeDaily", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} Day.
        /// </summary>
        public static string DataBarsTypeDay {
            get {
                return ResourceManager.GetString("DataBarsTypeDay", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} Minute{1}.
        /// </summary>
        public static string DataBarsTypeMinute {
            get {
                return ResourceManager.GetString("DataBarsTypeMinute", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} Month.
        /// </summary>
        public static string DataBarsTypeMonth {
            get {
                return ResourceManager.GetString("DataBarsTypeMonth", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Monthly.
        /// </summary>
        public static string DataBarsTypeMonthly {
            get {
                return ResourceManager.GetString("DataBarsTypeMonthly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} Point and Figure.
        /// </summary>
        public static string DataBarsTypePointAndFigure {
            get {
                return ResourceManager.GetString("DataBarsTypePointAndFigure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} Range{1}.
        /// </summary>
        public static string DataBarsTypeRange {
            get {
                return ResourceManager.GetString("DataBarsTypeRange", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} Renko.
        /// </summary>
        public static string DataBarsTypeRenko {
            get {
                return ResourceManager.GetString("DataBarsTypeRenko", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} Second.
        /// </summary>
        public static string DataBarsTypeSecond {
            get {
                return ResourceManager.GetString("DataBarsTypeSecond", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} Tick{1}.
        /// </summary>
        public static string DataBarsTypeTick {
            get {
                return ResourceManager.GetString("DataBarsTypeTick", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} Volume{1}.
        /// </summary>
        public static string DataBarsTypeVolume {
            get {
                return ResourceManager.GetString("DataBarsTypeVolume", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} Week.
        /// </summary>
        public static string DataBarsTypeWeek {
            get {
                return ResourceManager.GetString("DataBarsTypeWeek", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Weekly.
        /// </summary>
        public static string DataBarsTypeWeekly {
            get {
                return ResourceManager.GetString("DataBarsTypeWeekly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} Year.
        /// </summary>
        public static string DataBarsTypeYear {
            get {
                return ResourceManager.GetString("DataBarsTypeYear", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yearly.
        /// </summary>
        public static string DataBarsTypeYearly {
            get {
                return ResourceManager.GetString("DataBarsTypeYearly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Day.
        /// </summary>
        public static string Day {
            get {
                return ResourceManager.GetString("Day", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Days.
        /// </summary>
        public static string Days {
            get {
                return ResourceManager.GetString("Days", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deviation type.
        /// </summary>
        public static string DeviationType {
            get {
                return ResourceManager.GetString("DeviationType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deviation value.
        /// </summary>
        public static string DeviationValue {
            get {
                return ResourceManager.GetString("DeviationValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to -DI.
        /// </summary>
        public static string DMMinusDI {
            get {
                return ResourceManager.GetString("DMMinusDI", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to +DI.
        /// </summary>
        public static string DMPlusDI {
            get {
                return ResourceManager.GetString("DMPlusDI", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mean.
        /// </summary>
        public static string DonchianChannelMean {
            get {
                return ResourceManager.GetString("DonchianChannelMean", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Down bar color.
        /// </summary>
        public static string DownBarColor {
            get {
                return ResourceManager.GetString("DownBarColor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Drawing tool tile indicator adds the ability to have a floating tile in the chart that can be customized to quickly access the most commonly used drawing tools..
        /// </summary>
        public static string DrawingToolIndicatorDescription {
            get {
                return ResourceManager.GetString("DrawingToolIndicatorDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Drawing tool tile.
        /// </summary>
        public static string DrawingToolIndicatorName {
            get {
                return ResourceManager.GetString("DrawingToolIndicatorName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Draw lines.
        /// </summary>
        public static string DrawLines {
            get {
                return ResourceManager.GetString("DrawLines", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EMA1 period.
        /// </summary>
        public static string EMA1 {
            get {
                return ResourceManager.GetString("EMA1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EMA2 period.
        /// </summary>
        public static string EMA2 {
            get {
                return ResourceManager.GetString("EMA2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  Sent by NinjaTrader.
        /// </summary>
        public static string EmailSignature {
            get {
                return ResourceManager.GetString("EmailSignature", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Envelope percentage.
        /// </summary>
        public static string EnvelopePercentage {
            get {
                return ResourceManager.GetString("EnvelopePercentage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Facebook.
        /// </summary>
        public static string FacebookServiceName {
            get {
                return ResourceManager.GetString("FacebookServiceName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sent by NinjaTrader.
        /// </summary>
        public static string FacebookSignature {
            get {
                return ResourceManager.GetString("FacebookSignature", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fast.
        /// </summary>
        public static string Fast {
            get {
                return ResourceManager.GetString("Fast", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fast limit.
        /// </summary>
        public static string FastLimit {
            get {
                return ResourceManager.GetString("FastLimit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fast period.
        /// </summary>
        public static string FastPeriod {
            get {
                return ResourceManager.GetString("FastPeriod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Extreme left.
        /// </summary>
        public static string FibonacciTextAlignment_ExtremeLeft {
            get {
                return ResourceManager.GetString("FibonacciTextAlignment_ExtremeLeft", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Extreme right.
        /// </summary>
        public static string FibonacciTextAlignment_ExtremeRight {
            get {
                return ResourceManager.GetString("FibonacciTextAlignment_ExtremeRight", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Left.
        /// </summary>
        public static string FibonacciTextAlignment_Left {
            get {
                return ResourceManager.GetString("FibonacciTextAlignment_Left", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Off.
        /// </summary>
        public static string FibonacciTextAlignment_Off {
            get {
                return ResourceManager.GetString("FibonacciTextAlignment_Off", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Right.
        /// </summary>
        public static string FibonacciTextAlignment_Right {
            get {
                return ResourceManager.GetString("FibonacciTextAlignment_Right", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Any (*.*).
        /// </summary>
        public static string FileFilterAnyLoadingDialog {
            get {
                return ResourceManager.GetString("FileFilterAnyLoadingDialog", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Any (*.*)|*.*.
        /// </summary>
        public static string FileFilterAnyWinForms {
            get {
                return ResourceManager.GetString("FileFilterAnyWinForms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File name.
        /// </summary>
        public static string FileName {
            get {
                return ResourceManager.GetString("FileName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Font.
        /// </summary>
        public static string Font {
            get {
                return ResourceManager.GetString("Font", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Forecast.
        /// </summary>
        public static string Forecast {
            get {
                return ResourceManager.GetString("Forecast", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bars specified.
        /// </summary>
        public static string FVGBarsSpecified {
            get {
                return ResourceManager.GetString("FVGBarsSpecified", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bars to extend.
        /// </summary>
        public static string FVGBarsToExtend {
            get {
                return ResourceManager.GetString("FVGBarsToExtend", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Fair Value Gap indicator examines three consecutive bars to highlight a gap between the first and third bar..
        /// </summary>
        public static string FVGDescription {
            get {
                return ResourceManager.GetString("FVGDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Extend until.
        /// </summary>
        public static string FVGExtendUntil {
            get {
                return ResourceManager.GetString("FVGExtendUntil", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Filled.
        /// </summary>
        public static string FVGFilled {
            get {
                return ResourceManager.GetString("FVGFilled", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Max FVG.
        /// </summary>
        public static string FVGMaxFVG {
            get {
                return ResourceManager.GetString("FVGMaxFVG", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Minimum ticks.
        /// </summary>
        public static string FVGMinimumTicks {
            get {
                return ResourceManager.GetString("FVGMinimumTicks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fair Value Gap.
        /// </summary>
        public static string FVGName {
            get {
                return ResourceManager.GetString("FVGName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Partially filled.
        /// </summary>
        public static string FVGPartiallyFilled {
            get {
                return ResourceManager.GetString("FVGPartiallyFilled", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Down left.
        /// </summary>
        public static string GannFanDirection_DownLeft {
            get {
                return ResourceManager.GetString("GannFanDirection_DownLeft", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Down right.
        /// </summary>
        public static string GannFanDirection_DownRight {
            get {
                return ResourceManager.GetString("GannFanDirection_DownRight", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Up left.
        /// </summary>
        public static string GannFanDirection_UpLeft {
            get {
                return ResourceManager.GetString("GannFanDirection_UpLeft", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Up right.
        /// </summary>
        public static string GannFanDirection_UpRight {
            get {
                return ResourceManager.GetString("GannFanDirection_UpRight", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Authorize.
        /// </summary>
        public static string GuiAuthorize {
            get {
                return ResourceManager.GetString("GuiAuthorize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Color for doji bars.
        /// </summary>
        public static string GuiChartStyleDojiBrush {
            get {
                return ResourceManager.GetString("GuiChartStyleDojiBrush", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Text Position.
        /// </summary>
        public static string GuiPropertyNameTextPosition {
            get {
                return ResourceManager.GetString("GuiPropertyNameTextPosition", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Higher high.
        /// </summary>
        public static string HigherHigh {
            get {
                return ResourceManager.GetString("HigherHigh", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Higher low.
        /// </summary>
        public static string HigherLow {
            get {
                return ResourceManager.GetString("HigherLow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Currency.
        /// </summary>
        public static string HighlightVerticalRangeUnit_Currency {
            get {
                return ResourceManager.GetString("HighlightVerticalRangeUnit_Currency", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Percent.
        /// </summary>
        public static string HighlightVerticalRangeUnit_Percent {
            get {
                return ResourceManager.GetString("HighlightVerticalRangeUnit_Percent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pips.
        /// </summary>
        public static string HighlightVerticalRangeUnit_Pips {
            get {
                return ResourceManager.GetString("HighlightVerticalRangeUnit_Pips", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Price.
        /// </summary>
        public static string HighlightVerticalRangeUnit_Price {
            get {
                return ResourceManager.GetString("HighlightVerticalRangeUnit_Price", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ticks.
        /// </summary>
        public static string HighlightVerticalRangeUnit_Ticks {
            get {
                return ResourceManager.GetString("HighlightVerticalRangeUnit_Ticks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HLC calculation mode.
        /// </summary>
        public static string HLCCalculationMode {
            get {
                return ResourceManager.GetString("HLCCalculationMode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Calculated from intraday data.
        /// </summary>
        public static string HLCCalculationMode_CalcFromIntradayData {
            get {
                return ResourceManager.GetString("HLCCalculationMode_CalcFromIntradayData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Use daily bars.
        /// </summary>
        public static string HLCCalculationMode_DailyBars {
            get {
                return ResourceManager.GetString("HLCCalculationMode_DailyBars", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Use user defined values.
        /// </summary>
        public static string HLCCalculationMode_UserDefinedValues {
            get {
                return ResourceManager.GetString("HLCCalculationMode_UserDefinedValues", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Approach for calculation the prior day HLC values..
        /// </summary>
        public static string HLCCalculationModeDescription {
            get {
                return ResourceManager.GetString("HLCCalculationModeDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Import.
        /// </summary>
        public static string Import {
            get {
                return ResourceManager.GetString("Import", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NinjaTrader (beginning of bar timestamps).
        /// </summary>
        public static string ImportTypeNinjaTraderBeginningOfBar {
            get {
                return ResourceManager.GetString("ImportTypeNinjaTraderBeginningOfBar", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}: Date/Time format error in line {1}: {2}: &apos;{3}&apos;.
        /// </summary>
        public static string ImportTypeNinjaTraderDateTimeFormatError {
            get {
                return ResourceManager.GetString("ImportTypeNinjaTraderDateTimeFormatError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NinjaTrader (end of bar timestamps).
        /// </summary>
        public static string ImportTypeNinjaTraderEndOfBar {
            get {
                return ResourceManager.GetString("ImportTypeNinjaTraderEndOfBar", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}: Import field separator could not be identified..
        /// </summary>
        public static string ImportTypeNinjaTraderFieldSeparatorNotIdentified {
            get {
                return ResourceManager.GetString("ImportTypeNinjaTraderFieldSeparatorNotIdentified", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}: Format error in line {1}: {2}: &apos;{3}&apos;.
        /// </summary>
        public static string ImportTypeNinjaTraderFormatError {
            get {
                return ResourceManager.GetString("ImportTypeNinjaTraderFormatError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unable to import file &apos;{0}&apos;. Instrument is not supported by repository..
        /// </summary>
        public static string ImportTypeNinjaTraderInstrumentNotSupported {
            get {
                return ResourceManager.GetString("ImportTypeNinjaTraderInstrumentNotSupported", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}: Numeric price format not supported..
        /// </summary>
        public static string ImportTypeNinjaTraderNumericPriceFormatError {
            get {
                return ResourceManager.GetString("ImportTypeNinjaTraderNumericPriceFormatError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unable to read data from file &apos;{0}&apos;: {1}.
        /// </summary>
        public static string ImportTypeNinjaTraderUnableReadData {
            get {
                return ResourceManager.GetString("ImportTypeNinjaTraderUnableReadData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}: Unexpected number of fields in line &apos;{1}&apos;, should be 3, 5 or 6.
        /// </summary>
        public static string ImportTypeNinjaTraderUnexpectedFieldNumber {
            get {
                return ResourceManager.GetString("ImportTypeNinjaTraderUnexpectedFieldNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tick Data, LLC.
        /// </summary>
        public static string ImportTypeTickData {
            get {
                return ResourceManager.GetString("ImportTypeTickData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Incremental period.
        /// </summary>
        public static string IncrementalPeriod {
            get {
                return ResourceManager.GetString("IncrementalPeriod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Intermediate.
        /// </summary>
        public static string Intermediate {
            get {
                return ResourceManager.GetString("Intermediate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Interval.
        /// </summary>
        public static string Interval {
            get {
                return ResourceManager.GetString("Interval", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Midline.
        /// </summary>
        public static string KeltnerChannelMidline {
            get {
                return ResourceManager.GetString("KeltnerChannelMidline", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Plot 0.
        /// </summary>
        public static string KeyReversalPlot0 {
            get {
                return ResourceManager.GetString("KeyReversalPlot0", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last line length (% of chart).
        /// </summary>
        public static string LastLineLength {
            get {
                return ResourceManager.GetString("LastLineLength", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last line.
        /// </summary>
        public static string LastLineStroke {
            get {
                return ResourceManager.GetString("LastLineStroke", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Legend location.
        /// </summary>
        public static string LegendLocation {
            get {
                return ResourceManager.GetString("LegendLocation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bottom left.
        /// </summary>
        public static string LegendLocation_BottomLeft {
            get {
                return ResourceManager.GetString("LegendLocation_BottomLeft", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bottom right.
        /// </summary>
        public static string LegendLocation_BottomRight {
            get {
                return ResourceManager.GetString("LegendLocation_BottomRight", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Disabled.
        /// </summary>
        public static string LegendLocation_Disabled {
            get {
                return ResourceManager.GetString("LegendLocation_Disabled", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Top left.
        /// </summary>
        public static string LegendLocation_TopLeft {
            get {
                return ResourceManager.GetString("LegendLocation_TopLeft", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Top right.
        /// </summary>
        public static string LegendLocation_TopRight {
            get {
                return ResourceManager.GetString("LegendLocation_TopRight", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Length.
        /// </summary>
        public static string Length {
            get {
                return ResourceManager.GetString("Length", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Line 1 value.
        /// </summary>
        public static string Line1Value {
            get {
                return ResourceManager.GetString("Line1Value", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Line 2 value.
        /// </summary>
        public static string Line2Value {
            get {
                return ResourceManager.GetString("Line2Value", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Line 3 value.
        /// </summary>
        public static string Line3Value {
            get {
                return ResourceManager.GetString("Line3Value", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Line 4 value.
        /// </summary>
        public static string Line4Value {
            get {
                return ResourceManager.GetString("Line4Value", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Line color.
        /// </summary>
        public static string LineColor {
            get {
                return ResourceManager.GetString("LineColor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Load.
        /// </summary>
        public static string Load {
            get {
                return ResourceManager.GetString("Load", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Location.
        /// </summary>
        public static string Location {
            get {
                return ResourceManager.GetString("Location", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lower high.
        /// </summary>
        public static string LowerHigh {
            get {
                return ResourceManager.GetString("LowerHigh", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lower low.
        /// </summary>
        public static string LowerLow {
            get {
                return ResourceManager.GetString("LowerLow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CC:.
        /// </summary>
        public static string MailCcAddress {
            get {
                return ResourceManager.GetString("MailCcAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The email address of your carbon copy recipient. Separate multiple addresses with &apos;,&apos; or &apos;;&apos;.
        /// </summary>
        public static string MailCcAddressDescription {
            get {
                return ResourceManager.GetString("MailCcAddressDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email address.
        /// </summary>
        public static string MailServiceMailAddress {
            get {
                return ResourceManager.GetString("MailServiceMailAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email.
        /// </summary>
        public static string MailServiceName {
            get {
                return ResourceManager.GetString("MailServiceName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Connection - Port.
        /// </summary>
        public static string MailServicePort {
            get {
                return ResourceManager.GetString("MailServicePort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to From name.
        /// </summary>
        public static string MailServiceSenderDisplayName {
            get {
                return ResourceManager.GetString("MailServiceSenderDisplayName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Connection - Server.
        /// </summary>
        public static string MailServiceServer {
            get {
                return ResourceManager.GetString("MailServiceServer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Connection - SSL.
        /// </summary>
        public static string MailServiceSSL {
            get {
                return ResourceManager.GetString("MailServiceSSL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Subject:.
        /// </summary>
        public static string MailSubject {
            get {
                return ResourceManager.GetString("MailSubject", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The subject of your email message.
        /// </summary>
        public static string MailSubjectDescription {
            get {
                return ResourceManager.GetString("MailSubjectDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to To:.
        /// </summary>
        public static string MailToAddress {
            get {
                return ResourceManager.GetString("MailToAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The email address of your recipient. Separate multiple addresses with &apos;,&apos; or &apos;;&apos;.
        /// </summary>
        public static string MailToAddressDescription {
            get {
                return ResourceManager.GetString("MailToAddressDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FAMA.
        /// </summary>
        public static string MAMAFAMA {
            get {
                return ResourceManager.GetString("MAMAFAMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Moving average period.
        /// </summary>
        public static string MAPeriod {
            get {
                return ResourceManager.GetString("MAPeriod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Moving average type.
        /// </summary>
        public static string MAType {
            get {
                return ResourceManager.GetString("MAType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Moving average.
        /// </summary>
        public static string MovingAverage {
            get {
                return ResourceManager.GetString("MovingAverage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Moving average 1.
        /// </summary>
        public static string MovingAverageRibbonPlot1 {
            get {
                return ResourceManager.GetString("MovingAverageRibbonPlot1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Moving average 2.
        /// </summary>
        public static string MovingAverageRibbonPlot2 {
            get {
                return ResourceManager.GetString("MovingAverageRibbonPlot2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Moving average 3.
        /// </summary>
        public static string MovingAverageRibbonPlot3 {
            get {
                return ResourceManager.GetString("MovingAverageRibbonPlot3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Moving average 4.
        /// </summary>
        public static string MovingAverageRibbonPlot4 {
            get {
                return ResourceManager.GetString("MovingAverageRibbonPlot4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Moving average 5.
        /// </summary>
        public static string MovingAverageRibbonPlot5 {
            get {
                return ResourceManager.GetString("MovingAverageRibbonPlot5", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Moving average 6.
        /// </summary>
        public static string MovingAverageRibbonPlot6 {
            get {
                return ResourceManager.GetString("MovingAverageRibbonPlot6", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Moving average 7.
        /// </summary>
        public static string MovingAverageRibbonPlot7 {
            get {
                return ResourceManager.GetString("MovingAverageRibbonPlot7", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Moving average 8.
        /// </summary>
        public static string MovingAverageRibbonPlot8 {
            get {
                return ResourceManager.GetString("MovingAverageRibbonPlot8", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Trigger.
        /// </summary>
        public static string NBarsDownTrigger {
            get {
                return ResourceManager.GetString("NBarsDownTrigger", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Negative color.
        /// </summary>
        public static string NegativeColor {
            get {
                return ResourceManager.GetString("NegativeColor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bottom left.
        /// </summary>
        public static string NetChangePosition_BottomLeft {
            get {
                return ResourceManager.GetString("NetChangePosition_BottomLeft", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bottom right.
        /// </summary>
        public static string NetChangePosition_BottomRight {
            get {
                return ResourceManager.GetString("NetChangePosition_BottomRight", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Top left.
        /// </summary>
        public static string NetChangePosition_TopLeft {
            get {
                return ResourceManager.GetString("NetChangePosition_TopLeft", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Top right.
        /// </summary>
        public static string NetChangePosition_TopRight {
            get {
                return ResourceManager.GetString("NetChangePosition_TopRight", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Background.
        /// </summary>
        public static string NinjaScriptBackground {
            get {
                return ResourceManager.GetString("NinjaScriptBackground", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Day.
        /// </summary>
        public static string NinjaScriptBarsTypeDay {
            get {
                return ResourceManager.GetString("NinjaScriptBarsTypeDay", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Heiken Ashi.
        /// </summary>
        public static string NinjaScriptBarsTypeHeikenAshi {
            get {
                return ResourceManager.GetString("NinjaScriptBarsTypeHeikenAshi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kagi.
        /// </summary>
        public static string NinjaScriptBarsTypeKagi {
            get {
                return ResourceManager.GetString("NinjaScriptBarsTypeKagi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reversal.
        /// </summary>
        public static string NinjaScriptBarsTypeKagiReversal {
            get {
                return ResourceManager.GetString("NinjaScriptBarsTypeKagiReversal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Line Break.
        /// </summary>
        public static string NinjaScriptBarsTypeLineBreak {
            get {
                return ResourceManager.GetString("NinjaScriptBarsTypeLineBreak", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Line breaks.
        /// </summary>
        public static string NinjaScriptBarsTypeLineBreakLineBreaks {
            get {
                return ResourceManager.GetString("NinjaScriptBarsTypeLineBreakLineBreaks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Minute.
        /// </summary>
        public static string NinjaScriptBarsTypeMinute {
            get {
                return ResourceManager.GetString("NinjaScriptBarsTypeMinute", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Month.
        /// </summary>
        public static string NinjaScriptBarsTypeMonth {
            get {
                return ResourceManager.GetString("NinjaScriptBarsTypeMonth", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Point and Figure.
        /// </summary>
        public static string NinjaScriptBarsTypePointAndFigure {
            get {
                return ResourceManager.GetString("NinjaScriptBarsTypePointAndFigure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Box size.
        /// </summary>
        public static string NinjaScriptBarsTypePointAndFigureBoxSize {
            get {
                return ResourceManager.GetString("NinjaScriptBarsTypePointAndFigureBoxSize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reversal.
        /// </summary>
        public static string NinjaScriptBarsTypePointAndFigureReversal {
            get {
                return ResourceManager.GetString("NinjaScriptBarsTypePointAndFigureReversal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Range.
        /// </summary>
        public static string NinjaScriptBarsTypeRange {
            get {
                return ResourceManager.GetString("NinjaScriptBarsTypeRange", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Renko.
        /// </summary>
        public static string NinjaScriptBarsTypeRenko {
            get {
                return ResourceManager.GetString("NinjaScriptBarsTypeRenko", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Brick size.
        /// </summary>
        public static string NinjaScriptBarsTypeRenkoBrickSize {
            get {
                return ResourceManager.GetString("NinjaScriptBarsTypeRenkoBrickSize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Second.
        /// </summary>
        public static string NinjaScriptBarsTypeSecond {
            get {
                return ResourceManager.GetString("NinjaScriptBarsTypeSecond", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tick.
        /// </summary>
        public static string NinjaScriptBarsTypeTick {
            get {
                return ResourceManager.GetString("NinjaScriptBarsTypeTick", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Volume.
        /// </summary>
        public static string NinjaScriptBarsTypeVolume {
            get {
                return ResourceManager.GetString("NinjaScriptBarsTypeVolume", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Week.
        /// </summary>
        public static string NinjaScriptBarsTypeWeek {
            get {
                return ResourceManager.GetString("NinjaScriptBarsTypeWeek", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Border.
        /// </summary>
        public static string NinjaScriptBorder {
            get {
                return ResourceManager.GetString("NinjaScriptBorder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bar width.
        /// </summary>
        public static string NinjaScriptChartStyleBarWidth {
            get {
                return ResourceManager.GetString("NinjaScriptChartStyleBarWidth", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Box.
        /// </summary>
        public static string NinjaScriptChartStyleBox {
            get {
                return ResourceManager.GetString("NinjaScriptChartStyleBox", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Color for down bars.
        /// </summary>
        public static string NinjaScriptChartStyleBoxDownBarsColor {
            get {
                return ResourceManager.GetString("NinjaScriptChartStyleBoxDownBarsColor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Down bars outline.
        /// </summary>
        public static string NinjaScriptChartStyleBoxDownBarsOutline {
            get {
                return ResourceManager.GetString("NinjaScriptChartStyleBoxDownBarsOutline", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Color for up bars.
        /// </summary>
        public static string NinjaScriptChartStyleBoxUpBarsColor {
            get {
                return ResourceManager.GetString("NinjaScriptChartStyleBoxUpBarsColor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Up bars outline.
        /// </summary>
        public static string NinjaScriptChartStyleBoxUpBarsOutline {
            get {
                return ResourceManager.GetString("NinjaScriptChartStyleBoxUpBarsOutline", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Color for down bars.
        /// </summary>
        public static string NinjaScriptChartStyleCandleDownBarsColor {
            get {
                return ResourceManager.GetString("NinjaScriptChartStyleCandleDownBarsColor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Candle body outline.
        /// </summary>
        public static string NinjaScriptChartStyleCandleOutline {
            get {
                return ResourceManager.GetString("NinjaScriptChartStyleCandleOutline", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Candlestick.
        /// </summary>
        public static string NinjaScriptChartStyleCandlestick {
            get {
                return ResourceManager.GetString("NinjaScriptChartStyleCandlestick", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hollow candlestick.
        /// </summary>
        public static string NinjaScriptChartStyleCandlestickHollow {
            get {
                return ResourceManager.GetString("NinjaScriptChartStyleCandlestickHollow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Color for up bars.
        /// </summary>
        public static string NinjaScriptChartStyleCandleUpBarsColor {
            get {
                return ResourceManager.GetString("NinjaScriptChartStyleCandleUpBarsColor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Candle wick.
        /// </summary>
        public static string NinjaScriptChartStyleCandleWick {
            get {
                return ResourceManager.GetString("NinjaScriptChartStyleCandleWick", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Equivolume.
        /// </summary>
        public static string NinjaScriptChartStyleEquivolume {
            get {
                return ResourceManager.GetString("NinjaScriptChartStyleEquivolume", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Heiken Ashi.
        /// </summary>
        public static string NinjaScriptChartStyleHeikenAshi {
            get {
                return ResourceManager.GetString("NinjaScriptChartStyleHeikenAshi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kagi Line.
        /// </summary>
        public static string NinjaScriptChartStyleKagi {
            get {
                return ResourceManager.GetString("NinjaScriptChartStyleKagi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thick line.
        /// </summary>
        public static string NinjaScriptChartStyleKagiThickLine {
            get {
                return ResourceManager.GetString("NinjaScriptChartStyleKagiThickLine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thin line.
        /// </summary>
        public static string NinjaScriptChartStyleKagiThinLine {
            get {
                return ResourceManager.GetString("NinjaScriptChartStyleKagiThinLine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Line on Close.
        /// </summary>
        public static string NinjaScriptChartStyleLineOnClose {
            get {
                return ResourceManager.GetString("NinjaScriptChartStyleLineOnClose", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Color.
        /// </summary>
        public static string NinjaScriptChartStyleLineOnCloseColor {
            get {
                return ResourceManager.GetString("NinjaScriptChartStyleLineOnCloseColor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Line width.
        /// </summary>
        public static string NinjaScriptChartStyleLineOnCloseWidth {
            get {
                return ResourceManager.GetString("NinjaScriptChartStyleLineOnCloseWidth", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Line width.
        /// </summary>
        public static string NinjaScriptChartStyleLineWidth {
            get {
                return ResourceManager.GetString("NinjaScriptChartStyleLineWidth", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mountain.
        /// </summary>
        public static string NinjaScriptChartStyleMountain {
            get {
                return ResourceManager.GetString("NinjaScriptChartStyleMountain", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Color.
        /// </summary>
        public static string NinjaScriptChartStyleMountainColor {
            get {
                return ResourceManager.GetString("NinjaScriptChartStyleMountainColor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Outline.
        /// </summary>
        public static string NinjaScriptChartStyleMountainOutline {
            get {
                return ResourceManager.GetString("NinjaScriptChartStyleMountainOutline", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to OHLC.
        /// </summary>
        public static string NinjaScriptChartStyleOHLC {
            get {
                return ResourceManager.GetString("NinjaScriptChartStyleOHLC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Color for down bars.
        /// </summary>
        public static string NinjaScriptChartStyleOhlcDownBarsColor {
            get {
                return ResourceManager.GetString("NinjaScriptChartStyleOhlcDownBarsColor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Color for up bars.
        /// </summary>
        public static string NinjaScriptChartStyleOhlcUpBarsColor {
            get {
                return ResourceManager.GetString("NinjaScriptChartStyleOhlcUpBarsColor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open/Close.
        /// </summary>
        public static string NinjaScriptChartStyleOpenClose {
            get {
                return ResourceManager.GetString("NinjaScriptChartStyleOpenClose", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Color for down bars.
        /// </summary>
        public static string NinjaScriptChartStyleOpenCloseDownBarsColor {
            get {
                return ResourceManager.GetString("NinjaScriptChartStyleOpenCloseDownBarsColor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Down bars outline.
        /// </summary>
        public static string NinjaScriptChartStyleOpenCloseDownBarsOutline {
            get {
                return ResourceManager.GetString("NinjaScriptChartStyleOpenCloseDownBarsOutline", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Color for up bars.
        /// </summary>
        public static string NinjaScriptChartStyleOpenCloseUpBarsColor {
            get {
                return ResourceManager.GetString("NinjaScriptChartStyleOpenCloseUpBarsColor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Up bars outline.
        /// </summary>
        public static string NinjaScriptChartStyleOpenCloseUpBarsOutline {
            get {
                return ResourceManager.GetString("NinjaScriptChartStyleOpenCloseUpBarsOutline", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Point and Figure.
        /// </summary>
        public static string NinjaScriptChartStylePointAndFigure {
            get {
                return ResourceManager.GetString("NinjaScriptChartStylePointAndFigure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Down color.
        /// </summary>
        public static string NinjaScriptChartStylePointAndFigureDownColor {
            get {
                return ResourceManager.GetString("NinjaScriptChartStylePointAndFigureDownColor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Up color.
        /// </summary>
        public static string NinjaScriptChartStylePointAndFigureUpColor {
            get {
                return ResourceManager.GetString("NinjaScriptChartStylePointAndFigureUpColor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Anchor.
        /// </summary>
        public static string NinjaScriptDrawingToolAnchor {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolAnchor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to End.
        /// </summary>
        public static string NinjaScriptDrawingToolAnchorEnd {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolAnchorEnd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Extension .
        /// </summary>
        public static string NinjaScriptDrawingToolAnchorExtension {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolAnchorExtension", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Middle.
        /// </summary>
        public static string NinjaScriptDrawingToolAnchorMiddle {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolAnchorMiddle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Start.
        /// </summary>
        public static string NinjaScriptDrawingToolAnchorStart {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolAnchorStart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Text.
        /// </summary>
        public static string NinjaScriptDrawingToolAnchorText {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolAnchorText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Andrews pitchfork.
        /// </summary>
        public static string NinjaScriptDrawingToolAndrewsPitchfork {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolAndrewsPitchfork", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Calculation method.
        /// </summary>
        public static string NinjaScriptDrawingToolAndrewsPitchforkCalculationMethod {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolAndrewsPitchforkCalculationMethod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Strokes.
        /// </summary>
        public static string NinjaScriptDrawingToolAndrewsPitchforkCategoryStrokes {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolAndrewsPitchforkCategoryStrokes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Andrews pitchfork description.
        /// </summary>
        public static string NinjaScriptDrawingToolAndrewsPitchforkDescription {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolAndrewsPitchforkDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Extend lines back.
        /// </summary>
        public static string NinjaScriptDrawingToolAndrewsPitchforkExtendLinesBack {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolAndrewsPitchforkExtendLinesBack", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Extension Line Stroke.
        /// </summary>
        public static string NinjaScriptDrawingToolAndrewsPitchforkExtensionStroke {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolAndrewsPitchforkExtensionStroke", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Retracement.
        /// </summary>
        public static string NinjaScriptDrawingToolAndrewsPitchforkRetracement {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolAndrewsPitchforkRetracement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Arc.
        /// </summary>
        public static string NinjaScriptDrawingToolArc {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolArc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Opacity - area (%).
        /// </summary>
        public static string NinjaScriptDrawingToolAreaOpacity {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolAreaOpacity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Arrow line.
        /// </summary>
        public static string NinjaScriptDrawingToolArrowLine {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolArrowLine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Background Opacity (%).
        /// </summary>
        public static string NinjaScriptDrawingToolBackgroundOpacity {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolBackgroundOpacity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ellipse.
        /// </summary>
        public static string NinjaScriptDrawingToolEllipse {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolEllipse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Extended line.
        /// </summary>
        public static string NinjaScriptDrawingToolExtendedLine {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolExtendedLine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fibonacci circle.
        /// </summary>
        public static string NinjaScriptDrawingToolFibonacciCircle {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolFibonacciCircle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fibonacci extensions.
        /// </summary>
        public static string NinjaScriptDrawingToolFibonacciExtensions {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolFibonacciExtensions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Anchor.
        /// </summary>
        public static string NinjaScriptDrawingToolFibonacciLevelsBaseAnchorLineStroke {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolFibonacciLevelsBaseAnchorLineStroke", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fibonacci retracements.
        /// </summary>
        public static string NinjaScriptDrawingToolFibonacciRetracements {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolFibonacciRetracements", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Extend lines left.
        /// </summary>
        public static string NinjaScriptDrawingToolFibonacciRetracementsExtendLinesLeft {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolFibonacciRetracementsExtendLinesLeft", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Extend lines right.
        /// </summary>
        public static string NinjaScriptDrawingToolFibonacciRetracementsExtendLinesRight {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolFibonacciRetracementsExtendLinesRight", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Text alignment.
        /// </summary>
        public static string NinjaScriptDrawingToolFibonacciRetracementsTextAlignment {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolFibonacciRetracementsTextAlignment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Text location.
        /// </summary>
        public static string NinjaScriptDrawingToolFibonacciRetracementsTextLocation {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolFibonacciRetracementsTextLocation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Divide time/price separately.
        /// </summary>
        public static string NinjaScriptDrawingToolFibonacciTimeCircleDivideTimeSeparately {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolFibonacciTimeCircleDivideTimeSeparately", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fibonacci time extensions.
        /// </summary>
        public static string NinjaScriptDrawingToolFibonacciTimeExtensions {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolFibonacciTimeExtensions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show text.
        /// </summary>
        public static string NinjaScriptDrawingToolFibonacciTimeExtensionsShowText {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolFibonacciTimeExtensionsShowText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gann fan.
        /// </summary>
        public static string NinjaScriptDrawingToolGannFan {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolGannFan", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Display text.
        /// </summary>
        public static string NinjaScriptDrawingToolGannFanDisplayText {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolGannFanDisplayText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fan direction.
        /// </summary>
        public static string NinjaScriptDrawingToolGannFanFanDirection {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolGannFanFanDirection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Points per bar.
        /// </summary>
        public static string NinjaScriptDrawingToolGannFanPointsPerBar {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolGannFanPointsPerBar", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Horizontal Line.
        /// </summary>
        public static string NinjaScriptDrawingToolHorizontalLine {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolHorizontalLine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Line.
        /// </summary>
        public static string NinjaScriptDrawingToolLine {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolLine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Path.
        /// </summary>
        public static string NinjaScriptDrawingToolPath {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolPath", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Path begin.
        /// </summary>
        public static string NinjaScriptDrawingToolPathBegin {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolPathBegin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Path end.
        /// </summary>
        public static string NinjaScriptDrawingToolPathEnd {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolPathEnd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Segment.
        /// </summary>
        public static string NinjaScriptDrawingToolPathSegment {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolPathSegment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show count.
        /// </summary>
        public static string NinjaScriptDrawingToolPathShowCount {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolPathShowCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Polygon.
        /// </summary>
        public static string NinjaScriptDrawingToolPolygon {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolPolygon", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Price Levels Opacity (%).
        /// </summary>
        public static string NinjaScriptDrawingToolPriceLevelsOpacity {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolPriceLevelsOpacity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Price marker.
        /// </summary>
        public static string NinjaScriptDrawingToolPriceMarker {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolPriceMarker", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ray.
        /// </summary>
        public static string NinjaScriptDrawingToolRay {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolRay", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rectangle.
        /// </summary>
        public static string NinjaScriptDrawingToolRectangle {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolRectangle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Region.
        /// </summary>
        public static string NinjaScriptDrawingToolRegion {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolRegion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Direction.
        /// </summary>
        public static string NinjaScriptDrawingToolRegionHighlightDirection {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolRegionHighlightDirection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Direction Stroke.
        /// </summary>
        public static string NinjaScriptDrawingToolRegionHighlightDirectionStroke {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolRegionHighlightDirectionStroke", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} Bars Time: {1}.
        /// </summary>
        public static string NinjaScriptDrawingToolRegionHighlightHorizontalTextFormat {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolRegionHighlightHorizontalTextFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vertical range unit.
        /// </summary>
        public static string NinjaScriptDrawingToolRegionHighlightVerticalRangeUnit {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolRegionHighlightVerticalRangeUnit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Range value: {0} {1}.
        /// </summary>
        public static string NinjaScriptDrawingToolRegionHighlightVerticalTextFormat {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolRegionHighlightVerticalTextFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Region highlight x.
        /// </summary>
        public static string NinjaScriptDrawingToolRegionHiglightX {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolRegionHiglightX", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Region highlight y.
        /// </summary>
        public static string NinjaScriptDrawingToolRegionHiglightY {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolRegionHiglightY", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Regression channel.
        /// </summary>
        public static string NinjaScriptDrawingToolRegressionChannel {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolRegressionChannel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lower channel.
        /// </summary>
        public static string NinjaScriptDrawingToolRegressionChannelLowerChannel {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolRegressionChannelLowerChannel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lower Channel Color.
        /// </summary>
        public static string NinjaScriptDrawingToolRegressionChannelLowerChannelColor {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolRegressionChannelLowerChannelColor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Price type.
        /// </summary>
        public static string NinjaScriptDrawingToolRegressionChannelPriceType {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolRegressionChannelPriceType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Regression.
        /// </summary>
        public static string NinjaScriptDrawingToolRegressionChannelRegressionChannel {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolRegressionChannelRegressionChannel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Extend left.
        /// </summary>
        public static string NinjaScriptDrawingToolRegressionChannelStandardDeviationExtendLeft {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolRegressionChannelStandardDeviationExtendLeft", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Extend right.
        /// </summary>
        public static string NinjaScriptDrawingToolRegressionChannelStandardDeviationExtendRight {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolRegressionChannelStandardDeviationExtendRight", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Distance to lower channel.
        /// </summary>
        public static string NinjaScriptDrawingToolRegressionChannelStandardDeviationLowerDistance {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolRegressionChannelStandardDeviationLowerDistance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Distance to upper channel.
        /// </summary>
        public static string NinjaScriptDrawingToolRegressionChannelStandardDeviationUpperDistance {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolRegressionChannelStandardDeviationUpperDistance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mode.
        /// </summary>
        public static string NinjaScriptDrawingToolRegressionChannelType {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolRegressionChannelType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Upper channel.
        /// </summary>
        public static string NinjaScriptDrawingToolRegressionChannelUpperChannel {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolRegressionChannelUpperChannel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Upper channel color.
        /// </summary>
        public static string NinjaScriptDrawingToolRegressionChannelUpperChannelColor {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolRegressionChannelUpperChannelColor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Entry anchor.
        /// </summary>
        public static string NinjaScriptDrawingToolRiskRewardAnchorEntry {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolRiskRewardAnchorEntry", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Anchor.
        /// </summary>
        public static string NinjaScriptDrawingToolRiskRewardAnchorLineStroke {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolRiskRewardAnchorLineStroke", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reward anchor.
        /// </summary>
        public static string NinjaScriptDrawingToolRiskRewardAnchorReward {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolRiskRewardAnchorReward", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Risk anchor.
        /// </summary>
        public static string NinjaScriptDrawingToolRiskRewardAnchorRisk {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolRiskRewardAnchorRisk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Colors.
        /// </summary>
        public static string NinjaScriptDrawingToolRiskRewardCategoryColors {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolRiskRewardCategoryColors", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Automatically calculate your target based off a user defined stop loss.
        /// </summary>
        public static string NinjaScriptDrawingToolRiskRewardDescription {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolRiskRewardDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Entry extension.
        /// </summary>
        public static string NinjaScriptDrawingToolRiskRewardLineStrokeEntry {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolRiskRewardLineStrokeEntry", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reward extension.
        /// </summary>
        public static string NinjaScriptDrawingToolRiskRewardLineStrokeReward {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolRiskRewardLineStrokeReward", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Risk extension.
        /// </summary>
        public static string NinjaScriptDrawingToolRiskRewardLineStrokeRisk {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolRiskRewardLineStrokeRisk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Risk Reward.
        /// </summary>
        public static string NinjaScriptDrawingToolRiskRewardName {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolRiskRewardName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ratio.
        /// </summary>
        public static string NinjaScriptDrawingToolRiskRewardRatio {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolRiskRewardRatio", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ruler.
        /// </summary>
        public static string NinjaScriptDrawingToolRuler {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolRuler", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} days.
        /// </summary>
        public static string NinjaScriptDrawingToolRulerDaysFormat {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolRulerDaysFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to # bars:.
        /// </summary>
        public static string NinjaScriptDrawingToolRulerNumberBarsText {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolRulerNumberBarsText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Time:.
        /// </summary>
        public static string NinjaScriptDrawingToolRulerTimeText {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolRulerTimeText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Y value display unit.
        /// </summary>
        public static string NinjaScriptDrawingToolRulerYValueDisplayUnit {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolRulerYValueDisplayUnit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Y value:.
        /// </summary>
        public static string NinjaScriptDrawingToolRulerYValueText {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolRulerYValueText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Drawing tools.
        /// </summary>
        public static string NinjaScriptDrawingTools {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingTools", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Arrow down.
        /// </summary>
        public static string NinjaScriptDrawingToolsChartArrowDownMarkerName {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolsChartArrowDownMarkerName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Arrow up.
        /// </summary>
        public static string NinjaScriptDrawingToolsChartArrowUpMarkerName {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolsChartArrowUpMarkerName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Diamond.
        /// </summary>
        public static string NinjaScriptDrawingToolsChartDiamondMarkerName {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolsChartDiamondMarkerName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dot.
        /// </summary>
        public static string NinjaScriptDrawingToolsChartDotMarkerName {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolsChartDotMarkerName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Square.
        /// </summary>
        public static string NinjaScriptDrawingToolsChartSquareMarkerName {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolsChartSquareMarkerName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Triangle down.
        /// </summary>
        public static string NinjaScriptDrawingToolsChartTriangleDownMarkerName {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolsChartTriangleDownMarkerName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Triangle up.
        /// </summary>
        public static string NinjaScriptDrawingToolsChartTriangleUpMarkerName {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolsChartTriangleUpMarkerName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ratio time.
        /// </summary>
        public static string NinjaScriptDrawingToolsGannAngleRatioX {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolsGannAngleRatioX", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ratio price.
        /// </summary>
        public static string NinjaScriptDrawingToolsGannAngleRatioY {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolsGannAngleRatioY", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gann angles.
        /// </summary>
        public static string NinjaScriptDrawingToolsGannAngles {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolsGannAngles", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 1 Gann angle|{0} Gann angles|Add Gann angle..|Edit Gann angle...|Edit Gann angles....
        /// </summary>
        public static string NinjaScriptDrawingToolsGannAnglesPrompt {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolsGannAnglesPrompt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Color - area.
        /// </summary>
        public static string NinjaScriptDrawingToolShapesAreaBrush {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolShapesAreaBrush", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Color - outline.
        /// </summary>
        public static string NinjaScriptDrawingToolShapesOutlineBrush {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolShapesOutlineBrush", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Visible.
        /// </summary>
        public static string NinjaScriptDrawingToolsPriceLevelIsVisible {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolsPriceLevelIsVisible", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Line.
        /// </summary>
        public static string NinjaScriptDrawingToolsPriceLevelLineStroke {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolsPriceLevelLineStroke", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Levels.
        /// </summary>
        public static string NinjaScriptDrawingToolsPriceLevels {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolsPriceLevels", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 1 price level|{0} price levels|Add price level..|Edit price level...|Edit price levels....
        /// </summary>
        public static string NinjaScriptDrawingToolsPriceLevelsPrompt {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolsPriceLevelsPrompt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unset.
        /// </summary>
        public static string NinjaScriptDrawingToolsPriceLevelUnset {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolsPriceLevelUnset", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Value (%).
        /// </summary>
        public static string NinjaScriptDrawingToolsPriceLevelValue {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolsPriceLevelValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Line.
        /// </summary>
        public static string NinjaScriptDrawingToolStroke {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolStroke", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Text.
        /// </summary>
        public static string NinjaScriptDrawingToolText {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Text alignment.
        /// </summary>
        public static string NinjaScriptDrawingToolTextAlignment {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolTextAlignment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Text background brush.
        /// </summary>
        public static string NinjaScriptDrawingToolTextBackBrush {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolTextBackBrush", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Color - font.
        /// </summary>
        public static string NinjaScriptDrawingToolTextBrush {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolTextBrush", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fixed text.
        /// </summary>
        public static string NinjaScriptDrawingToolTextFixed {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolTextFixed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string NinjaScriptDrawingToolTextFixedTextPosition {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolTextFixedTextPosition", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Font.
        /// </summary>
        public static string NinjaScriptDrawingToolTextFont {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolTextFont", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Outline.
        /// </summary>
        public static string NinjaScriptDrawingToolTextOutlineStroke {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolTextOutlineStroke", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Outline - enabled.
        /// </summary>
        public static string NinjaScriptDrawingToolTextOutlineVisible {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolTextOutlineVisible", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Time Cycles.
        /// </summary>
        public static string NinjaScriptDrawingToolTimeCycles {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolTimeCycles", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Trend channel.
        /// </summary>
        public static string NinjaScriptDrawingToolTrendChannel {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolTrendChannel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Draws a trend channel using parallel lines.
        /// </summary>
        public static string NinjaScriptDrawingToolTrendChannelDescription {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolTrendChannelDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Trend end.
        /// </summary>
        public static string NinjaScriptDrawingToolTrendChannelEnd1AnchorDisplayName {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolTrendChannelEnd1AnchorDisplayName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Parallel.
        /// </summary>
        public static string NinjaScriptDrawingToolTrendChannelParallelStroke {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolTrendChannelParallelStroke", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Trend start.
        /// </summary>
        public static string NinjaScriptDrawingToolTrendChannelStart1AnchorDisplayName {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolTrendChannelStart1AnchorDisplayName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Parallel.
        /// </summary>
        public static string NinjaScriptDrawingToolTrendChannelStart2AnchorDisplayName {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolTrendChannelStart2AnchorDisplayName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Trend.
        /// </summary>
        public static string NinjaScriptDrawingToolTrendChannelTrendStroke {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolTrendChannelTrendStroke", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Triangle.
        /// </summary>
        public static string NinjaScriptDrawingToolTriangle {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolTriangle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vertical line.
        /// </summary>
        public static string NinjaScriptDrawingToolVerticalLine {
            get {
                return ResourceManager.GetString("NinjaScriptDrawingToolVerticalLine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to General.
        /// </summary>
        public static string NinjaScriptGeneral {
            get {
                return ResourceManager.GetString("NinjaScriptGeneral", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Average performance offset (%).
        /// </summary>
        public static string NinjaScriptGeneticOptimizerAveragePerformanceOffsetPercent {
            get {
                return ResourceManager.GetString("NinjaScriptGeneticOptimizerAveragePerformanceOffsetPercent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Convergence threshold.
        /// </summary>
        public static string NinjaScriptGeneticOptimizerConvergenceThreshold {
            get {
                return ResourceManager.GetString("NinjaScriptGeneticOptimizerConvergenceThreshold", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Crossover index.
        /// </summary>
        public static string NinjaScriptGeneticOptimizerCrossoverIndex {
            get {
                return ResourceManager.GetString("NinjaScriptGeneticOptimizerCrossoverIndex", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Crossover rate (%).
        /// </summary>
        public static string NinjaScriptGeneticOptimizerCrossoverRatePercent {
            get {
                return ResourceManager.GetString("NinjaScriptGeneticOptimizerCrossoverRatePercent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fast generations.
        /// </summary>
        public static string NinjaScriptGeneticOptimizerFastGenerations {
            get {
                return ResourceManager.GetString("NinjaScriptGeneticOptimizerFastGenerations", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Generations.
        /// </summary>
        public static string NinjaScriptGeneticOptimizerGenerations {
            get {
                return ResourceManager.GetString("NinjaScriptGeneticOptimizerGenerations", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Generation size.
        /// </summary>
        public static string NinjaScriptGeneticOptimizerGenerationSize {
            get {
                return ResourceManager.GetString("NinjaScriptGeneticOptimizerGenerationSize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Minimum performance.
        /// </summary>
        public static string NinjaScriptGeneticOptimizerMinimumPerformance {
            get {
                return ResourceManager.GetString("NinjaScriptGeneticOptimizerMinimumPerformance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mutation rate (%).
        /// </summary>
        public static string NinjaScriptGeneticOptimizerMutationRatePercent {
            get {
                return ResourceManager.GetString("NinjaScriptGeneticOptimizerMutationRatePercent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mutation strength (%).
        /// </summary>
        public static string NinjaScriptGeneticOptimizerMutationStrengthPercent {
            get {
                return ResourceManager.GetString("NinjaScriptGeneticOptimizerMutationStrengthPercent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reset size (%).
        /// </summary>
        public static string NinjaScriptGeneticOptimizerResetSizePercent {
            get {
                return ResourceManager.GetString("NinjaScriptGeneticOptimizerResetSizePercent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Slow generations.
        /// </summary>
        public static string NinjaScriptGeneticOptimizerSlowGenerations {
            get {
                return ResourceManager.GetString("NinjaScriptGeneticOptimizerSlowGenerations", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stability size (%).
        /// </summary>
        public static string NinjaScriptGeneticOptimizerStabilitySizePercent {
            get {
                return ResourceManager.GetString("NinjaScriptGeneticOptimizerStabilitySizePercent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Threshold generations.
        /// </summary>
        public static string NinjaScriptGeneticOptimizerThresholdGenerations {
            get {
                return ResourceManager.GetString("NinjaScriptGeneticOptimizerThresholdGenerations", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Indicator.
        /// </summary>
        public static string NinjaScriptIndicator {
            get {
                return ResourceManager.GetString("NinjaScriptIndicator", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Avg.
        /// </summary>
        public static string NinjaScriptIndicatorAvg {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorAvg", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Count.
        /// </summary>
        public static string NinjaScriptIndicatorCount {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default.
        /// </summary>
        public static string NinjaScriptIndicatorDefault {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDefault", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Accumulation/Distribution (AD) study attempts to quantify the amount of volume flowing into or out of an instrument by identifying the position of the close of the period in relation to that period&apos;s high/low range..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionADL {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionADL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Average Directional Index measures the strength of a prevailing trend as well as whether movement exists in the market. The ADX is measured on a scale of 0  100. A low ADX value (generally less than 20) can indicate a non-trending market with low volumes whereas a cross above 20 may indicate the start of a trend (either up or down). If the ADX is over 40 and begins to fall, it can indicate the slowdown of a current trend..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionADX {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionADX", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Average Directional Movement Rating quantifies momentum change in the ADX. It is calculated by adding two values of ADX (the current value and a value n periods back), then dividing by two. This additional smoothing makes the ADXR slightly less responsive than ADX. The interpretation is the same as the ADX; the higher the value, the stronger the trend..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionADXR {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionADXR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The APZ (Adaptive Prize Zone) forms a steady channel based on double smoothed exponential moving averages around the average price. See S/C, September 2006, p.28..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionAPZ {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionAPZ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Aroon Indicator was developed by Tushar Chande. It is comprised of two plots: one measuring the number of periods since the most recent x-period high (Aroon Up) and the other measuring the number of periods since the most recent x-period low (Aroon Down)..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionAroon {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionAroon", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Aroon Oscillator is based upon his Aroon Indicator. Much like the Aroon Indicator, the Aroon Oscillator measures the strength of a trend..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionAroonOscillator {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionAroonOscillator", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Average True Range (ATR) is a measure of volatility. It was introduced by Welles Wilder in his book &apos;New Concepts in Technical Trading Systems&apos; and has since been used as a component of many indicators and trading systems..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionATR {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionATR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Displays remaining time of the time based bar.
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionBarTimer {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionBarTimer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Block volume detects block trades and display how many occurred per bar. This can be displayed either as trades or volume. Historical tick data is required to plot historically..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionBlockVolume {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionBlockVolume", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bollinger Bands are plotted at standard deviation levels above and below a moving average. Since standard deviation is a measure of volatility, the bands are self-adjusting: widening during volatile markets and contracting during calmer periods..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionBollinger {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionBollinger", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The balance of power indicator measures the strength of the bulls vs. bears by assessing the ability of each to push price to an extreme level..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionBOP {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionBOP", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Indicates the current buying or selling pressure as a perecentage. This is a tick by tick indicator. If &apos;Calculate&apos; is set to &apos;On bar close&apos;, the indicator values will always be 100..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionBuySellPressure {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionBuySellPressure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Plots a histogram splitting volume between trades at the ask or higher and trades at the bid and lower.  Only works on historical data if using Tick Replay.
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionBuySellVolume {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionBuySellVolume", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Camarilla pivots are a price analysis too that generates potential support and resistance levels by multiplying the prior range then adding or subtracting it from the close..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionCamarillaPivots {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionCamarillaPivots", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Detects common candlestick patterns and marks them on the chart.
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionCandlestickPattern {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionCandlestickPattern", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Commodity Channel Index (CCI) measures the variation of a security&apos;s price from its statistical mean. High values show that prices are unusually high compared to average prices whereas low values indicate that prices are unusually low..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionCCI {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionCCI", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Calculates the amount of money flow volume over n bars..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionChaikinMoneyFlow {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionChaikinMoneyFlow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Calculates the momentum of the accumulation distribution line using the difference between two exponential moving averages..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionChaikinOscillator {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionChaikinOscillator", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Compares difference between an instruments current and historical range using exponential moving averages..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionChaikinVolatility {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionChaikinVolatility", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Choppiness Index is designed to determine if the market is choppy (trading sideways) or not choppy (trading within a trend in either direction).
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionChoppinessIndex {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionChoppinessIndex", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The CMO differs from other momentum oscillators such as Relative Strength Index (RSI) and Stochastics. It uses both up and down days data in the numerator of the calculation to measure momentum directly. Primarily used to look for extreme overbought and oversold conditions, CMO can also be used to look for trends..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionCMO {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionCMO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Plots lines at user defined values..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionConstantLines {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionConstantLines", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The correlation indicator will plot the correlation of the data series to a desired instrument. Values close to 1 indicate movement in the same direction. Values close to -1 indicate movement in opposite directions. Values near 0 indicate no correlation..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionCorrelation {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionCorrelation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The COT indicator plots weekly data from the Commitment Of Traders report, indicating holdings of different participants in the U.S. futures market..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionCOT {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionCOT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Plots the open, high, and low values from the session starting on the current day..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionCurrentDayOHL {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionCurrentDayOHL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Darvas Boxes were taken from the pages of Nicolas Darvas book, How I Made $2,000,000 in the Stock Market. The boxes are used to normalize a trend. A &apos;buy&apos; signal would be indicated when the price of the stock exceeds the top of the box. A &apos;sell&apos; signal would be indicated when the price of the stock falls below the bottom of the box..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionDarvas {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionDarvas", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Double Exponential Moving Average (DEMA) is a combination of a single exponential moving average and a double exponential moving average..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionDEMA {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionDEMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Disparity Index measures the difference between the price and an exponential moving average. A value greater could suggest bullish momentum, while a value less than zero could suggest bearish momentum..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionDisparityIndex {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionDisparityIndex", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Directional Movement (DM). This is the same indicator as the ADX, with the addition of the two directional movement indicators +DI and -DI. +DI and -DI measure upward and downward momentum. A buy signal is generated when +DI crosses -DI to the upside. A sell signal is generated when -DI crosses +DI to the downside..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionDM {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionDM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Directional Movement Index. Directional Movement Index is quite similiar to Welles Wilder&apos;s Relative Strength Index. The difference is the DMI uses variable time periods (from 3 to 30) vs. the RSI&apos;s fixed periods..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionDMI {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionDMI", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Dynamic Momentum Index is a variable term RSI. The RSI term varies from 3 to 30. The variable time period makes the RSI more responsive to short-term moves. The more volatile the price is, the shorter the time period is. It is interpreted in the same way as the RSI, but provides signals earlier..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionDMIndex {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionDMIndex", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Donchian Channel. The Donchian Channel indicator was created by Richard Donchian. It uses the highest high and the lowest low of a period of time to plot the channel..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionDonchianChannel {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionDonchianChannel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Double Stochastics is a variation of the Stochastics indicator developed by William Blau..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionDoubleStochastics {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionDoubleStochastics", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Ease of Movement (EMV) indicator emphasizes days in which the stock is moving easily and minimizes the days in which the stock is finding it difficult to move. A buy signal is generated when the EMV crosses above zero, a sell signal when it crosses below zero. When the EMV hovers around zero, then there are small price movements and/or high volume, which is to say, the price is not moving easily..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionEaseOfMovement {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionEaseOfMovement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Exponential Moving Average is an indicator that shows the average value of a security&apos;s price over a period of time. When calculating a moving average, the EMA applies more weight to recent prices than the SMA..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionEMA {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionEMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fibonacci pivots are a price analysis too that generates potential support and resistance levels by multiplying the prior range against Fibonacci values then adding or subtracting it from the average of the prior high, low, and close..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionFibonacciPivots {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionFibonacciPivots", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Fisher Transform has sharp and distinct turning points that occur in a timely fashion. The resulting peak swings are used to identify price reversals..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionFisherTransform {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionFisherTransform", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Forecast Oscillator (FOSC) is an extension of the linear regression based indicators made popular by Tushar Chande. The Forecast Oscillator plots the percentage difference between the forecast price (generated by an x-period linear regression line) and the actual price. The oscillator is above zero when the forecast price is greater than the actual price.  Conversely, it&apos;s less than zero if its below. In the rare case when the forecast price and the actual price are the same, the oscillator would plot z [rest of string was truncated]&quot;;.
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionFOSC {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionFOSC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Hull Moving Average (HMA) employs weighted MA calculations to offer superior smoothing, and much less lag, over traditional SMA indicators..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionHMA {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionHMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Ichimoku Cloud is a charting tool that shows potential support and resistance areas, trend direction, and momentum using a set of moving average-based lines and a shaded area called the &apos;cloud.&apos; It helps users observe how price interacts with these components over time..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionIchimokuCloud {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionIchimokuCloud", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Developed by Perry Kaufman, this indicator is an EMA using an Efficiency Ratio to modify the smoothing constant, which ranges from a minimum of Fast Length to a maximum of Slow Length. Since this moving average is adaptive it tends to follow prices more closely than other MA&apos;s..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionKAMA {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionKAMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Keltner Channel is a similar indicator to Bollinger Bands. Here the midline is a standard moving average with the upper and lower bands offset by the SMA of the difference between the high and low of the previous bars. The offset multiplier as well as the SMA period is configurable..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionKeltnerChannel {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionKeltnerChannel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Returns a value of 1 when the current close is less than the prior close after penetrating the highest high of the last n bars..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionKeyReversalDown {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionKeyReversalDown", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Returns a value of 1 when the current close is greater than the prior close after penetrating the lowest low of the last n bars..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionKeyReversalUp {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionKeyReversalUp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Linear Regression is an indicator that &apos;predicts&apos; the value of a security&apos;s price..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionLinReg {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionLinReg", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Linear Regression Intercept provides the intercept value of the Linear Regression trendline..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionLinRegIntercept {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionLinRegIntercept", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Linear Regression Slope provides the slope value of the Linear Regression trendline..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionLinRegSlope {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionLinRegSlope", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The MACD (Moving Average Convergence/Divergence) is a trend following momentum indicator that shows the relationship between two moving averages of prices..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionMACD {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionMACD", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Plots % envelopes around a moving average.
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionMAEnvelopes {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionMAEnvelopes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The MAMA (MESA Adaptive Moving Average) was developed by John Ehlers. It adapts to price movement in a new and unique way. The adaptation is based on the Hilbert Transform Discriminator. The advantage of this method features fast attack average and a slow decay average. The MAMA + the FAMA (Following Adaptive Moving Average) lines only cross at major market reversals..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionMAMA {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionMAMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Maximum shows the maximum of the last n bars..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionMAX {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionMAX", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to McClellan Oscillator is the difference between two exponential moving averages of the NYSE advance decline spread. This indicator require ADV and DECL index data..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionMcClellanOscillator {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionMcClellanOscillator", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The MFI (Money Flow Index) is a momentum indicator that measures the strength of money flowing in and out of a security..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionMFI {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionMFI", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Minimum shows the minimum of the last n bars..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionMIN {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionMIN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Momentum indicator measures the amount that a security&apos;s price has changed over a given time span..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionMomentum {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionMomentum", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Money Flow Oscillator measures the amount of money flow volume over a specific period. A move into positive territory indicates buying pressure while a move into negative territory indicates selling pressure..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionMoneyFlowOscillator {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionMoneyFlowOscillator", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Moving Average Ribbon is a series of incrementing moving averages..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionMovingAverageRibbon {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionMovingAverageRibbon", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This indicator returns 1 when we have n of consecutive bars down, otherwise returns 0. A down bar is defined as a bar where the close is below the open and the bars makes a lower high and a lower low. You can adjust the specific requirements with the indicator options..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionNBarsDown {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionNBarsDown", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This indicator returns 1 when we have n of consecutive bars up, otherwise returns 0. An up bar is defined as a bar where the close is above the open and the bars makes a higher high and a higher low. You can adjust the specific requirements with the indicator options..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionNBarsUp {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionNBarsUp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Displays net change on the chart..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionNetChangeDisplay {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionNetChangeDisplay", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to OBV (On Balance Volume) is a running total of volume. It shows if volume is flowing into or out of a security. When the security closes higher than the previous close, all of the day&apos;s volume is considered up-volume. When the security closes lower than the previous close, all of the day&apos;s volume is considered down-volume..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionOBV {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionOBV", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Parabolic SAR according to Stocks and Commodities magazine V 11:11 (477-479)..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionParabolicSAR {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionParabolicSAR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The PFE (Polarized Fractal Efficiency) is an indicator that uses fractal geometry to determine how efficiently the price is moving..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionPFE {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionPFE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Pivots (Pivot Points) indicator plots the averages of the High, Low, and Close of a prior session or group of prior sessions. This is based on the historical data as provided by your market data feed provider..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionPivots {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionPivots", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The PPO (Percentage Price Oscillator) is based on two moving averages expressed as a percentage. The PPO is found by subtracting the longer MA from the shorter MA and then dividing the difference by the longer MA..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionPPO {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionPPO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Displays ask, bid, and/or last lines on the chart..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionPriceLine {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionPriceLine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Price Oscillator indicator shows the variation among two moving averages for the price of a security..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionPriceOscillator {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionPriceOscillator", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Plots the open, high, low and close values from the session starting on the prior day..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionPriorDayOHLC {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionPriorDayOHLC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Psychological Line is the ratio of the number of rising bars over the specified number of bars..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionPsychologicalLine {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionPsychologicalLine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Calculates the range of a bar..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionRange {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionRange", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Displays the range count of a bar..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionRangeCounter {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionRangeCounter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Linear regression is used to calculate a best fit line for the price data. In addition an upper and lower band is added by calculating the standard deviation of prices from the regression line..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionRegressionChannel {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionRegressionChannel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Relative Vigor Index measures the strength of a trend by comparing an instruments closing price to its price range. It&apos;s based on the fact that prices tend to close higher than they open in up trends, and closer lower than they open in downtrends..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionRelativeVigorIndex {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionRelativeVigorIndex", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to RIND (Range Indicator) compares the intraday range (high - low) to the inter-day (close - previous close) range. When the intraday range is greater than the inter-day range, the Range Indicator will be a high value. This signals an end to the current trend. When the Range Indicator is at a low level, a new trend is about to start..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionRIND {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionRIND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The ROC (Rate-of-Change) indicator displays the percent change between the current price and the price x-time periods ago..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionROC {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionROC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The RSI (Relative Strength Index) is a price-following oscillator that ranges between 0 and 100..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionRSI {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionRSI", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The R-Squared indicator calculates how well the price approximates a linear regression line. The indicator gets its name from the calculation, which is, the square of the correlation coefficient (referred to in mathematics by the Greek letter rho, or r). The range of the R-Squared is from zero to one..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionRSquared {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionRSquared", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Relative Spread Strength of the spread between two moving averages. TASC, October 2006, p. 16..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionRSS {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionRSS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The RVI (Relative Volatility Index) was developed by Donald Dorsey as a compliment to and a confirmation of momentum based indicators. When used to confirm other signals, only buy when the RVI is over 50 and only sell when the RVI is under 50..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionRVI {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionRVI", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sample script to show OnRender() capabilities.
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionSampleCustomRender {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionSampleCustomRender", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The SMA (Simple Moving Average) is an indicator that shows the average value of a security&apos;s price over a period of time..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionSMA {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionSMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Standard Deviation is a statistical measure of volatility. Standard Deviation is typically used as a component of other indicators, rather than as a stand-alone indicator. For example, Bollinger Bands are calculated by adding a security&apos;s Standard Deviation to a moving average..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionStdDev {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionStdDev", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Standard Error shows how near prices go around a linear regression line.  The closer the prices are to the linear regression line, the stronger is the trend..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionStdError {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionStdError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Stochastic Oscillator is made up of two lines that oscillate between a vertical scale of 0 to 100. The %K is the main line and it is drawn as a solid line. The second is the %D line and is a moving average of %K. The %D line is drawn as a dotted line. Use as a buy/sell signal generator, buying when fast moves above slow and selling when fast moves below slow..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionStochastics {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionStochastics", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Stochastic Oscillator is made up of two lines that oscillate between a vertical scale of 0 to 100. The %K is the main line and it is drawn as a solid line. The second is the %D line and is a moving average of %K. The %D line is drawn as a dotted line. Use as a buy/sell signal generator, buying when fast moves above slow and selling when fast moves below slow..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionStochasticsFast {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionStochasticsFast", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The StochRSI is an oscillator similar in computation to the stochastic measure, except instead of price values as input, the StochRSI uses RSI values. The StochRSI computes the current position of the RSI relative to the high and low RSI values over a specified number of days. The intent of this measure, designed by Tushar Chande and Stanley Kroll, is to provide further information about the overbought/oversold nature of the RSI. The StochRSI ranges between 0.0 and 1.0. Values above 0.8 are generally seen t [rest of string was truncated]&quot;;.
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionStochRSI {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionStochRSI", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Sum shows the summation of the last n data points..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionSUM {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionSUM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Swing indicator plots lines that represents the swing high and low points..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionSwing {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionSwing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The T3 is a type of moving average, or smoothing function. It is based on the DEMA. The T3 takes the DEMA calculation and adds a vfactor which is between zero and 1. The resultant function is called the GD, or Generalized DEMA. A GD with vfactor of 1 is the same as the DEMA. A GD with a vfactor of zero is the same as an Exponential Moving Average. The T3 typically uses a vfactor of 0.7..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionT3 {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionT3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The TEMA is a smoothing indicator. It was designed by Patrick Mulloy and is described in his article in the January, 1994 issue of Technical Analysis of Stocks and Commodities magazine..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionTEMA {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionTEMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Displays tick count of a bar.
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionTickCounter {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionTickCounter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The TMA (Triangular Moving Average) is a weighted moving average. Compared to the WMA which puts more weight on the latest price bar, the TMA puts more weight on the data in the middle of the specified period..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionTMA {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionTMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to When a high swing is followed by a lower high swing, a trend line high is automatically plotted. When a low swing is followed by a higher low swing, a trend line low is automatically plotted..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionTrendLines {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionTrendLines", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The TRIX (Triple Exponential Average) displays the percentage Rate of Change (ROC) of a triple EMA. Trix oscillates above and below the zero value. The indicator applies triple smoothing in an attempt to eliminate insignificant price movements within the trend that you&apos;re trying to isolate..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionTRIX {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionTRIX", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The TSF (Time Series Forecast) calculates probable future values for the price by fitting a linear regression line over a given number of price bars and following that line forward into the future. A linear regression line is a straight line which is as close to all of the given price points as possible. Also see the Linear Regression indicator..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionTSF {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionTSF", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The TSI (True Strength Index) is a momentum-based indicator, developed by William Blau. Designed to determine both trend and overbought/oversold conditions, the TSI is applicable to intraday time frames as well as long term trading..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionTSI {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionTSI", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Ultimate Oscillator is the weighted sum of three oscillators of different time periods. The typical time periods are 7, 14 and 28. The values of the Ultimate Oscillator range from zero to 100. Values over 70 indicate overbought conditions, and values under 30 indicate oversold conditions. Also look for agreement/divergence with the price to confirm a trend or signal the end of a trend..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionUltimateOscillator {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionUltimateOscillator", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The VMA (Variable Moving Average, also known as VIDYA or Variable Index Dynamic Average) is an exponential moving average that automatically adjusts the smoothing weight based on the volatility of the data series. VMA solves a problem with most moving averages. In times of low volatility, such as when the price is trending, the moving average time period should be shorter to be sensitive to the inevitable break in the trend. Whereas, in more volatile non-trending times, the moving average time period should [rest of string was truncated]&quot;;.
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionVMA {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionVMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Volume is simply the number of shares (or contracts) traded during a specified time frame (e.g. hour, day, week, month, etc)..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionVOL {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionVOL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The VOLMA (Volume Moving Average) plots an exponential moving average (EMA) of volume..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionVOLMA {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionVOLMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Displays the volume count of each bar.
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionVolumeCounter {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionVolumeCounter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Volume Oscillator measures volume by calculating the difference of a fast and a slow moving average of volume. The Volume Oscillator can provide insight into the strength or weakness of a price trend. A positive value suggests there is enough market support to continue driving price activity in the direction of the current trend. A negative value suggests there is a lack of support, that prices may begin to become stagnant or reverse..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionVolumeOscillator {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionVolumeOscillator", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Plots a horizontal histogram of volume by price..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionVolumeProfile {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionVolumeProfile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Variation of the VOL (Volume) indicator that colors the volume histogram different color depending if the current bar is up or down bar.
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionVolumeUpDown {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionVolumeUpDown", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Volume Zones plots a horizontal histogram that overlays a price chart. The histogram bars stretch from left to right starting at the left side of the chart. The length of each bar is determined by the cumulative total of all volume bars for the periods during which the price fell within the vertical range of the histogram bar..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionVolumeZones {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionVolumeZones", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Vortex indicator is an oscillator used to identify trends. A bullish signal triggers when the VIPlus line crosses above the VIMinus line. A bearish signal triggers when the VIMinus line crosses above the VIPlus line..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionVortex {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionVortex", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The VROC (Volume Rate-of-Change) shows whether or not a volume trend is developing in either an up or down direction. It is similar to the ROC indicator, but is applied to volume instead..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionVROC {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionVROC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The VWMA (Volume-Weighted Moving Average) returns the volume-weighted moving average for the specified price series and period. VWMA is similar to a Simple Moving Average (SMA), but each bar of data is weighted by the bar&apos;s Volume. VWMA places more significance on the days with the largest volume and the least for the days with lowest volume for the period specified..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionVWMA {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionVWMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Williams %R is a momentum indicator that is designed to identify overbought and oversold areas in a nontrending market..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionWilliamsR {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionWilliamsR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The WMA (Weighted Moving Average) is a Moving Average indicator that shows the average value of a security&apos;s price over a period of time with special emphasis on the more recent portions of the time period under analysis as opposed to the earlier..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionWMA {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionWMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The ZigZag indicator shows trend lines filtering out changes below a defined level..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionZigZag {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionZigZag", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The ZLEMA (Zero-Lag Exponential Moving Average) is an EMA variant that attempts to adjust for lag..
        /// </summary>
        public static string NinjaScriptIndicatorDescriptionZLEMA {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDescriptionZLEMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Diff.
        /// </summary>
        public static string NinjaScriptIndicatorDiff {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDiff", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Disparity line.
        /// </summary>
        public static string NinjaScriptIndicatorDisparityLine {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDisparityLine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Down.
        /// </summary>
        public static string NinjaScriptIndicatorDown {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorDown", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lower.
        /// </summary>
        public static string NinjaScriptIndicatorLower {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorLower", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to McClellan Oscillator line.
        /// </summary>
        public static string NinjaScriptIndicatorMcClellanOscillatorLine {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorMcClellanOscillatorLine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Middle.
        /// </summary>
        public static string NinjaScriptIndicatorMiddle {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorMiddle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Money flow line.
        /// </summary>
        public static string NinjaScriptIndicatorMoneyFlowLine {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorMoneyFlowLine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ADL.
        /// </summary>
        public static string NinjaScriptIndicatorNameADL {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameADL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ADX.
        /// </summary>
        public static string NinjaScriptIndicatorNameADX {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameADX", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ADXR.
        /// </summary>
        public static string NinjaScriptIndicatorNameADXR {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameADXR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to APZ.
        /// </summary>
        public static string NinjaScriptIndicatorNameAPZ {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameAPZ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Aroon.
        /// </summary>
        public static string NinjaScriptIndicatorNameAroon {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameAroon", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Aroon oscillator.
        /// </summary>
        public static string NinjaScriptIndicatorNameAroonOscillator {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameAroonOscillator", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ATR.
        /// </summary>
        public static string NinjaScriptIndicatorNameATR {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameATR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bar timer.
        /// </summary>
        public static string NinjaScriptIndicatorNameBarTimer {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameBarTimer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Block volume.
        /// </summary>
        public static string NinjaScriptIndicatorNameBlockVolume {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameBlockVolume", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bollinger.
        /// </summary>
        public static string NinjaScriptIndicatorNameBollinger {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameBollinger", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BOP.
        /// </summary>
        public static string NinjaScriptIndicatorNameBOP {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameBOP", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Buy sell pressure.
        /// </summary>
        public static string NinjaScriptIndicatorNameBuySellPressure {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameBuySellPressure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Buy sell volume.
        /// </summary>
        public static string NinjaScriptIndicatorNameBuySellVolume {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameBuySellVolume", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Camarilla pivots.
        /// </summary>
        public static string NinjaScriptIndicatorNameCamarillaPivots {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameCamarillaPivots", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Candlestick pattern.
        /// </summary>
        public static string NinjaScriptIndicatorNameCandlestickPattern {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameCandlestickPattern", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CCI.
        /// </summary>
        public static string NinjaScriptIndicatorNameCCI {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameCCI", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Chaikin money flow.
        /// </summary>
        public static string NinjaScriptIndicatorNameChaikinMoneyFlow {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameChaikinMoneyFlow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Chaikin oscillator.
        /// </summary>
        public static string NinjaScriptIndicatorNameChaikinOscillator {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameChaikinOscillator", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Chaikin volatility.
        /// </summary>
        public static string NinjaScriptIndicatorNameChaikinVolatility {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameChaikinVolatility", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Choppiness index.
        /// </summary>
        public static string NinjaScriptIndicatorNameChoppinessIndex {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameChoppinessIndex", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CMO.
        /// </summary>
        public static string NinjaScriptIndicatorNameCMO {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameCMO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Constant lines.
        /// </summary>
        public static string NinjaScriptIndicatorNameConstantLines {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameConstantLines", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Correlation.
        /// </summary>
        public static string NinjaScriptIndicatorNameCorrelation {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameCorrelation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to COT.
        /// </summary>
        public static string NinjaScriptIndicatorNameCOT {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameCOT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current day OHL.
        /// </summary>
        public static string NinjaScriptIndicatorNameCurrentDayOHL {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameCurrentDayOHL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Darvas.
        /// </summary>
        public static string NinjaScriptIndicatorNameDarvas {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameDarvas", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DEMA.
        /// </summary>
        public static string NinjaScriptIndicatorNameDEMA {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameDEMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Disparity index.
        /// </summary>
        public static string NinjaScriptIndicatorNameDisparityIndex {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameDisparityIndex", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DM.
        /// </summary>
        public static string NinjaScriptIndicatorNameDM {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameDM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DMI.
        /// </summary>
        public static string NinjaScriptIndicatorNameDMI {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameDMI", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DM index.
        /// </summary>
        public static string NinjaScriptIndicatorNameDMIndex {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameDMIndex", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Donchian channel.
        /// </summary>
        public static string NinjaScriptIndicatorNameDonchianChannel {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameDonchianChannel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Double stochastics.
        /// </summary>
        public static string NinjaScriptIndicatorNameDoubleStochastics {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameDoubleStochastics", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ease of movement.
        /// </summary>
        public static string NinjaScriptIndicatorNameEaseOfMovement {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameEaseOfMovement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EMA.
        /// </summary>
        public static string NinjaScriptIndicatorNameEMA {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameEMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fibonacci pivots.
        /// </summary>
        public static string NinjaScriptIndicatorNameFibonacciPivots {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameFibonacciPivots", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fisher transform.
        /// </summary>
        public static string NinjaScriptIndicatorNameFisherTransform {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameFisherTransform", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FOSC.
        /// </summary>
        public static string NinjaScriptIndicatorNameFOSC {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameFOSC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HMA.
        /// </summary>
        public static string NinjaScriptIndicatorNameHMA {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameHMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ichimoku Cloud.
        /// </summary>
        public static string NinjaScriptIndicatorNameIchimokuCloud {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameIchimokuCloud", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to KAMA.
        /// </summary>
        public static string NinjaScriptIndicatorNameKAMA {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameKAMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Keltner channel.
        /// </summary>
        public static string NinjaScriptIndicatorNameKelterChannel {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameKelterChannel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Key reversal down.
        /// </summary>
        public static string NinjaScriptIndicatorNameKeyReversalDown {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameKeyReversalDown", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Key reversal up.
        /// </summary>
        public static string NinjaScriptIndicatorNameKeyReversalUp {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameKeyReversalUp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lin. reg..
        /// </summary>
        public static string NinjaScriptIndicatorNameLinReg {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameLinReg", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lin. reg. intercept.
        /// </summary>
        public static string NinjaScriptIndicatorNameLinRegIntercept {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameLinRegIntercept", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lin. reg. slope.
        /// </summary>
        public static string NinjaScriptIndicatorNameLinRegSlope {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameLinRegSlope", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MACD.
        /// </summary>
        public static string NinjaScriptIndicatorNameMACD {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameMACD", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MA envelopes.
        /// </summary>
        public static string NinjaScriptIndicatorNameMAEnvelopes {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameMAEnvelopes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MAMA.
        /// </summary>
        public static string NinjaScriptIndicatorNameMAMA {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameMAMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MAX.
        /// </summary>
        public static string NinjaScriptIndicatorNameMAX {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameMAX", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to McClellan oscillator.
        /// </summary>
        public static string NinjaScriptIndicatorNameMcClellanOscillator {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameMcClellanOscillator", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MFI.
        /// </summary>
        public static string NinjaScriptIndicatorNameMFI {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameMFI", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MIN.
        /// </summary>
        public static string NinjaScriptIndicatorNameMIN {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameMIN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Momentum.
        /// </summary>
        public static string NinjaScriptIndicatorNameMomentum {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameMomentum", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Money flow oscillator.
        /// </summary>
        public static string NinjaScriptIndicatorNameMoneyFlowOscillator {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameMoneyFlowOscillator", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Moving average ribbon.
        /// </summary>
        public static string NinjaScriptIndicatorNameMovingAverageRibbon {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameMovingAverageRibbon", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to N bars down.
        /// </summary>
        public static string NinjaScriptIndicatorNameNBarsDown {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameNBarsDown", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to N bars up.
        /// </summary>
        public static string NinjaScriptIndicatorNameNBarsUp {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameNBarsUp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Net change display.
        /// </summary>
        public static string NinjaScriptIndicatorNameNetChangeDisplay {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameNetChangeDisplay", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to OBV.
        /// </summary>
        public static string NinjaScriptIndicatorNameOBV {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameOBV", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Parabolic SAR.
        /// </summary>
        public static string NinjaScriptIndicatorNameParabolicSAR {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameParabolicSAR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PFE.
        /// </summary>
        public static string NinjaScriptIndicatorNamePFE {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNamePFE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pivots.
        /// </summary>
        public static string NinjaScriptIndicatorNamePivots {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNamePivots", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PPO.
        /// </summary>
        public static string NinjaScriptIndicatorNamePPO {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNamePPO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Price line.
        /// </summary>
        public static string NinjaScriptIndicatorNamePriceLine {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNamePriceLine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Price oscillator.
        /// </summary>
        public static string NinjaScriptIndicatorNamePriceOscillator {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNamePriceOscillator", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Prior day OHLC.
        /// </summary>
        public static string NinjaScriptIndicatorNamePriorDayOHLC {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNamePriorDayOHLC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Psychological line.
        /// </summary>
        public static string NinjaScriptIndicatorNamePsychologicalLine {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNamePsychologicalLine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Range.
        /// </summary>
        public static string NinjaScriptIndicatorNameRange {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameRange", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Range counter.
        /// </summary>
        public static string NinjaScriptIndicatorNameRangeCounter {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameRangeCounter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Regression channel.
        /// </summary>
        public static string NinjaScriptIndicatorNameRegressionChannel {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameRegressionChannel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Relative vigor index.
        /// </summary>
        public static string NinjaScriptIndicatorNameRelativeVigorIndex {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameRelativeVigorIndex", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to RIND.
        /// </summary>
        public static string NinjaScriptIndicatorNameRIND {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameRIND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ROC.
        /// </summary>
        public static string NinjaScriptIndicatorNameROC {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameROC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to RSI.
        /// </summary>
        public static string NinjaScriptIndicatorNameRSI {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameRSI", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to R squared.
        /// </summary>
        public static string NinjaScriptIndicatorNameRSquared {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameRSquared", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to RSS.
        /// </summary>
        public static string NinjaScriptIndicatorNameRSS {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameRSS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to RVI.
        /// </summary>
        public static string NinjaScriptIndicatorNameRVI {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameRVI", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sample custom render.
        /// </summary>
        public static string NinjaScriptIndicatorNameSampleCustomRender {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameSampleCustomRender", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SMA.
        /// </summary>
        public static string NinjaScriptIndicatorNameSMA {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameSMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Std. dev..
        /// </summary>
        public static string NinjaScriptIndicatorNameStdDev {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameStdDev", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Std. error.
        /// </summary>
        public static string NinjaScriptIndicatorNameStdError {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameStdError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stochastics.
        /// </summary>
        public static string NinjaScriptIndicatorNameStochastics {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameStochastics", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stochastics fast.
        /// </summary>
        public static string NinjaScriptIndicatorNameStochasticsFast {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameStochasticsFast", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stoch RSI.
        /// </summary>
        public static string NinjaScriptIndicatorNameStochRSI {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameStochRSI", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SUM.
        /// </summary>
        public static string NinjaScriptIndicatorNameSUM {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameSUM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Swing.
        /// </summary>
        public static string NinjaScriptIndicatorNameSwing {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameSwing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to T3.
        /// </summary>
        public static string NinjaScriptIndicatorNameT3 {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameT3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to TEMA.
        /// </summary>
        public static string NinjaScriptIndicatorNameTEMA {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameTEMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tick counter.
        /// </summary>
        public static string NinjaScriptIndicatorNameTickCounter {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameTickCounter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to TMA.
        /// </summary>
        public static string NinjaScriptIndicatorNameTMA {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameTMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Trend lines.
        /// </summary>
        public static string NinjaScriptIndicatorNameTrendLines {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameTrendLines", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to TRIX.
        /// </summary>
        public static string NinjaScriptIndicatorNameTRIX {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameTRIX", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to TSF.
        /// </summary>
        public static string NinjaScriptIndicatorNameTSF {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameTSF", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to TSI.
        /// </summary>
        public static string NinjaScriptIndicatorNameTSI {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameTSI", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ultimate oscillator.
        /// </summary>
        public static string NinjaScriptIndicatorNameUltimateOscillator {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameUltimateOscillator", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to VMA.
        /// </summary>
        public static string NinjaScriptIndicatorNameVMA {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameVMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to VOL.
        /// </summary>
        public static string NinjaScriptIndicatorNameVOL {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameVOL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to VOLMA.
        /// </summary>
        public static string NinjaScriptIndicatorNameVOLMA {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameVOLMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Volume counter.
        /// </summary>
        public static string NinjaScriptIndicatorNameVolumeCounter {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameVolumeCounter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Volume oscillator.
        /// </summary>
        public static string NinjaScriptIndicatorNameVolumeOscillator {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameVolumeOscillator", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Volume profile.
        /// </summary>
        public static string NinjaScriptIndicatorNameVolumeProfile {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameVolumeProfile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Volume zones.
        /// </summary>
        public static string NinjaScriptIndicatorNameVolumesZones {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameVolumesZones", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Volume up down.
        /// </summary>
        public static string NinjaScriptIndicatorNameVolumeUpDown {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameVolumeUpDown", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vortex.
        /// </summary>
        public static string NinjaScriptIndicatorNameVortex {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameVortex", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to VROC.
        /// </summary>
        public static string NinjaScriptIndicatorNameVROC {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameVROC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to VWMA.
        /// </summary>
        public static string NinjaScriptIndicatorNameVWMA {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameVWMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Williams R.
        /// </summary>
        public static string NinjaScriptIndicatorNameWilliamsR {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameWilliamsR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to WMA.
        /// </summary>
        public static string NinjaScriptIndicatorNameWMA {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameWMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Zig zag.
        /// </summary>
        public static string NinjaScriptIndicatorNameZigZag {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameZigZag", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ZLEMA.
        /// </summary>
        public static string NinjaScriptIndicatorNameZLEMA {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNameZLEMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Neutral.
        /// </summary>
        public static string NinjaScriptIndicatorNeutral {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorNeutral", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Overbought.
        /// </summary>
        public static string NinjaScriptIndicatorOverbought {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorOverbought", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Over bought line.
        /// </summary>
        public static string NinjaScriptIndicatorOverBoughtLine {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorOverBoughtLine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Oversold.
        /// </summary>
        public static string NinjaScriptIndicatorOversold {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorOversold", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Over sold line.
        /// </summary>
        public static string NinjaScriptIndicatorOverSoldLine {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorOverSoldLine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Relative Vigor Index.
        /// </summary>
        public static string NinjaScriptIndicatorRelativeVigorIndex {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorRelativeVigorIndex", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Signal.
        /// </summary>
        public static string NinjaScriptIndicatorSignal {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorSignal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Up.
        /// </summary>
        public static string NinjaScriptIndicatorUp {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorUp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Upper.
        /// </summary>
        public static string NinjaScriptIndicatorUpper {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorUpper", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to VIMinus.
        /// </summary>
        public static string NinjaScriptIndicatorVIMinus {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorVIMinus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to VIPlus.
        /// </summary>
        public static string NinjaScriptIndicatorVIPlus {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorVIPlus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Visual.
        /// </summary>
        public static string NinjaScriptIndicatorVisualGroup {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorVisualGroup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Zero line.
        /// </summary>
        public static string NinjaScriptIndicatorZeroLine {
            get {
                return ResourceManager.GetString("NinjaScriptIndicatorZeroLine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Visible only when in focus.
        /// </summary>
        public static string NinjaScriptIsVisibleOnlyFocused {
            get {
                return ResourceManager.GetString("NinjaScriptIsVisibleOnlyFocused", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Line.
        /// </summary>
        public static string NinjaScriptLine {
            get {
                return ResourceManager.GetString("NinjaScriptLine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lines.
        /// </summary>
        public static string NinjaScriptLines {
            get {
                return ResourceManager.GetString("NinjaScriptLines", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to   Loading data... {0}.
        /// </summary>
        public static string NinjaScriptLoadingData {
            get {
                return ResourceManager.GetString("NinjaScriptLoadingData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current ask price.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionAskPrice {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionAskPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current ask size.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionAskSize {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionAskSize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Average daily volume.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionAverageDailyVolume {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionAverageDailyVolume", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A measure of the volatility, or systematic risk, of a security or a portfolio in comparison to the market as a whole..
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionBeta {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionBeta", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The difference between current bid and ask prices.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionBidAskSpread {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionBidAskSpread", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current bid price.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionBidPrice {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionBidPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current bid size.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionBidSize {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionBidSize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to High price for current calendar year.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionCalendarYearHigh {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionCalendarYearHigh", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date the high price for current calendar year occurred.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionCalendarYearHighDate {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionCalendarYearHighDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Low price for current calendar year.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionCalendarYearLow {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionCalendarYearLow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date the low price for current calendar year occurred.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionCalendarYearLowDate {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionCalendarYearLowDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This Market Analyzer column plots a mini chart per the input properties..
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionChartMini {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionChartMini", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This Market Analyzer column plots a mini chart per the input properties..
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionChartNetChange {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionChartNetChange", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current assets divided by current liabilities.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionCurrentRatio {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionCurrentRatio", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Today&apos;s high.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionDailyHigh {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionDailyHigh", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Today&apos;s low.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionDailyLow {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionDailyLow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Today&apos;s volume.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionDailyVolume {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionDailyVolume", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Displays how many days away from rollover to next contract.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionDaysUntilRollover {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionDaysUntilRollover", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Instrument description.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionDescription {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dividend amount.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionDividendAmount {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionDividendAmount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dividend pay date.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionDividendPayDate {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionDividendPayDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ratio that shows how much a company pays out in dividends each year relative to its share price. .
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionDividendYield {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionDividendYield", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Portion of a company&apos;s earnings allocated to each outstanding share of common stock..
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionEarningsPerShare {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionEarningsPerShare", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Five years growth percentage.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionFiveYearsGrowthPercentage {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionFiveYearsGrowthPercentage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to High of last 52 weeks.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionHigh52Weeks {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionHigh52Weeks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date the high price of last 52 weeks occurred.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionHigh52WeeksDate {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionHigh52WeeksDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Realized volatility of an instrument over time.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionHistoricalVolatility {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionHistoricalVolatility", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Instrument name.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionInstrument {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionInstrument", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Close of last trading session.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionLastClose {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionLastClose", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last traded price.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionLastPrice {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionLastPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last trade size.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionLastSize {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionLastSize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Low of last 52 weeks.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionLow52Weeks {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionLow52Weeks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date the low price of last 52 weeks occurred.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionLow52WeeksDate {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionLow52WeeksDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Market capitalization. The total value of issued shares..
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionMarketCap {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionMarketCap", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current price and net change.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionMarketPrice {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionMarketPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current price compared to last close price.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionNetChange {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionNetChange", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current low compared to last close price.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionNetChangeMaxDown {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionNetChangeMaxDown", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current high compared to last close price.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionNetChangeMaxUp {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionNetChangeMaxUp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Projected earnings per share.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionNextYearsEarningsPerShare {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionNextYearsEarningsPerShare", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User definable field. Double click on applied notes column to create or edit notes..
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionNotes {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open price for current trading session.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionOpening {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionOpening", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The total number of options and/or futures contracts that are not closed or delivered on a particular day.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionOpenInterest {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionOpenInterest", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Percentage of shares held by institutions.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionPercentHeldByInstitutions {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionPercentHeldByInstitutions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Average entry price of current position.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionPositionAvgPrice {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionPositionAvgPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current position size.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionPositionSize {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionPositionSize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current share price compared to its per-share earnings..
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionPriceEarningsRatio {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionPriceEarningsRatio", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total of unrealized and realized profit and loss. .
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionProfitLoss {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionProfitLoss", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Realized profit or loss.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionRealizedProfitLoss {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionRealizedProfitLoss", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ratio of revenue to share price.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionRevenuePerShare {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionRevenuePerShare", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Today&apos;s settlement price.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionSettlement {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionSettlement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Number of shares outstanding.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionSharesOutstanding {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionSharesOutstanding", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quantity of stock shares that investors have sold short but not yet covered or closed out..
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionShortInterest {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionShortInterest", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Short interest divided by average daily volume.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionShortInterestRatio {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionShortInterestRatio", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Time the last trade occurred.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionTimeLastTick {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionTimeLastTick", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Today&apos;s filled contracts.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionTradedContracts {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionTradedContracts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This columndisplays a colored bar that represents the incoming ticks with the same colors that the T &amp; S window uses.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionTSTrend {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionTSTrend", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Profit or loss for the current position. .
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionUnrealizedProfitLoss {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionUnrealizedProfitLoss", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Volume weighted average price.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnDescriptionVwap {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnDescriptionVwap", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ask price.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNameAskPrice {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNameAskPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ask size.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNameAskSize {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNameAskSize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Average daily volume.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNameAverageDailyVolume {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNameAverageDailyVolume", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Beta.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNameBeta {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNameBeta", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bid ask spread.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNameBidAskSpread {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNameBidAskSpread", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bid price.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNameBidPrice {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNameBidPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bid size.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNameBidSize {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNameBidSize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Calendar year high.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNameCalendarYearHigh {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNameCalendarYearHigh", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Calendar year high date.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNameCalendarYearHighDate {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNameCalendarYearHighDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Calendar year low.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNameCalendarYearLow {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNameCalendarYearLow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Calendar year low date.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNameCalendarYearLowDate {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNameCalendarYearLowDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Chart - Mini.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNameChartMini {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNameChartMini", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Chart - Net change.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNameChartNetChange {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNameChartNetChange", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current ratio.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNameCurrentRatio {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNameCurrentRatio", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Daily high.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNameDailyHigh {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNameDailyHigh", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Daily low.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNameDailyLow {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNameDailyLow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Daily volume.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNameDailyVolume {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNameDailyVolume", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Days until rollover.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNameDaysUntilRollover {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNameDaysUntilRollover", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Description.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNameDescription {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNameDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dividend amount.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNameDividendAmount {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNameDividendAmount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dividend pay date.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNameDividendPayDate {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNameDividendPayDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dividend yield.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNameDividendYield {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNameDividendYield", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Earnings per share.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNameEarningsPerShare {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNameEarningsPerShare", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Five years growth percentage.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNameFiveYearsGrowthPercentage {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNameFiveYearsGrowthPercentage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to High 52 weeks.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNameHigh52Weeks {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNameHigh52Weeks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to High 52 weeks date.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNameHigh52WeeksDate {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNameHigh52WeeksDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Historical volatility.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNameHistoricalVolatility {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNameHistoricalVolatility", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Instrument.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNameInstrument {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNameInstrument", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last close.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNameLastClose {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNameLastClose", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last price.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNameLastPrice {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNameLastPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last size.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNameLastSize {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNameLastSize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Low 52 weeks.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNameLow52Weeks {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNameLow52Weeks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Low 52 weeks date.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNameLow52WeeksDate {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNameLow52WeeksDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Market capitalization.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNameMarketCap {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNameMarketCap", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Market price.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNameMarketPrice {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNameMarketPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Net change.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNameNetChange {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNameNetChange", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Net change max down.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNameNetChangeMaxDown {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNameNetChangeMaxDown", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Net change max up.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNameNetChangeMaxUp {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNameNetChangeMaxUp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Next year earnings per share.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNameNextYearsEarningsPerShare {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNameNextYearsEarningsPerShare", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notes.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNameNotes {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNameNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Opening.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNameOpening {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNameOpening", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open interest.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNameOpenInterest {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNameOpenInterest", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Percent held by institutions.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNamePercentHeldByInstitutions {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNamePercentHeldByInstitutions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Position avg. price.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNamePositionAvgPrice {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNamePositionAvgPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Position size.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNamePositionSize {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNamePositionSize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Price earnings ratio.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNamePriceEarningsRatio {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNamePriceEarningsRatio", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Profit loss.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNameProfitLoss {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNameProfitLoss", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Realized profit loss.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNameRealizedProfitLoss {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNameRealizedProfitLoss", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Revenue per share.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNameRevenuePerShare {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNameRevenuePerShare", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Settlement price.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNameSettlement {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNameSettlement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shares outstanding.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNameSharesOutstanding {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNameSharesOutstanding", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Short interest.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNameShortInterest {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNameShortInterest", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Short interest ratio.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNameShortInterestRatio {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNameShortInterestRatio", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Time last tick.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNameTimeLastTick {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNameTimeLastTick", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Traded contracts.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNameTradedContracts {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNameTradedContracts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to T &amp; S trend.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNameTSTrend {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNameTSTrend", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unrealized profit loss.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNameUnrealizedProfitLoss {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNameUnrealizedProfitLoss", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to VWAP.
        /// </summary>
        public static string NinjaScriptMarketAnalyzerColumnNameVwap {
            get {
                return ResourceManager.GetString("NinjaScriptMarketAnalyzerColumnNameVwap", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rows.
        /// </summary>
        public static string NinjaScriptNumberOfRows {
            get {
                return ResourceManager.GetString("NinjaScriptNumberOfRows", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} relies on bid/ask tick updates expecting Calculate &apos;On each tick&apos;.
        /// </summary>
        public static string NinjaScriptOnBarCloseError {
            get {
                return ResourceManager.GetString("NinjaScriptOnBarCloseError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} relies on volume updates expecting Calculate &apos;On each tick&apos; or &apos;On bar close&apos;.
        /// </summary>
        public static string NinjaScriptOnPriceChangeError {
            get {
                return ResourceManager.GetString("NinjaScriptOnPriceChangeError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Max. avg. favorable excursion.
        /// </summary>
        public static string NinjaScriptOptimizationFitnessNameMaxAvgMfe {
            get {
                return ResourceManager.GetString("NinjaScriptOptimizationFitnessNameMaxAvgMfe", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Max. avg. favorable excursion (long).
        /// </summary>
        public static string NinjaScriptOptimizationFitnessNameMaxAvgMfeLong {
            get {
                return ResourceManager.GetString("NinjaScriptOptimizationFitnessNameMaxAvgMfeLong", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Max. avg. favorable excursion (short).
        /// </summary>
        public static string NinjaScriptOptimizationFitnessNameMaxAvgMfeShort {
            get {
                return ResourceManager.GetString("NinjaScriptOptimizationFitnessNameMaxAvgMfeShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Max. avg. profit.
        /// </summary>
        public static string NinjaScriptOptimizationFitnessNameMaxAvgProfit {
            get {
                return ResourceManager.GetString("NinjaScriptOptimizationFitnessNameMaxAvgProfit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Max. avg. profit (long).
        /// </summary>
        public static string NinjaScriptOptimizationFitnessNameMaxAvgProfitLong {
            get {
                return ResourceManager.GetString("NinjaScriptOptimizationFitnessNameMaxAvgProfitLong", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Max. avg. profit (short).
        /// </summary>
        public static string NinjaScriptOptimizationFitnessNameMaxAvgProfitShort {
            get {
                return ResourceManager.GetString("NinjaScriptOptimizationFitnessNameMaxAvgProfitShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Max. net profit.
        /// </summary>
        public static string NinjaScriptOptimizationFitnessNameMaxNetProfit {
            get {
                return ResourceManager.GetString("NinjaScriptOptimizationFitnessNameMaxNetProfit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Max. net profit (long).
        /// </summary>
        public static string NinjaScriptOptimizationFitnessNameMaxNetProfitLong {
            get {
                return ResourceManager.GetString("NinjaScriptOptimizationFitnessNameMaxNetProfitLong", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Max. net profit (short).
        /// </summary>
        public static string NinjaScriptOptimizationFitnessNameMaxNetProfitShort {
            get {
                return ResourceManager.GetString("NinjaScriptOptimizationFitnessNameMaxNetProfitShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Max. % profitable.
        /// </summary>
        public static string NinjaScriptOptimizationFitnessNameMaxPercentProfitable {
            get {
                return ResourceManager.GetString("NinjaScriptOptimizationFitnessNameMaxPercentProfitable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Max. % profitable (long).
        /// </summary>
        public static string NinjaScriptOptimizationFitnessNameMaxPercentProfitableLong {
            get {
                return ResourceManager.GetString("NinjaScriptOptimizationFitnessNameMaxPercentProfitableLong", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Max. % profitable (short).
        /// </summary>
        public static string NinjaScriptOptimizationFitnessNameMaxPercentProfitableShort {
            get {
                return ResourceManager.GetString("NinjaScriptOptimizationFitnessNameMaxPercentProfitableShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Max. probability.
        /// </summary>
        public static string NinjaScriptOptimizationFitnessNameMaxProbablity {
            get {
                return ResourceManager.GetString("NinjaScriptOptimizationFitnessNameMaxProbablity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Max. probability (long).
        /// </summary>
        public static string NinjaScriptOptimizationFitnessNameMaxProbablityLong {
            get {
                return ResourceManager.GetString("NinjaScriptOptimizationFitnessNameMaxProbablityLong", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Max. probability (short).
        /// </summary>
        public static string NinjaScriptOptimizationFitnessNameMaxProbablityShort {
            get {
                return ResourceManager.GetString("NinjaScriptOptimizationFitnessNameMaxProbablityShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Max. profit factor.
        /// </summary>
        public static string NinjaScriptOptimizationFitnessNameMaxProfitFactor {
            get {
                return ResourceManager.GetString("NinjaScriptOptimizationFitnessNameMaxProfitFactor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Max. profit factor (long).
        /// </summary>
        public static string NinjaScriptOptimizationFitnessNameMaxProfitFactorLong {
            get {
                return ResourceManager.GetString("NinjaScriptOptimizationFitnessNameMaxProfitFactorLong", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Max. profit factor (short).
        /// </summary>
        public static string NinjaScriptOptimizationFitnessNameMaxProfitFactorShort {
            get {
                return ResourceManager.GetString("NinjaScriptOptimizationFitnessNameMaxProfitFactorShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Max. R^2.
        /// </summary>
        public static string NinjaScriptOptimizationFitnessNameMaxR2 {
            get {
                return ResourceManager.GetString("NinjaScriptOptimizationFitnessNameMaxR2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Max. R^2 (long).
        /// </summary>
        public static string NinjaScriptOptimizationFitnessNameMaxR2Long {
            get {
                return ResourceManager.GetString("NinjaScriptOptimizationFitnessNameMaxR2Long", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Max. R^2 (short).
        /// </summary>
        public static string NinjaScriptOptimizationFitnessNameMaxR2Short {
            get {
                return ResourceManager.GetString("NinjaScriptOptimizationFitnessNameMaxR2Short", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Max. Sharpe ratio.
        /// </summary>
        public static string NinjaScriptOptimizationFitnessNameMaxSharpeRatio {
            get {
                return ResourceManager.GetString("NinjaScriptOptimizationFitnessNameMaxSharpeRatio", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Max. Sharpe ratio (long).
        /// </summary>
        public static string NinjaScriptOptimizationFitnessNameMaxSharpeRatioLong {
            get {
                return ResourceManager.GetString("NinjaScriptOptimizationFitnessNameMaxSharpeRatioLong", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Max. Sharpe ratio (short).
        /// </summary>
        public static string NinjaScriptOptimizationFitnessNameMaxSharpeRatioShort {
            get {
                return ResourceManager.GetString("NinjaScriptOptimizationFitnessNameMaxSharpeRatioShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Max. Sortino ratio.
        /// </summary>
        public static string NinjaScriptOptimizationFitnessNameMaxSortinoRatio {
            get {
                return ResourceManager.GetString("NinjaScriptOptimizationFitnessNameMaxSortinoRatio", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Max. Sortino ratio (long).
        /// </summary>
        public static string NinjaScriptOptimizationFitnessNameMaxSortinoRatioLong {
            get {
                return ResourceManager.GetString("NinjaScriptOptimizationFitnessNameMaxSortinoRatioLong", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Max. Sortino ratio (short).
        /// </summary>
        public static string NinjaScriptOptimizationFitnessNameMaxSortinoRatioShort {
            get {
                return ResourceManager.GetString("NinjaScriptOptimizationFitnessNameMaxSortinoRatioShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Max. strength.
        /// </summary>
        public static string NinjaScriptOptimizationFitnessNameMaxStrength {
            get {
                return ResourceManager.GetString("NinjaScriptOptimizationFitnessNameMaxStrength", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Max. strength (long).
        /// </summary>
        public static string NinjaScriptOptimizationFitnessNameMaxStrengthLong {
            get {
                return ResourceManager.GetString("NinjaScriptOptimizationFitnessNameMaxStrengthLong", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Max. strength (short).
        /// </summary>
        public static string NinjaScriptOptimizationFitnessNameMaxStrengthShort {
            get {
                return ResourceManager.GetString("NinjaScriptOptimizationFitnessNameMaxStrengthShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Max. Ulcer ratio.
        /// </summary>
        public static string NinjaScriptOptimizationFitnessNameMaxUlcerRatio {
            get {
                return ResourceManager.GetString("NinjaScriptOptimizationFitnessNameMaxUlcerRatio", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Max. Ulcer ratio (long).
        /// </summary>
        public static string NinjaScriptOptimizationFitnessNameMaxUlcerRatioLong {
            get {
                return ResourceManager.GetString("NinjaScriptOptimizationFitnessNameMaxUlcerRatioLong", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Max. Ulcer ratio (short).
        /// </summary>
        public static string NinjaScriptOptimizationFitnessNameMaxUlcerRatioShort {
            get {
                return ResourceManager.GetString("NinjaScriptOptimizationFitnessNameMaxUlcerRatioShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Max. win/loss ratio.
        /// </summary>
        public static string NinjaScriptOptimizationFitnessNameMaxWinLossRatio {
            get {
                return ResourceManager.GetString("NinjaScriptOptimizationFitnessNameMaxWinLossRatio", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Max. win/loss ratio (long).
        /// </summary>
        public static string NinjaScriptOptimizationFitnessNameMaxWinLossRatioLong {
            get {
                return ResourceManager.GetString("NinjaScriptOptimizationFitnessNameMaxWinLossRatioLong", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Max. win/loss ratio (short).
        /// </summary>
        public static string NinjaScriptOptimizationFitnessNameMaxWinLossRatioShort {
            get {
                return ResourceManager.GetString("NinjaScriptOptimizationFitnessNameMaxWinLossRatioShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Min. avg. adverse excursion.
        /// </summary>
        public static string NinjaScriptOptimizationFitnessNameMinAvgMae {
            get {
                return ResourceManager.GetString("NinjaScriptOptimizationFitnessNameMinAvgMae", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Min. avg. adverse excursion (long).
        /// </summary>
        public static string NinjaScriptOptimizationFitnessNameMinAvgMaeLong {
            get {
                return ResourceManager.GetString("NinjaScriptOptimizationFitnessNameMinAvgMaeLong", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Min. avg. adverse excursion (short).
        /// </summary>
        public static string NinjaScriptOptimizationFitnessNameMinAvgMaeShort {
            get {
                return ResourceManager.GetString("NinjaScriptOptimizationFitnessNameMinAvgMaeShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Min. draw down.
        /// </summary>
        public static string NinjaScriptOptimizationFitnessNameMinDrawDown {
            get {
                return ResourceManager.GetString("NinjaScriptOptimizationFitnessNameMinDrawDown", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Min. draw down (long).
        /// </summary>
        public static string NinjaScriptOptimizationFitnessNameMinDrawDownLong {
            get {
                return ResourceManager.GetString("NinjaScriptOptimizationFitnessNameMinDrawDownLong", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Min. draw down (short).
        /// </summary>
        public static string NinjaScriptOptimizationFitnessNameMinDrawDownShort {
            get {
                return ResourceManager.GetString("NinjaScriptOptimizationFitnessNameMinDrawDownShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default.
        /// </summary>
        public static string NinjaScriptOptimizerDefault {
            get {
                return ResourceManager.GetString("NinjaScriptOptimizerDefault", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Genetic.
        /// </summary>
        public static string NinjaScriptOptimizerGenetic {
            get {
                return ResourceManager.GetString("NinjaScriptOptimizerGenetic", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Parameters.
        /// </summary>
        public static string NinjaScriptParameters {
            get {
                return ResourceManager.GetString("NinjaScriptParameters", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ask background.
        /// </summary>
        public static string NinjaScriptRecentColumnAskBackground {
            get {
                return ResourceManager.GetString("NinjaScriptRecentColumnAskBackground", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ask foreground.
        /// </summary>
        public static string NinjaScriptRecentColumnAskForeground {
            get {
                return ResourceManager.GetString("NinjaScriptRecentColumnAskForeground", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bid background.
        /// </summary>
        public static string NinjaScriptRecentColumnBidBackground {
            get {
                return ResourceManager.GetString("NinjaScriptRecentColumnBidBackground", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bid foreground.
        /// </summary>
        public static string NinjaScriptRecentColumnBidForeground {
            get {
                return ResourceManager.GetString("NinjaScriptRecentColumnBidForeground", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Display.
        /// </summary>
        public static string NinjaScriptRecentColumnDiplay {
            get {
                return ResourceManager.GetString("NinjaScriptRecentColumnDiplay", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reset tolerance.
        /// </summary>
        public static string NinjaScriptRecentColumnResetTolerance {
            get {
                return ResourceManager.GetString("NinjaScriptRecentColumnResetTolerance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reset when.
        /// </summary>
        public static string NinjaScriptRecentColumnResetWhen {
            get {
                return ResourceManager.GetString("NinjaScriptRecentColumnResetWhen", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Setup.
        /// </summary>
        public static string NinjaScriptSetup {
            get {
                return ResourceManager.GetString("NinjaScriptSetup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Advanced trade management sample strategy..
        /// </summary>
        public static string NinjaScriptStrategyDescriptionSampleATMStrategy {
            get {
                return ResourceManager.GetString("NinjaScriptStrategyDescriptionSampleATMStrategy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sample to demonstrate usage of custom performance.
        /// </summary>
        public static string NinjaScriptStrategyDescriptionSampleCustomPerformance {
            get {
                return ResourceManager.GetString("NinjaScriptStrategyDescriptionSampleCustomPerformance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This strategy demonstrates some of the capabilities of the NinjaTrader Development Framework.
        /// </summary>
        public static string NinjaScriptStrategyDescriptionSampleFramework {
            get {
                return ResourceManager.GetString("NinjaScriptStrategyDescriptionSampleFramework", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Simple moving average cross over strategy..
        /// </summary>
        public static string NinjaScriptStrategyDescriptionSampleMACrossOver {
            get {
                return ResourceManager.GetString("NinjaScriptStrategyDescriptionSampleMACrossOver", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Multi-Instrument sample strategy..
        /// </summary>
        public static string NinjaScriptStrategyDescriptionSampleMultiInstrument {
            get {
                return ResourceManager.GetString("NinjaScriptStrategyDescriptionSampleMultiInstrument", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Multi-time frame sample strategy..
        /// </summary>
        public static string NinjaScriptStrategyDescriptionSampleMultiTimeFrame {
            get {
                return ResourceManager.GetString("NinjaScriptStrategyDescriptionSampleMultiTimeFrame", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Strategy generator.
        /// </summary>
        public static string NinjaScriptStrategyGenerator {
            get {
                return ResourceManager.GetString("NinjaScriptStrategyGenerator", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 1 candle stick pattern|{0} candle stick patterns|Add candle stick pattern...|Configure candle stick pattern...|Configure candle stick patterns....
        /// </summary>
        public static string NinjaScriptStrategyGeneratorCandleStickPatternPrompt {
            get {
                return ResourceManager.GetString("NinjaScriptStrategyGeneratorCandleStickPatternPrompt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Entry conditions.
        /// </summary>
        public static string NinjaScriptStrategyGeneratorEntries {
            get {
                return ResourceManager.GetString("NinjaScriptStrategyGeneratorEntries", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You needed to at least one entry order exit condition..
        /// </summary>
        public static string NinjaScriptStrategyGeneratorEntriesOrExits {
            get {
                return ResourceManager.GetString("NinjaScriptStrategyGeneratorEntriesOrExits", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Exception on expression:{0}{1}.
        /// </summary>
        public static string NinjaScriptStrategyGeneratorIndicatorException {
            get {
                return ResourceManager.GetString("NinjaScriptStrategyGeneratorIndicatorException", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 1 indicator|{0} indicators|Add indicator...|Configure indicator...|Configure indicators....
        /// </summary>
        public static string NinjaScriptStrategyGeneratorIndicatorsPrompt {
            get {
                return ResourceManager.GetString("NinjaScriptStrategyGeneratorIndicatorsPrompt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Performance for {0} = {1}.
        /// </summary>
        public static string NinjaScriptStrategyGeneratorPeformance {
            get {
                return ResourceManager.GetString("NinjaScriptStrategyGeneratorPeformance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AI Generate Properties.
        /// </summary>
        public static string NinjaScriptStrategyGeneratorProperties {
            get {
                return ResourceManager.GetString("NinjaScriptStrategyGeneratorProperties", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Strategy generator terminated after {0} generations, since there was no performance improvement for {1} generations.
        /// </summary>
        public static string NinjaScriptStrategyGeneratorTerminated {
            get {
                return ResourceManager.GetString("NinjaScriptStrategyGeneratorTerminated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Candle stick pattern.
        /// </summary>
        public static string NinjaScriptStrategyGeneratorUseCandleStickPattern {
            get {
                return ResourceManager.GetString("NinjaScriptStrategyGeneratorUseCandleStickPattern", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Indicators.
        /// </summary>
        public static string NinjaScriptStrategyGeneratorUseIndicators {
            get {
                return ResourceManager.GetString("NinjaScriptStrategyGeneratorUseIndicators", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sample ATM strategy.
        /// </summary>
        public static string NinjaScriptStrategyNameSampleATMStrategy {
            get {
                return ResourceManager.GetString("NinjaScriptStrategyNameSampleATMStrategy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sample custom performance.
        /// </summary>
        public static string NinjaScriptStrategyNameSampleCustomPerformance {
            get {
                return ResourceManager.GetString("NinjaScriptStrategyNameSampleCustomPerformance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sample framework.
        /// </summary>
        public static string NinjaScriptStrategyNameSampleFramework {
            get {
                return ResourceManager.GetString("NinjaScriptStrategyNameSampleFramework", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sample MA crossover.
        /// </summary>
        public static string NinjaScriptStrategyNameSampleMACrossOver {
            get {
                return ResourceManager.GetString("NinjaScriptStrategyNameSampleMACrossOver", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sample multi-instrument.
        /// </summary>
        public static string NinjaScriptStrategyNameSampleMultiInstrument {
            get {
                return ResourceManager.GetString("NinjaScriptStrategyNameSampleMultiInstrument", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sample multi-timeframe.
        /// </summary>
        public static string NinjaScriptStrategyNameSampleMultiTimeFrame {
            get {
                return ResourceManager.GetString("NinjaScriptStrategyNameSampleMultiTimeFrame", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Strategy parameters.
        /// </summary>
        public static string NinjaScriptStrategyParameters {
            get {
                return ResourceManager.GetString("NinjaScriptStrategyParameters", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to APQ.
        /// </summary>
        public static string NinjaScriptSuperDomColumnApq {
            get {
                return ResourceManager.GetString("NinjaScriptSuperDomColumnApq", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error on loading bars series for &apos;{0}/{1}&apos;: {2}.
        /// </summary>
        public static string NinjaScriptSuperDomColumnBaseInitializeBarsPoolError {
            get {
                return ResourceManager.GetString("NinjaScriptSuperDomColumnBaseInitializeBarsPoolError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Approximate Position In Queue (APQ) indicator gives you a conservative estimation of the current position in the queue for orders you have placed..
        /// </summary>
        public static string NinjaScriptSuperDomColumnDescriptionApq {
            get {
                return ResourceManager.GetString("NinjaScriptSuperDomColumnDescriptionApq", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Notes column provides text entry at price points directly in the SuperDOM and can be used to add notes per price level..
        /// </summary>
        public static string NinjaScriptSuperDomColumnDescriptionNotes {
            get {
                return ResourceManager.GetString("NinjaScriptSuperDomColumnDescriptionNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Profit and Loss (PnL) column will display the potential profit and loss at each price point once your are in a trade..
        /// </summary>
        public static string NinjaScriptSuperDomColumnDescriptionPnl {
            get {
                return ResourceManager.GetString("NinjaScriptSuperDomColumnDescriptionPnl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Volume column will use historical tick data to display the number of contracts traded at each price level. You can optionally color the bars based on if trades occurred on the ask or bid..
        /// </summary>
        public static string NinjaScriptSuperDomColumnDescriptionVolume {
            get {
                return ResourceManager.GetString("NinjaScriptSuperDomColumnDescriptionVolume", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notes.
        /// </summary>
        public static string NinjaScriptSuperDomColumnNotes {
            get {
                return ResourceManager.GetString("NinjaScriptSuperDomColumnNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PnL.
        /// </summary>
        public static string NinjaScriptSuperDomColumnProfitAndLoss {
            get {
                return ResourceManager.GetString("NinjaScriptSuperDomColumnProfitAndLoss", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Volume.
        /// </summary>
        public static string NinjaScriptSuperDomColumnVolume {
            get {
                return ResourceManager.GetString("NinjaScriptSuperDomColumnVolume", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Text Position.
        /// </summary>
        public static string NinjaScriptTextPosition {
            get {
                return ResourceManager.GetString("NinjaScriptTextPosition", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Text Position.
        /// </summary>
        public static string NinjaScriptTextPosition_ {
            get {
                return ResourceManager.GetString("NinjaScriptTextPosition ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error while loading Drawing tool {0} : {1}.
        /// </summary>
        public static string NinjaScriptTileError {
            get {
                return ResourceManager.GetString("NinjaScriptTileError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Y pixel offset.
        /// </summary>
        public static string NinjaScriptYOffset {
            get {
                return ResourceManager.GetString("NinjaScriptYOffset", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Number of COT plots.
        /// </summary>
        public static string NumberOfCotPlots {
            get {
                return ResourceManager.GetString("NumberOfCotPlots", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Number of trend lines.
        /// </summary>
        public static string NumberOfTrendLines {
            get {
                return ResourceManager.GetString("NumberOfTrendLines", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Number of standard deviations.
        /// </summary>
        public static string NumStdDev {
            get {
                return ResourceManager.GetString("NumStdDev", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Offset multiplier.
        /// </summary>
        public static string OffsetMultiplier {
            get {
                return ResourceManager.GetString("OffsetMultiplier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Old trends opacity.
        /// </summary>
        public static string OldTrendsOpacity {
            get {
                return ResourceManager.GetString("OldTrendsOpacity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Opacity.
        /// </summary>
        public static string Opacity {
            get {
                return ResourceManager.GetString("Opacity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Arrow.
        /// </summary>
        public static string PathCapMode_Arrow {
            get {
                return ResourceManager.GetString("PathCapMode_Arrow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Line.
        /// </summary>
        public static string PathCapMode_Line {
            get {
                return ResourceManager.GetString("PathCapMode_Line", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Arrow.
        /// </summary>
        public static string PathToolCapMode_Arrow {
            get {
                return ResourceManager.GetString("PathToolCapMode_Arrow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Line.
        /// </summary>
        public static string PathToolCapMode_Line {
            get {
                return ResourceManager.GetString("PathToolCapMode_Line", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sample cum. profit performance metric.
        /// </summary>
        public static string PerformanceMetricSampleCumProfit {
            get {
                return ResourceManager.GetString("PerformanceMetricSampleCumProfit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Period.
        /// </summary>
        public static string Period {
            get {
                return ResourceManager.GetString("Period", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Period D.
        /// </summary>
        public static string PeriodD {
            get {
                return ResourceManager.GetString("PeriodD", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Period K.
        /// </summary>
        public static string PeriodK {
            get {
                return ResourceManager.GetString("PeriodK", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Period Q.
        /// </summary>
        public static string PeriodQ {
            get {
                return ResourceManager.GetString("PeriodQ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Zero.
        /// </summary>
        public static string PFEZero {
            get {
                return ResourceManager.GetString("PFEZero", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Intraday or Daily bars must be used for Pivots.
        /// </summary>
        public static string PiviotsDailyBarsError {
            get {
                return ResourceManager.GetString("PiviotsDailyBarsError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Insufficient Daily data to calculate Pivots.
        /// </summary>
        public static string PiviotsDailyDataError {
            get {
                return ResourceManager.GetString("PiviotsDailyDataError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Insufficient historical data to calculate pivots. Increase chart look back period (DaysToLoad, BarsToLoad, or Start Date).
        /// </summary>
        public static string PiviotsInsufficentDataError {
            get {
                return ResourceManager.GetString("PiviotsInsufficentDataError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Period Type will need to be Daily with a Value of 1.
        /// </summary>
        public static string PiviotsPeriodTypeError {
            get {
                return ResourceManager.GetString("PiviotsPeriodTypeError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Daily bars require the use of Weekly or Monthly Pivot range.
        /// </summary>
        public static string PiviotsWeeklyBarsError {
            get {
                return ResourceManager.GetString("PiviotsWeeklyBarsError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pivot range.
        /// </summary>
        public static string PivotRange {
            get {
                return ResourceManager.GetString("PivotRange", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Daily.
        /// </summary>
        public static string PivotRange_Daily {
            get {
                return ResourceManager.GetString("PivotRange_Daily", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Monthly.
        /// </summary>
        public static string PivotRange_Monthly {
            get {
                return ResourceManager.GetString("PivotRange_Monthly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Weekly.
        /// </summary>
        public static string PivotRange_Weekly {
            get {
                return ResourceManager.GetString("PivotRange_Weekly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PP.
        /// </summary>
        public static string PivotsPP {
            get {
                return ResourceManager.GetString("PivotsPP", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to R1.
        /// </summary>
        public static string PivotsR1 {
            get {
                return ResourceManager.GetString("PivotsR1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to R2.
        /// </summary>
        public static string PivotsR2 {
            get {
                return ResourceManager.GetString("PivotsR2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to R3.
        /// </summary>
        public static string PivotsR3 {
            get {
                return ResourceManager.GetString("PivotsR3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to R4.
        /// </summary>
        public static string PivotsR4 {
            get {
                return ResourceManager.GetString("PivotsR4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to S1.
        /// </summary>
        public static string PivotsS1 {
            get {
                return ResourceManager.GetString("PivotsS1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to S2.
        /// </summary>
        public static string PivotsS2 {
            get {
                return ResourceManager.GetString("PivotsS2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to S3.
        /// </summary>
        public static string PivotsS3 {
            get {
                return ResourceManager.GetString("PivotsS3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to S4.
        /// </summary>
        public static string PivotsS4 {
            get {
                return ResourceManager.GetString("PivotsS4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Plot current value only.
        /// </summary>
        public static string PlotCurrentValue {
            get {
                return ResourceManager.GetString("PlotCurrentValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Positive color.
        /// </summary>
        public static string PositiveColor {
            get {
                return ResourceManager.GetString("PositiveColor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Smoothed.
        /// </summary>
        public static string PPOSmoothed {
            get {
                return ResourceManager.GetString("PPOSmoothed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ask line.
        /// </summary>
        public static string PriceLinePlotAsk {
            get {
                return ResourceManager.GetString("PriceLinePlotAsk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bid line.
        /// </summary>
        public static string PriceLinePlotBid {
            get {
                return ResourceManager.GetString("PriceLinePlotBid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last line.
        /// </summary>
        public static string PriceLinePlotLast {
            get {
                return ResourceManager.GetString("PriceLinePlotLast", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Prior close.
        /// </summary>
        public static string PriorDayOHLCClose {
            get {
                return ResourceManager.GetString("PriorDayOHLCClose", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Prior high.
        /// </summary>
        public static string PriorDayOHLCHigh {
            get {
                return ResourceManager.GetString("PriorDayOHLCHigh", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PriorDayOHLC only works on intraday intervals.
        /// </summary>
        public static string PriorDayOHLCIntradayError {
            get {
                return ResourceManager.GetString("PriorDayOHLCIntradayError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Prior low.
        /// </summary>
        public static string PriorDayOHLCLow {
            get {
                return ResourceManager.GetString("PriorDayOHLCLow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Prior open.
        /// </summary>
        public static string PriorDayOHLCOpen {
            get {
                return ResourceManager.GetString("PriorDayOHLCOpen", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Visual.
        /// </summary>
        public static string PropertyCategoryVisual {
            get {
                return ResourceManager.GetString("PropertyCategoryVisual", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ask.
        /// </summary>
        public static string PullingStackingDisplayType_Ask {
            get {
                return ResourceManager.GetString("PullingStackingDisplayType_Ask", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bid.
        /// </summary>
        public static string PullingStackingDisplayType_Bid {
            get {
                return ResourceManager.GetString("PullingStackingDisplayType_Bid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bid &amp; Ask.
        /// </summary>
        public static string PullingStackingDisplayType_BidAsk {
            get {
                return ResourceManager.GetString("PullingStackingDisplayType_BidAsk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bid/Ask change.
        /// </summary>
        public static string PullingStackingResetWhen_BidAskChange {
            get {
                return ResourceManager.GetString("PullingStackingResetWhen_BidAskChange", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No longer receiving depth data.
        /// </summary>
        public static string PullingStackingResetWhen_NoMoreData {
            get {
                return ResourceManager.GetString("PullingStackingResetWhen_NoMoreData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Range Counter only works on Range bars.
        /// </summary>
        public static string RangeCounterBarError {
            get {
                return ResourceManager.GetString("RangeCounterBarError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Range remaining = {0}.
        /// </summary>
        public static string RangeCounterRemaing {
            get {
                return ResourceManager.GetString("RangeCounterRemaing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Range count = {0}.
        /// </summary>
        public static string RangerCounterCount {
            get {
                return ResourceManager.GetString("RangerCounterCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Range value.
        /// </summary>
        public static string RangeValue {
            get {
                return ResourceManager.GetString("RangeValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ask.
        /// </summary>
        public static string RecentDisplayType_Ask {
            get {
                return ResourceManager.GetString("RecentDisplayType_Ask", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bid.
        /// </summary>
        public static string RecentDisplayType_Bid {
            get {
                return ResourceManager.GetString("RecentDisplayType_Bid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bid &amp; Ask.
        /// </summary>
        public static string RecentDisplayType_BidAsk {
            get {
                return ResourceManager.GetString("RecentDisplayType_BidAsk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bid/Ask change.
        /// </summary>
        public static string RecentResetWhen_BidAskChange {
            get {
                return ResourceManager.GetString("RecentResetWhen_BidAskChange", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Price returns.
        /// </summary>
        public static string RecentResetWhen_PriceReturns {
            get {
                return ResourceManager.GetString("RecentResetWhen_PriceReturns", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Horizontal.
        /// </summary>
        public static string RegionHighlightDirection_Horizontal {
            get {
                return ResourceManager.GetString("RegionHighlightDirection_Horizontal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vertical.
        /// </summary>
        public static string RegionHighlightDirection_Vertical {
            get {
                return ResourceManager.GetString("RegionHighlightDirection_Vertical", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Segment.
        /// </summary>
        public static string RegressionChannelType_Segment {
            get {
                return ResourceManager.GetString("RegressionChannelType_Segment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Standard deviation distance.
        /// </summary>
        public static string RegressionChannelType_StandardDeviation {
            get {
                return ResourceManager.GetString("RegressionChannelType_StandardDeviation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rate of change period.
        /// </summary>
        public static string ROCPeriod {
            get {
                return ResourceManager.GetString("ROCPeriod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Signal line.
        /// </summary>
        public static string RVISignalLine {
            get {
                return ResourceManager.GetString("RVISignalLine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sample name description.
        /// </summary>
        public static string SampleAddOnDescription {
            get {
                return ResourceManager.GetString("SampleAddOnDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hi there!.
        /// </summary>
        public static string SampleAddOnHiThere {
            get {
                return ResourceManager.GetString("SampleAddOnHiThere", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sample AddOn name.
        /// </summary>
        public static string SampleAddOnName {
            get {
                return ResourceManager.GetString("SampleAddOnName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sample Cumulative Profit.
        /// </summary>
        public static string SampleCumProfit {
            get {
                return ResourceManager.GetString("SampleCumProfit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cumulative Profit as a sample of a custom performance metric.
        /// </summary>
        public static string SampleCumProfitDescription {
            get {
                return ResourceManager.GetString("SampleCumProfitDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lower Right Corner.
        /// </summary>
        public static string SampleCustomPlotLowerRightCorner {
            get {
                return ResourceManager.GetString("SampleCustomPlotLowerRightCorner", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Upper Left Corner.
        /// </summary>
        public static string SampleCustomPlotUpperLeftCorner {
            get {
                return ResourceManager.GetString("SampleCustomPlotUpperLeftCorner", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select pattern.
        /// </summary>
        public static string SelectPattern {
            get {
                return ResourceManager.GetString("SelectPattern", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Choose a pattern to detect.
        /// </summary>
        public static string SelectPatternDescription {
            get {
                return ResourceManager.GetString("SelectPatternDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Send alerts.
        /// </summary>
        public static string SendAlerts {
            get {
                return ResourceManager.GetString("SendAlerts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Set true to send alert messages to Alerts Window.
        /// </summary>
        public static string SendAlertsDescription {
            get {
                return ResourceManager.GetString("SendAlertsDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There was a problem calling OnShare with arguments: {0}.
        /// </summary>
        public static string ShareArgsException {
            get {
                return ResourceManager.GetString("ShareArgsException", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Share provider returned a Bad Gateway error: &apos;{0}&apos;.
        /// </summary>
        public static string ShareBadGatewayError {
            get {
                return ResourceManager.GetString("ShareBadGatewayError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Share provider returned a Bad Request error: &apos;{0}&apos;.
        /// </summary>
        public static string ShareBadRequestError {
            get {
                return ResourceManager.GetString("ShareBadRequestError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A WebException was thrown. Status: &apos;{0}&apos; Message: &apos;{1}&apos;.
        /// </summary>
        public static string ShareException {
            get {
                return ResourceManager.GetString("ShareException", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The user could not be found.
        /// </summary>
        public static string ShareFacebookCouldNotRetrieveUser {
            get {
                return ResourceManager.GetString("ShareFacebookCouldNotRetrieveUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Facebook could not verify the token for this user.
        /// </summary>
        public static string ShareFacebookCouldNotVerifyToken {
            get {
                return ResourceManager.GetString("ShareFacebookCouldNotVerifyToken", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to receive response from Facebook.
        /// </summary>
        public static string ShareFacebookNoResult {
            get {
                return ResourceManager.GetString("ShareFacebookNoResult", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Needed Facebook permissions were denied by the user.
        /// </summary>
        public static string ShareFacebookPermissionDenied {
            get {
                return ResourceManager.GetString("ShareFacebookPermissionDenied", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Could not verify Facebook permissions.
        /// </summary>
        public static string ShareFacebookScopesNotFound {
            get {
                return ResourceManager.GetString("ShareFacebookScopesNotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} - Post sent successfully.
        /// </summary>
        public static string ShareFacebookSentSuccessfully {
            get {
                return ResourceManager.GetString("ShareFacebookSentSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Share provider returned a Forbidden message: &apos;{0}&apos;.
        /// </summary>
        public static string ShareForbidden {
            get {
                return ResourceManager.GetString("ShareForbidden", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Share provider returned a Gateway Timeout error:&apos;{0}&apos;.
        /// </summary>
        public static string ShareGatewayTimeoutError {
            get {
                return ResourceManager.GetString("ShareGatewayTimeoutError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The image at location &apos;{0}&apos; cannot be found..
        /// </summary>
        public static string ShareImageNoLongerExists {
            get {
                return ResourceManager.GetString("ShareImageNoLongerExists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Share provider returned an Internal Server Error: &apos;{0}&apos;.
        /// </summary>
        public static string ShareInternalServerError {
            get {
                return ResourceManager.GetString("ShareInternalServerError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There was an error sending a mail message: {0}.
        /// </summary>
        public static string ShareMailException {
            get {
                return ResourceManager.GetString("ShareMailException", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AOL.
        /// </summary>
        public static string ShareMailPreconfiguredAol {
            get {
                return ResourceManager.GetString("ShareMailPreconfiguredAol", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Comcast.
        /// </summary>
        public static string ShareMailPreconfiguredComcast {
            get {
                return ResourceManager.GetString("ShareMailPreconfiguredComcast", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gmail.
        /// </summary>
        public static string ShareMailPreconfiguredGmail {
            get {
                return ResourceManager.GetString("ShareMailPreconfiguredGmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to iCloud.
        /// </summary>
        public static string ShareMailPreconfiguredICloud {
            get {
                return ResourceManager.GetString("ShareMailPreconfiguredICloud", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Manual.
        /// </summary>
        public static string ShareMailPreconfiguredManual {
            get {
                return ResourceManager.GetString("ShareMailPreconfiguredManual", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Outlook.
        /// </summary>
        public static string ShareMailPreconfiguredOutlook {
            get {
                return ResourceManager.GetString("ShareMailPreconfiguredOutlook", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yahoo.
        /// </summary>
        public static string ShareMailPreconfiguredYahoo {
            get {
                return ResourceManager.GetString("ShareMailPreconfiguredYahoo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There was an error sending your message: {0}.
        /// </summary>
        public static string ShareMailSendError {
            get {
                return ResourceManager.GetString("ShareMailSendError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} - Message sent successfully.
        /// </summary>
        public static string ShareMailSentSuccessfully {
            get {
                return ResourceManager.GetString("ShareMailSentSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Share provider returned a {0} error message: &apos;{1}&apos;.
        /// </summary>
        public static string ShareNonSuccessCode {
            get {
                return ResourceManager.GetString("ShareNonSuccessCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Share provider returned a Not Authorized message: &apos;{0}&apos;.
        /// </summary>
        public static string ShareNotAuthorized {
            get {
                return ResourceManager.GetString("ShareNotAuthorized", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Credentials.
        /// </summary>
        public static string ShareServiceParameters {
            get {
                return ResourceManager.GetString("ShareServiceParameters", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Password.
        /// </summary>
        public static string ShareServicePassword {
            get {
                return ResourceManager.GetString("ShareServicePassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There was an exception in the Share service: &apos;{0}&apos;.
        /// </summary>
        public static string ShareServiceSignature {
            get {
                return ResourceManager.GetString("ShareServiceSignature", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User name.
        /// </summary>
        public static string ShareServiceUserName {
            get {
                return ResourceManager.GetString("ShareServiceUserName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to StockTwits account could not be verified.
        /// </summary>
        public static string ShareStockTwitsNoAccount {
            get {
                return ResourceManager.GetString("ShareStockTwitsNoAccount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} - Message sent successfully.
        /// </summary>
        public static string ShareStockTwitsSentSuccessfully {
            get {
                return ResourceManager.GetString("ShareStockTwitsSentSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email.
        /// </summary>
        public static string ShareTextMessageEmail {
            get {
                return ResourceManager.GetString("ShareTextMessageEmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to To configure the Text message via email Share Service you must first set up an Email Share Service..
        /// </summary>
        public static string ShareTextMessageEmailRequired {
            get {
                return ResourceManager.GetString("ShareTextMessageEmailRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There was an error sending message via {0} email service: &apos;{1}&apos;.
        /// </summary>
        public static string ShareTextMessageErrorOnShare {
            get {
                return ResourceManager.GetString("ShareTextMessageErrorOnShare", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MMS address.
        /// </summary>
        public static string ShareTextMessageMmsAddress {
            get {
                return ResourceManager.GetString("ShareTextMessageMmsAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Text message via email.
        /// </summary>
        public static string ShareTextMessageName {
            get {
                return ResourceManager.GetString("ShareTextMessageName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Phone number.
        /// </summary>
        public static string ShareTextMessagePhoneNumber {
            get {
                return ResourceManager.GetString("ShareTextMessagePhoneNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AT&amp;T.
        /// </summary>
        public static string ShareTextMessagePreconfiguredAtt {
            get {
                return ResourceManager.GetString("ShareTextMessagePreconfiguredAtt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Manual.
        /// </summary>
        public static string ShareTextMessagePreconfiguredManual {
            get {
                return ResourceManager.GetString("ShareTextMessagePreconfiguredManual", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sprint.
        /// </summary>
        public static string ShareTextMessagePreconfiguredSprint {
            get {
                return ResourceManager.GetString("ShareTextMessagePreconfiguredSprint", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to T-Mobile.
        /// </summary>
        public static string ShareTextMessagePreconfiguredTMobile {
            get {
                return ResourceManager.GetString("ShareTextMessagePreconfiguredTMobile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Verizon.
        /// </summary>
        public static string ShareTextMessagePreconfiguredVerizon {
            get {
                return ResourceManager.GetString("ShareTextMessagePreconfiguredVerizon", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} - Text message sent.
        /// </summary>
        public static string ShareTextMessageSentSuccessfully {
            get {
                return ResourceManager.GetString("ShareTextMessageSentSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SMS address.
        /// </summary>
        public static string ShareTextMessageSmsAddress {
            get {
                return ResourceManager.GetString("ShareTextMessageSmsAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Share provider returned a TooManyRequests message: &apos;{0}&apos;.
        /// </summary>
        public static string ShareTooManyRequests {
            get {
                return ResourceManager.GetString("ShareTooManyRequests", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} - Tweet sent successfully.
        /// </summary>
        public static string ShareTwitterSentSuccessfully {
            get {
                return ResourceManager.GetString("ShareTwitterSentSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show ask line.
        /// </summary>
        public static string ShowAskLine {
            get {
                return ResourceManager.GetString("ShowAskLine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show bid line.
        /// </summary>
        public static string ShowBidLine {
            get {
                return ResourceManager.GetString("ShowBidLine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show close.
        /// </summary>
        public static string ShowClose {
            get {
                return ResourceManager.GetString("ShowClose", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show high.
        /// </summary>
        public static string ShowHigh {
            get {
                return ResourceManager.GetString("ShowHigh", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show last line.
        /// </summary>
        public static string ShowLastLine {
            get {
                return ResourceManager.GetString("ShowLastLine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show low.
        /// </summary>
        public static string ShowLow {
            get {
                return ResourceManager.GetString("ShowLow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show open.
        /// </summary>
        public static string ShowOpen {
            get {
                return ResourceManager.GetString("ShowOpen", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show pattern count.
        /// </summary>
        public static string ShowPatternCount {
            get {
                return ResourceManager.GetString("ShowPatternCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Set true to display on chart the count of patterns found.
        /// </summary>
        public static string ShowPatternCountDescription {
            get {
                return ResourceManager.GetString("ShowPatternCountDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show percent.
        /// </summary>
        public static string ShowPercent {
            get {
                return ResourceManager.GetString("ShowPercent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Signal period.
        /// </summary>
        public static string SignalPeriod {
            get {
                return ResourceManager.GetString("SignalPeriod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Slow.
        /// </summary>
        public static string Slow {
            get {
                return ResourceManager.GetString("Slow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Slow limit.
        /// </summary>
        public static string SlowLimit {
            get {
                return ResourceManager.GetString("SlowLimit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Slow period.
        /// </summary>
        public static string SlowPeriod {
            get {
                return ResourceManager.GetString("SlowPeriod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Small area color.
        /// </summary>
        public static string SmallAreaColor {
            get {
                return ResourceManager.GetString("SmallAreaColor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Smooth.
        /// </summary>
        public static string Smooth {
            get {
                return ResourceManager.GetString("Smooth", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Smoothing.
        /// </summary>
        public static string Smoothing {
            get {
                return ResourceManager.GetString("Smoothing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to D.
        /// </summary>
        public static string StochasticsD {
            get {
                return ResourceManager.GetString("StochasticsD", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to K.
        /// </summary>
        public static string StochasticsK {
            get {
                return ResourceManager.GetString("StochasticsK", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sentiment:.
        /// </summary>
        public static string StockTwitsSentiment {
            get {
                return ResourceManager.GetString("StockTwitsSentiment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Choose Bearish, Neutral, or Bullish for this message.
        /// </summary>
        public static string StockTwitsSentimentDescription {
            get {
                return ResourceManager.GetString("StockTwitsSentimentDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to StockTwits.
        /// </summary>
        public static string StockTwitsServiceName {
            get {
                return ResourceManager.GetString("StockTwitsServiceName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  Sent by NinjaTrader.
        /// </summary>
        public static string StockTwitsSignature {
            get {
                return ResourceManager.GetString("StockTwitsSignature", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Strength.
        /// </summary>
        public static string Strength {
            get {
                return ResourceManager.GetString("Strength", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SuperDOM column &apos;{0}&apos;: Error on calling &apos;{1}&apos; method: {2}.
        /// </summary>
        public static string SuperDomColumnException {
            get {
                return ResourceManager.GetString("SuperDomColumnException", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Swing high.
        /// </summary>
        public static string SwingHigh {
            get {
                return ResourceManager.GetString("SwingHigh", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Swing low.
        /// </summary>
        public static string SwingLow {
            get {
                return ResourceManager.GetString("SwingLow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}.SwingHighBar: barsAgo must be greater/equal 0 but was {1}.
        /// </summary>
        public static string SwingSwingHighBarBarsAgoGreaterEqual {
            get {
                return ResourceManager.GetString("SwingSwingHighBarBarsAgoGreaterEqual", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}.SwingHighBar: barsAgo out of valid range 0 through {1}, was {2}..
        /// </summary>
        public static string SwingSwingHighBarBarsAgoOutOfRange {
            get {
                return ResourceManager.GetString("SwingSwingHighBarBarsAgoOutOfRange", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}.SwingHighBar: instance must be greater/equal 1 but was {1}.
        /// </summary>
        public static string SwingSwingHighBarInstanceGreaterEqual {
            get {
                return ResourceManager.GetString("SwingSwingHighBarInstanceGreaterEqual", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}.SwingLowBar: barsAgo must be greater/equal 0 but was {1}.
        /// </summary>
        public static string SwingSwingLowBarBarsAgoGreaterEqual {
            get {
                return ResourceManager.GetString("SwingSwingLowBarBarsAgoGreaterEqual", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}.SwingLowBar: barsAgo out of valid range 0 through {1}, was {2}..
        /// </summary>
        public static string SwingSwingLowBarBarsAgoOutOfRange {
            get {
                return ResourceManager.GetString("SwingSwingLowBarBarsAgoOutOfRange", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}.SwingLowBar: instance must be greater/equal 1 but was {1}.
        /// </summary>
        public static string SwingSwingLowBarInstanceGreaterEqual {
            get {
                return ResourceManager.GetString("SwingSwingLowBarInstanceGreaterEqual", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to T count.
        /// </summary>
        public static string TCount {
            get {
                return ResourceManager.GetString("TCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Text color.
        /// </summary>
        public static string TextColor {
            get {
                return ResourceManager.GetString("TextColor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Text font.
        /// </summary>
        public static string TextFont {
            get {
                return ResourceManager.GetString("TextFont", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select font, style, size to display on chart.
        /// </summary>
        public static string TextFontDescription {
            get {
                return ResourceManager.GetString("TextFontDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bottom left.
        /// </summary>
        public static string TextPosition_BottomLeft {
            get {
                return ResourceManager.GetString("TextPosition_BottomLeft", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bottom right.
        /// </summary>
        public static string TextPosition_BottomRight {
            get {
                return ResourceManager.GetString("TextPosition_BottomRight", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Center.
        /// </summary>
        public static string TextPosition_Center {
            get {
                return ResourceManager.GetString("TextPosition_Center", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Top left.
        /// </summary>
        public static string TextPosition_TopLeft {
            get {
                return ResourceManager.GetString("TextPosition_TopLeft", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Top right.
        /// </summary>
        public static string TextPosition_TopRight {
            get {
                return ResourceManager.GetString("TextPosition_TopRight", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bottom Left.
        /// </summary>
        public static string TextPositionFine_BottomLeft {
            get {
                return ResourceManager.GetString("TextPositionFine_BottomLeft", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bottom Middle.
        /// </summary>
        public static string TextPositionFine_BottomMiddle {
            get {
                return ResourceManager.GetString("TextPositionFine_BottomMiddle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bottom Right.
        /// </summary>
        public static string TextPositionFine_BottomRight {
            get {
                return ResourceManager.GetString("TextPositionFine_BottomRight", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Middle Left.
        /// </summary>
        public static string TextPositionFine_MiddleLeft {
            get {
                return ResourceManager.GetString("TextPositionFine_MiddleLeft", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Middle Right.
        /// </summary>
        public static string TextPositionFine_MiddleRight {
            get {
                return ResourceManager.GetString("TextPositionFine_MiddleRight", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Top Left.
        /// </summary>
        public static string TextPositionFine_TopLeft {
            get {
                return ResourceManager.GetString("TextPositionFine_TopLeft", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Top Middle.
        /// </summary>
        public static string TextPositionFine_TopMiddle {
            get {
                return ResourceManager.GetString("TextPositionFine_TopMiddle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Top Right.
        /// </summary>
        public static string TextPositionFine_TopRight {
            get {
                return ResourceManager.GetString("TextPositionFine_TopRight", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tick Counter only works on bars built with a set number of ticks.
        /// </summary>
        public static string TickCounterBarError {
            get {
                return ResourceManager.GetString("TickCounterBarError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tick Count = .
        /// </summary>
        public static string TickCounterTickCount {
            get {
                return ResourceManager.GetString("TickCounterTickCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ticks Remaining = .
        /// </summary>
        public static string TickCounterTicksRemaining {
            get {
                return ResourceManager.GetString("TickCounterTicksRemaining", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current trend line.
        /// </summary>
        public static string TrendLinesCurrentTrendLine {
            get {
                return ResourceManager.GetString("TrendLinesCurrentTrendLine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to TrendLines indicator is not visible with Strategy Analyzer.
        /// </summary>
        public static string TrendLinesNotVisible {
            get {
                return ResourceManager.GetString("TrendLinesNotVisible", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} broken.
        /// </summary>
        public static string TrendLinesTrendLineBroken {
            get {
                return ResourceManager.GetString("TrendLinesTrendLineBroken", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Trend line high.
        /// </summary>
        public static string TrendLinesTrendLineHigh {
            get {
                return ResourceManager.GetString("TrendLinesTrendLineHigh", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Trend line low.
        /// </summary>
        public static string TrendLinesTrendLineLow {
            get {
                return ResourceManager.GetString("TrendLinesTrendLineLow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Trend strength.
        /// </summary>
        public static string TrendStrength {
            get {
                return ResourceManager.GetString("TrendStrength", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Number of bars required to define a trend when a pattern requires a prevailing trend. \nA value of zero will disable trend requirement..
        /// </summary>
        public static string TrendStrengthDescription {
            get {
                return ResourceManager.GetString("TrendStrengthDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Signal.
        /// </summary>
        public static string TRIXSignal {
            get {
                return ResourceManager.GetString("TRIXSignal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Account Successfully Authorized.
        /// </summary>
        public static string TwitterAuthHeader {
            get {
                return ResourceManager.GetString("TwitterAuthHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You have successfully authorized {0} to access your Twitter account..
        /// </summary>
        public static string TwitterAuthText1 {
            get {
                return ResourceManager.GetString("TwitterAuthText1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You may close this window and return to {0}..
        /// </summary>
        public static string TwitterAuthText2 {
            get {
                return ResourceManager.GetString("TwitterAuthText2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Twitter.
        /// </summary>
        public static string TwitterServiceName {
            get {
                return ResourceManager.GetString("TwitterServiceName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  #NinjaTrader.
        /// </summary>
        public static string TwitterSignature {
            get {
                return ResourceManager.GetString("TwitterSignature", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unit.
        /// </summary>
        public static string Unit {
            get {
                return ResourceManager.GetString("Unit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Up bar color.
        /// </summary>
        public static string UpBarColor {
            get {
                return ResourceManager.GetString("UpBarColor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Use high low.
        /// </summary>
        public static string UseHighLow {
            get {
                return ResourceManager.GetString("UseHighLow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User defined close.
        /// </summary>
        public static string UserDefinedClose {
            get {
                return ResourceManager.GetString("UserDefinedClose", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User defined high.
        /// </summary>
        public static string UserDefinedHigh {
            get {
                return ResourceManager.GetString("UserDefinedHigh", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User defined low.
        /// </summary>
        public static string UserDefinedLow {
            get {
                return ResourceManager.GetString("UserDefinedLow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to V factor.
        /// </summary>
        public static string VFactor {
            get {
                return ResourceManager.GetString("VFactor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Volatility period.
        /// </summary>
        public static string VolatilityPeriod {
            get {
                return ResourceManager.GetString("VolatilityPeriod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Volume Counter only works on volume based intervals.
        /// </summary>
        public static string VolumeCounterBarError {
            get {
                return ResourceManager.GetString("VolumeCounterBarError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Volume = .
        /// </summary>
        public static string VolumeCounterVolumeCount {
            get {
                return ResourceManager.GetString("VolumeCounterVolumeCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Volume remaining = .
        /// </summary>
        public static string VolumeCounterVolumeRemaining {
            get {
                return ResourceManager.GetString("VolumeCounterVolumeRemaining", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Volume divisor.
        /// </summary>
        public static string VolumeDivisor {
            get {
                return ResourceManager.GetString("VolumeDivisor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Down volume.
        /// </summary>
        public static string VolumeDown {
            get {
                return ResourceManager.GetString("VolumeDown", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Volume down color.
        /// </summary>
        public static string VolumeDownColor {
            get {
                return ResourceManager.GetString("VolumeDownColor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Volume neutral color.
        /// </summary>
        public static string VolumeNeutralColor {
            get {
                return ResourceManager.GetString("VolumeNeutralColor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Up volume.
        /// </summary>
        public static string VolumeUp {
            get {
                return ResourceManager.GetString("VolumeUp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Volume up color.
        /// </summary>
        public static string VolumeUpColor {
            get {
                return ResourceManager.GetString("VolumeUpColor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Volume.
        /// </summary>
        public static string VOLVolume {
            get {
                return ResourceManager.GetString("VOLVolume", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Width.
        /// </summary>
        public static string Width {
            get {
                return ResourceManager.GetString("Width", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Williams %R.
        /// </summary>
        public static string WilliamsPercentR {
            get {
                return ResourceManager.GetString("WilliamsPercentR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &quot;ZigZag can&apos;t plot any values since the deviation value is too large. Please reduce it.&quot;.
        /// </summary>
        public static string ZigZagDeviationValueError {
            get {
                return ResourceManager.GetString("ZigZagDeviationValueError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}.HighBar: barsAgo out of valid range 0 through {1}, was {2}.
        /// </summary>
        public static string ZigZagHighBarBarsAgoOutOfRange {
            get {
                return ResourceManager.GetString("ZigZagHighBarBarsAgoOutOfRange", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}.HighBar: instance must be greater/equal 1 but was {1}.
        /// </summary>
        public static string ZigZagHighBarInstanceGreaterEqual {
            get {
                return ResourceManager.GetString("ZigZagHighBarInstanceGreaterEqual", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}.LowBar: barsAgo out of valid range 0 through {1}, was {2}.
        /// </summary>
        public static string ZigZagLowBarBarsAgoOutOfRange {
            get {
                return ResourceManager.GetString("ZigZagLowBarBarsAgoOutOfRange", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}.LowBar: instance must be greater/equal 1 but was {1}.
        /// </summary>
        public static string ZigZagLowBarInstanceGreaterEqual {
            get {
                return ResourceManager.GetString("ZigZagLowBarInstanceGreaterEqual", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}.HighBar: barsAgo must be greater/equal 0 but was {1}.
        /// </summary>
        public static string ZigZigHighBarBarsAgoGreaterEqual {
            get {
                return ResourceManager.GetString("ZigZigHighBarBarsAgoGreaterEqual", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}.LowBar: barsAgo must be greater/equal 0 but was {1}.
        /// </summary>
        public static string ZigZigLowBarBarsAgoGreaterEqual {
            get {
                return ResourceManager.GetString("ZigZigLowBarBarsAgoGreaterEqual", resourceCulture);
            }
        }
    }
}
