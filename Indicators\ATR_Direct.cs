using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Windows.Media;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;

namespace NinjaTrader.NinjaScript.Indicators
{
    /// <summary>
    /// Direct ATR indicator for comparison
    /// </summary>
    public class ATR_Direct : Indicator
    {
        private ATR atr;

        protected override void OnStateChange()
        {
            if (State == State.SetDefaults)
            {
                Description = @"Direct ATR indicator for comparison";
                Name = "ATR_Direct";
                Calculate = Calculate.OnBarClose;
                IsOverlay = false;
                DisplayInDataBox = true;
                DrawOnPricePanel = false;
                DrawHorizontalGridLines = true;
                DrawVerticalGridLines = true;
                PaintPriceMarkers = true;
                ScaleJustification = NinjaTrader.Gui.Chart.ScaleJustification.Right;

                // Default parameters
                TimeframeType = BarsPeriodType.Minute;
                TimeframeValue = 5;
                Period = 14;
            }
            else if (State == State.Configure)
            {
                // Add a data series with the specified timeframe
                AddDataSeries(TimeframeType, TimeframeValue);
            }
            else if (State == State.DataLoaded)
            {
                // Add the plot
                AddPlot(new Stroke(Brushes.Green, 2), PlotStyle.Line, "ATR_Direct");

                // Create the ATR indicator for the secondary data series
                atr = ATR(BarsArray[1], Period);

                Print("ATR_Direct initialized");
            }
        }

        protected override void OnBarUpdate()
        {
            try
            {
                // Process on the primary data series (for displaying the plot)
                if (BarsInProgress == 0)
                {
                    // Set the plot value
                    if (atr.IsValidDataPoint(0))
                    {
                        Value[0] = atr[0];

                        // Debug output
                        if (CurrentBar % 100 == 0)
                        {
                            Print("ATR_Direct: Bar " + CurrentBar + ", ATR value: " + atr[0]);
                        }
                    }
                    return;
                }
            }
            catch (Exception ex)
            {
                Print("Error in ATR_Direct.OnBarUpdate: " + ex.Message);
            }
        }

        #region Properties
        [NinjaScriptProperty]
        [Display(Name = "Timeframe Type", Description = "The type of timeframe to use for ATR calculation", Order = 1, GroupName = "Parameters")]
        public BarsPeriodType TimeframeType { get; set; }

        [Range(1, int.MaxValue), NinjaScriptProperty]
        [Display(Name = "Timeframe Value", Description = "The value of timeframe to use for ATR calculation", Order = 2, GroupName = "Parameters")]
        public int TimeframeValue { get; set; }

        [Range(1, int.MaxValue), NinjaScriptProperty]
        [Display(Name = "ATR Period", Description = "The period for ATR calculation", Order = 3, GroupName = "Parameters")]
        public int Period { get; set; }
        #endregion
    }
}

#region NinjaScript generated code. Neither change nor remove.

namespace NinjaTrader.NinjaScript.Indicators
{
	public partial class Indicator : NinjaTrader.Gui.NinjaScript.IndicatorRenderBase
	{
		private ATR_Direct[] cacheATR_Direct;
		public ATR_Direct ATR_Direct(BarsPeriodType timeframeType, int timeframeValue, int period)
		{
			return ATR_Direct(Input, timeframeType, timeframeValue, period);
		}

		public ATR_Direct ATR_Direct(ISeries<double> input, BarsPeriodType timeframeType, int timeframeValue, int period)
		{
			if (cacheATR_Direct != null)
				for (int idx = 0; idx < cacheATR_Direct.Length; idx++)
					if (cacheATR_Direct[idx] != null && cacheATR_Direct[idx].TimeframeType == timeframeType && cacheATR_Direct[idx].TimeframeValue == timeframeValue && cacheATR_Direct[idx].Period == period && cacheATR_Direct[idx].EqualsInput(input))
						return cacheATR_Direct[idx];
			return CacheIndicator<ATR_Direct>(new ATR_Direct(){ TimeframeType = timeframeType, TimeframeValue = timeframeValue, Period = period }, input, ref cacheATR_Direct);
		}
	}
}

namespace NinjaTrader.NinjaScript.MarketAnalyzerColumns
{
	public partial class MarketAnalyzerColumn : MarketAnalyzerColumnBase
	{
		public Indicators.ATR_Direct ATR_Direct(BarsPeriodType timeframeType, int timeframeValue, int period)
		{
			return indicator.ATR_Direct(Input, timeframeType, timeframeValue, period);
		}

		public Indicators.ATR_Direct ATR_Direct(ISeries<double> input , BarsPeriodType timeframeType, int timeframeValue, int period)
		{
			return indicator.ATR_Direct(input, timeframeType, timeframeValue, period);
		}
	}
}

namespace NinjaTrader.NinjaScript.Strategies
{
	public partial class Strategy : NinjaTrader.Gui.NinjaScript.StrategyRenderBase
	{
		public Indicators.ATR_Direct ATR_Direct(BarsPeriodType timeframeType, int timeframeValue, int period)
		{
			return indicator.ATR_Direct(Input, timeframeType, timeframeValue, period);
		}

		public Indicators.ATR_Direct ATR_Direct(ISeries<double> input , BarsPeriodType timeframeType, int timeframeValue, int period)
		{
			return indicator.ATR_Direct(input, timeframeType, timeframeValue, period);
		}
	}
}

#endregion
