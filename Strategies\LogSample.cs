#region USING_DECLARATIONS
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Runtime.CompilerServices;	/// For MemberCallerName
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Gui.Tools;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.Indicators;
using NinjaTrader.NinjaScript.DrawingTools;
using NinjaTrader.NinjaScript.Indicators.VOLAREIndicators;
using NinjaTrader.NinjaScript.SharedPOC;
#endregion

/// This namespace holds Strategies in this folder and is required. Do not change it. 
namespace NinjaTrader.NinjaScript.Strategies
{
	public class LogSample : Strategy
	{
		private const string		VERSION = "1";
		#region GLOBALS
		private int					histBarsCount = -1;
		private int					barRange = -1;

		private bool				oneBarClosed = false;
		#endregion
		
		protected override void OnStateChange()
		{
			if (State == State.SetDefaults)
			{
				Description									= @"Enter the description for your new custom Strategy here.";
				Name										= "LogSample";
				Calculate									= Calculate.OnBarClose;
				EntriesPerDirection							= 1;
				EntryHandling								= EntryHandling.AllEntries;
				IsExitOnSessionCloseStrategy				= true;
				ExitOnSessionCloseSeconds					= 30;
				IsFillLimitOnTouch							= false;
				MaximumBarsLookBack							= MaximumBarsLookBack.TwoHundredFiftySix;
				OrderFillResolution							= OrderFillResolution.Standard;
				Slippage									= 0;
				StartBehavior								= StartBehavior.WaitUntilFlat;
				TimeInForce									= TimeInForce.Gtc;
				TraceOrders									= false;
				RealtimeErrorHandling						= RealtimeErrorHandling.StopCancelClose;
				StopTargetHandling							= StopTargetHandling.PerEntryExecution;
				BarsRequiredToTrade							= 3;
				IsInstantiatedOnEachOptimizationIteration	= true;
				/// Disable IsInstantiatedOnEachOptimizationIteration for performance gains in 
				/// Strategy Analyzer optimizations. See the Help Guide for additional information
			}
			else if (State == State.Configure)
			{
				AddDataSeries(BarsPeriodType.Tick, 1);
			}
			else if (State == State.DataLoaded)
			{
			}
			else if (State == State.Historical)
			{
				histBarsCount = Bars.Count;
			}
			else if (State == State.Realtime)
			{
				Print("\n\n\n-=-  -=-  SWITCHING TO REAL TIME PROCESSING  -=-  -=-  -=-  -=-  -=-  -=-  -=-  -=-  -=-  -=-  -=-\n\n\n\n");
			}
		}
		
		protected override void OnBarUpdate()
		{
			//Print($"                                                           CurrentBar = {CurrentBar}      BarsRequiredToTrade = {BarsRequiredToTrade}");
			if (CurrentBar < BarsRequiredToTrade)
				return;

			if (barRange == -1  &&  CurrentBar > 1  &&  BarsInProgress == 0)
			{
				barRange = (int)((Highs[0][1] - Lows[0][1]) / TickSize);
				Print($"\n\n                       SET barRange = {barRange}\n\n\n");
			}
			
			/// Chart series (Range bars)
			if (BarsInProgress == 0)
			{
				oneBarClosed = true;
			}
			
			/// Tick series
			/// Need to wait for one bar to close (BiP == 0), so IsFirst/IsLast will work (historical will trigger)
			else if (BarsInProgress == 1  &&  oneBarClosed  &&  State == State.Realtime)
			{
				//Print($"(BarsInProgress == 1), Tick series Time[0] = {Time[0].ToString("HH:mm:ss")}             oneBarClosed = {oneBarClosed}");

				/// For some reason, when I get a first or last tick, according to the log, it seems to 
				/// call OnBarUpdate several times for each one, where the 'Now' time is the same, 
				/// e.g. 02:42:40.777458.  So I need a way to limit it to just once, thus this:
				if (IsFirstCandleTick())
				{
					Print($"\n\n\n\n        ==  @  ==   @  ==  @  ==  @  ==  @  ==  @  ==  @  ==  @  ==  @  ==  @  ==  @  ==  @  ==  @  == \n");
					Print($"          IS FIRST TICK OF NEW BAR AFTER CANDLE[1] TIME = {Times[0][1].ToString("HH:mm:ss")}, Time NOW = {DateTime.Now.ToString("HH:mm:ss.ffffff")}\n");
					Print($"        ==  @  ==   @  ==  @  ==  @  ==  @  ==  @  ==  @  ==  @  ==  @  ==  @  ==  @  ==  @  ==  @  == \n\n\n\n");
				}
				else if (IsLastCandleTick())
				{
					Print($"\n\n\n\n        &&  *  &&   *  &&  *  &&  *  &&  *  &&  *  &&  *  &&  *  &&  *  &&  *  &&  *  &&  *  &&  *  && \n");
					Print($"          IS LAST TICK OF BAR AFTER CANDLE[1] TIME = {Times[0][1].ToString("HH:mm:ss")}, Time NOW = {DateTime.Now.ToString("HH:mm:ss.ffffff")}\n");
					Print($"        &&  *  &&   *  &&  *  &&  *  &&  *  &&  *  &&  *  &&  *  &&  *  &&  *  &&  *  &&  *  &&  *  && \n\n\n\n");
				}
			}			
		}
		
		private bool IsFirstCandleTick()
		{
			/// Must wait for one bar to close to establish range bar height - historical will do 
			if (!oneBarClosed)	return false;
			
			/// [May be] called from tick series, so specify to use chart bars
			return (Highs[0][0] == Lows[0][0]);
		}

		private bool IsLastCandleTick()
		{
			/// [May be] called from tick series, so specify to use chart bars
			/// Must wait for one bar to close - historical will do 
			if (!oneBarClosed)	return false;

			double priceNow = Closes[1][0];
			bool last = false;
			double barCurrHeight = (Highs[0][0] - Lows[0][0]) / TickSize;
			if (barCurrHeight == barRange)
			{
				Print($"Highs[0][0] ({Highs[0][0]}) - Lows[0][0] ({Lows[0][0]}) / TickSize ({barCurrHeight}) == barRange ({barRange})    priceNow = {priceNow}");
				if (priceNow == Highs[0][0]  ||  priceNow == Lows[0][0])
				{
					Print($"barCurrHeight ({barCurrHeight}) == barRange ({barRange})   &&   Close ({priceNow}) = High ({Highs[0][0]}) | Close ({priceNow}) = Low ({Lows[0][0]})");
					return true;
				}
			}
			return false;
		}
	}
}
