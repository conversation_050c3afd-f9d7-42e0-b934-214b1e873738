#region Using declarations
using System;
using Npgsql;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Net.Http;			/// Added for HttpClient	
using System.Net.Http.Headers;	/// Added for AuthenticationHeaderValue
using Newtonsoft.Json;			/// Added for automatic parsing of http response
using Npgsql;					/// Added for Benoit new signal get method Sep2024

using System.Linq;
using System.Runtime.CompilerServices;	/// For MemberCallerName
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Gui.Tools;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.Indicators;
using NinjaTrader.NinjaScript.Strategies;
using NinjaTrader.NinjaScript.DrawingTools;
#endregion

/// This namespace holds Strategies in this folder and is required. Do not change it. 
namespace NinjaTrader.NinjaScript.Strategies
{
	public class RazorLight : Strategy
	{
		#region GLOBALS
		
		private double 			beTriggered1Price = 0;
		private double 			beTriggered2Price = 0;

		private int				firstEntryBarIdx = -1;
		private int				firstEntryBarsAgo = -1;
		private int				signalSum = 0;
		private int				fakeSignalSum = 0;
		
		private string 			signalDir = "None";
		private string 			lastTradeDir = "None";
		private string 			lastDashboard = "";
		private string 			lastLogMsg = "";
		private string 			lastCaller = "";
		private string			lastLogTime = "";
		private string			chartInstrument;
		
		private bool 			entriesEnabled = true;
		private bool			longOn = true;
		private bool			shortOn = true;
		private bool			userRequestedExit = false;
		private bool			logAllSignals = false;
		
		/// These variables were taken from NinjaTrader sample called UnmanagedTemplate
		private Order 			entryOrder = null;
		private Order 			slOrder = null;
		private Order 			tpOrder = null;
		private string			oco;
		private bool			stopsSubmitted = false;
		
		//private List<double>	tradeSetsPnL;
		private MACD 			macd;
		private ATR 			atr;
		
		private System.Windows.Controls.Button	shortButton;
		private System.Windows.Controls.Button	longButton;
		private System.Windows.Controls.Button	exitButton;
		private System.Windows.Controls.Button	logSignalsButton;
		private System.Windows.Controls.Button	increaseSum;
		private System.Windows.Controls.Button	decreaseSum;
		private System.Windows.Controls.Grid	myGrid;
		#endregion
		
		protected override void OnStateChange()
		{
			#region State Change Info
			/// By printing out "State", dicovered this order:
			/*
				OnStateChange(), State = SetDefaults	// 1st clone for listing the strategies available in UI
				OnStateChange(), State = Configure
				OnStateChange(), State = Configure
				OnStateChange(), State = SetDefaults	// 2nd clone for configuring the one selected
				OnStateChange(), State = Terminated
			
				// Then the real one that we care about after we apply the settings:
				OnStateChange(), State = Configure		// Cloned again, so already has defaults set
				OnStateChange(), State = DataLoaded
				OnStateChange(), State = Historical
				OnStateChange(), State = Transition
				OnStateChange(), State = Realtime
				OnStateChange(), State = Terminated		// would be called again when terminated
			*/
			#endregion
			
			/// Set Input Defaults
			if (State == State.SetDefaults)
			{
				Description						= @"Strategy to automatically trade Razor";
				Name							= "RazorLight";
				Calculate						= Calculate.OnBarClose;
				EntriesPerDirection				= 1000;
				EntryHandling					= EntryHandling.AllEntries;
				IsExitOnSessionCloseStrategy	= true;
				ExitOnSessionCloseSeconds		= 2700;		// 45 mins: Exit @ 13:15 PST
				IsFillLimitOnTouch				= false;
				MaximumBarsLookBack				= MaximumBarsLookBack.TwoHundredFiftySix;
				OrderFillResolution				= OrderFillResolution.Standard;
				Slippage						= 0;
				StartBehavior					= StartBehavior.WaitUntilFlat;
				TimeInForce						= TimeInForce.Gtc;
				TraceOrders						= true;
				RealtimeErrorHandling			= RealtimeErrorHandling.StopCancelClose;
				//StopTargetHandling			= StopTargetHandling.ByStrategyPosition;	// method from managed Razor version
				StopTargetHandling				= StopTargetHandling.PerEntryExecution;
				BarsRequiredToTrade				= 20;
				IsUnmanaged 					= true;
				IsAdoptAccountPositionAware 	= true;		// Not sure if this is correct/needed
				
				
				/// Disable this property for performance gains in Strategy Analyzer optimizations
				/// See the Help Guide for additional information
				IsInstantiatedOnEachOptimizationIteration	= true;
				
				/// Optional scaler for Occam signal order quantity
				QuantityMult			= 1;
				MaxContracts			= 10;
				
				/// Override for Prevailing market dir at open
				LastTradeDir			= "None";

				/// StopLoss & TakeProfit
				StopLoss				= 100;
				TakeProfit				= 101;
				
				/// Move to BreakEven
				BETrigger1				= 0;
				BEOffset1				= 0;
				BETrigger2				= 0;
				BEOffset2				= 0;
				
				StrategyName			= "Razor";
				DisplayOCD				= true;
				DisableLogging			= false;
				UseOutput2				= false;
				UniqueID				= "1";
			}
			
			/// Initialize Member Variables
			else if (State == State.Configure)
			{
				chartInstrument = this.Instrument.FullName;
				AddDataSeries(BarsPeriodType.Tick, 1);
			}
			
			else if (State == State.DataLoaded)
			{
			}
			
			else if (State == State.Historical)
			{
				// Init user override of lasttradeDir
				// Not sure this is the best place for this, but seems to work
				if (LastTradeDir.ToUpper() == "LONG")		lastTradeDir = "Long";
				else
				if (LastTradeDir.ToUpper() == "SHORT")		lastTradeDir = "Short";
				
				if (ChartControl != null)
				{
					ChartControl.Dispatcher.InvokeAsync(() =>
					{
						CreateButtons();
					});
				}	
			}
			
			else if (State == State.Terminated)
			{
				if (ChartControl != null)
				{
					ChartControl.Dispatcher.InvokeAsync(() =>
					{
						DisposeButtons();
					});
				}
			}
		}

		protected override void OnExecutionUpdate(Cbi.Execution execution, string executionId, double price, int quantity, 
												  Cbi.MarketPosition marketPosition, string orderId, DateTime time)
		{
			Log($"execution.IsEntryStrategy = {execution.IsEntryStrategy},   execution.Order.OrderAction = {execution.Order.OrderAction},   marketPosition = {marketPosition},   quantity (input parameter) = {quantity},   Position.Quantity = {Position.Quantity},   execution.Order.OrderAction = {execution.Order.OrderAction}");
			
			/// This was taken from a NinjaTrader sample called UnmanagedTemplate
			// We advise monitoring OnExecution to trigger submission of stop/target orders instead of OnOrderUpdate()
			// since OnExecution() is called after OnOrderUpdate() which ensures your strategy has received the execution
			// which is used for internal signal tracking.
			
			double m = (Position.MarketPosition == MarketPosition.Long) ? 1.0 : -1.0;
			double avePrice = AvePrice();
			
			/// Handle actions for this execution being an entry order (execution.Order.OrderAction.Buy or SellShort)
			if (execution.IsEntryStrategy)
			{
				if (StopLoss > 0  ||  TakeProfit > 0)
				{
					Log($"StopLoss > 0 ({StopLoss})  ||  TakeProfit > 0 ({TakeProfit})");
					/// [Only] if we are using both stops do we need to link OCO
					oco = "";
					if (StopLoss > 0  &&  TakeProfit > 0)
					{
						if (!stopsSubmitted)
						{
							/// TakeProfit is required (if using any TP), TakeProfit 1 & 2 are incidental
							if (State == State.Historical)
								oco = DateTime.Now.ToString() + "_" + CurrentBar + "_" + "Exits";
							else
								oco = GetAtmStrategyUniqueId() + "_" + "Exits";
							Log($"Set OCO = {oco}");
						}
						else if (slOrder != null)
						{
							oco = slOrder.Oco;
							Log($"Fetched existing OCO = {oco}");
						}
						else
							Log($"Could not fetch OCO because slOrder == null");
					}
					
					if (entryOrder != null  &&  entryOrder == execution.Order)
					{
						Log($"IsEntryStrategy = True, execution.Order.OrderState = {execution.Order.OrderState}");
						
						/// Not specifically handing this case; assuming this method will be called again w/ order state == filled
						if (execution.Order.OrderState == OrderState.PartFilled)
							Log($"   ENTRY ORDER ONLY PARTIALLY FILLED; execution.Order.Filled (Qty) = {execution.Order.Filled}");
						
						else if (execution.Order.OrderState == OrderState.Cancelled)
						{
							/// If the order was somehow cancelled, then we need to just and position that partially opened
							Log($"   ENTRY ORDER CANCELLED; dir = {marketPosition.ToString()}, execution.Order.Filled = {execution.Order.Filled}");
							if (execution.Order.Filled > 0)
							{
								Log($"Partial order was filled, so closing now");
								ClosePosition("Partial Fill", marketPosition.ToString(), execution.Order.Filled);
							}
						}
						else if (execution.Order.OrderState == OrderState.Filled)
						{
							if (!stopsSubmitted)
							{
								/// This used to check that slOrder & tpOrders were null, because we were initially setting, 
								/// but it won't place the stops until order is filled, so no need to check for that. 
								var action = (marketPosition == MarketPosition.Long) ? OrderAction.Sell : OrderAction.BuyToCover;

								Log($"INIT PLACEMENT OF SL & TP; Action = {action.ToString()}");
								if (StopLoss > 0)
								{
									Log($"Submitting StopLoss for {Position.Quantity} contracts");
									SubmitOrderUnmanaged(0, action, OrderType.StopMarket, Position.Quantity, 0, avePrice - m * StopLoss * TickSize, oco, "StopLoss");
								}
								if (TakeProfit > 0)
								{
									Log($"Submitting TakeProfit for {Position.Quantity} contracts");
									SubmitOrderUnmanaged(0, action, OrderType.Limit, Position.Quantity, avePrice + m * TakeProfit * TickSize, 0, oco, "TakeProfit");
								}
								stopsSubmitted = true;
							}
							/// Stops have been submitted, so this must be an entry order that is 
							/// adding contracts.  Therefore, we need to update stops
							else
							{
								Log($"NEW ENTRY: Moving StopLoss and TakeProfit; avePrice = {avePrice}, newSL = {avePrice - m * StopLoss * TickSize}, newTP = {avePrice + m * TakeProfit * TickSize}");
								ModifyStopLoss(avePrice - m * StopLoss * TickSize);
								ModifyTakeProfit(avePrice + m * TakeProfit * TickSize);
							}
						}
					}
					else
						Log($"entryOrder != execution.Order, execution.Order.OrderState = {execution.Order.OrderState}");
				}
			}
			
			//if (execution.Order.OrderAction == OrderAction.Sell  ||  execution.Order.OrderAction == OrderAction.BuyToCover)
			else	// Is exit strategy
			{
				/// If we did NOT just close *entire* position, update SL & TP
				if (Position.Quantity > 0)
				{
//					ChangeOrder(slOrder, Position.Quantity, 0, slOrder.StopPrice);
					Log($"PARTIAL EXIT: Changing SL & TP quantity; SL = {slOrder.StopPrice}, TP = {tpOrder.StopPrice}, new Qty should be {Position.Quantity}");
					if (slOrder != null)	ModifyStopLoss(slOrder.StopPrice);
					if (tpOrder != null)	ModifyTakeProfit(tpOrder.LimitPrice);
				}
				else
				{
					/// Cancel any stops that are left open. SL & TP3 OCO, but check all to be sure
					if (slOrder != null  ||  tpOrder != null)
						CancelStops();
					
					/// Reset some variables
					Log($"Position.Quantity == 0; checking DailyMaxLoss & DailyMinTarget..");
					stopsSubmitted = false;
					beTriggered1Price = beTriggered2Price = 0;
					firstEntryBarIdx = -1;
					Log($"RESETTING ALL ORDER VARS BACK TO NULL");
					entryOrder = slOrder = tpOrder = null;
				}
			}
		}
		
		protected override void OnOrderUpdate(Cbi.Order order, double limitPrice, double stopPrice, int quantity, 
											  int filled, double averageFillPrice, Cbi.OrderState orderState, 
											  DateTime time, Cbi.ErrorCode error, string comment)
        {
			Log($"order.Name = {order.Name}, order.OrderAction = {order.OrderAction.ToString()}, order.OrderType = {order.OrderType.ToString()}, Position.Quantity = {Position.Quantity}");
			
			/// Assign Order objects here
			/// This is more reliable than assigning Order objects in OnBarUpdate, as the assignment 
			/// is not guaranteed to be complete if it is referenced immediately after submitting
			if (order.Name == "Entry")
			{
				//Log($"Setting entryOrder");
				entryOrder = order;
			}
			else if (orderState != OrderState.Filled)
			{
				if (order.Name == "StopLoss")
				{
					Log($"Setting slOrder");
					slOrder = order;
				}
				else if (order.Name == "TakeProfit")
				{
					Log($"Setting tpOrder");
					tpOrder = order;
				}
			}
		}

		protected override void OnBarUpdate()
		{
			if (CurrentBars[0] < BarsRequiredToTrade)
				return;
			
			/// No point in trying to do historical trades without the signals
			if (State == State.Historical)
			{
				return;
			}
			// Don't waste time on OCD for historical
			else 
				ManageOCD();	// Update the On-Chart Display
			
			/// Entered on close of each candle of chart TF where strategy is applied (first data series)
			bool highFractal, lowFractal;
			if (BarsInProgress == 0)
			{
				if (Bars.IsFirstBarOfSession)
				{
					/// This is not useful for us because we do not let the EA run 24/5 such that this would trigger at 6pm ET
					/// And more importantly, we do not do anthing with historical (yet); that is where this is important:
					/// to reset some daily things that will be done with running over historical bars.
					lastTradeDir = "None";
					Log($"\nNEW SESSION (DAY) - RESET EVERYTHING\n");
					
					/// This code was in a sample showing how to get all previous profit made 
					/// by this strategy in the past, but SINCE IT WAS APPLIED TO THE CHART
					//int priorTradesCount = SystemPerformance.AllTrades.Count;
					//double priorTradesCumProfit = SystemPerformance.AllTrades.TradesPerformance.Currency.CumProfit;
					/// This is not useful for us because we do not have historical signals [yet] (see comment at top)
				}
				
				/// Set up how many bars ago was the entry candle
				firstEntryBarsAgo = (firstEntryBarIdx != -1) ? CurrentBar - firstEntryBarIdx : -1;
				
				/// If not in market and not in-session (or limit hit), no need to go further
				if (Position.MarketPosition == MarketPosition.Flat)
					return;
			}

			/// Entered on each tick (third data series)
			else if (BarsInProgress == 1)
			{
				Print("............tick");

				/// Assign whether we are active for continuation trades
				#region MAIN SIGNAL ENTRY
				signalSum = 0;
				signalDir = "None";
				if (entriesEnabled)
				{
					// Fake an entry?
					if (signalSum == 0  &&  fakeSignalSum != 0)
					{
						signalSum = fakeSignalSum;
						fakeSignalSum = 0;
						Log($"FAKE SIGNAL GENERATED, SUM = {signalSum}");

						// Reset buttons
						UpdateIncreaseSumButton("Increase Sum", Brushes.Yellow);
						UpdateDecreaseSumButton("Decrease Sum", Brushes.Yellow);
					}
					
					// Zero means change nothing
					if (signalSum != 0)
					{
						/// Convert current position to signed int (neg for short, pos for long)
						int posSum = 0;
						string posDir = Position.MarketPosition.ToString();
						if (posDir != "Flat")
						{
							int m = (posDir == "Long") ? 1 : -1;
							posSum = Position.Quantity * m;
						}
						
						int newSum = posSum + signalSum;
						bool needsOpen = false;
						string logStr = "";
						int qty = QuantityMult * Math.Abs(signalSum);

						signalDir = (newSum > 0) ? "Long" : (newSum < 0) ? "Short" : "Flat";
						Log($"Occam Signal Received @ {DateTime.Now.ToString()} - New Direction = {signalDir}, Signal Sum = {signalSum}, old Pos Sum = {posSum}, New Pos Sum = {newSum}, QuantityMult = {QuantityMult}");
						
						/// A sum of zero means to go flat
						if (signalDir == "Flat")	// Implies that current pos is not flat
						{
							Log($"\nCLOSING {posDir} POSITION\n");
							ClosePosition("SigExit", posDir);
						}
						
						/// Handle position size modification, but no reversal
						else if (posDir == signalDir)
						{
							/// Open additional contracts
							if ((posDir == "Long"   &&  signalSum > 0)	||  (posDir == "Short"  &&  signalSum < 0))
							{
								logStr = $"\nSignal to Increase: ENTERING ADDITIONAL {qty} CONTRACTS {signalDir.ToUpper()}\n";
								needsOpen = true;
							}
							/// Close partial contracts
							else if ((posDir == "Long"   &&  signalSum < 0)  ||  (posDir == "Short"  &&  signalSum > 0))
							{
								Log($"\nSignal to Reduce: CLOSING {Math.Abs(QuantityMult * signalSum)} {signalDir.ToUpper()} CONTRACTS (out of {Position.Quantity})\n");
								ClosePosition("SigReduce", posDir, qty);
							}
						}
						
						/// Reverse or (open new position if flat)
						else if (posDir != signalDir)
						{
							string rev = (posDir == "Flat") ? "POSITION IS FLAT; " : $"REVERSING {Position.Quantity} {posDir.ToUpper()} POSITION & ";
							qty = QuantityMult * Math.Abs(newSum);

							logStr = $"\nSignal to Flip or Open: {rev}ENTERING NEW {signalDir.ToUpper()} POSITION FOR {qty} CONTRACTS\n";
							needsOpen = true;
						}
						
						/// Open new pos (or reverse)
						if (needsOpen)
						{
							// Do not trade more than user-defined max
							if (Position.Quantity + qty > MaxContracts)
							{
								Log($"WARNING!! {Position.Quantity} Contracts already open, and max set to {MaxContracts}, so we cannot open {qty} more;  opening {MaxContracts - Position.Quantity} instead");
								qty = MaxContracts - Position.Quantity;
							}
							
							Log(logStr);
							Log($"Before EntryLong/Short(), Position: {Position.MarketPosition}, Quantity: {Position.Quantity}");
							Log($"Attempting to enter {signalDir} position for {qty} contracts");
							PlaceMarketOrder(signalDir, qty);
							lastTradeDir = signalDir;
						}
					}
				}
				#endregion
				
				/// No need to check anything further if no position open
				if (Position.MarketPosition == MarketPosition.Flat)
					return;
				
				#region BREAK EVEN
		        double newStopPrice = 0;
				double oldPrice = 0;
				
				/// Get existing stop price if exists
				/// Working seems to not mean 'active', but that it's still in exchange queue. Accepted means it's active.
				if (slOrder != null  &&  (slOrder.OrderState == OrderState.Working  ||  slOrder.OrderState == OrderState.Accepted))
					oldPrice = slOrder.StopPrice;

				double avePrice = AvePrice();
				if (Position.MarketPosition == MarketPosition.Long)
				{
			        /// Calculate new stop price
					if (beTriggered1Price == 0  &&  BETrigger1 != 0  &&  High[0] >= avePrice + BETrigger1*TickSize)
						beTriggered1Price = newStopPrice = avePrice + BEOffset1*TickSize;
					else if (beTriggered2Price == 0  &&  BETrigger2 != 0  &&  High[0] >= avePrice + BETrigger2*TickSize)
						beTriggered2Price = newStopPrice = avePrice + BEOffset2*TickSize;

					/// Abort if new price does not tighten stop
					if (newStopPrice != 0  &&  oldPrice != 0  &&  newStopPrice <= oldPrice)
						newStopPrice = 0;
				}
				else if (Position.MarketPosition == MarketPosition.Short)
				{
			        /// Calculate new stop price
					if (beTriggered1Price == 0  &&  BETrigger1 != 0  &&  Low[0] <= avePrice - BETrigger1*TickSize)
						beTriggered1Price = newStopPrice = avePrice - BEOffset1*TickSize;
					else if (beTriggered2Price == 0  &&  BETrigger2 != 0  &&  Low[0] <= avePrice - BETrigger2*TickSize)
						beTriggered2Price = newStopPrice = avePrice - BEOffset2*TickSize;
					
					/// Abort if new price does not tighten stop
					if (newStopPrice != 0  &&  oldPrice != 0  &&  newStopPrice >= oldPrice)
						newStopPrice = 0;
				}
				if (newStopPrice != 0  &&  Position.MarketPosition != MarketPosition.Flat)
				{
					Log($"MoveToBE TRIGGERED: newStopPrice set to {newStopPrice}.  Old stop price = {oldPrice}");
					ModifyStopLoss(newStopPrice);
				}
				#endregion
			}
		}
		
		#region TRADE MANAGEMENT
		private void PlaceMarketOrder(string dir, int qty)
		{
			Log($"Placing market order, dir = {dir}, qty = {qty}");
			if (Position.MarketPosition != MarketPosition.Flat  &&  Position.MarketPosition.ToString() != dir)
			{
				Log($"Existing position is {Position.MarketPosition.ToString()}, so closing position first");
				ClosePosition("CloseOnRev");
				
				// Assume the close worked; need to reset this NOW because it's multi-threaded
				stopsSubmitted = false;
			}
			var action = (dir == "Long") ? OrderAction.Buy : OrderAction.SellShort;
			SubmitOrderUnmanaged(0, action, OrderType.Market, qty, 0, 0, "", "Entry");
		}
		
		private void ClosePosition(string name="Close", string dir="", int qty=0)
		{
			if (Position.MarketPosition == MarketPosition.Flat)
				return;
			
			if (qty == 0)	qty = Position.Quantity;
			if (dir == "")	dir = Position.MarketPosition.ToString();
			var action = (dir == "Long") ? OrderAction.Sell : OrderAction.BuyToCover;
			
			if (qty == Position.Quantity)
			{
				if (slOrder != null  ||  tpOrder != null)
				{
					Log($"Closing all contracts, and slOrder != null  ||  tpOrder != null, so Cancelling Stops");
					CancelStops();
				}
			}
			Log($"Closing Position: name = {name}, dir = {dir}, qty = {qty}");
			SubmitOrderUnmanaged(0, action, OrderType.Market, qty, 0, 0, "", name);
		}

		private void ModifyStopLoss(double newSL)
		{
			/// If there is no existing SL, we must place the order
			if (slOrder == null)
			{
				oco = (tpOrder != null) ? tpOrder.Oco : "";
				Log($"No SL existed on Long position, so placing new SL order, price @ {newSL}");
				SubmitOrderUnmanaged(0, OrderAction.Sell, OrderType.StopMarket, Position.Quantity, 0, newSL, oco, "StopLoss");
			}
			else
			{
				Log($"Moving SL on Long position to new price @ {newSL}, Position.Quantity = {Position.Quantity}");
				ChangeOrder(slOrder, Position.Quantity, 0, newSL);
			}
		}

		private void ModifyTakeProfit(double newTP)
		{
			/// If there is no existing SL, we must place the order
			if (tpOrder == null)
			{
				oco = (slOrder != null) ? slOrder.Oco : "";
				Log($"No TP existed on Long position, so placing new TP order, price @ {newTP}");
				SubmitOrderUnmanaged(0, OrderAction.Sell, OrderType.Limit, Position.Quantity, newTP, 0, oco, "TakeProfit");
			}
			else
			{
				Log($"Moving TP on Long position to new price @ {newTP}, Position.Quantity = {Position.Quantity}");
				ChangeOrder(tpOrder, Position.Quantity, newTP, 0);
			}
		}
		
		private void CancelStops()
		{
			if (slOrder != null)
			{
				Log($"Cancelling slOrder");
				CancelOrder(slOrder);
				slOrder = null;
			}
			if (tpOrder != null)
			{
				Log($"Cancelling tpOrder");
				CancelOrder(tpOrder);
				tpOrder = null;
			}
		}
		
		private double AvePrice()
		{
			double avePrice = Position.AveragePrice;
			
			/// If ave price does not fall on tick boundary, round up
			int numTicks = (int)(avePrice / TickSize);
			if (numTicks * TickSize != avePrice)
			{
				double prc = Instrument.MasterInstrument.RoundToTickSize(avePrice);
				Log($"\n      AvePrice between ticks, Rounding, from {avePrice} to {prc}\n\n");
				avePrice = prc;
			}
			return(avePrice);
		}
		#endregion
		
		#region DEBUG & OCD
		private void ManageOCD()
		{
			string text = $"\n\n Strategy Name            :   {StrategyName}";
			if (lastTradeDir != "None")
				text += $"\n Last Signal Direction   :   {lastTradeDir}";

			if (lastDashboard == text)
				return;
			
			Draw.TextFixed(this, "OCD", text, TextPosition.TopLeft);	
			lastDashboard = text;
		}
		
		private void Log(string message, [CallerMemberName] string memberName = "")
		{
			if (DisableLogging  ||  lastLogMsg == message)
				return;

			// Set this scripts Print() calls to the second output tab?
			if (UseOutput2)		PrintTo = PrintTo.OutputTab2;
			else				PrintTo = PrintTo.OutputTab1;
			
			string id = "EA #" + UniqueID;
			
			DateTime date = (State == State.Historical) ? Time[0] : DateTime.Now;
			string dateStr = (State == State.Historical) ? date.ToString() : date.ToString(@"HH\:mm\:ss");
			
			if (lastCaller != memberName)
			{
				Print($"\n{id} - {chartInstrument} - [{memberName}]  -  {dateStr}  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -\n");
				Print(message);
			}
			else if (lastLogMsg != message)
			{
				if (lastLogTime != dateStr)	// Output just time if diff time but not new caller
					Print(message + $"   ( {dateStr} )");
				else
					Print(message);
			}

			lastLogMsg = message;
			lastCaller = memberName;
			lastLogTime = dateStr;
		}		
		#endregion
		
		#region BUTTONS
		private void OnMyButtonClick(object sender, RoutedEventArgs rea)
		{
			System.Windows.Controls.Button button = sender as System.Windows.Controls.Button;
			
			Log("\n");
			bool lWasOn = longOn;
			bool sWasOn = shortOn;
			if (button.Name == "longButton")
			{
				if (longOn)
				{
					button.Background = Brushes.Gray;
					button.Content = "Long Disabled";
					longOn = false;
					Log("\nLONG ENTRIES DISABLED!");
				}
				else
				{
					button.Background = Brushes.Green;
					button.Content = "Long Enabled";
					longOn = true;
					Log("\nLong entries Enabled!");
				}
			}
			else if (button.Name == "shortButton")
			{
				if (shortOn)
				{
					button.Background = Brushes.Gray;
					button.Content = "Short Disabled";
					shortOn = false;
					Log("\nSHORT ENTRIES DISABLED!");
				}
				else
				{
					button.Background = Brushes.Crimson;
					button.Content = "Short Enabled";
					shortOn = true;
					Log("\nShort entries Enabled!");
				}
			}
			else if (button.Name == "exitButton")
			{
				if (Position.MarketPosition == MarketPosition.Flat)
				Log("\nUser-Instigated Close of All Open Strategy Positions, but nothing open..");
				else
				{
					Log("\n\n=====================================================");
					Log("User-Instigated Close of All Open Strategy Positions");
					Log("=====================================================\n");
					ExitLong("User Instigated", "Long");
					ExitShort("User Instigated", "Short");
				}
			}
			else if (button.Name == "logSignalsButton")
			{
				logAllSignals = true;
			}
			if (button.Name == "increaseSum")
			{
				fakeSignalSum++;
				Log($"fakeSignalSum increased to: {fakeSignalSum}");
				if (fakeSignalSum > 0)
				{
					button.Content = "Incr, Sum = " + fakeSignalSum;
					button.Background = Brushes.Orange;
				}
				else
				{
					button.Content = "Increase Sum";
					button.Background = Brushes.Yellow;
				}
				if (fakeSignalSum < 0)
				{
					decreaseSum.Content = "Decr, Sum = " + fakeSignalSum;
					decreaseSum.Background = Brushes.Orange;
				}
				else
				{
					decreaseSum.Content = "Decrease Sum";
					decreaseSum.Background = Brushes.Yellow;
				}
			}
			else if (button.Name == "decreaseSum")
			{
				fakeSignalSum--;
				Log($"fakeSignalSum decreased to: {fakeSignalSum}");
				if (fakeSignalSum < 0)
				{
					button.Content = "Decr, Sum = " + fakeSignalSum;
					button.Background = Brushes.Orange;
				}
				else
				{
					button.Content = "Decrease Sum";
					button.Background = Brushes.Yellow;
				}
				if (fakeSignalSum > 0)
				{
					increaseSum.Content = "Incr, Sum = " + fakeSignalSum;
					increaseSum.Background = Brushes.Orange;
				}
				else
				{
					increaseSum.Content = "Increase Sum";
					increaseSum.Background = Brushes.Yellow;
				}
			}
			
			if (!longOn  &&  !shortOn)
				if (lWasOn  ||  sWasOn)
					Log("ALL ENTRIES DISABLED!");
			else if (longOn  &&  shortOn)
				if (!lWasOn  ||  !sWasOn)
					Log("ALL ENTRIES ENABLED!");
			Log("\n");

			entriesEnabled = (longOn  ||  shortOn);
		}
		
		
		protected void CreateButtons()
		{
			if (UserControlCollection.Contains(myGrid))
		        return;
		
			myGrid = new System.Windows.Controls.Grid
	        {
	          Name = "MyCustomGrid",
	          HorizontalAlignment = HorizontalAlignment.Left,
	          VerticalAlignment = VerticalAlignment.Bottom,
	        };
	 	 
	        myGrid.ColumnDefinitions.Add(new System.Windows.Controls.ColumnDefinition());
	        myGrid.ColumnDefinitions.Add(new System.Windows.Controls.ColumnDefinition());
	        myGrid.ColumnDefinitions.Add(new System.Windows.Controls.ColumnDefinition());
			myGrid.RowDefinitions.Add(new System.Windows.Controls.RowDefinition());
			myGrid.RowDefinitions.Add(new System.Windows.Controls.RowDefinition());
			myGrid.ColumnDefinitions[0].Width = new GridLength(80);
			myGrid.ColumnDefinitions[1].Width = new GridLength(80);
			myGrid.ColumnDefinitions[2].Width = new GridLength(80);
			myGrid.RowDefinitions[0].Height = new GridLength(25);
			myGrid.RowDefinitions[1].Height = new GridLength(25);
	 
	        longButton = new System.Windows.Controls.Button
	        {
	          	Name = "longButton",
	          	Content = "Long Enabled",
	          	Foreground = Brushes.White,
	          	Background = Brushes.Green,
				Height = 25,
				Width = 80,
				FontSize = 10,
				HorizontalAlignment = HorizontalAlignment.Center,
				Focusable = false
	        };
			
			shortButton = new System.Windows.Controls.Button
	        {
	          	Name = "shortButton",
	          	Content = "Short Enabled",
	          	Foreground = Brushes.White,
	          	Background = Brushes.Crimson,
				Height = 25,
				Width = 80,
				FontSize = 10,
				HorizontalAlignment = HorizontalAlignment.Center,
				Focusable = false
	        };
	 
			exitButton = new System.Windows.Controls.Button
	        {
	          	Name = "exitButton",
	          	Content = "Exit All",
	          	Foreground = Brushes.White,
	          	Background = Brushes.DarkMagenta,
				Height = 25,
				Width = 80,
				FontSize = 10,
				HorizontalAlignment = HorizontalAlignment.Center,
				Focusable = false
	        };
	 
			logSignalsButton = new System.Windows.Controls.Button
	        {
	          	Name = "logSignalsButton",
	          	Content = "Log Signals",
	          	Foreground = Brushes.White,
	          	Background = Brushes.Blue,
				Height = 25,
				Width = 80,
				FontSize = 10,
				HorizontalAlignment = HorizontalAlignment.Center,
				Focusable = false
	        };
	 
			increaseSum = new System.Windows.Controls.Button
	        {
	          	Name = "increaseSum",
	          	Content = "Increase Sum",
	          	Foreground = Brushes.Black,
	          	Background = Brushes.Yellow,
				Height = 25,
				Width = 80,
				FontSize = 10,
				HorizontalAlignment = HorizontalAlignment.Center,
				Focusable = false
	        };
	 
			decreaseSum = new System.Windows.Controls.Button
	        {
	          	Name = "decreaseSum",
	          	Content = "Decrease Sum",
	          	Foreground = Brushes.Black,
	          	Background = Brushes.Yellow,
				Height = 25,
				Width = 80,
				FontSize = 10,
				HorizontalAlignment = HorizontalAlignment.Center,
				Focusable = false
	        };
		
			longButton.Click += OnMyButtonClick;
			shortButton.Click += OnMyButtonClick;
			exitButton.Click += OnMyButtonClick;
			logSignalsButton.Click += OnMyButtonClick;
			increaseSum.Click += OnMyButtonClick;
			decreaseSum.Click += OnMyButtonClick;
			
	        System.Windows.Controls.Grid.SetColumn(shortButton, 1);
			System.Windows.Controls.Grid.SetRow(shortButton, 0);
			System.Windows.Controls.Grid.SetColumn(longButton, 0);
			System.Windows.Controls.Grid.SetRow(longButton, 0);
			System.Windows.Controls.Grid.SetColumn(exitButton, 0);
			System.Windows.Controls.Grid.SetRow(exitButton, 1);
			System.Windows.Controls.Grid.SetColumn(logSignalsButton, 1);
			System.Windows.Controls.Grid.SetRow(logSignalsButton, 1);
			System.Windows.Controls.Grid.SetColumn(increaseSum, 2);
			System.Windows.Controls.Grid.SetRow(increaseSum, 0);
			System.Windows.Controls.Grid.SetColumn(decreaseSum, 2);
			System.Windows.Controls.Grid.SetRow(decreaseSum, 1);
			
	        myGrid.Children.Add(longButton);
			myGrid.Children.Add(shortButton);
			myGrid.Children.Add(exitButton);
			myGrid.Children.Add(logSignalsButton);
			myGrid.Children.Add(increaseSum);
			myGrid.Children.Add(decreaseSum);
			
	        UserControlCollection.Add(myGrid);
		}
		
		
		public void DisposeButtons() 
		{
			if (myGrid != null)
	        {
				if (longButton != null)
				{
					myGrid.Children.Remove(longButton);
					longButton.Click -= OnMyButtonClick;
					longButton = null;
				}
				if (shortButton != null)
				{
					myGrid.Children.Remove(shortButton);
					shortButton.Click -= OnMyButtonClick;
					shortButton = null;
				}
				if (exitButton != null)
				{
					myGrid.Children.Remove(exitButton);
					exitButton.Click -= OnMyButtonClick;
					exitButton = null;
				}
				if (logSignalsButton != null)
				{
					myGrid.Children.Remove(logSignalsButton);
					logSignalsButton.Click -= OnMyButtonClick;
					logSignalsButton = null;
				}
				if (increaseSum != null)
				{
					myGrid.Children.Remove(increaseSum);
					increaseSum.Click -= OnMyButtonClick;
					increaseSum = null;
				}
				if (decreaseSum != null)
				{
					myGrid.Children.Remove(decreaseSum);
					decreaseSum.Click -= OnMyButtonClick;
					decreaseSum = null;
				}
	        }
		}

		private void UpdateIncreaseSumButton(string content, Brush background)
		{
			if (Dispatcher.CheckAccess())
			{
				increaseSum.Content = content;
				increaseSum.Background = background;
			}
			else
			{
				Dispatcher.Invoke(() => {
					increaseSum.Content = content;
					increaseSum.Background = background;
				});
			}
		}
		
		private void UpdateDecreaseSumButton(string content, Brush background)
		{
			if (Dispatcher.CheckAccess())
			{
				decreaseSum.Content = content;
				decreaseSum.Background = background;
			}
			else
			{
				Dispatcher.Invoke(() => {
					decreaseSum.Content = content;
					decreaseSum.Background = background;
				});
			}
		}
		#endregion
				
		#region PROPERTIES
		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name = "Quantity Multiplier", Description = "Multiplier for the number of contracts to place, given by signal", Order=1, GroupName = "01. Entry")]
		public int QuantityMult
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name = "Max Contracts", Description = "Maximum number of contracts we can have open at once", Order=2, GroupName = "01. Entry")]
		public int MaxContracts
		{ get; set; }
		
		[NinjaScriptProperty]
        [Display(Name = "Last Trade Direction", Description = "Override for initial Last Trade Direction", Order=3, GroupName = "01. Entry")]
        public string LastTradeDir
        { get; set; }

		
		
		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name = "Stop Loss", Description = "Initial stop loss in ticks", Order=20, GroupName = "03. Exits")]
		public int StopLoss
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name = "Take Profit", Description = "Take profit in ticks", Order=21, GroupName = "03. Exits")]
		public int TakeProfit
		{ get; set; }
		
		
		
		[NinjaScriptProperty]
		[Range(int.MinValue, int.MaxValue)]
		[Display(Name = "BE Trigger #1", Description = "1st profit triger in ticks", Order=40, GroupName = "04. Break Even")]
		public int BETrigger1
		{ get; set; }
	
		[NinjaScriptProperty]
		[Range(int.MinValue, int.MaxValue)]
		[Display(Name = "BE Offset #1", Description = "1st move to breakeven offset", Order=41, GroupName = "04. Break Even")]
		public int BEOffset1
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(int.MinValue, int.MaxValue)]
		[Display(Name = "BE Trigger #2", Description = "2nd profit triger in ticks", Order=42, GroupName = "04. Break Even")]
		public int BETrigger2
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(int.MinValue, int.MaxValue)]
		[Display(Name = "BE Offset #2", Description = "2nd move to breakeven offset", Order=43, GroupName = "04. Break Even")]
		public int BEOffset2
		{ get; set; }
		
		
		
		[NinjaScriptProperty]
        [Display(Name = "Display OCD", Description = "Show the On-Chart Display", Order=120, GroupName = "13. Misc / Debug")]
        public bool DisplayOCD
        { get; set; }

		[NinjaScriptProperty]
        [Display(Name = "Strategy Name", Description = "Name of Strategy", Order=121, GroupName = "13. Misc / Debug")]
        public string StrategyName
        { get; set; }

		[NinjaScriptProperty]
        [Display(Name = "Disable Logging", Description = "Disable logging to Ninjascript Output windows", Order=122, GroupName = "13. Misc / Debug")]
        public bool DisableLogging
        { get; set; }

		[NinjaScriptProperty]
        [Display(Name = "Use Output 2", Description = "Send logging to second Output window", Order=123, GroupName = "13. Misc / Debug")]
        public bool UseOutput2
        { get; set; }

		[NinjaScriptProperty]
        [Display(Name = "Unique ID", Description = "Unique number to identify strategy in Output window", Order=124, GroupName = "13. Misc / Debug")]
        public string UniqueID
        { get; set; }
		#endregion
	}
}






