#region Using declarations
using System;
using Npgsql;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Net.Http;			/// Added for HttpClient	
using System.Net.Http.Headers;	/// Added for AuthenticationHeaderValue
using Newtonsoft.Json;			/// Added for automatic parsing of http response
using Npgsql;					/// Added for Benoit new signal get method Sep2024

using System.Linq;
using System.Runtime.CompilerServices;	/// For MemberCallerName
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Gui.Tools;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.Indicators;
using NinjaTrader.NinjaScript.Strategies;
using NinjaTrader.NinjaScript.DrawingTools;
#endregion

/// This namespace holds Strategies in this folder and is required. Do not change it. 
namespace NinjaTrader.NinjaScript.Strategies
{
	public class RazorURL : Strategy
	{
		#region GLOBALS
		
		/// HttpClient is intended to be instantiated once per application, rather than per-use
		private static readonly HttpClient Client = new HttpClient();
		private const string 	URL = "http://***************:3338/api/signal/all";
		private const int 		MIN_LOOKBACK = 61;
		private const int 		ADD_LOOKBACK = 20;
		private const int 		MAX_LOOKBACK = 120;
		private const int		TS_SWING_STRENGTH = 5;

		private double 			dailyProfit = 0;
		private double 			beTriggered1Price = 0;
		private double 			beTriggered2Price = 0;
		private double			lastSigSwingPrice = 0;
		private double			lastSetPnL = 0;
		
		private int 			checkSeconds;
		private int				contTradesTaken = 0;
		private int				maxContTrades;
		private int				firstEntryBarIdx = -1;
		private int				firstEntryBarsAgo = -1;
		private int				urlRetries = 0;
		private int				signalSum = 0;
		private int				fakeSignalSum = 0;
		
		private string 			signalDir = "None";
		private string 			lastTradeDir = "None";
		private string			positionEMAs = "None";
		private string 			lastDashboard = "";
		private string 			lastLogMsg = "";
		private string 			lastCaller = "";
		private string			lastLogTime = "";
		private string			chartInstrument;
		
		private bool 			entriesEnabled = true;
		private bool 			trailTriggered = false;
		private bool 			dailyLimitHit = false;
		private bool			longOn = true;
		private bool			shortOn = true;
		private bool			firstSignalRecevied = false;
		private bool			contTradesEnabled = false;
		private bool			inContinuationTrade = false;
		private bool			contMaxHit = false;
		private bool			contHadLoss = false;
		private bool			contHad2Loss = false;
		private bool			userRequestedExit = false;
		private bool			logAllSignals = false;
		private bool			inSession = true;
		private bool			noPrevailingToday = false;
		
		/// These variables were taken from NinjaTrader sample called UnmanagedTemplate
		private Order 			entryOrder = null;
		private Order 			slOrder = null;
		private Order 			tpOrder = null;
		private string			oco;
		private bool			stopsSubmitted = false;
		
		//private List<double>	tradeSetsPnL;
		private DateTime		prvSignalTime = DateTime.MinValue;
		private DateTime		lastCheck = DateTime.MinValue;
		private DateTime		signalTime = DateTime.MinValue.AddSeconds(1);
		private DateTime		nextCheckTime = DateTime.MinValue.AddSeconds(1);

		private EMA 			slowEMA, fastEMA;

		private System.Windows.Controls.Button	shortButton;
		private System.Windows.Controls.Button	longButton;
		private System.Windows.Controls.Button	exitButton;
		private System.Windows.Controls.Button	logSignalsButton;
		private System.Windows.Controls.Button	increaseSum;
		private System.Windows.Controls.Button	decreaseSum;
		private System.Windows.Controls.Grid	myGrid;
		
		public enum ContTradesModeEnum
		{
			Disabled = 0,
			OnlyBeforeSignal = 1,
			OnlyAfterSignal = 2,
			BeforeAndAfter = 3
		}
		
		public enum ContTradesNumberEnum
		{
			One = 1,
			Two = 2,
			Three = 3,
			Four = 4,
			UntilLoss = 98,
			UntilTwoLoss = 99,
			All = 100
		}
		#endregion
		
		protected override void OnStateChange()
		{
			#region State Change Info
			/// By printing out "State", dicovered this order:
			/*
				OnStateChange(), State = SetDefaults	// 1st clone for listing the strategies available in UI
				OnStateChange(), State = Configure
				OnStateChange(), State = Configure
				OnStateChange(), State = SetDefaults	// 2nd clone for configuring the one selected
				OnStateChange(), State = Terminated
			
				// Then the real one that we care about after we apply the settings:
				OnStateChange(), State = Configure		// Cloned again, so already has defaults set
				OnStateChange(), State = DataLoaded
				OnStateChange(), State = Historical
				OnStateChange(), State = Transition
				OnStateChange(), State = Realtime
				OnStateChange(), State = Terminated		// would be called again when terminated
			*/
			#endregion
			
			/// Set Input Defaults
			if (State == State.SetDefaults)
			{
				Description						= @"Strategy to automatically trade Occam signals";
				Name							= "RazorURL";
				Calculate						= Calculate.OnBarClose;
				EntriesPerDirection				= 1000;
				EntryHandling					= EntryHandling.AllEntries;
				IsExitOnSessionCloseStrategy	= true;
				ExitOnSessionCloseSeconds		= 2700;		// 45 mins: Exit @ 13:15 PST
				IsFillLimitOnTouch				= false;
				MaximumBarsLookBack				= MaximumBarsLookBack.TwoHundredFiftySix;
				OrderFillResolution				= OrderFillResolution.Standard;
				Slippage						= 0;
				StartBehavior					= StartBehavior.WaitUntilFlat;
				TimeInForce						= TimeInForce.Gtc;
				TraceOrders						= false;
				RealtimeErrorHandling			= RealtimeErrorHandling.StopCancelClose;
				//StopTargetHandling			= StopTargetHandling.ByStrategyPosition;	// method from managed Razor version
				StopTargetHandling				= StopTargetHandling.PerEntryExecution;
				BarsRequiredToTrade				= 20;
				IsUnmanaged 					= true;
				IsAdoptAccountPositionAware 	= true;		// Not sure if this is correct/needed
				
				
				/// Disable this property for performance gains in Strategy Analyzer optimizations
				/// See the Help Guide for additional information
				IsInstantiatedOnEachOptimizationIteration	= true;
				
				/// Optional scaler for Occam signal order quantity
				QuantityMult			= 1;
				MaxContracts			= 10;
				
				/// Override for Prevailing market dir at open
				LastTradeDir			= "None";

				/// Setting related to checking the URL for new signal
				CheckSeconds			= 10;		
				MaxDelaySeconds			= 300;		// Seems to be ~1-2 min delay built-in....
				TimeZoneOffset			= -3; 		// Signals are posted EST, so we need to subtract 3 to get to PT
				
				/// Take trades before first signal, and/or between signals?
				ContTradesMode			= ContTradesModeEnum.Disabled;
				ContQuantity			= 5;
				BiDirectional			= true;
				ContTradesNum			= ContTradesNumberEnum.Two;
				ContStrength			= 3;
				ContReqMAPosition		= true;
				
				/// StopLoss & TakeProfit
				StopLoss				= 100;
				TakeProfit				= 101;
				
				/// Daily Targets
				DailyMinTarget			= 0;		
				DailyMaxLoss			= 0;
				
				/// Move to BreakEven
				BETrigger1				= 0;
				BEOffset1				= 0;
				BETrigger2				= 0;
				BEOffset2				= 0;
				
				/// Trailing
				UseTrailingStop 		= false;
				TS_UseSimpleTrail		= false;
				TS_SimpleTicks			= 32;
				TS_UseClose				= true;
				TS_TakeProfitTicks 		= 300;
				TS_OffsetTicks 			= -2;		// Negative for extra room, positive to lock in more
				TS_ActivationTicks 		= 31;
				TS_MinTicks				= 31;
				TS_MaxTicks				= 39;
				TS_ReAdjustAtTicks1 	= 50;
				TS_MinTicks1			= 23;
				TS_MaxTicks1			= 29;
				TS_ReAdjustAtTicks2 	= 80;
				TS_MinTicks2			= 16;
				TS_MaxTicks2			= 20;
				
				/// Min Volume
				AveVolumePeriod 		= 14;
				MinAveVolume			= 50;
				
				LimitTradingHours		= true;
				StartTime				= DateTime.Parse("06:34", System.Globalization.CultureInfo.InvariantCulture);
				EndTime 				= DateTime.Parse("12:45", System.Globalization.CultureInfo.InvariantCulture);
				UseCloseTime			= true;
				CloseTime 				= DateTime.Parse("12:58", System.Globalization.CultureInfo.InvariantCulture);
				
				StrategyName			= "Razor";
				DisplayOCD				= true;
				DisableLogging			= false;
				UseOutput2				= false;
				UniqueID				= "1";
			}
			
			/// Initialize Member Variables
			else if (State == State.Configure)
			{
				checkSeconds = CheckSeconds;
				maxContTrades = (int) ContTradesNum;
				chartInstrument = this.Instrument.FullName;
				//tradeSetsPnL = new List<double>();

				AddDataSeries(BarsPeriodType.Tick, 1);
			}
			
			else if (State == State.DataLoaded)
			{
				slowEMA = EMA(SlowEMA_Period);
				fastEMA = EMA(FastEMA_Period);
			}
			
			else if (State == State.Historical)
			{
				// Init user override of lasttradeDir
				// Not sure this is the best place for this, but seems to work
				if (LastTradeDir.ToUpper() == "LONG")		lastTradeDir = "Long";
				else
				if (LastTradeDir.ToUpper() == "SHORT")		lastTradeDir = "Short";
				
				if (ChartControl != null)
				{
					ChartControl.Dispatcher.InvokeAsync(() =>
					{
						CreateButtons();
					});
				}	
			}
			
			else if (State == State.Terminated)
			{
				if (ChartControl != null)
				{
					ChartControl.Dispatcher.InvokeAsync(() =>
					{
						DisposeButtons();
					});
				}
			}
		}

		protected override void OnExecutionUpdate(Cbi.Execution execution, string executionId, double price, int quantity, 
												  Cbi.MarketPosition marketPosition, string orderId, DateTime time)
		{
			//Log($"execution.IsEntryStrategy = {execution.IsEntryStrategy},   execution.Order.OrderAction = {execution.Order.OrderAction},   marketPosition = {marketPosition},   quantity (input parameter) = {quantity},   Position.Quantity = {Position.Quantity},   execution.Order.OrderAction = {execution.Order.OrderAction}");
			
			/// This was taken from a NinjaTrader sample called UnmanagedTemplate
			// We advise monitoring OnExecution to trigger submission of stop/target orders instead of OnOrderUpdate()
			// since OnExecution() is called after OnOrderUpdate() which ensures your strategy has received the execution
			// which is used for internal signal tracking.
			
			double m = (Position.MarketPosition == MarketPosition.Long) ? 1.0 : -1.0;
			double avePrice = AvePrice();

			/// Handle actions for this execution being an entry order (execution.Order.OrderAction.Buy or SellShort)
			if (execution.IsEntryStrategy)
			{
				if (StopLoss > 0  ||  TakeProfit > 0)
				{
					//Log($"StopLoss > 0 ({StopLoss})  ||  TakeProfit > 0 ({TakeProfit})");
					
					/// [Only] if we are using both stops do we need to link OCO
					oco = "";
					if (StopLoss > 0  &&  TakeProfit > 0)
					{
						if (!stopsSubmitted)
						{
							/// TakeProfit is required (if using any TP), TakeProfit 1 & 2 are incidental
							if (State == State.Historical)
								oco = DateTime.Now.ToString() + "_" + CurrentBar + "_" + "Exits";
							else
								oco = GetAtmStrategyUniqueId() + "_" + "Exits";
							//Log($"Set OCO = {oco}");
						}
						else if (slOrder != null)
						{
							oco = slOrder.Oco;
							//Log($"Fetched existing OCO = {oco}");
						}
						else
							Log($"Could not fetch OCO because slOrder == null");
					}
					
					if (entryOrder != null  &&  entryOrder == execution.Order)
					{
						//Log($"IsEntryStrategy = True, execution.Order.OrderState = {execution.Order.OrderState}");
						
						/// Not specifically handing this case; assuming this method will be called again w/ order state == filled
						if (execution.Order.OrderState == OrderState.PartFilled)
							Log($"   ENTRY ORDER ONLY PARTIALLY FILLED; execution.Order.Filled (Qty) = {execution.Order.Filled}");
						
						else if (execution.Order.OrderState == OrderState.Cancelled)
						{
							/// If the order was somehow cancelled, then we need to just and position that partially opened
							Log($"   ENTRY ORDER CANCELLED; dir = {marketPosition.ToString()}, execution.Order.Filled = {execution.Order.Filled}");
							if (execution.Order.Filled > 0)
							{
								Log($"Partial order was filled, so closing now");
								ClosePosition("Partial Fill", marketPosition.ToString(), execution.Order.Filled);
							}
						}
						else if (execution.Order.OrderState == OrderState.Filled)
						{
							if (!stopsSubmitted)
							{
								//Log($"ORDER FILLED, stopsSubmitted = false");
								/// This used to check that slOrder & tpOrders were null, because we were initially setting, 
								/// but it won't place the stops until order is filled, so no need to check for that. 
								var action = (marketPosition == MarketPosition.Long) ? OrderAction.Sell : OrderAction.BuyToCover;

								Log($"INIT PLACEMENT OF SL & TP; Action = {action.ToString()}");
								if (StopLoss > 0)
								{
									//Log($"Submitting StopLoss for {Position.Quantity} contracts");
									SubmitOrderUnmanaged(0, action, OrderType.StopMarket, Position.Quantity, 0, avePrice - m * StopLoss * TickSize, oco, "StopLoss");
								}
								if (TakeProfit > 0)
								{
									//Log($"Submitting TakeProfit for {Position.Quantity} contracts");
									SubmitOrderUnmanaged(0, action, OrderType.Limit, Position.Quantity, avePrice + m * TakeProfit * TickSize, 0, oco, "TakeProfit");
								}
								stopsSubmitted = true;
							}
							/// Stops have been submitted, so this must be an entry order that is 
							/// adding contracts.  Therefore, we need to update stops
							else
							{
								Log($"NEW ENTRY: Moving StopLoss and TakeProfit; avePrice = {avePrice}, newSL = {avePrice - m * StopLoss * TickSize}, newTP = {avePrice + m * TakeProfit * TickSize}");
								ModifyStopLoss(avePrice - m * StopLoss * TickSize);
								ModifyTakeProfit(avePrice + m * TakeProfit * TickSize);
							}
						}
					}
					else
						Log($"entryOrder != execution.Order, execution.Order.OrderState = {execution.Order.OrderState}");
				}
			}
			
			//if (execution.Order.OrderAction == OrderAction.Sell  ||  execution.Order.OrderAction == OrderAction.BuyToCover)
			else	// Is exit strategy
			{
				/// If we did NOT just close *entire* position, update SL & TP
				if (Position.Quantity > 0)
				{
//					ChangeOrder(slOrder, Position.Quantity, 0, slOrder.StopPrice);
					Log($"PARTIAL EXIT: Changing SL & TP quantity; SL = {avePrice - m * StopLoss * TickSize}, TP = {avePrice + m * TakeProfit * TickSize}, new Qty should be {Position.Quantity}");
					ModifyStopLoss(avePrice - m * StopLoss * TickSize);
					ModifyTakeProfit(avePrice + m * TakeProfit * TickSize);
				}
				else
				{
					/// Cancel any stops that are left open. SL & TP3 OCO, but check all to be sure
					if (slOrder != null  ||  tpOrder != null)
						CancelStops();
					
					/// Reset some variables
					Log($"Position.Quantity == 0; RESETTING VARS & checking DailyMaxLoss & DailyMinTarget..");
					stopsSubmitted = false;
					beTriggered1Price = beTriggered2Price = 0;
					trailTriggered = false;
					inContinuationTrade = false;
					firstEntryBarIdx = -1;
					entryOrder = slOrder = tpOrder = null;
					
			        /// Use SystemPerformance.AllTrades to get the last closed trade
			        if (SystemPerformance.AllTrades.Count > 0)
			        {
			            var lastTrade = SystemPerformance.AllTrades[SystemPerformance.AllTrades.Count - 1];
			
			            /// Access trade details
						double pnl = lastTrade.ProfitCurrency;	// includes commission
						Log($"Last trade PnL: ${pnl}");
						//double com = lastTrade.Commission;
						//Log($"Last trade Commission: ${com}");
						
						dailyProfit += pnl;		// we need to save this each time, so we can reload it if we have to restart platform
						
						/// Don't know how to get the last item from the list.  Should be "Item[]" or "Item()", but does not work:
						//lastSetPnL = tradeSetsPnL[tradeSetsPnL.Count-1];
						//tradeSetsPnL.Add(pnl);
						lastSetPnL += pnl;		
			            Log($"Current set PnL: ${lastSetPnL}");
			        }
					
					/// Check for daily profit/loss limits
					Log($"DailyMinTarget = {DailyMinTarget}, DailyMaxLoss = ${DailyMaxLoss}");
					Log($"pDaily = (${dailyProfit}), SystemPerformance.RealTimeTrades.TradesPerformance.NetProfit = (${SystemPerformance.RealTimeTrades.TradesPerformance.NetProfit})");
					if (DailyMaxLoss > 0  &&  -dailyProfit > DailyMaxLoss)
					{
						/// if (Position.Quantity == 0), Exiting should be unnecessary, but are 
						/// left in case we are reversing position, or otherwise 'just to be safe'
						ClosePosition("Daily Loss");
						Log($"pDaily (${dailyProfit}) - NetProfit (${SystemPerformance.AllTrades.TradesPerformance.NetProfit}) > DailyMaxLoss (${DailyMaxLoss}");
						Log($"Daily Loss Hit! Set dailyLimitHit = true\n");
						dailyLimitHit = true;
					}
					if (DailyMinTarget > 0  &&  dailyProfit > DailyMinTarget)
					{
						ClosePosition("Daily Target");
						Log($"NetProfit (${SystemPerformance.AllTrades.TradesPerformance.NetProfit}) - dailyProfit (${dailyProfit}) > DailyMinTarget (${DailyMinTarget}");
						Log($"Daily Profit Hit! Set dailyLimitHit = true\n");
						dailyLimitHit = true;
					}
				}
			}
		}
		
		
		protected override void OnOrderUpdate(Cbi.Order order, double limitPrice, double stopPrice, int quantity, 
											  int filled, double averageFillPrice, Cbi.OrderState orderState, 
											  DateTime time, Cbi.ErrorCode error, string comment)
        {
			//Log($"order.Name = {order.Name}, order.OrderAction = {order.OrderAction.ToString()}, order.OrderType = {order.OrderType.ToString()}, Position.Quantity = {Position.Quantity}, order.StopPrice = {order.StopPrice}, order.LimitPrice = {order.LimitPrice}");
			
			/// Assign Order objects here
			/// This is more reliable than assigning Order objects in OnBarUpdate, as the assignment 
			/// is not guaranteed to be complete if it is referenced immediately after submitting
			if (order.Name == "Entry")
			{
				//Log($"Setting entryOrder");
				entryOrder = order;
			}
			else if (orderState != OrderState.Filled)
			{
				if (order.Name == "StopLoss")
				{
					//Log($"Setting slOrder");
					slOrder = order;
				}
				else if (order.Name == "TakeProfit")
				{
					//Log($"Setting tpOrder");
					tpOrder = order;
				}
			}
		}

		
		protected override void OnBarUpdate()
		{
			if (CurrentBars[0] < BarsRequiredToTrade)
				return;
			
			/// No point in trying to do historical trades without the signals
			if (State != State.Historical)
				ManageOCD();	// Update the On-Chart Display
			
			/// Entered on close of each candle of chart TF where strategy is applied (first data series)
			bool highFractal, lowFractal;
			if (BarsInProgress == 0)
			{
				if (Bars.IsFirstBarOfSession)
				{
					/// This is not useful for us because we do not let the EA run 24/5 such that this would trigger at 6pm ET
					/// And more importantly, we do not do anthing with historical (yet); that is where this is important:
					/// to reset some daily things that will be done with running over historical bars.
					dailyProfit = 0;	//SystemPerformance.AllTrades.TradesPerformance.NetProfit;
					dailyLimitHit = false;
					noPrevailingToday = false;
					firstSignalRecevied = false;
					lastTradeDir = "None";
					contTradesTaken = 0;
					contMaxHit = contHadLoss = contHad2Loss = false;
					//tradeSetsPnL.Clear();
					Log($"\nNEW SESSION (DAY) - RESET EVERYTHING\n");
					
					/// This code was in a sample showing how to get all previous profit made 
					/// by this strategy in the past, but SINCE IT WAS APPLIED TO THE CHART
					//int priorTradesCount = SystemPerformance.AllTrades.Count;
					//double priorTradesCumProfit = SystemPerformance.AllTrades.TradesPerformance.Currency.CumProfit;
					/// This is not useful for us because we do not have historical signals [yet] (see comment at top)
				}
				
				/// Close all trades at end of user-defined session
				if (UseCloseTime  &&  Position.MarketPosition != MarketPosition.Flat)
				{
					//if (Time[0].TimeOfDay >= CloseTime.AddMinutes(-3).TimeOfDay)
					//	Log($"UseCloseTime enabled and near EoD.  Position.MarketPosition = {Position.MarketPosition}");
					
					if (Time[0].TimeOfDay >= CloseTime.TimeOfDay)//  &&  Time[1].TimeOfDay < CloseTime.TimeOfDay)
					{
						// This does not seem to be working... Add logs
						Log($"Time[0] ({Time[0].TimeOfDay.ToString()}) >= CloseTime.TimeOfDay ({CloseTime.TimeOfDay.ToString()})");//  &&  Time[1] ({Time[1].TimeOfDay.ToString()}) < CloseTime.TimeOfDay");
						Log($"End of Session Close activated @ {DateTime.Now.ToString()}; Closing all open orders");
						ClosePosition("EoD Close", Position.MarketPosition.ToString());
					}
				}

				/// Set up how many bars ago was the entry candle
				firstEntryBarsAgo = (firstEntryBarIdx != -1) ? CurrentBar - firstEntryBarIdx : -1;
				
				/// Need position of EMAs relative to one another for cont entry option
				if (fastEMA[0] == slowEMA[0])		positionEMAs = "None";
				else if (fastEMA[0] > slowEMA[0])	positionEMAs = "Long";
				else if (fastEMA[0] < slowEMA[0])	positionEMAs = "Short";

				/// If not in market and not in-session (or limit hit), no need to go further
				if (!inSession  ||  dailyLimitHit)
					if (Position.MarketPosition == MarketPosition.Flat)
						return;

				#region CONTINUATION ENTRY
				if (entriesEnabled  &&  Position.MarketPosition == MarketPosition.Flat  &&  contTradesEnabled)
				{
					string direction = "None";
					string ltd = lastTradeDir;

					/// If no lastTradeDir, then we want to check both directions (if BiDirectional enabled)
					if (ltd == "None"  &&  BiDirectional)
					{
						/// If candle is closing above, it'd have to be a long candle...
						if (Close[0] > Open[0])	ltd = "Long";
						else					ltd = "Short";
						//Log($"lastTradeDir == None & BiDirectional = {BiDirectional}; so using lastTradeDir as {ltd}");
					}
					/// Check every candle because any cond above could be false, then there would be a gap...
					if (ltd != "None")
					{
						//Log($"    CHECKING GetLastSignificantSwingPrice() - contTradesEnabled = {contTradesEnabled}, lastTradeDir = {ltd}");
						lastSigSwingPrice = GetLastSignificantSwingPrice(ltd);
						
						if (lastSigSwingPrice > 0)
						{
							if (ltd == "Long"  &&  Close[0] > lastSigSwingPrice)
							{
								direction = "Long";
								Log($"No trades open, TradeContinuation enabled, lastTradeDir = {ltd}, BiDirectional = {BiDirectional}, and candle closed ({Close[0]}) above previous fractal @ {lastSigSwingPrice}; Continuation signal = {direction}");
							}
							else if (ltd == "Short"  &&  Close[0] < lastSigSwingPrice)
							{
								direction = "Short";
								Log($"No trades open, TradeContinuation enabled, lastTradeDir = {ltd}, BiDirectional = {BiDirectional}, and candle closed ({Close[0]}) below previous fractal @ {lastSigSwingPrice}; Continuation signal = {direction}");
							}
						}
						
						// Confirm EMAf/EMAs position
						if (direction != "None"  &&  ContReqMAPosition  &&  positionEMAs == direction)
						{
							Log($"{direction} Continuation trade cancelled because EMA positions do not match");
							direction = "None";
						}
					}
					
					if (direction != "None")
					{
						if (QuantityMult != 1)
							Log($"Continuation Signal Detected @ {DateTime.Now.ToString()}!  Direction = {direction}, Signal Quantity = {signalSum}, QuantityMult = {QuantityMult}; checking trade filters...");
						else
							Log($"Continuation Signal Detected @ {DateTime.Now.ToString()}!  Direction = {direction}, Signal Quantity = {signalSum}; checking trade filters...");
						if (TradeFiltersOkay(direction))
						{
							Log($"\nTrade Filters Okay; ENTERING {direction.ToUpper()} CONTINUATION TRADE FOR {ContQuantity} CONTRACTS");
							PlaceMarketOrder(direction, ContQuantity);
							contTradesTaken++;
							inContinuationTrade = true;
							lastTradeDir = ltd;
						}
					}
					/// No need to do any trail computations if we were flat a moment ago, or still are
					return;
				}
				#endregion
				
				#region TRAILING STOP
				/// Looking for a better plan for trailing than "fractal + candle".  I really want to prioritize fractals, 
				/// so it seems a better way it to skip input settings and just look for various Swing lows (assume long)
				/// We already have a min & max trail distance, so we'll look for a swing low with strength=x (say we 
				/// hardcode to 5).  If not found in the range between min & max, try 4, then 3, then 2, then 1.  And if 
				/// we don't find any, we can just move to MinTicks.
				if (UseTrailingStop  &&  Position.MarketPosition != MarketPosition.Flat)
				{
					double newSL = GetNewTrailPrice(Position.MarketPosition.ToString());
					if (newSL != 0)
						ModifyStopLoss(newSL);
				}
				#endregion
			}

			/// Entered on each tick (third data series)
			else if (BarsInProgress == 1)
			{
				// Check each tick whether we are in-session (when it can place new trades)
				inSession = CheckSession();
				
				/// If not in market and not in-session (or limit hit), no need to go further
				if ((!inSession  ||  dailyLimitHit)  &&  fakeSignalSum == 0)
					if (Position.MarketPosition == MarketPosition.Flat)
						return;
				
				/// Assign whether we are active for continuation trades
				#region ENABLE CONTINUATION TRADES?
				if (!contMaxHit  &&  contTradesTaken >= maxContTrades)
					contMaxHit = true;
				if (lastSetPnL < 0)
				{
					if (!contHadLoss)
					{
						if (maxContTrades == (int) ContTradesNumberEnum.UntilLoss  ||  maxContTrades == (int) ContTradesNumberEnum.UntilTwoLoss)
							contHadLoss = true;
					}
					/// May want to make it two IN A ROW, which would require more coding...
					else if (maxContTrades == (int) ContTradesNumberEnum.UntilTwoLoss)
						contHad2Loss = true;
				}
				
				if (contMaxHit)
					contTradesEnabled = false;
				else if (contHadLoss  &&  maxContTrades == (int) ContTradesNumberEnum.UntilLoss)
					contTradesEnabled = false;
				else if (contHad2Loss  &&  maxContTrades == (int) ContTradesNumberEnum.UntilTwoLoss)
					contTradesEnabled = false;
				else
				{
					switch (ContTradesMode)
					{
						case ContTradesModeEnum.OnlyBeforeSignal:
							contTradesEnabled = !firstSignalRecevied;
							break;
						case ContTradesModeEnum.OnlyAfterSignal:
							contTradesEnabled = firstSignalRecevied;
							break;
						case ContTradesModeEnum.BeforeAndAfter:
							contTradesEnabled = true;
							break;
						default:
						case ContTradesModeEnum.Disabled:
							contTradesEnabled = false;
							break;
					}
				}
				#endregion
				
				#region MAIN SIGNAL ENTRY
				signalDir = "None";
				if (entriesEnabled)
				{
					// If URL read fails, increase checkSeconds & nextCheckTime time
					if (urlRetries >= 3  &&  checkSeconds == CheckSeconds)
					{
						checkSeconds = 300;
						nextCheckTime = DateTime.Now.AddSeconds(checkSeconds - CheckSeconds);
						Log($"\nurlRetries = {urlRetries} : Trouble getting signal; Changed checkSeconds = {checkSeconds}; nextCheckTime = {nextCheckTime.ToString()}\n");
					}
					else 
					{
						// If URL read *had* failed, now succeeeded, reset checkSeconds
						if (urlRetries < 3  &&  checkSeconds != CheckSeconds)
						{
							checkSeconds = CheckSeconds;
							Log($"\nurlRetries = {urlRetries} : Reset checkSeconds = {checkSeconds}\n");
						}
						
						if (DateTime.Now >= nextCheckTime  ||  fakeSignalSum != 0)
						{
							//Log($"Checked NOW ( {DateTime.Now.ToString()} ) against nextCheckTime ( {nextCheckTime.ToString()} ) - CALLING CheckSignal()");
							urlRetries = 0;
							if (fakeSignalSum == 0)
								CheckSignalSync();  // This will wait for the async method to complete
							//Log($"Back from CheckSignalSync, signalDir = {signalDir}");

							// Fake an entry?
							if (signalSum == 0  &&  fakeSignalSum != 0)
							{
								signalSum = fakeSignalSum;
								fakeSignalSum = 0;
								Log($"FAKE SIGNAL GENERATED, SUM = {signalSum}");

								// Reset buttons
								UpdateIncreaseSumButton("Increase Sum", Brushes.Yellow);
								UpdateDecreaseSumButton("Decrease Sum", Brushes.Yellow);
							}
							
							// Zero means change nothing
							if (signalSum != 0)
							{
								string posDir = Position.MarketPosition.ToString();
								int posSum = 0;
								
								/// If we are in a Continuation trade, and get a proper Occam signal 
								/// that's opposite, go ahdead and close the whole thing, and reverse
								if (inContinuationTrade  &&  ((signalSum > 0  &&  posDir == "Short")  ||  (signalSum < 0  &&  posDir == "Long")))
								{
									ClosePosition("CloseOnRev", posDir);
									posDir = "Flat";
								}
								
								/// Convert current position to signed int (neg for short, pos for long)
								if (posDir != "Flat")
								{
									int m = (posDir == "Long") ? 1 : -1;
									posSum = Position.Quantity * m;
								}
								
								int newSum = posSum + signalSum;
								bool needsOpen = false;
								string logStr = "";
								int qty = QuantityMult * Math.Abs(signalSum);

								signalDir = (newSum > 0) ? "Long" : (newSum < 0) ? "Short" : "Flat";
								Log($"Occam Signal Received @ {DateTime.Now.ToString()} - New Direction = {signalDir}, Signal Sum = {signalSum}, old Pos Sum = {posSum}, New Pos Sum = {newSum}, QuantityMult = {QuantityMult}");
								
								/// A sum of zero means to go flat
								if (signalDir == "Flat")	// Implies that current pos is not flat
								{
									Log($"\nCLOSING {posDir} POSITION\n");
									ClosePosition("SigExit", posDir);
								}
								
								/// Handle position size modification, but no reversal
								else if (posDir == signalDir)
								{
									/// Open additional contracts
									if ((posDir == "Long"   &&  signalSum > 0)	||  (posDir == "Short"  &&  signalSum < 0))
									{
										logStr = $"\nSignal to Increase: ENTERING ADDITIONAL {qty} CONTRACTS {signalDir.ToUpper()}\n";
										needsOpen = true;
									}
									/// Close partial contracts
									else if ((posDir == "Long"   &&  signalSum < 0)  ||  (posDir == "Short"  &&  signalSum > 0))
									{
										Log($"\nSignal to Reduce: CLOSING {Math.Abs(QuantityMult * signalSum)} {signalDir.ToUpper()} CONTRACTS (out of {Position.Quantity})\n");
										ClosePosition("SigReduce", posDir, qty);
									}
								}
								
								/// Reverse or (open new position if flat)
								else if (posDir != signalDir)
								{
									string rev = (posDir == "Flat") ? "POSITION IS FLAT; " : $"REVERSING {Position.Quantity} {posDir.ToUpper()} POSITION & ";
									qty = QuantityMult * Math.Abs(newSum);

									logStr = $"\nSignal to Flip or Open: {rev}ENTERING NEW {signalDir.ToUpper()} POSITION FOR {qty} CONTRACTS\n";
									needsOpen = true;
								}
								
								/// Open new pos (or reverse)
								if (needsOpen)
								{
									if (TradeFiltersOkay(signalDir))
									{
										// Do not trade more than user-defined max
										if (Position.Quantity + qty > MaxContracts)
										{
											Log($"WARNING!! {Position.Quantity} Contracts already open, and max set to {MaxContracts}, so we cannot open {qty} more;  opening {MaxContracts - Position.Quantity} instead");
											qty = MaxContracts - Position.Quantity;
										}
										
										Log(logStr);
										Log($"Before EntryLong/Short(), Position: {Position.MarketPosition}, Quantity: {Position.Quantity}");
										Log($"Attempting to enter {signalDir} position for {qty} contracts");
										PlaceMarketOrder(signalDir, qty);
										lastTradeDir = signalDir;
										signalSum = 0;
									}
								}
							}
							//else Log($"Signal confirms current position; nothing to change");
						}
					}
				}
				#endregion
				
				/// No need to check anything further if no position open
				if (Position.MarketPosition == MarketPosition.Flat)
					return;
				
				#region BREAK EVEN
		        double newStopPrice = 0;
				double oldPrice = 0;
				
				/// Get existing stop price if exists
				/// Working seems to not mean 'active', but that it's still in exchange queue. Accepted means it's active.
				if (slOrder != null  &&  (slOrder.OrderState == OrderState.Working  ||  slOrder.OrderState == OrderState.Accepted))
					oldPrice = slOrder.StopPrice;

				double avePrice = AvePrice();
				if (Position.MarketPosition == MarketPosition.Long)
				{
			        /// Calculate new stop price
					if (beTriggered1Price == 0  &&  BETrigger1 != 0  &&  High[0] >= avePrice + BETrigger1*TickSize)
						beTriggered1Price = newStopPrice = avePrice + BEOffset1*TickSize;
					else if (beTriggered2Price == 0  &&  BETrigger2 != 0  &&  High[0] >= avePrice + BETrigger2*TickSize)
						beTriggered2Price = newStopPrice = avePrice + BEOffset2*TickSize;

					/// Abort if new price does not tighten stop
					if (newStopPrice != 0  &&  oldPrice != 0  &&  newStopPrice <= oldPrice)
						newStopPrice = 0;
				}
				else if (Position.MarketPosition == MarketPosition.Short)
				{
			        /// Calculate new stop price
					if (beTriggered1Price == 0  &&  BETrigger1 != 0  &&  Low[0] <= avePrice - BETrigger1*TickSize)
						beTriggered1Price = newStopPrice = avePrice - BEOffset1*TickSize;
					else if (beTriggered2Price == 0  &&  BETrigger2 != 0  &&  Low[0] <= avePrice - BETrigger2*TickSize)
						beTriggered2Price = newStopPrice = avePrice - BEOffset2*TickSize;
					
					/// Abort if new price does not tighten stop
					if (newStopPrice != 0  &&  oldPrice != 0  &&  newStopPrice >= oldPrice)
						newStopPrice = 0;
				}
				if (newStopPrice != 0  &&  Position.MarketPosition != MarketPosition.Flat)
				{
					Log($"MoveToBE TRIGGERED: newStopPrice set to {newStopPrice}.  Old stop price = {oldPrice}");
					ModifyStopLoss(newStopPrice);
				}
				#endregion
				
				#region TRAIL TRIGGER
				/// This section just looks at triggering trail, and expanding TP to new value (just once)
				if (!trailTriggered  &&  UseTrailingStop)
				{
					if ((Position.MarketPosition == MarketPosition.Long  &&  High[0] >= AvePrice() + TS_ActivationTicks*TickSize)
					||  (Position.MarketPosition == MarketPosition.Short  &&  Low[0] <= AvePrice() - TS_ActivationTicks*TickSize))
					{
						trailTriggered = true;
						if (TS_TakeProfitTicks > TakeProfit)
						{
							double m = (Position.MarketPosition == MarketPosition.Long) ? 1.0 : -1.0;
							ModifyTakeProfit(AvePrice() + m*TS_TakeProfitTicks*TickSize);
						}
					}
				}
				#endregion
			}
		}
		
		#region OCCAM SIGNAL
		/// This class needed for JSON parsing
		public class SignalResponse
		{
		    public int deltaflip { get; set; }
			public DateTime est_time { get; set; }
		    public int gammaflip { get; set; }
		    public int thetaflip { get; set; }
		    public int vegaflip { get; set; }
		}
		
		
		/// Synchronous method to call async process
		public void CheckSignalSync()
		{
			try
			{
				/// Use Task.Run to run the async method and Wait to block until it's complete
				Task.Run(async () => await GetSignal()).Wait();
				//Log($"Back from Parse, signalDir = {signalDir}");
			}
			catch (AggregateException ae)
			{
				/// Handle exceptions thrown by the task
				foreach (var e in ae.InnerExceptions)
				{
					Log("Exception: " + e.Message);
				}
			}
		}		
		
		
		/// Get signal from URL
		public async Task GetSignal()
		{
			//-------------------------------------------------------------------------------------------
			// Microsoft help site said HttpWebRequest, and to use HttpClient
			//		See: 
			//		https://learn.microsoft.com/en-us/dotnet/api/system.net.http.httpclient?view=net-8.0
			//		(though ChatGPT got me a long way from this)
			//-------------------------------------------------------------------------------------------
			// Call asynchronous network methods in a try/catch block to handle exceptions.
			try
			{
				/// Set up the next time for checking URL
				nextCheckTime = DateTime.Now.AddSeconds(checkSeconds);
				/// Optimally we do not want multiple instances to NOT access the URL at 
				/// exactly the same time. Therefore, we need to align each'next' check to 
				/// a certain seconds.  checkSeconds defaults to 10, so we want one instance 
				/// checking at x:10, x:20, x:30, and the next at x:12, x:22, x:32, and so on.
				/// Use UniqueID to calculate it (times 2).
				/*
				int sec, rem, id;
				id = Convert.ToInt32(UniqueID);
				if (id >= 100)
				{
					sec = 2 * (id % 100);
					rem = nextCheckTime.Second % 10;
					
					// If next check happens to fall on the next second, great!  Do nothing.
					if (rem != sec)
					{
						// Otherwise add enough seconds to make it what we want
						sec = Math.Abs(rem - sec);
						nextCheckTime.AddSeconds(sec);
					}
				}
				*/
				//Log($"Set nextCheckTime to now + checkSeconds: {nextCheckTime.ToString()}");
				
				if (urlRetries >= 3)
					return;			// Only try 3 times
				
				/// Get response
				string urlResponse = await Client.GetStringAsync(URL);
				//Log("\n{DateTime.Now.ToString()}  ::  Response = " + urlResponse);
				
				if (urlResponse != "[]")
				{
					//Log($"Calling ParseResponse() @ {DateTime.Now.ToString(@"hh\:mm\:ss")}");
					ParseResponse(urlResponse);
				}
			}
			catch (HttpRequestException e)
			{
				//Log($"\nException Caught! Retries = {urlRetries}");
				Log($"HTTP Message :{e.Message}");
				urlRetries++;
				
	            /// Retry logic
	            await Task.Delay(5000); // Wait for 5 seconds before retrying
	            GetSignal().Wait();
	        }
	        catch (Exception e)
	        {
	            Log($"\nUnexpected Exception Caught!");
				Log($"Message :{e.Message}");
	        }
		}

		
		/// Parse the response received from GetSignal()
		/// 
		/// URL contains all signals (including historical since Benoit created it).
		/// Format example:
		//
		//		{
		//			"deltaflip": 0,
		//			"est_time": "2024-08-28T09:32:00Z",
		//			"gammaflip": 1,
		//			"thetaflip": 1,
		//			"vegaflip": 1
		//		},
		//
		/// Interpretation method:
		///
		///	Consider our current open position to be represented by a signed integer, 
		/// so e.g. 3 means three contracts long, -2 means two contracts short, and 
		/// zero means flat. Call this PosSum.
		/// 
		/// Sum up the flips from our signal.  It can only range from -4 to 4; call 
		/// this SigSum.  When we add the two together we get a number that represents 
		/// the position we desire.  So if PosSum = -1 and SigSum = 3, then they sum 
		/// to 2, which means we want to go from being one short to two long.
		/// 
		/// So a change of sign from PosSum to SigSum means a reversal.
		/// 
		/// A SigSum of zero means "leave position as is".
		/// 
		private void ParseResponse(string response)
		{
			signalSum = 0;
			
			try
			{
				/// Deserialize the JSON response into a list of SignalResponse objects
				List<SignalResponse> signalList = JsonConvert.DeserializeObject<List<SignalResponse>>(response);
				
				/// Check if there are any signals in the list
				if (signalList == null  ||  signalList.Count < 1)
				{
					Log($"No signals found in the response ({response}).  signalList = {signalList}, count = {signalList.Count}");
					return;
				}
				
				/// For debugging, we log all [today's] symbols when button is pressed
				if (logAllSignals)
				{
					Log($"======================");
					Log($"        Today's Occam Signals");
					Log($"======================");
					//int prvSum = 0;
					foreach (var sig in signalList)
					{
						if (sig.est_time.Date != DateTime.Now.Date)
							continue;
						//int logSum = sig.gammaflip*1000 + sig.deltaflip*100 + sig.thetaflip*10 + sig.vegaflip;
						int logSum = sig.gammaflip + sig.deltaflip + sig.thetaflip + sig.vegaflip;
						if (sig.gammaflip != 0  ||  sig.deltaflip != 0  ||  sig.thetaflip != 0  ||  sig.vegaflip != 0)
						{
							Log($"\n  Post Time: {sig.est_time.ToString()} :");
							Log($"      GammaFlip Quantity: {sig.gammaflip}");
							Log($"      DeltaFlip Quantity: {sig.deltaflip}");
							Log($"      ThetaFlip Quantity: {sig.thetaflip}");
							Log($"      VegaFlip  Quantity: {sig.vegaflip}");
							Log($"      SUM = {logSum}");
							//prvSum = logSum;
						}
					}
					Log($"\n======================");
					logAllSignals = false;
					return;
				}
				
				/// Process the latest signal from the list
				SignalResponse lastSignal = signalList[signalList.Count - 1];
				//Log($"Last Signal Time = {lastSignal.est_time}");
				
				/// If the last signal seen isn't even today, do nothing (wait)
				if (lastSignal.est_time.Date != DateTime.Now.Date)
					return;
				
				/// date time vars used below
				DateTime myTimeZone, delayLimit, currentTime = DateTime.Now;

				/// Check for "prevailing" (pre-market) direction first
				if (lastTradeDir == "None"  &&  !noPrevailingToday  &&  !firstSignalRecevied)
				{
					/// The 1st or 2nd entry (09:30 or 09:31) will be "Prevailing", if it has all 4 greeks set to 1 or -1.  
					/// But we don't know when the EA will be applied; perhaps after a genuine signal is posted.
					/// So to get accurate lastTradeDir, we just need to look for the last signal.  Technically, 
					/// if it as all 4 set and is 09:30 or 09:31, then it's prevailing, and if otherwise, it's just 
					/// lastTradeDir; basically all the same in function though.
					int yy = DateTime.Now.Year;
					int mm = DateTime.Now.Month;
					int dd = DateTime.Now.Day;
					DateTime usOpen0 = new DateTime(yy, mm, dd, 9, 30, 0);	// The signals come in stamped ET
					DateTime usOpen1 = usOpen0.AddMinutes(1);

					/// Iterate backward over signals starting at the end
					for (int i = signalList.Count-1; lastTradeDir == "None"; i--)
					{
						SignalResponse sigResp = signalList[i];
						
						/// If we get back to one which is no longer the current day, we have gone too far
						if (sigResp.est_time.Date != DateTime.Now.Date)
							break;
						
						int sumP = sigResp.gammaflip + sigResp.deltaflip + sigResp.thetaflip + sigResp.vegaflip;

						if (sumP != 0)
						{
							/// We don't need to go back to 09:31 nec; the last non-zero sum is the last direction.
							/// session when user restarted EA
							lastTradeDir = (sumP > 0) ? "Long" : "Short";
							prvSignalTime = sigResp.est_time;
							if ((sumP == 4  ||  sumP == -4)  
							&&  (sigResp.est_time == usOpen0  ||  sigResp.est_time == usOpen1))
								Log($"Captured 'Prevailing Dir' as {lastTradeDir} (Sum = {sumP}) @ {sigResp.est_time.ToString()}, and set to lastTradeDir for Continuation trade");
							else
								Log($"The Last Signal Dir was {lastTradeDir} (Sum = {sumP}) @ {sigResp.est_time.ToString()}; set to lastTradeDir for Continuation trade");
								
							/// We found most recent direction; so long as it is not a currently valid signal, return
							myTimeZone = sigResp.est_time.AddHours(TimeZoneOffset);
							delayLimit = myTimeZone.AddSeconds(MaxDelaySeconds);
							if (MaxDelaySeconds != -1  &&  currentTime > delayLimit)
								return;
						}
					}
					/// If we did not find it, then set a reminder so we don't repeat this every 5 seconds...
					if (lastTradeDir == "None"  &&  lastSignal.est_time > usOpen1)
						noPrevailingToday = true;
				}

				
				/// Sum the quantity of contracts from whichever signal is active
				int sum = lastSignal.gammaflip + lastSignal.deltaflip + lastSignal.thetaflip + lastSignal.vegaflip;

				/// Check if the last signal's timestamp is newer than the previous one
				if (lastSignal.est_time > prvSignalTime)
				{
					// If we have a fresh signal, then add more time to next check, because signals are each minute
					//nextCheckTime = nextCheckTime.AddSeconds(60 - CheckSeconds);
					
					/// Update prvSignalTime to the current signal's timestamp
					prvSignalTime = lastSignal.est_time;
					
					/// Check that there was not too much delay between the signal post time and now
					myTimeZone = lastSignal.est_time.AddHours(TimeZoneOffset);
					delayLimit = myTimeZone.AddSeconds(MaxDelaySeconds);
					
					if (MaxDelaySeconds == -1)
						Log($"Ignoring delay between Current Time ( {currentTime.ToString()} ) & Adjusted Post Time ( {myTimeZone.ToString()} )");
					else if (currentTime > delayLimit)
					{
						Log($"Rejected: Difference between Current Time ( {currentTime.ToString()} ) & Adjusted Post Time ( {myTimeZone.ToString()} ) Too Large (Max {MaxDelaySeconds} seconds)");
						return;
					}
					
					/// Handle buy/sell signals based on gammaflip value
					signalSum = sum;
					string extra = "";
					if (signalSum != 0)
						firstSignalRecevied = true;
					else
						extra = "  --  Current Position Confirmed";
					if (signalSum != 0)
					{
						Log($"gammaflip = {lastSignal.gammaflip} , deltaflip = {lastSignal.deltaflip}, thetaflip = {lastSignal.thetaflip}, vegaflip = {lastSignal.vegaflip}");
						Log($"Signal received @ {DateTime.Now.ToString(@"hh\:mm\:ss")}, dated {lastSignal.est_time}  :  Sum = {signalSum}" + extra);
					}
				}
				else if (lastSignal.est_time < prvSignalTime)
					Log($"SHOULD NOT HAPPEN : Rejected: Post Time ( {lastSignal.est_time.ToString()} ) older than previous signal ( {prvSignalTime.ToString()} )");
				//else Log($"Last signal rejected: Post Time ( {lastSignal.est_time.ToString()} ) is the same as previous signal ( {prvSignalTime.ToString()} )");
		    }
			catch (Exception ex)
			{
				Log("Error parsing JSON response: " + ex.Message);
			}
		}
		#endregion
	
		#region TRADE MANAGEMENT
		private void PlaceMarketOrder(string dir, int qty)
		{
			Log($"Placing market order, dir = {dir}, qty = {qty}");
			if (Position.MarketPosition != MarketPosition.Flat  &&  Position.MarketPosition.ToString() != dir)
			{
				Log($"Existing position is {Position.MarketPosition.ToString()}, so closing position first");
				ClosePosition("CloseOnRev");
				
				// Assume the close worked; need to reset this NOW because it's multi-threaded
				stopsSubmitted = false;
			}
			var action = (dir == "Long") ? OrderAction.Buy : OrderAction.SellShort;
			SubmitOrderUnmanaged(0, action, OrderType.Market, qty, 0, 0, "", "Entry");
		}
		
		private void ClosePosition(string name="Close", string dir="", int qty=0)
		{
			if (Position.MarketPosition == MarketPosition.Flat)
				return;
			
			if (qty == 0)	qty = Position.Quantity;
			if (dir == "")	dir = Position.MarketPosition.ToString();
			var action = (dir == "Long") ? OrderAction.Sell : OrderAction.BuyToCover;
			
			if (qty == Position.Quantity)
			{
				if (slOrder != null  ||  tpOrder != null)
				{
					Log($"Closing all contracts, and slOrder != null  ||  tpOrder != null, so Cancelling Stops");
					CancelStops();
					inContinuationTrade = false;	// assume it closes
				}
			}
			Log($"Closing Position: name = {name}, dir = {dir}, qty = {qty}");
			SubmitOrderUnmanaged(0, action, OrderType.Market, qty, 0, 0, "", name);
		}

		private void ModifyStopLoss(double newSL)
		{
			/// If there is no existing SL, we must place the order
			if (slOrder == null)
			{
				var action = (Position.MarketPosition == MarketPosition.Long) ? OrderAction.Sell : OrderAction.BuyToCover;
				oco = (tpOrder != null) ? tpOrder.Oco : "";
				Log($"No SL existed on Long position, so placing new SL order, price @ {newSL}");
				SubmitOrderUnmanaged(0, action, OrderType.StopMarket, Position.Quantity, 0, newSL, oco, "StopLoss");
			}
			else
			{
				Log($"Moving SL to new price @ {newSL} (either trail or move to breakeven)");
				ChangeOrder(slOrder, Position.Quantity, 0, newSL);
			}
		}

		private void ModifyTakeProfit(double newTP)
		{
			/// If there is no existing TP (e.g. No TakeProfit3 set, but TS_TakeProfitTicks set), then we must place the order
			if (tpOrder == null)
			{
				var action = (Position.MarketPosition == MarketPosition.Long) ? OrderAction.Sell : OrderAction.BuyToCover;
				oco = (slOrder != null) ? slOrder.Oco : "";
				Log($"No TP existed on Long position, so placing new TP order, price @ {newTP}");
				SubmitOrderUnmanaged(0, action, OrderType.Limit, Position.Quantity, newTP, 0, oco, "TakeProfit");
			}
			else
			{
				Log($"Moving TP3 to new price @ {newTP}");
				ChangeOrder(tpOrder, Position.Quantity, newTP, 0);
			}
		}
		
		private void CancelStops()
		{
			if (slOrder != null)
			{
				Log($"Cancelling slOrder");
				CancelOrder(slOrder);
				slOrder = null;
			}
			if (tpOrder != null)
			{
				Log($"Cancelling tpOrder");
				CancelOrder(tpOrder);
				tpOrder = null;
			}
		}
		
		private double AvePrice()
		{
			double avePrice = Position.AveragePrice;
			
			/// If ave price does not fall on tick boundary, round up
			int numTicks = (int)(avePrice / TickSize);
			if (numTicks * TickSize != avePrice)
			{
				double prc = Instrument.MasterInstrument.RoundToTickSize(avePrice);
				Log($"\n      AvePrice between ticks, Rounding, from {avePrice} to {prc}\n\n");
				avePrice = prc;
			}
			return(avePrice);
		}
		#endregion
		
		#region TRAIL & SWING
		/// Return new trailing stoploss value, or zero if none/unchanged
		private double GetNewTrailPrice(string direction)
		{
			double priceHLC = Close[0];
			double oldSL = 0;
			double newSL = 0;
			if (Position.MarketPosition.ToString() != direction  ||  !trailTriggered)
				return newSL;
			
			/// Working seems to not mean 'active', but that it's still in exchange queue. Accepted means it's active.
			if (slOrder != null  &&  (slOrder.OrderState == OrderState.Working  ||  slOrder.OrderState == OrderState.Accepted))
				oldSL = slOrder.StopPrice;
			Log("Trail: Old Stop Price = " + oldSL);

			double entryPrice = AvePrice();
			double ticksInProfit = (direction == "Short") ? (entryPrice - Close[0]) / TickSize : (Close[0] - entryPrice) / TickSize;
			double ticksToOldSL = 0;
			priceHLC = (TS_UseClose) ? Close[0] : (direction == "Short") ? Low[0] : High[0];
			if (oldSL != 0)
				ticksToOldSL = (direction == "Short") ? (oldSL - priceHLC) / TickSize : (priceHLC - oldSL) / TickSize;

			/// Set up Min and Max Ticks according to how much we are in profit
			int minDist = TS_MinTicks;
			int maxDist = TS_MaxTicks;
			if (TS_ReAdjustAtTicks2 > 0  &&  ticksInProfit >= TS_ReAdjustAtTicks2)
			{
				minDist = TS_MinTicks2;
				maxDist = TS_MaxTicks2;
			}
			else if (TS_ReAdjustAtTicks1 > 0  &&  ticksInProfit >= TS_ReAdjustAtTicks1)
			{
				minDist = TS_MinTicks1;
				maxDist = TS_MaxTicks1;
			}
				
			if (TS_UseSimpleTrail)
			{
				/// ticksToOldSL is zero if there is no old SL
				if (ticksToOldSL > TS_SimpleTicks)
				{
					if (direction == "Short") 
						newSL = priceHLC + TS_SimpleTicks*TickSize;
					else
						newSL = priceHLC - TS_SimpleTicks*TickSize;
				}
				if (newSL != 0)
					Log("Simple trail: OldSL = {oldSL}, NewSL = {newSL}");
				return newSL;
			}
			
			/// Loop through swing strengths from 5 down to 1, looking for 
			/// the highest strength that fits the min & max requirement
			/// Yes, this seems brute force, but c'est la vie!
			///
			/// No, screw that.  It seems like if we just look at strength=1, 
			/// in the small range between TS_MinTicks & TS_MaxTicks, and use 
			/// the first one we find, it will often actually have a better 
			/// strength.  Lets test it out...
			///
			/// Tried to write it do potentially do the original idea, but 
			/// it's pretty complex, so if this seems to work out fine, 
			/// functionally what we want, I should simplify this.  
			///
			/// And if not, need to make modifications as well...
			int i, j, ss;
			double distToFractal, slPrice;
			for (i=1; newSL == 0  &&  i < firstEntryBarsAgo; i++)
			{
				/// Until proven otherwise, assume the candle we are testing (index i) 
				/// DOES indeed the have the fractal H/L we seek, so start slPrice as H/L
				slPrice = (direction == "Short") ? High[i] - TS_OffsetTicks*TickSize : Low[i] + TS_OffsetTicks*TickSize;
				
				/// If slPrice wouldn't even move the stop, skip to next candle
				if (oldSL != 0)
					if ((direction == "Short") ? (oldSL <= slPrice) : (oldSL >= slPrice))
						continue;

				// Get the distance from the H/L (or Close) to the alleged fractal
				priceHLC = (TS_UseClose) ? Close[0] : (direction == "Short") ? Low[0] : High[0];
				distToFractal = (direction == "Short") ? (slPrice - priceHLC) / TickSize : (priceHLC - slPrice) / TickSize;
				
				// ss = swing strength; here hard-coded to 1
				for (ss=1; newSL == 0  &&  ss > 0; ss--)
				{
					for (j = 1; newSL == 0  &&  j <= ss; j++)
					{
						if (direction == "Short")
						{
							/// Not a Swing High; try next candle
							if (High[i+j] > High[i]  ||  High[i-j] > High[i])
								slPrice = 0;
						}
						else if (direction == "Long")
						{
							/// These aren't the droids you're looking for...
							if (Low[i+j] < Low[i]  ||  Low[i-j] < Low[i])
								slPrice = 0;
						}
						/// Found a LL or HH, so this ain't it; check next smaller SS
						if (slPrice == 0)
							break;	// out of for(j...) loop
						
						/// Found a fractal; see if its far enough and not too far from price
						else if (distToFractal >= minDist  &&  distToFractal <= maxDist)
						{
							if (oldSL == 0 
							|| (direction == "Short"  &&  slPrice < oldSL) 
							|| (direction == "Long"   &&  slPrice > oldSL))
							{
								newSL = slPrice;
								Log($"Found SwingStrength(1) fractal @ {Time[i].ToString()} (index {i}); set newSL = {newSL}  (oldSL = {oldSL})");
							}
						}
					}
				}
			}
			
			/// If we found no fractal, but our current stop is > max, we need to move it
			if (newSL == 0  &&  (ticksToOldSL > maxDist  ||  oldSL == 0))
			{
				Log($"No fractal found since trade opened; checking for max distance");
				priceHLC = (TS_UseClose) ? Close[0] : (direction == "Short") ? Low[0] : High[0];
				if (direction == "Short") 
				{
					newSL = priceHLC + maxDist*TickSize;
					if (newSL >= oldSL)	newSL = 0;
				}
				else
				{
					newSL = priceHLC - maxDist*TickSize;
					if (newSL <= oldSL)	newSL = 0;
				}
				if (newSL != 0)
					Log($"old SL too distant (or absent), so moving to {newSL}");
			}
			
			if (oldSL == 0)		// No old SL means we need to place new stoploss order
			{
				Log($"Calculated new SL price = {newSL}; old SL price = 0");
				return newSL;
			}
			
			// Otherwise return new SL value if it will actually move the stop in the correct direction
			if ((direction == "Long"   &&  newSL > oldSL)  
			||  (direction == "Short"  &&  newSL < oldSL))
			{
				Log($"Calculated new SL price = {newSL}; old SL price = {oldSL}");
				return newSL;
			}
			return 0;
		}
		
		/// This is used to get the last swing H/L for a breakout price used for continuation trades
		private double GetLastSignificantSwingPrice(string direction, int min=MIN_LOOKBACK, int add=ADD_LOOKBACK, int max=MAX_LOOKBACK)
		{
			/// Reset to zero
			lastSigSwingPrice = 0;
			
			if (direction != "Long"  &&  direction != "Short")
				return -1;
			///
			/// fetch the price of the last most significant swing H/L in the given direction
			/// (we use the term "Fractal" and "Swing H/L" interchangeably
			///

			/// To initialize, we want to use the last Strength(1) SwingHL
			int i, j;
			double initPrice = (direction == "Long") ? High[1] : Low[1];
			double price = (direction == "Long") ? High[1] : Low[1];
			int lastIdx = 1;
			for (i=1; i < 50; i++)
			{
				if (direction == "Short"  &&  Low[i+1] > Low[i]  &&  Low[i-1] > Low[i])
				{
					initPrice = price = Low[i];
					lastIdx = i;
					break;
				}
				else if (direction == "Long"  &&  High[i+1] < High[i]  &&  High[i-1] < High[i])
				{
					initPrice = price = High[i];
					lastIdx = i;
					break;
				}
			}
			if (i == 50)	Log($"{direction} : Could not find Strength(1) fractal in last 50 bars; using index[1] H/L @ {price}");
			else			Log($"{direction} : Found Strength(1) fractal at bar[{lastIdx}] ({Time[lastIdx].ToString()}) @ {price}");
			
			
			//Log($"Finding last {direction} fractal price...");
			/// Use hard-coded (for now at least):
			/// We will use an initial lookback of 61 bars, because often there is a volume/price 
			/// spike 60 minutes before 9:30 open.  Then we will keep looking back 20 bars at a 
			/// time until we do not find a more extreme swing price.
			int lookBack = MIN_LOOKBACK;
			int additional = 20;
			if (CurrentBars[0] - ContStrength < lookBack)
			{
				Log($"GetLastSignificantSwingPrice: There are only {CurrentBars[0]} bars on the chart, so must reduce look back period");
				lookBack = CurrentBars[0] - ContStrength;
			}
			
			int start = ContStrength + 1;
			int lb = lookBack;
			bool furtherFound = true;
			while (furtherFound  &&  lb < MAX_LOOKBACK)
			{
				/// iterate over last lookBack candles seeking fractal levels
				for (j = start; j < lb; j++)
				{
					/// Test candle 'j' to see if it qualifies as Swing
					bool newFound = true;
					for (i = 1; i <= ContStrength; i++)
					{
						if (direction == "Long")
						{
							if (High[j+i] > High[j]  ||  High[j-i] > High[j])	
							{
								newFound = false;
								break;
							}
						}
						else if (direction == "Short")
						{
							if (Low[j+i] < Low[j]  ||  Low[j-i] < Low[j])
							{
								newFound = false;
								break;
							}
						}
					}
					
					/// If it DID qualify and is more extreme than previous, save it
					if (newFound)
					{
						/// Save last swing index, even if it's not a new H/L (see below)
						//Log($"Continuation Fractal Found @ index {j} ({Time[j].ToString()})");
						if (direction == "Long"  &&  High[j] > price)
						{
							lastIdx = j;
							price = High[j];
							Log($"Fractal @ index {j} ({Time[j].ToString()}) also marks new High @ {price}");
						}
						else if (direction == "Short"  &&  Low[j] < price)
						{
							lastIdx = j;
							price = Low[j];
							Log($"Fractal @ index {j} ({Time[j].ToString()}) also marks new Low @ {price}");
						}
						/// No need to check the next 'ContStrength' candles; we know they are lower/higher
						j += ContStrength;
					}
				}
				
				/// If the lastIdx was more than (lookBack - additional) back, 
				/// then look back a bit further for a more extreme H/L
				if (lastIdx > lb - additional)
				{
					start = lb;
					lb += additional;
					//Log($"Last fractal index @ {lastIdx} was close to end, so look back {additional} bars further; start = {start}, new limit = {lb}");
				}
				/// If the lastIdx found was more than 20 candles forward 
				/// the earliest one checked, then just use this fractal point
				else
					furtherFound = false;
			}
			/// If whatever fractal found was not higher/lower than H/L[1], then we are 
			/// actively heading up/down and need to wait for at least a 1-bar retrace
			if (direction == "Short")
			{
				if (price > initPrice)
				{
					Log($"Fractal low found @ Bar[{lastIdx}] ({Time[lastIdx].ToString()}) was {price}, which is higher than last Low Swing(1) @ {initPrice}, so NONE FOUND");
					return -1;
				}
				else if (price > Low[1])
				{
					Log($"Fractal low found @ Bar[{lastIdx}] ({Time[lastIdx].ToString()}) was {price}, which is higher than Low[1] @ {Low[1]}, so NONE FOUND");
					return -1;
				}
			}
			else	// Long
			{
				if (price < initPrice)
				{
					Log($"Fractal high found @ Bar[{lastIdx}] ({Time[lastIdx].ToString()}) was {price}, which is lower than last High Swing(1) @ {initPrice}, so NONE FOUND");
					return -1;
				}
				else if (price < High[1])
				{
					Log($"Fractal high found @ Bar[{lastIdx}] ({Time[lastIdx].ToString()}) was {price}, which is lower than High[1] @ {High[1]}, so NONE FOUND");
					return -1;
				}
			}
			/// Set the H/L price of the fractal/swing candle we want to use
			lastSigSwingPrice = price;
			Log($"Found Fractal price level @ Bar[{lastIdx}] ({Time[lastIdx].ToString()}) = {price}");
			return price;
		}
		#endregion

		#region DEBUG & OCD
		private void ManageOCD()
		{
			string text = $"\n\n Strategy Name            :   {StrategyName}";
			text += $"\n Trade Session Active    :   {inSession}";
			if (lastTradeDir != "None"  &&  !firstSignalRecevied)
				text += $"\n Prevailing Dir @ Open :   {lastTradeDir}";
			else
				text += $"\n Last Signal Direction   :   {lastTradeDir}";

			if (ContTradesMode != ContTradesModeEnum.Disabled  &&  inSession)
			{
				text += $"\n\n Cont Trades Enabled   :   {contTradesEnabled}";
				text += $"\n Continuation Trades    :   {contTradesTaken}";
				text += $"\n Swing Price                :   {((lastSigSwingPrice == -1) ? "None" : lastSigSwingPrice.ToString("F2"))}";
			}

			text += $"\n Daily Limit Hit              :   {dailyLimitHit}";

			if (nextCheckTime != DateTime.MinValue)
			{
				TimeSpan remaining = nextCheckTime - DateTime.Now;
				text += $"\n\n Next Signal Check in    :   {remaining.ToString(@"hh\:mm\:ss")}";
				//text += $"\n\n Next Signal Check in:   {nextCheckTime.ToString(@"hh\:mm\:ss")}";	// old method
			}

			if (lastDashboard == text)
				return;
			
			Draw.TextFixed(this, "OCD", text, TextPosition.TopLeft);	
			lastDashboard = text;
		}
		
		private void Log(string message, [CallerMemberName] string memberName = "")
		{
			if (DisableLogging  ||  lastLogMsg == message)
				return;

			// Set this scripts Print() calls to the second output tab?
			if (UseOutput2)		PrintTo = PrintTo.OutputTab2;
			else				PrintTo = PrintTo.OutputTab1;
			
			string id = "EA #" + UniqueID;
			
			DateTime date = (State == State.Historical) ? Time[0] : DateTime.Now;
			string dateStr = (State == State.Historical) ? date.ToString() : date.ToString(@"HH\:mm\:ss");
			
			if (lastCaller != memberName)
			{
				Print($"\n{id} - {chartInstrument} - [{memberName}]  -  {dateStr}  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -\n");
				Print(message);
			}
			else if (lastLogMsg != message)
			{
				if (lastLogTime != dateStr)	// Output just time if diff time but not new caller
					Print(message + $"   ( {dateStr} )");
				else
					Print(message);
			}

			lastLogMsg = message;
			lastCaller = memberName;
			lastLogTime = dateStr;
		}		
		#endregion
		
		#region FILTERS
		/// Enter a trade [set] if all enabled filters are satisfied
		private bool TradeFiltersOkay(string direction)
		{
			if (direction != "Long"  &&  direction != "Short")
			{
				Log($"Direction passed into TradeFiltersOkay ({direction}) must be 'Long' or 'Short'; Aborting");
				return false;
			}
			
			/// Save the last signal direction (even if actual trade is blocked by filters)
			lastTradeDir = direction;
			
			if (direction == "Long")
			{
				if (!longOn)
				{
					Log($"Long trade disabled; Aborting entry");
					direction = "None";
					return false;
				}
			}
			else if (direction == "Short")
			{
				if (!shortOn)
				{
					Log($"Short trade disabled; Aborting entry");
					direction = "None";
					return false;
				}
			}
			
			if (VOLMA(Closes[0], AveVolumePeriod)[1] < MinAveVolume)
			{
				Log($"Volume insufficient ({VOLMA(Closes[0], AveVolumePeriod)[1]} vs. {MinAveVolume} minimum); Aborting {direction} entry");
				return false;
			}
			if (!CheckSession())
			{
				Log($"Out of session; Aborting {direction} entry");
				return false;
			}
			return true;
		}
		
		/// Check trading sessions; return false if out-of-session
		private bool CheckSession()
		{
			if (!LimitTradingHours)
				return true;
			
			bool hoursOk = false; 
			if (LimitTradingHours)
			{
				if (StartTime.TimeOfDay < EndTime.TimeOfDay)
				{
					if (Time[0].TimeOfDay >= StartTime.TimeOfDay  &&  Time[0].TimeOfDay < EndTime.TimeOfDay)
						hoursOk = true;
				}
				else if (StartTime.TimeOfDay > EndTime.TimeOfDay)
				{
					if (Time[0].TimeOfDay >= StartTime.TimeOfDay  ||  Time[0].TimeOfDay < EndTime.TimeOfDay)
						hoursOk = true;
				}
				else // (StartTime1.TimeOfDay == EndTime1.TimeOfDay)
				{
					Log($"Start Time 1 is the same as End Time 1; Trading [always] approved");
						hoursOk = true;
				}
			}
			return hoursOk;
		}
		#endregion
		
		#region BUTTONS
		private void OnMyButtonClick(object sender, RoutedEventArgs rea)
		{
			System.Windows.Controls.Button button = sender as System.Windows.Controls.Button;
			
			Log("\n");
			bool lWasOn = longOn;
			bool sWasOn = shortOn;
			if (button.Name == "longButton")
			{
				if (longOn)
				{
					button.Background = Brushes.Gray;
					button.Content = "Long Disabled";
					longOn = false;
					Log("\nLONG ENTRIES DISABLED!");
				}
				else
				{
					button.Background = Brushes.Green;
					button.Content = "Long Enabled";
					longOn = true;
					Log("\nLong entries Enabled!");
				}
			}
			else if (button.Name == "shortButton")
			{
				if (shortOn)
				{
					button.Background = Brushes.Gray;
					button.Content = "Short Disabled";
					shortOn = false;
					Log("\nSHORT ENTRIES DISABLED!");
				}
				else
				{
					button.Background = Brushes.Crimson;
					button.Content = "Short Enabled";
					shortOn = true;
					Log("\nShort entries Enabled!");
				}
			}
			else if (button.Name == "exitButton")
			{
				if (Position.MarketPosition == MarketPosition.Flat)
				Log("\nUser-Instigated Close of All Open Strategy Positions, but nothing open..");
				else
				{
					Log("\n\n=====================================================");
					Log("User-Instigated Close of All Open Strategy Positions");
					Log("=====================================================\n");
					ExitLong("User Instigated", "Long");
					ExitShort("User Instigated", "Short");
				}
			}
			else if (button.Name == "logSignalsButton")
			{
				logAllSignals = true;
				CheckSignalSync();
			}
			if (button.Name == "increaseSum")
			{
				fakeSignalSum++;
				Log($"fakeSignalSum increased to: {fakeSignalSum}");
				if (fakeSignalSum > 0)
				{
					button.Content = "Incr, Sum = " + fakeSignalSum;
					button.Background = Brushes.Orange;
				}
				else
				{
					button.Content = "Increase Sum";
					button.Background = Brushes.Yellow;
				}
				if (fakeSignalSum < 0)
				{
					decreaseSum.Content = "Decr, Sum = " + fakeSignalSum;
					decreaseSum.Background = Brushes.Orange;
				}
				else
				{
					decreaseSum.Content = "Decrease Sum";
					decreaseSum.Background = Brushes.Yellow;
				}
			}
			else if (button.Name == "decreaseSum")
			{
				fakeSignalSum--;
				Log($"fakeSignalSum decreased to: {fakeSignalSum}");
				if (fakeSignalSum < 0)
				{
					button.Content = "Decr, Sum = " + fakeSignalSum;
					button.Background = Brushes.Orange;
				}
				else
				{
					button.Content = "Decrease Sum";
					button.Background = Brushes.Yellow;
				}
				if (fakeSignalSum > 0)
				{
					increaseSum.Content = "Incr, Sum = " + fakeSignalSum;
					increaseSum.Background = Brushes.Orange;
				}
				else
				{
					increaseSum.Content = "Increase Sum";
					increaseSum.Background = Brushes.Yellow;
				}
			}
			
			if (!longOn  &&  !shortOn)
				if (lWasOn  ||  sWasOn)
					Log("ALL ENTRIES DISABLED!");
			else if (longOn  &&  shortOn)
				if (!lWasOn  ||  !sWasOn)
					Log("ALL ENTRIES ENABLED!");
			Log("\n");

			entriesEnabled = (longOn  ||  shortOn);
		}
		
		
		protected void CreateButtons()
		{
			if (UserControlCollection.Contains(myGrid))
		        return;
		
			myGrid = new System.Windows.Controls.Grid
	        {
	          Name = "MyCustomGrid",
	          HorizontalAlignment = HorizontalAlignment.Left,
	          VerticalAlignment = VerticalAlignment.Bottom,
	        };
	 	 
	        myGrid.ColumnDefinitions.Add(new System.Windows.Controls.ColumnDefinition());
	        myGrid.ColumnDefinitions.Add(new System.Windows.Controls.ColumnDefinition());
	        myGrid.ColumnDefinitions.Add(new System.Windows.Controls.ColumnDefinition());
			myGrid.RowDefinitions.Add(new System.Windows.Controls.RowDefinition());
			myGrid.RowDefinitions.Add(new System.Windows.Controls.RowDefinition());
			myGrid.ColumnDefinitions[0].Width = new GridLength(80);
			myGrid.ColumnDefinitions[1].Width = new GridLength(80);
			myGrid.ColumnDefinitions[2].Width = new GridLength(80);
			myGrid.RowDefinitions[0].Height = new GridLength(25);
			myGrid.RowDefinitions[1].Height = new GridLength(25);
	 
	        longButton = new System.Windows.Controls.Button
	        {
	          	Name = "longButton",
	          	Content = "Long Enabled",
	          	Foreground = Brushes.White,
	          	Background = Brushes.Green,
				Height = 25,
				Width = 80,
				FontSize = 10,
				HorizontalAlignment = HorizontalAlignment.Center,
				Focusable = false
	        };
			
			shortButton = new System.Windows.Controls.Button
	        {
	          	Name = "shortButton",
	          	Content = "Short Enabled",
	          	Foreground = Brushes.White,
	          	Background = Brushes.Crimson,
				Height = 25,
				Width = 80,
				FontSize = 10,
				HorizontalAlignment = HorizontalAlignment.Center,
				Focusable = false
	        };
	 
			exitButton = new System.Windows.Controls.Button
	        {
	          	Name = "exitButton",
	          	Content = "Exit All",
	          	Foreground = Brushes.White,
	          	Background = Brushes.DarkMagenta,
				Height = 25,
				Width = 80,
				FontSize = 10,
				HorizontalAlignment = HorizontalAlignment.Center,
				Focusable = false
	        };
	 
			logSignalsButton = new System.Windows.Controls.Button
	        {
	          	Name = "logSignalsButton",
	          	Content = "Log Signals",
	          	Foreground = Brushes.White,
	          	Background = Brushes.Blue,
				Height = 25,
				Width = 80,
				FontSize = 10,
				HorizontalAlignment = HorizontalAlignment.Center,
				Focusable = false
	        };
	 
			increaseSum = new System.Windows.Controls.Button
	        {
	          	Name = "increaseSum",
	          	Content = "Increase Sum",
	          	Foreground = Brushes.Black,
	          	Background = Brushes.Yellow,
				Height = 25,
				Width = 80,
				FontSize = 10,
				HorizontalAlignment = HorizontalAlignment.Center,
				Focusable = false
	        };
	 
			decreaseSum = new System.Windows.Controls.Button
	        {
	          	Name = "decreaseSum",
	          	Content = "Decrease Sum",
	          	Foreground = Brushes.Black,
	          	Background = Brushes.Yellow,
				Height = 25,
				Width = 80,
				FontSize = 10,
				HorizontalAlignment = HorizontalAlignment.Center,
				Focusable = false
	        };
		
			longButton.Click += OnMyButtonClick;
			shortButton.Click += OnMyButtonClick;
			exitButton.Click += OnMyButtonClick;
			logSignalsButton.Click += OnMyButtonClick;
			increaseSum.Click += OnMyButtonClick;
			decreaseSum.Click += OnMyButtonClick;
			
	        System.Windows.Controls.Grid.SetColumn(shortButton, 1);
			System.Windows.Controls.Grid.SetRow(shortButton, 0);
			System.Windows.Controls.Grid.SetColumn(longButton, 0);
			System.Windows.Controls.Grid.SetRow(longButton, 0);
			System.Windows.Controls.Grid.SetColumn(exitButton, 0);
			System.Windows.Controls.Grid.SetRow(exitButton, 1);
			System.Windows.Controls.Grid.SetColumn(logSignalsButton, 1);
			System.Windows.Controls.Grid.SetRow(logSignalsButton, 1);
			System.Windows.Controls.Grid.SetColumn(increaseSum, 2);
			System.Windows.Controls.Grid.SetRow(increaseSum, 0);
			System.Windows.Controls.Grid.SetColumn(decreaseSum, 2);
			System.Windows.Controls.Grid.SetRow(decreaseSum, 1);
			
	        myGrid.Children.Add(longButton);
			myGrid.Children.Add(shortButton);
			myGrid.Children.Add(exitButton);
			myGrid.Children.Add(logSignalsButton);
			myGrid.Children.Add(increaseSum);
			myGrid.Children.Add(decreaseSum);
			
	        UserControlCollection.Add(myGrid);
		}
		
		
		public void DisposeButtons() 
		{
			if (myGrid != null)
	        {
				if (longButton != null)
				{
					myGrid.Children.Remove(longButton);
					longButton.Click -= OnMyButtonClick;
					longButton = null;
				}
				if (shortButton != null)
				{
					myGrid.Children.Remove(shortButton);
					shortButton.Click -= OnMyButtonClick;
					shortButton = null;
				}
				if (exitButton != null)
				{
					myGrid.Children.Remove(exitButton);
					exitButton.Click -= OnMyButtonClick;
					exitButton = null;
				}
				if (logSignalsButton != null)
				{
					myGrid.Children.Remove(logSignalsButton);
					logSignalsButton.Click -= OnMyButtonClick;
					logSignalsButton = null;
				}
				if (increaseSum != null)
				{
					myGrid.Children.Remove(increaseSum);
					increaseSum.Click -= OnMyButtonClick;
					increaseSum = null;
				}
				if (decreaseSum != null)
				{
					myGrid.Children.Remove(decreaseSum);
					decreaseSum.Click -= OnMyButtonClick;
					decreaseSum = null;
				}
	        }
		}

		private void UpdateIncreaseSumButton(string content, Brush background)
		{
			if (Dispatcher.CheckAccess())
			{
				increaseSum.Content = content;
				increaseSum.Background = background;
			}
			else
			{
				Dispatcher.Invoke(() => {
					increaseSum.Content = content;
					increaseSum.Background = background;
				});
			}
		}
		
		private void UpdateDecreaseSumButton(string content, Brush background)
		{
			if (Dispatcher.CheckAccess())
			{
				decreaseSum.Content = content;
				decreaseSum.Background = background;
			}
			else
			{
				Dispatcher.Invoke(() => {
					decreaseSum.Content = content;
					decreaseSum.Background = background;
				});
			}
		}
		#endregion
				
		#region PROPERTIES
		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name = "Quantity Multiplier", Description = "Multiplier for the number of contracts to place, given by signal", Order=1, GroupName = "1. Entry")]
		public int QuantityMult
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name = "Max Contracts", Description = "Maximum number of contracts we can have open at once", Order=2, GroupName = "1. Entry")]
		public int MaxContracts
		{ get; set; }
		
		[NinjaScriptProperty]
        [Display(Name = "Last Trade Direction", Description = "Override for initial Last Trade Direction", Order=3, GroupName = "1. Entry")]
        public string LastTradeDir
        { get; set; }

		[NinjaScriptProperty]
		[Range(1, 300)]
		[Display(Name = "Check URL Interval", Description = "How many seconds between URL signal checks", Order=5, GroupName = "1. Entry")]
		public int CheckSeconds
		{ get; set; }

		[NinjaScriptProperty]
		[Range(1, 300)]
		[Display(Name = "Signal Delay Max Seconds", Description = "Maximum seconds allowed between signal time stamp and entry time", Order=6, GroupName = "1. Entry")]
		public int MaxDelaySeconds
		{ get; set; }

		[NinjaScriptProperty]
		[Range(-23, 23)]
		[Display(Name = "Timezone Offset", Description = "Number of hours to add the signal PostTime to compare with local time", Order=7, GroupName = "1. Entry")]
		public int TimeZoneOffset
		{ get; set; }

		
		
		[NinjaScriptProperty]
		[Display(Name = "Continuation Trades Mode", Description = "How continuation trades are handled", Order=10, GroupName = "2. ReEntry")]
		public ContTradesModeEnum ContTradesMode { get; set; }
		
		[NinjaScriptProperty]
        [Display(Name = "BiDirectional", Description = "If no previous trade / prevailing direction, trade either breakout", Order=11, GroupName = "2. ReEntry")]
        public bool BiDirectional
        { get; set; }
		
		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name = "Quantity", Description = "Contracts to trade on a Continuation signal", Order=12, GroupName = "2. ReEntry")]
		public int ContQuantity
		{ get; set; }
		
		[NinjaScriptProperty]
		[Display(Name = "Continuation Trades Number", Description = "Number of continuation trades to take", Order=14, GroupName = "2. ReEntry")]
		public ContTradesNumberEnum ContTradesNum { get; set; }
		
		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name = "Continuation Swing Strength", Description = "Swing ind setting for H/L on Prevailing & Continuation trades", Order=15, GroupName = "2. ReEntry")]
		public int ContStrength
		{ get; set; }
		
		[NinjaScriptProperty]
        [Display(Name = "Require MA position", Description = "Require fast EMA above slow for long cont. trade and vice versa", Order=16, GroupName = "2. ReEntry")]
        public bool ContReqMAPosition
        { get; set; }
		
        [NinjaScriptProperty]
        [Range(2, int.MaxValue)]
        [Display(Name = "Slow EMA Period", Description = "Period parameter of Slow EMA", Order=18, GroupName = "2. ReEntry")]
        public int SlowEMA_Period
        { get; set; }

        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "Fast EMA Period", Description = "Period parameter of Fast EMA", Order=19, GroupName = "2. ReEntry")]
        public int FastEMA_Period
        { get; set; }
		

		
		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name = "Stop Loss", Description = "Initial stop loss in ticks", Order=20, GroupName = "3. Exits")]
		public int StopLoss
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name = "Take Profit", Description = "Take profit in ticks", Order=21, GroupName = "3. Exits")]
		public int TakeProfit
		{ get; set; }
		
		[NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name = "Daily Target Min $", Description = "Profit amount at which to stop trading for the day", Order=23, GroupName = "3. Exits")]
        public int DailyMinTarget
        { get; set; }
		
		[NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name = "Daily Loss Max $", Description = "Loss amount at which to stop trading for the day", Order=24, GroupName = "3. Exits")]
        public int DailyMaxLoss
        { get; set; }
		
		
		
		[NinjaScriptProperty]
		[Range(int.MinValue, int.MaxValue)]
		[Display(Name = "BE Trigger #1", Description = "1st profit triger in ticks", Order=30, GroupName = "4. Break Even")]
		public int BETrigger1
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(int.MinValue, int.MaxValue)]
		[Display(Name = "BE Offset #1", Description = "1st move to breakeven offset", Order=31, GroupName = "4. Break Even")]
		public int BEOffset1
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(int.MinValue, int.MaxValue)]
		[Display(Name = "BE Trigger #2", Description = "2nd profit triger in ticks", Order=32, GroupName = "4. Break Even")]
		public int BETrigger2
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(int.MinValue, int.MaxValue)]
		[Display(Name = "BE Offset #2", Description = "2nd move to breakeven offset", Order=33, GroupName = "4. Break Even")]
		public int BEOffset2
		{ get; set; }
		
		
		
		[NinjaScriptProperty]
        [Display(Name = "Use Trailing Stop", Description = "", Order=40, GroupName = "5. Trailing Stop")]
        public bool UseTrailingStop
        { get; set; }
		
        [NinjaScriptProperty]
        [Display(Name = "Use Simple Trail", Description = "Trail tick-for-tick", Order=41, GroupName = "5. Trailing Stop")]
        public bool TS_UseSimpleTrail
        { get; set; }
		
        [NinjaScriptProperty]
        [Range(int.MinValue, int.MaxValue)]
        [Display(Name = "Simple Trail Ticks", Description = "Number of ticks offset from Swing or Candle H/L; Negative to allow more cushion", Order=42, GroupName = "5. Trailing Stop")]
        public int TS_SimpleTicks
        { get; set; }
		
        [NinjaScriptProperty]
        [Display(Name = "Use Candle Close", Description = "Use candle close price, not H/L", Order=43, GroupName = "5. Trailing Stop")]
        public bool TS_UseClose
        { get; set; }
		
		[NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name = "Take Profit Ticks", Description = "Expand Take Profit to this when activated", Order=45, GroupName = "5. Trailing Stop")]
        public int TS_TakeProfitTicks
        { get; set; }

        [NinjaScriptProperty]
        [Range(int.MinValue, int.MaxValue)]
        [Display(Name = "Tick Offset", Description = "Number of ticks offset from Swing or Candle H/L; Negative to allow more cushion", Order=46, GroupName = "5. Trailing Stop")]
        public int TS_OffsetTicks
        { get; set; }
		
        [NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name = "Activation Ticks", Description = "Number of ticks in profit to activate trail", Order=47, GroupName = "5. Trailing Stop")]
        public int TS_ActivationTicks
        { get; set; }

        [NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name = "Minimum Tick Distance", Description = "Minimum number of ticks SL can be placed from current price", Order=48, GroupName = "5. Trailing Stop")]
        public int TS_MinTicks
        { get; set; }

        [NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name = "Maximum Tick Distance", Description = "Maximum number of ticks SL can be placed from current price", Order=50, GroupName = "5. Trailing Stop")]
        public int TS_MaxTicks
        { get; set; }

        [NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name = "Adjust Min/Max Ticks #1", Description = "Adjust min/max number of ticks after reaching this profit level (Zero to disable)", Order=51, GroupName = "5. Trailing Stop")]
        public int TS_ReAdjustAtTicks1
        { get; set; }

        [NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name = "Minimum Tick Distance #1", Description = "Minimum number of ticks SL can be placed from current price", Order=52, GroupName = "5. Trailing Stop")]
        public int TS_MinTicks1
        { get; set; }

        [NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name = "Maximum Tick Distance #1", Description = "Maximum number of ticks SL can be placed from current price", Order=53, GroupName = "5. Trailing Stop")]
        public int TS_MaxTicks1
        { get; set; }

        [NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name = "Adjust Min/Max Ticks #2", Description = "Adjust min/max number of ticks after reaching this profit level (Zero to disable)", Order=55, GroupName = "5. Trailing Stop")]
        public int TS_ReAdjustAtTicks2
        { get; set; }

        [NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name = "Minimum Tick Distance #2", Description = "Minimum number of ticks SL can be placed from current price", Order=56, GroupName = "5. Trailing Stop")]
        public int TS_MinTicks2
        { get; set; }

        [NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name = "Maximum Tick Distance #2", Description = "Maximum number of ticks SL can be placed from current price", Order=57, GroupName = "5. Trailing Stop")]
        public int TS_MaxTicks2
        { get; set; }

		
		
		[NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "Average Volume Period", Description = "Period parameter to use for Average Volume", Order=60, GroupName = "6. Volume")]
        public int AveVolumePeriod
        { get; set; }
		
		[NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "Minimum Average Volume", Description = "Minimum Average Volume to place trade", Order=61, GroupName = "6. Volume")]
        public int MinAveVolume
        { get; set; }
		

		
		[NinjaScriptProperty]
        [Display(Name = "Limit Trading Hours", Description = "Use Trading Session #1", Order=70, GroupName = "7. Trading Hours")]
        public bool LimitTradingHours
        { get; set; }

        [NinjaScriptProperty]
        [PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
        [Display(Name = "Start Time", Description = "Enter Start Time for Entries", Order=71, GroupName = "7. Trading Hours")]
        public DateTime StartTime
        { get; set; }

        [NinjaScriptProperty]
        [PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
        [Display(Name = "End Time", Description = "Enter End Time for Entries", Order=72, GroupName = "7. Trading Hours")]
        public DateTime EndTime
        { get; set; }

		[NinjaScriptProperty]
        [Display(Name = "Use Close Time", Description = "Enable to close all trades at given time", Order=73, GroupName = "7. Trading Hours")]
        public bool UseCloseTime
        { get; set; }

        [NinjaScriptProperty]
        [PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
        [Display(Name = "Close Time", Description = "Enter Time to Close All Trades", Order=74, GroupName = "7. Trading Hours")]
        public DateTime CloseTime
        { get; set; }

		
		
		[NinjaScriptProperty]
        [Display(Name = "Display OCD", Description = "Show the On-Chart Display", Order=80, GroupName = "8. Misc / Debug")]
        public bool DisplayOCD
        { get; set; }

		[NinjaScriptProperty]
        [Display(Name = "Strategy Name", Description = "Name of Strategy", Order=81, GroupName = "8. Misc / Debug")]
        public string StrategyName
        { get; set; }

		[NinjaScriptProperty]
        [Display(Name = "Disable Logging", Description = "Disable logging to Ninjascript Output windows", Order=82, GroupName = "8. Misc / Debug")]
        public bool DisableLogging
        { get; set; }

		[NinjaScriptProperty]
        [Display(Name = "Use Output 2", Description = "Send logging to second Output window", Order=83, GroupName = "8. Misc / Debug")]
        public bool UseOutput2
        { get; set; }

		[NinjaScriptProperty]
        [Display(Name = "Unique ID", Description = "Unique number to identify strategy in Output window", Order=84, GroupName = "8. Misc / Debug")]
        public string UniqueID
        { get; set; }
		#endregion
	}
}






