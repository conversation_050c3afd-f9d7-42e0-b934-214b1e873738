#region Using declarations
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Gui.Tools;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.Indicators;
using NinjaTrader.NinjaScript.DrawingTools;
#endregion

//This namespace holds Strategies in this folder and is required. Do not change it. 
namespace NinjaTrader.NinjaScript.Strategies
{
	public class TepVDS : Strategy
	{
		
		#region Globals
		private ATR atr;
		private double cSH, pSH, cHTF_SH, pHTF_SH, cSL, pSL, cHTF_SL, pHTF_SL, cDaily, pDaily, currentTrailSwingHigh, currentTrailSwingLow;
		private int swTrendCTF, swTrendHTF, macdCandles, lMacd, hMacd, Signal, halfQuantity, entryBar;
		private MACD macd, higherMacd;
		private string barNum;
		private EMA slow, fast;
		private bool trade, beTriggered, trailTriggered, divergenceOk;
		private Order stopOrder = null;
		private Order entryOrder = null;
		
		private System.Windows.Controls.Button shortButton, longButton;
		private System.Windows.Controls.Grid   myGrid;
		
		private bool shorton, longon;
		#endregion
		
		protected override void OnStateChange()
		{
			if (State == State.SetDefaults)
			{
				Description									= @"Enter the description for your new custom Strategy here.";
				Name										= "TepVDS";
				Calculate									= Calculate.OnBarClose;
				EntriesPerDirection							= 2;
				EntryHandling								= EntryHandling.AllEntries;
				IsExitOnSessionCloseStrategy				= true;
				ExitOnSessionCloseSeconds					= 30;
				IsFillLimitOnTouch							= false;
				MaximumBarsLookBack							= MaximumBarsLookBack.TwoHundredFiftySix;
				OrderFillResolution							= OrderFillResolution.Standard;
				Slippage									= 0;
				StartBehavior								= StartBehavior.WaitUntilFlat;
				TimeInForce									= TimeInForce.Gtc;
				TraceOrders									= false;
				RealtimeErrorHandling						= RealtimeErrorHandling.StopCancelClose;
				StopTargetHandling							= StopTargetHandling.ByStrategyPosition;
				BarsRequiredToTrade							= 20;
				// Disable this property for performance gains in Strategy Analyzer optimizations
				// See the Help Guide for additional information
				IsInstantiatedOnEachOptimizationIteration	= true;
				
				/// ATR
				ATRLength					= 14;
				/// Swing
				RequireTrendDirection 		= true;
				HigherTimeFrameMinutes		= 5;
				Strength					= 5;
				/// MACD
				MACD_Fast				= 20;
				MACD_Slow				= 120;
				MACD_Smooth				= 1;
				MACD_Candles			= 5;
				MACD_Percent			= 90;
				RequireMACD_SlopeDir	= true;
				TradeHTF_MACD_DirOnly	= true;

				
				MaxEntryCandles = 0;
				
				/// EMA
				UseSlowEMA			= true;
				SlowEMA_Period		= 120;
				SlowEMA_TicksPast	= 0;
				UseFastEMA			= true;
				FastEMA_Period		= 20;
				FastEMA_TicksPast	= 0;
				RequirePosition		= true;
				Position_TicksPast	= 0;
				UseSlopeException	= false;
				SlopeATR_Div		= 3.0;
				/// Volume
				AveVolumePeriod 	= 14;
				MinAveVolume		= 50;
				DailyMinTarget		= 0;		
				DailyMaxLoss		= 0;
				
				StopLoss			= 0;
				ProfitTarget		= 0;
				BETrigger			= 0;
				BEOffset			= 2;
				
				/// Trail
				UseTrailStop 		= true;
				TS_TakeProfitTicks 		= 100;
                TS_ActivationTicks 		= 18;
                TS_Offset 				= 2;
                TS_SwingStrength 		= 2;
                TS_MinSwingTickDist 	= 12;
                TS_CandleTrailTickDist 	= 15;
                TS_DivergenceOnly 		= true;
				TS_LookbackBars			= 5;
				TS_MinDivrPercent 		= 50;
				
				Quantity				= 1;
				
				SplitOrder				= false;
				UseRetracePercentage	= false;
				MinCandleBodyTicks		= 8;
				RetracePercentage		= 33;

				LimitExpirationBars		= 15;
				TP_PercentReached		= 50;

				
				limitTradingHours		= false;
				StartTime				= DateTime.Parse("8:00", System.Globalization.CultureInfo.InvariantCulture);
                EndTime 				= DateTime.Parse("10:00", System.Globalization.CultureInfo.InvariantCulture);
				
				limitTradingHours1		= false;
				StartTime1				= DateTime.Parse("8:00", System.Globalization.CultureInfo.InvariantCulture);
                EndTime1 				= DateTime.Parse("10:00", System.Globalization.CultureInfo.InvariantCulture);

			}
			
			else if (State == State.Configure)
			{
				cSH = 0;
				pSH = 0; 
				cHTF_SH = 0;
				pHTF_SH = 0;
				cSL = 0;
				pSL = 0;
				cHTF_SL = 0;
				pHTF_SL = 0;
				swTrendCTF = 0;
				swTrendHTF = 0;
				macdCandles = (int) Math.Floor((double) MACD_Candles * MACD_Percent/100);
				lMacd = 0;
				hMacd = 0;
				Signal = 0;
				barNum = "0";
				cDaily = 0;
				pDaily = 0;
				trade = true;
				beTriggered = false;
				trailTriggered = false;
				divergenceOk = !TS_DivergenceOnly;
				currentTrailSwingHigh = 0;
				currentTrailSwingLow = 0;
				halfQuantity = (int) Math.Ceiling((double) Quantity/2);
				entryBar = 0;
				shorton = true;
				longon = true;
				
				AddDataSeries(BarsPeriodType.Minute, HigherTimeFrameMinutes);
				AddDataSeries(BarsPeriodType.Tick, 1);
			}
			
			else if (State == State.DataLoaded)
			{
				atr = ATR(ATRLength);
				macd = MACD(MACD_Fast, MACD_Slow, MACD_Smooth);
				higherMacd = MACD(Closes[1], MACD_Fast, MACD_Slow, MACD_Smooth);
				slow = EMA(SlowEMA_Period);
				fast = EMA(FastEMA_Period);
			}
			
			else if (State == State.Historical)
			{
				
				if (ChartControl != null)
				{
					ChartControl.Dispatcher.InvokeAsync(() =>
					{
						CreateButtons();
					});
				}	
			}
			
			else if (State == State.Terminated)
			{
				if (ChartControl != null)
				{
					ChartControl.Dispatcher.InvokeAsync(() =>
					{
						DisposeButtons();
					});
				}
			}
		}

		protected override void OnExecutionUpdate(Cbi.Execution execution, string executionId, double price, int quantity, 
			Cbi.MarketPosition marketPosition, string orderId, DateTime time)
		{
			if (execution.IsEntryStrategy)
			{
				if (marketPosition == MarketPosition.Long)
				{
					if (StopLoss != 0)
						ExitLongStopMarket(0, true, Position.Quantity, price - StopLoss*TickSize, "Stop Loss", "Long");
					if (ProfitTarget != 0)
						ExitLongLimit(0, true, Position.Quantity, price + ProfitTarget*TickSize, "Profit Target", "Long");
				}
				else
				{
					if (StopLoss != 0)
						ExitShortStopMarket(0, true, Position.Quantity, price + StopLoss*TickSize, "Stop Loss", "Short");
					if (ProfitTarget != 0)
						ExitShortLimit(0, true, Position.Quantity, price - ProfitTarget*TickSize, "Profit Target", "Short");
				}
			}
			if (Position.Quantity == 0)
			{
				beTriggered = false;
				trailTriggered = false;
				
				if (SystemPerformance.AllTrades.TradesPerformance.NetProfit - pDaily < -DailyMaxLoss)
				{
					ExitLong("Daily Loss", "Long");
					ExitShort("Daily Loss", "Short");
					trade = false;
				}
				if (SystemPerformance.AllTrades.TradesPerformance.NetProfit - pDaily > DailyMinTarget)
				{
					ExitLong("Daily Target", "Long");
					ExitShort("Daily Target", "Short");
					trade = false;
				}
			}
		}

		
		protected override void OnOrderUpdate(Cbi.Order order, double limitPrice, double stopPrice, 
			int quantity, int filled, double averageFillPrice, 
			Cbi.OrderState orderState, DateTime time, Cbi.ErrorCode error, string comment)
		{
			if (order.Name == "Stop Loss")
			{
				if (order.OrderState != OrderState.Filled)
					stopOrder = order;
				else
					stopOrder = null;
				
			}
		}

		
		protected override void OnBarUpdate()
		{
			if (CurrentBars[0] < BarsRequiredToTrade  ||  CurrentBars[1] < BarsRequiredToTrade)
				return;
			
			if (BarsInProgress == 0)
			{
				#region Main
				
				if (Bars.IsFirstBarOfSession)
				{
					pDaily = SystemPerformance.AllTrades.TradesPerformance.NetProfit;
					trade = true;
				}
				if (!trade)
					return;
				
				#region Trend Direction by Swing
				bool HighFractal = true;
				bool LowFractal = true;
				
				for (int i = 0; i <= Strength*2; i++)
				{
					if (i < Strength)
					{
						if (High[i] > High[Strength])
							HighFractal = false;
						if (Low[i] < Low[Strength])
							LowFractal = false;
					}
					else if (i > Strength)
					{
						if (High[i] >= High[Strength])
							HighFractal = false;
						if (Low[i] <= Low[Strength])
							LowFractal = false;
					}
					else
						continue;
				}
				
				if (HighFractal)
				{
					pSH = cSH;
					cSH = High[Strength];
				}
				if (LowFractal)
				{
					pSL = cSL;
					cSL = Low[Strength];
				}
					
				// Set dir of trend based on Swing higher timeframe and chart timeframe
				swTrendCTF = cSH > pSH  &&  cSL > pSL ? 1 : cSL < pSL  &&  cSH < pSH ? -1 : 0;
				swTrendHTF = cHTF_SH > pHTF_SH  &&  cHTF_SL > pHTF_SL ? 1 : cHTF_SL < pHTF_SL  &&  cHTF_SH < pHTF_SH ? -1 : 0;
					
				#endregion
				
				#region Macd Trend
				int lGoingUp = 0;
				int lGoingDown = 0;
				for (int i = 0; i < macdCandles; i++)
				{
					if (macd[i] > macd[i + 1])
						lGoingUp++;
					if (macd[i] < macd[i + 1])
						lGoingDown++;
				}
				
				lMacd = lGoingUp >= macdCandles ? 1 : lGoingDown >= macdCandles ? -1 : 0;
				
				#endregion
				
//				Signal = TachEonPVT();
				
				Signal = 1;
				
				
				if (Signal == 1)
				{
					bool entrySignal = TimeCheckCross()  &&  CheckEMA("Long")  &&  CheckPositionEMA("Long")  &&  CheckMacdSlope("Long")  &&  CheckSwingTrend("Long") 
						&& VOLMA(AveVolumePeriod)[1] > MinAveVolume  &&  Position.MarketPosition == MarketPosition.Flat  &&  longon;
					if (entrySignal)
					{
						if (!SplitOrder  ||  Quantity == 1)
							EnterLong(Quantity, "Long");
						else
						{
							int X = (int)(Math.Abs(Close[0] - Open[0]) * RetracePercentage / 100);
							int signalBarNum = Int32.Parse(barNum);
							EnterLong(halfQuantity, "Long");
							double limitPrice = !UseRetracePercentage ? Close[CurrentBar - signalBarNum] : Close[CurrentBar - signalBarNum] - X;
							entryOrder = EnterLongLimit(0, true, Quantity - halfQuantity, limitPrice, "Long");
							entryBar = CurrentBars[0];
						}
					}
				}
				else if (Signal == -1)
				{
					bool entrySignal = TimeCheckCross()  &&  CheckEMA("Short")  &&  CheckPositionEMA("Short")  &&  CheckMacdSlope("Short")  &&  CheckSwingTrend("Short") 
						&& VOLMA(AveVolumePeriod)[1] > MinAveVolume  &&  Position.MarketPosition == MarketPosition.Flat  &&  shorton;
					if (entrySignal)
					{
						if (!SplitOrder  ||  Quantity == 1)
							EnterShort(Quantity, "Short");
						else
						{
							int X = (int)(Math.Abs(Close[0] - Open[0]) * RetracePercentage / 100);
							int signalBarNum = Int32.Parse(barNum);
							EnterShort(halfQuantity, "Short");
							double limitPrice = !UseRetracePercentage ? Close[CurrentBar - signalBarNum] : Close[CurrentBar - signalBarNum] + X;
							entryOrder = EnterShortLimit(0, true, Quantity - halfQuantity, limitPrice, "Short");
							entryBar = CurrentBars[0];
						}
					}
				}
				
				if (entryOrder != null)
				{
					if (CurrentBars[0] - entryBar >= LimitExpirationBars  &&  LimitExpirationBars != 0)
					{
						CancelOrder(entryOrder);
						entryOrder = null;
					}
					if (Position.MarketPosition == MarketPosition.Long)
					{
						if (High[0] >= Position.AveragePrice + (ProfitTarget * TP_PercentReached / 100)*TickSize  &&  TP_PercentReached != 0)
						{
							CancelOrder(entryOrder);
							entryOrder = null;
						}
					}
					else if (Position.MarketPosition == MarketPosition.Short)
					{
						if (Low[0] <= Position.AveragePrice - (ProfitTarget * TP_PercentReached / 100)*TickSize  &&  TP_PercentReached != 0)
						{
							CancelOrder(entryOrder);
							entryOrder = null;
						}
					}
				}
				#endregion

				#region Trails
				bool HighFractalTrail = true;
				bool LowFractalTrail = true;
				
				for (int i = 0; i <= TS_SwingStrength*2; i++)
				{
					if (i < TS_SwingStrength)
					{
						if (High[i] > High[TS_SwingStrength])
							HighFractalTrail = false;
						if (Low[i] < Low[TS_SwingStrength])
							LowFractalTrail = false;
					}
					else if (i > TS_SwingStrength)
					{
						if (High[i] >= High[TS_SwingStrength])
							HighFractalTrail = false;
						if (Low[i] <= Low[TS_SwingStrength])
							LowFractalTrail = false;
					}
					else
						continue;
				}
				
				if (HighFractalTrail)
					currentTrailSwingHigh = High[TS_SwingStrength];
				if (LowFractalTrail)
					currentTrailSwingLow = Low[TS_SwingStrength];
				
				if (UseTrailStop)
				{
					if (Position.MarketPosition == MarketPosition.Long)
					{
						#region Divergence Check
						if (TS_DivergenceOnly  &&  !trailTriggered)
						{
							int B = Math.Min(CurrentBars[0] - Convert.ToInt32(barNum), TS_LookbackBars);
							int C = 0;
							for (int i = 0; i < B; i++)
							{
								if (fast[i] - slow[i] > fast[i+1] - slow[i+1])
									C++;
							}
							divergenceOk = (C / B > TS_MinDivrPercent / 100);
						}
						#endregion
						
						#region Swing Trail
						if (TS_SwingStrength != 0)
						{
							bool tooClose = (Close[0] - currentTrailSwingLow)/TickSize < TS_MinSwingTickDist;
							if (!tooClose)
							{
								if (stopOrder == null)
									ExitLongStopMarket(0, true, Position.Quantity, currentTrailSwingLow - TS_Offset*TickSize, "Stop Loss", "Long");
								else
								{
									if (currentTrailSwingLow > stopOrder.StopPrice)
										ExitLongStopMarket(0, true, Position.Quantity, currentTrailSwingLow - TS_Offset*TickSize, "Stop Loss", "Long");
								}
							}
						}
						#endregion
						
						#region Candle Trail
						if (TS_CandleTrailTickDist != 0)
						{
							if (Close[0] - Low[0] > TS_CandleTrailTickDist)
							{
								if (stopOrder == null)
									ExitLongStopMarket(0, true, Position.Quantity, Low[0] - TS_Offset*TickSize, "Stop Loss", "Long");
								else
								{
									if (Low[0] - TS_Offset*TickSize > stopOrder.StopPrice)
										ExitLongStopMarket(0, true, Position.Quantity, Low[0] - TS_Offset*TickSize, "Stop Loss", "Long");
								}
							}
						}
						#endregion
					}
					else if (Position.MarketPosition == MarketPosition.Short)
					{
						#region Divergence Check
						if (TS_DivergenceOnly  &&  !trailTriggered)
						{
							int B = Math.Min(CurrentBars[0] - Convert.ToInt32(barNum), TS_LookbackBars);
							int C = 0;
							for (int i = 0; i < B; i++)
							{
								if (slow[i] - fast[i] > slow[i+1] - fast[i+1])
									C++;
							}
							divergenceOk = (C / B > TS_MinDivrPercent / 100);
						}
						#endregion
						
						#region Swing Trail
						if (TS_SwingStrength != 0)
						{
							bool tooClose = (currentTrailSwingHigh - Close[0])/TickSize < TS_MinSwingTickDist;
							if (!tooClose)
							{
								if (stopOrder == null)
									ExitShortStopMarket(0, true, Position.Quantity, currentTrailSwingHigh + TS_Offset*TickSize, "Stop Loss", "Short");
								else
								{
									if (currentTrailSwingHigh < stopOrder.StopPrice)
										ExitShortStopMarket(0, true, Position.Quantity, currentTrailSwingHigh + TS_Offset*TickSize, "Stop Loss", "Short");
								}
							}	
						}
						#endregion
						
						#region Candle Trail
						if (TS_CandleTrailTickDist != 0)
						{
							if (High[0] - Close[0] > TS_CandleTrailTickDist)
							{
								if (stopOrder == null)
									ExitShortStopMarket(0, true, Position.Quantity, High[0] + TS_Offset*TickSize, "Stop Loss", "Short");
								else
								{
									if (High[0] + TS_Offset*TickSize < stopOrder.StopPrice)
										ExitShortStopMarket(0, true, Position.Quantity, High[0] + TS_Offset*TickSize, "Stop Loss", "Short");
								}
							}
						}
						#endregion
					}
					
				}
				#endregion
			}
			if (BarsInProgress == 1)
			{
				#region Secondary Series
				if (!trade)
					return;
				
				#region Higher TimeFrame Swings
				bool HighFractal = true;
				bool LowFractal = true;
				
				for (int i = 0; i <= Strength*2; i++)
				{
					if (i == Strength)
						continue;
					if (i < Strength)
					{
						if (Highs[1][i] > Highs[1][Strength])
							HighFractal = false;
						if (Lows[1][i] < Lows[1][Strength])
							LowFractal = false;
					}
					else
					{
						if (Highs[1][i] >= Highs[1][Strength])
							HighFractal = false;
						if (Lows[1][i] <= Lows[1][Strength])
							LowFractal = false;
					}
					
				}
				
				if (HighFractal)
				{
					pHTF_SH = cHTF_SH;
					cHTF_SH = Highs[1][Strength];
				}
				if (LowFractal)
				{
					pHTF_SL = cHTF_SL;
					cHTF_SL = Lows[1][Strength];
				}
				#endregion
				
				#region Macd Trend
				int hGoingUp = 0;
				int hGoingDown = 0;
				for (int i = 0; i < macdCandles; i++)
				{
					if (higherMacd[i] > higherMacd[i + 1])
						hGoingUp++;
					if (higherMacd[i] < higherMacd[i + 1])
						hGoingDown++;
				}
				hMacd = hGoingUp >= macdCandles ? 1 : hGoingDown >= macdCandles ? -1 : 0;
				
				#endregion
				#endregion
			}
			if (BarsInProgress == 2)
			{
				#region Breakeven
				if (Position.MarketPosition == MarketPosition.Long)
				{
					if (!beTriggered  &&  BETrigger != 0  &&  High[0] >= Position.AveragePrice + BETrigger*TickSize)
					{
						beTriggered = true;
						ExitLongStopMarket(2, true, Position.Quantity, Position.AveragePrice + BEOffset*TickSize, "Stop Loss", "Long");
					}
				}
				else if (Position.MarketPosition == MarketPosition.Short)
				{
					if (!beTriggered  &&  BETrigger != 0  &&  Low[0] <= Position.AveragePrice - BETrigger*TickSize)
					{
						beTriggered = true;
						ExitShortStopMarket(2, true, Position.Quantity, Position.AveragePrice - BEOffset*TickSize, "Stop Loss", "Short");
					}
				}
				#endregion
				
				#region Trail
				if (UseTrailStop  &&  divergenceOk)
				{
					if (Position.MarketPosition == MarketPosition.Long)
					{
						if (High[0] >= Position.AveragePrice + TS_ActivationTicks*TickSize  &&  !trailTriggered)
						{
							trailTriggered = true;
							if (TS_TakeProfitTicks > ProfitTarget)
								ExitLongLimit(2, true, Position.Quantity, Position.AveragePrice + TS_TakeProfitTicks*TickSize, "Profit Target", "Long");
						}
					}
					else if (Position.MarketPosition == MarketPosition.Short)
					{
						if (Low[0] <= Position.AveragePrice - TS_ActivationTicks*TickSize  &&  !trailTriggered)
						{
							trailTriggered = true;	
							if (TS_TakeProfitTicks > ProfitTarget)
								ExitShortLimit(2, true, Position.Quantity, Position.AveragePrice - TS_TakeProfitTicks*TickSize, "Profit Target", "Short");
						}
					}
				}
				#endregion
			}
		}
		
		
		private int TachEonPVT()
        {
            string tag = "none";
            foreach (DrawingTool draw in DrawObjects.ToList())
            {
                if (draw.Tag != null)
                {
                    tag = draw.Tag.ToString();
                    Draw.Text(this, "text" + CurrentBar, CurrentBar.ToString(), 0, Low[0] - 6 * TickSize);
                    if (tag.ToLower().Contains("up")  ||  tag.ToLower().Contains("dn"))
                    {
                        barNum = tag.Substring(2);
                        int signalBarNum1 = Int32.Parse(barNum);
                        if (barNum == CurrentBar.ToString())
                        {
                            if (tag.Substring(0, 2).ToLower() == "up")
                            {
                                Signal = 1;
                                Draw.Text(this, "Long" + CurrentBar, "Basic Signal", 0, High[0] + (10 * TickSize));

                                break;
                            }
                            else if (tag.Substring(0, 2).ToLower() == "dn")
                            {
                                Signal = -1;
                                Draw.Text(this, "Short" + CurrentBar, "Basic Signal", 0, High[0] + (10 * TickSize));
                                break;
                            }
                            else
                                Signal = 0;

                        }
                    }
                    if (Signal != 0)
                    {
                        int signalBarNum = Int32.Parse(barNum);
                        if ((CurrentBar > signalBarNum)  &&  (((CurrentBar - signalBarNum) < MaxEntryCandles)  ||  (MaxEntryCandles == 0)))
                        {
                            if (tag.ToLower().Contains("vsl")  ||  tag.ToLower().Contains("opl"))
                            {
                                if ((Close[CurrentBar - signalBarNum] < Close[0])  &&  Signal == 1)
                                {
                                    Draw.Text(this, "placedlong" + CurrentBar, "Long", 0, High[0] + 10 * TickSize, Brushes.Red);
                                }
                                else if ((Close[CurrentBar - signalBarNum] > Close[0])  &&  Signal == -1)
                                {
                                    Draw.Text(this, "placedshort" + CurrentBar, "Short", 0, High[0] + 10 * TickSize, Brushes.Red);
                                }
                            }
                        }
                        if (((CurrentBar - signalBarNum) >= MaxEntryCandles)  &&  (MaxEntryCandles != 0))
                            Signal = 0;
                    }
                }
            }

            return Signal;
        }
		
		
		private bool TimeCheckCross()
		{
			if (!limitTradingHours  &&  !limitTradingHours1)
				return true;
			
			bool timeLimit = false; 
			if (StartTime.TimeOfDay < EndTime.TimeOfDay)
			{
				if (Time[0].TimeOfDay >= StartTime.TimeOfDay  &&  Time[0].TimeOfDay <= EndTime.TimeOfDay)
					timeLimit = true;
			}
			else
			{
				if (Time[0].TimeOfDay >= StartTime.TimeOfDay  ||  Time[0].TimeOfDay <= EndTime.TimeOfDay)
					timeLimit = true;
			}
			if (StartTime1.TimeOfDay < EndTime1.TimeOfDay)
			{
				if (Time[0].TimeOfDay >= StartTime1.TimeOfDay  &&  Time[0].TimeOfDay <= EndTime1.TimeOfDay)
					timeLimit = true;
			}
			else
			{
				if (Time[0].TimeOfDay >= StartTime1.TimeOfDay  ||  Time[0].TimeOfDay <= EndTime1.TimeOfDay)
					timeLimit = true;
			}
			
			return timeLimit;
		}
		
		
		private bool CheckEMA(string direction)
        {
            if (UseSlowEMA)
            {
                double slowEMA = slow[0];
                bool isAboveSlowEMA = direction == "Long" ? Close[0] > slowEMA + SlowEMA_TicksPast*TickSize : Close[0] < slowEMA - SlowEMA_TicksPast*TickSize;
				return isAboveSlowEMA;
            }

            if (UseFastEMA)
            {
                double fastEMA = fast[0];
                bool isAboveFastEMA = direction == "Long" ? Close[0] > fastEMA + FastEMA_TicksPast : Close[0] < fastEMA - FastEMA_TicksPast;
				return isAboveFastEMA;
            }
            return false;
        }
		
		
		private bool CheckPositionEMA(string direction)
		{
			if (!UseSlowEMA  ||  !UseFastEMA  ||  !RequirePosition)
				return true;
			else
			{
				bool isCorrect = direction == "Long" ? fast[0] >= slow[0] + Position_TicksPast : fast[0] <= slow[0] - Position_TicksPast;
				bool slopeException = UseSlopeException  &&  fast[0] < slow[0]  &&  Slope(fast, 0, Convert.ToInt32(barNum)) > atr[0]/SlopeATR_Div ? true : false;
				bool result = isCorrect ? true : slopeException ? true : false;
				return result;
			}
		}
		
		
		private bool CheckMacdSlope(string direction)
		{
			if (!RequireMACD_SlopeDir)
				return true;
			else
				return TradeHTF_MACD_DirOnly ? direction == "Long"  &&  hMacd == 1 ? true : direction == "Short"  &&  hMacd == -1 : direction == "Long"  &&  hMacd == 1  &&  lMacd == 1 ? true : direction == "Short"  &&  hMacd == -1  &&  lMacd == -1 ? true : false;	
		}
		
		
		private bool CheckSwingTrend(string direction)
		{
			if (!RequireTrendDirection)
				return true;
			else
				return direction == "Long" ? swTrendHTF == 1  &&  swTrendCTF == 1 : swTrendHTF == -1  &&  swTrendCTF == -1;	
		}
		
		
		private void OnMyButtonClick(object sender, RoutedEventArgs rea)
		{
			System.Windows.Controls.Button button = sender as System.Windows.Controls.Button;
			
			if (button.Name == "longButton")
			{
				if (longon)
				{
					button.Background = Brushes.Gray;
					longon = false;
				}
				else
				{
					button.Background = Brushes.Green;
					longon = true;
				}
			}
			else if (button.Name == "shortButton")
			{
				if (shorton)
				{
					button.Background = Brushes.Gray;
					shorton = false;
				}
				else
				{
					button.Background = Brushes.Crimson;
					shorton = true;
				}
			}
		}
		
		
		protected void CreateButtons()
		{
			if (UserControlCollection.Contains(myGrid))
		        return;
		
			myGrid = new System.Windows.Controls.Grid
	        {
	          Name = "MyCustomGrid",
	          HorizontalAlignment = HorizontalAlignment.Left,
	          VerticalAlignment = VerticalAlignment.Bottom,
	        };
	 	 
	        myGrid.ColumnDefinitions.Add(new System.Windows.Controls.ColumnDefinition());
	        myGrid.ColumnDefinitions.Add(new System.Windows.Controls.ColumnDefinition());
			myGrid.RowDefinitions.Add(new System.Windows.Controls.RowDefinition());
			myGrid.ColumnDefinitions[0].Width = new GridLength(60);
			myGrid.ColumnDefinitions[1].Width = new GridLength(60);
			myGrid.RowDefinitions[0].Height = new GridLength(20);
	 
	        longButton = new System.Windows.Controls.Button
	        {
	          	Name = "longButton",
	          	Content = "Long",
	          	Foreground = Brushes.White,
	          	Background = Brushes.Green,
				Height = 20,
				Width = 60,
				FontSize = 8,
				HorizontalAlignment = HorizontalAlignment.Center,
				Focusable = false
	        };
			
			shortButton = new System.Windows.Controls.Button
	        {
	          	Name = "shortButton",
	          	Content = "Short",
	          	Foreground = Brushes.White,
	          	Background = Brushes.Crimson,
				Height = 20,
				Width = 20,
				FontSize = 8,
				HorizontalAlignment = HorizontalAlignment.Center,
				Focusable = false
				
	        };
	 
			longButton.Click += OnMyButtonClick;
			shortButton.Click += OnMyButtonClick;
	 
	        System.Windows.Controls.Grid.SetColumn(shortButton, 1);
			System.Windows.Controls.Grid.SetRow(shortButton, 0);
			System.Windows.Controls.Grid.SetColumn(longButton, 0);
			System.Windows.Controls.Grid.SetRow(longButton, 0);
	 
	        myGrid.Children.Add(longButton);
			myGrid.Children.Add(shortButton);
	 
	        UserControlCollection.Add(myGrid);
		}
		
		
		public void DisposeButtons() 
		{
			if (myGrid != null)
	        {
	          if (longButton != null)
	          {
	              myGrid.Children.Remove(longButton);
	              longButton.Click -= OnMyButtonClick;
	              longButton = null;
	          }
			  if (shortButton != null)
	          {
	              myGrid.Children.Remove(shortButton);
	              shortButton.Click -= OnMyButtonClick;
	              shortButton = null;
	          }
	        }
		}
		
		#region Properties
		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name="ATRLength", Order=0, GroupName="Parameters")]
		public int ATRLength
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name="HigherTimeFrameMinutes", Order=1, GroupName="Parameters")]
		public int HigherTimeFrameMinutes
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name="Strength", Order=2, GroupName="Parameters")]
		public int Strength
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name="MACD_Fast", Order=3, GroupName="Parameters")]
		public int MACD_Fast
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name="MACD_Slow", Order=4, GroupName="Parameters")]
		public int MACD_Slow
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name="MACD_Smooth", Order=5, GroupName="Parameters")]
		public int MACD_Smooth
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name="MACD_Candles", Order=6, GroupName="Parameters")]
		public int MACD_Candles
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name="MACD_Percent", Order=7, GroupName="Parameters")]
		public int MACD_Percent
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name="MaxEntryCandles", Order=8, GroupName="Parameters")]
		public int MaxEntryCandles
		{ get; set; }
		
		[NinjaScriptProperty]
        [Display(Name = "UseSlowEMA", Order = 9, GroupName = "Parameters")]
        public bool UseSlowEMA
        { get; set; }

        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "SlowEMA_Period", Order = 10, GroupName = "Parameters")]
        public int SlowEMA_Period
        { get; set; }

        [NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name = "SlowEMA_TicksPast", Order = 11, GroupName = "Parameters")]
        public int SlowEMA_TicksPast
        { get; set; }

        [NinjaScriptProperty]
        [Display(Name = "UseFastEMA", Order = 12, GroupName = "Parameters")]
        public bool UseFastEMA
        { get; set; }

        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "FastEMA_Period", Order = 13, GroupName = "Parameters")]
        public int FastEMA_Period
        { get; set; }

        [NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name = "FastEMA_TicksPast", Order = 14, GroupName = "Parameters")]
        public int FastEMA_TicksPast
        { get; set; }

        [NinjaScriptProperty]
        [Display(Name = "RequirePosition", Order = 15, GroupName = "Parameters")]
        public bool RequirePosition
        { get; set; }

        [NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name = "Position_TicksPast", Order = 16, GroupName = "Parameters")]
        public int Position_TicksPast
        { get; set; }

        [NinjaScriptProperty]
        [Display(Name = "UseSlopeException", Order = 17, GroupName = "Parameters")]
        public bool UseSlopeException
        { get; set; }

        [NinjaScriptProperty]
        [Range(1, double.MaxValue)]
        [Display(Name = "SlopeATR_Div", Order = 18, GroupName = "Parameters")]
        public double SlopeATR_Div
        { get; set; }
		
		[NinjaScriptProperty]
        [Display(Name = "RequireMACD_SlopeDir", Order = 19, GroupName = "Parameters")]
        public bool RequireMACD_SlopeDir
        { get; set; }

        [NinjaScriptProperty]
        [Display(Name = "TradeHTF_MACD_DirOnly", Order = 20, GroupName = "Parameters")]
        public bool TradeHTF_MACD_DirOnly
        { get; set; }
		
		[NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "AveVolumePeriod", Order = 21, GroupName = "Parameters")]
        public int AveVolumePeriod
        { get; set; }
		
		[NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "MinAveVolume", Order = 22, GroupName = "Parameters")]
        public int MinAveVolume
        { get; set; }
		
		[NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name = "DailyMinTarget", Order = 23, GroupName = "Parameters")]
        public int DailyMinTarget
        { get; set; }
		
		[NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name = "DailyMaxLoss", Order = 24, GroupName = "Parameters")]
        public int DailyMaxLoss
        { get; set; }
		
		[NinjaScriptProperty]
        [Display(Name = "RequireTrendDirection", Order = 25, GroupName = "Parameters")]
        public bool RequireTrendDirection
        { get; set; }
		
		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name="Stop Loss", Order=26, GroupName="Parameters")]
		public int StopLoss
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name="Profit Target", Order=27, GroupName="Parameters")]
		public int ProfitTarget
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(int.MinValue, int.MaxValue)]
		[Display(Name="BETrigger", Order=28, GroupName="Parameters")]
		public int BETrigger
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(int.MinValue, int.MaxValue)]
		[Display(Name="BEOffset", Order=29, GroupName="Parameters")]
		public int BEOffset
		{ get; set; }
		
		[NinjaScriptProperty]
        [Display(Name = "UseTrailingStop", Order = 30, GroupName = "Parameters")]
        public bool UseTrailStop
        { get; set; }
		
		[NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name = "TS_TakeProfitTicks", Order = 31, GroupName = "Parameters")]
        public int TS_TakeProfitTicks
        { get; set; }

        [NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name = "TS_ActivationTicks", Order = 32, GroupName = "Parameters")]
        public int TS_ActivationTicks
        { get; set; }

        [NinjaScriptProperty]
        [Range(int.MinValue, int.MaxValue)]
        [Display(Name = "TS_Offset", Order = 33, GroupName = "Parameters")]
        public int TS_Offset
        { get; set; }

        [NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name = "TS_SwingStrength", Order = 34, GroupName = "Parameters")]
        public int TS_SwingStrength
        { get; set; }

        [NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name = "TS_MinSwingTickDist", Order = 35, GroupName = "Parameters")]
        public int TS_MinSwingTickDist
        { get; set; }

        [NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name = "TS_CandleTrailTickDist", Order = 36, GroupName = "Parameters")]
        public int TS_CandleTrailTickDist
        { get; set; }

        [NinjaScriptProperty]
        [Display(Name = "TS_DivergenceOnly", Order = 37, GroupName = "Parameters")]
        public bool TS_DivergenceOnly
        { get; set; }
		
		[NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name = "TS_LookbackBars", Order = 38, GroupName = "Parameters")]
        public int TS_LookbackBars
        { get; set; }
		
		[NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name = "TS_MinDivrPercent", Order = 39, GroupName = "Parameters")]
        public int TS_MinDivrPercent
        { get; set; }
		
		[NinjaScriptProperty]
        [Display(Name = "SplitOrder", Order = 52, GroupName = "Parameters")]
        public bool SplitOrder
        { get; set; }

        [NinjaScriptProperty]
        [Display(Name = "UseRetracePercentage", Order = 53, GroupName = "Parameters")]
        public bool UseRetracePercentage
        { get; set; }
		
        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "MinCandleBodyTicks", Order = 54, GroupName = "Parameters")]
        public int MinCandleBodyTicks
        { get; set; }
        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "RetracePercentage", Order = 54, GroupName = "Parameters")]
        public int RetracePercentage
        { get; set; }

        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "LimitExpirationBars", Order = 55, GroupName = "Parameters")]
        public int LimitExpirationBars
        { get; set; }

        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "TP_PercentReached", Order = 56, GroupName = "Parameters")]
        public int TP_PercentReached
        { get; set; }
						
		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name="Quantity", Order=80, GroupName="Parameters")]
		public int Quantity
		{ get; set; }
		
		[NinjaScriptProperty]
        [Display(Name = "Limit Trading Hours", Description = "", Order = 10, GroupName = "Time Frames")]
        public bool limitTradingHours
        { get; set; }

        [NinjaScriptProperty]
        [PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
        [Display(Name = "Start Time", Description = "Enter your time", Order = 20, GroupName = "Time Frames")]
        public DateTime StartTime
        { get; set; }

        [NinjaScriptProperty]
        [PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
        [Display(Name = "End Time", Description = "Enter your time", Order = 30, GroupName = "Time Frames")]
        public DateTime EndTime
        { get; set; }
		
		[NinjaScriptProperty]
        [Display(Name = "Limit Trading Hours #2", Description = "", Order = 40, GroupName = "Time Frames")]
        public bool limitTradingHours1
        { get; set; }

        [NinjaScriptProperty]
        [PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
        [Display(Name = "Start Time", Description = "Enter your time", Order = 50, GroupName = "Time Frames")]
        public DateTime StartTime1
        { get; set; }

        [NinjaScriptProperty]
        [PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
        [Display(Name = "End Time", Description = "Enter your time", Order = 60, GroupName = "Time Frames")]
        public DateTime EndTime1
        { get; set; }
		
		#endregion

	}
}
