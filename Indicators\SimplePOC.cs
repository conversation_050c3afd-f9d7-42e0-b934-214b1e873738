#region Using declarations
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Gui.Tools;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.DrawingTools;
#endregion

//This namespace holds Indicators in this folder and is required. Do not change it. 
namespace NinjaTrader.NinjaScript.Indicators
{
	public class SimplePOC : Indicator
	{
		/// <summary>
		/// SimplePOC - By PM
		/// The Script Display The POC "inside" the Candles (with a White Dash)
		/// It also Displays The Regular Volume in a secondary Panel (Hash PlotStyle added)
		/// It works in Realtime Only: 
		/// 			- it Displays the POC for non TickReplay Brokers from within the last 10 seconds of the bar window and on
		/// 			- no "Historical" POC Display
		/// 			- no "Kept Data" Upon Chart Refresh
		/// Special thanks to NinjaTrader_Jim for his scripts and ideas/inputs that inspired this version of the indicator.
		/// </summary>
		 
		
		#region Class Level Variables
		
			private Series<Dictionary<double, double>> MySeriesDictCombinedVolume;
			
			private Dictionary<double, double> TempCombinedVolumeDict;
		
		#endregion

		
		#region OnStateChange()
		
			protected override void OnStateChange()
			{
				if (State == State.SetDefaults)
				{
					Description									= @"Realtime POC for non TickReplay Brokers";
					Name										= "SimplePOC";
					Calculate									= Calculate.OnEachTick;
					IsOverlay									= false;
					DisplayInDataBox							= true;
					DrawOnPricePanel							= true;
					DrawHorizontalGridLines						= true;
					DrawVerticalGridLines						= true;
					PaintPriceMarkers							= true;
					ScaleJustification							= NinjaTrader.Gui.Chart.ScaleJustification.Right;
					IsSuspendedWhileInactive					= true;
					
					
					StartDisplay								= 7000;
					EndDisplay									= 2000;
					
					DashWidth									= 5;
					
					BarsBack									= 10;
					
					
					AddPlot(new Stroke(Brushes.Magenta,	5), PlotStyle.Hash, "CombinedVolume");
					
				}
				else if (State == State.Configure)
				{
					AddDataSeries(BarsPeriodType.Tick, 1);
				}
				else if (State == State.DataLoaded)
				{
					
					MySeriesDictCombinedVolume = new Series<Dictionary<double, double>>(this, MaximumBarsLookBack.Infinite);
					
					TempCombinedVolumeDict = new Dictionary<double, double>();
				}
			}
		
		#endregion
		
			
		#region CalculateValues() / OnBarUpdate() / SetValues() / ResetValues() needed Variables
			
			private double	combined 	= 1;
			
			private bool 	isReset;

			private int 	lastBar;
			
			private bool 	lastInTransition;
		
		#endregion
		
		
		#region OnBarUpdate()
			
			protected override void OnBarUpdate()
			{
				
				if (BarsInProgress == 0)
				{
					// This lets us know what processing mode we are in
					// if indexOffset == 0 then we are in 'realtime processing mode'
					// if indexOffset is > 0 then we are in 'historical processing mode'
					int indexOffset = BarsArray[1].Count - 1 - CurrentBars[1];
					
					// If we are not Calculate.OnBarClose and we are in Realtime processing mode
					if (IsFirstTickOfBar && Calculate != Calculate.OnBarClose && (State == State.Realtime || BarsArray[0].IsTickReplay))
					{
						// We always get the last tick after the primary triggers OBU so we update the last bar
						if (CurrentBars[0] > 0)
							SetValues(1);
						
						// We have the last tick of the bar added so now we can reset
						if (BarsArray[0].IsTickReplay || State == State.Realtime && indexOffset == 0)
							ResetValues(false);
					}
					
					// We only set the value on the primary to preserve external programmatic access to plot as well as indicator-as-input cases
					SetValues(0);
					
					// If we are Calculate.OnBarClose or we are in Historical processing mode, we are already update to date on the 1 tick series so we reset here
					if (Calculate == Calculate.OnBarClose || (lastBar != CurrentBars[0] && (State == State.Historical || State == State.Realtime && indexOffset > 0)))
						ResetValues(false);
					
					lastBar = CurrentBars[0];
					
				}
				else if (BarsInProgress == 1)
				{
					// The more granular series will open the new session so we have to reset any session related stuff here
					if (BarsArray[1].IsFirstBarOfSession)
						ResetValues(true);
					
					// We only calculate values from the 1 tick series
					CalculateValues(false);
				}
			}
		
		#endregion
			
		
		#region CalculateValues()
			
			private void CalculateValues(bool forceCurrentBar)
			{
				// This lets us know what processing mode we are in
				// if indexOffset == 0 and State is Realtime then we are in 'realtime processing mode'
				// if indexOffset is > 0 then we are in 'historical processing mode'
				int 	indexOffset 	= BarsArray[1].Count - 1 - CurrentBars[1];
				bool 	inTransition 	= State == State.Realtime && indexOffset > 1;
				
				// For Calculate.OnBarClose in realtime processing we have to advance the index on the tick series to not be one tick behind
				// The means, at the end of the 'transition' (where State is Realtime but we are still in historical processing mode) -> we have to calculate two ticks (CurrentBars[1] and CurrentBars[1] + 1)
				if (!inTransition && lastInTransition && !forceCurrentBar && Calculate == Calculate.OnBarClose)
					CalculateValues(true);
				
				bool 	useCurrentBar 	= State == State.Historical || inTransition || Calculate != Calculate.OnBarClose || forceCurrentBar;
				
				// This is where we decide what index to use
				int 	whatBar 		= useCurrentBar ? CurrentBars[1] : Math.Min(CurrentBars[1] + 1, BarsArray[1].Count - 1);
				
				// This is how we get the right tick values
				double 	volume 			= BarsArray[1].GetVolume(whatBar);
				double	price			= BarsArray[1].GetClose(whatBar);			
				
				// Accumulate volume
				if (price == price)
				{
					combined += volume;
					if (TempCombinedVolumeDict.ContainsKey(price))
						TempCombinedVolumeDict[price] += volume;
					else
						TempCombinedVolumeDict.Add(price, volume);
				}
				
				
				
				Dictionary<double, double> dctTemp = new Dictionary<double, double>();
				
				KeyValuePair<double, double> max = new KeyValuePair<double, double>();
				
				for (double p = Low[0]; p <= High[0]; p += TickSize)
				{
					//if (TempCombinedVolumeDict.ContainsKey(p))
					// Print(String.Format("Ask: {0} {1} {2}", p, TempCombinedVolumeDict[p], Time[0]));
					
					foreach (KeyValuePair<double, double> pair in TempCombinedVolumeDict.OrderBy(key => key.Value))
			        {
			            dctTemp.Add(pair.Key, pair.Value);
						// Print(String.Format("Ask: {0} {1} {2}", pair.Key, pair.Value, Time[0]));
			        }
					
					// KeyValuePair<double, double> max = new KeyValuePair<double, double>(); 
					foreach (var kvp in dctTemp)
					{
					  if (kvp.Value > max.Value)
					    max = kvp;
					}
					
				}
				
				
				
				DateTime myStartTime = new DateTime();
				DateTime myEndTime 	 = new DateTime();
				
				for (int barIndex = ((BarsBack < ChartBars.FromIndex) ? (ChartBars.ToIndex-BarsBack) : ChartBars.FromIndex); 
			  		barIndex <= ChartBars.ToIndex;	
			  		barIndex++)
				{
					myStartTime = BarsArray[0].GetTime(barIndex).Subtract(TimeSpan.FromMilliseconds(StartDisplay));
					
					myEndTime = BarsArray[0].GetTime(barIndex).Subtract(TimeSpan.FromMilliseconds(EndDisplay));
				}
				
				if (Time[0] >= myStartTime && Time[0] <= myEndTime)
				{
					Draw.Line(this, "tag1"+CurrentBar, true, 0, max.Key, 0, max.Key + (1 * TickSize), Brushes.White, DashStyleHelper.Dash, DashWidth);
					Print("max.Key : " + max.Key + " Time[0] : " + Time[0]);
				}
				
				
				
				lastInTransition 	= inTransition;
				
			}
		
		#endregion
			
			
		#region SetValues()
		
			private void SetValues(int barsAgo)
			{
				// Typical assignment for BuySellVolume
				CombinedVolume[barsAgo] = combined;
				
				// When using Dictionaries, make sure we create a new empty dictionaries to store values from our TempCombinedVolumeDict
				MySeriesDictCombinedVolume[barsAgo] = new Dictionary<double, double>();

				// Populate values from the TempCombined Dictionary to the price's level Dictionary
				foreach (KeyValuePair<double, double> kvp in TempCombinedVolumeDict)
					MySeriesDictCombinedVolume[barsAgo].Add(kvp.Key, kvp.Value);
				
				// Be sure to reset our accumulation of Combined Volume from Dictionary
				double dictCombinedTotalVolume = 0;
				foreach (KeyValuePair<double, double> kvp in MySeriesDictCombinedVolume[barsAgo])
					dictCombinedTotalVolume += kvp.Value;
				
				// Assign our accumulation of Combined Volume to the plot
				Values[0][barsAgo] = dictCombinedTotalVolume;

			}
		
		#endregion
		
			
		#region ResetValues()
			
			private void ResetValues(bool isNewSession)
			{
				combined = 0;
				
				isReset = true;
				
				TempCombinedVolumeDict.Clear();
				
				if (isNewSession)
				{
					// Cumulative values (per session) would reset here
					// EMA is not gapless so we ignore in this example
				}
			}
		
		#endregion

			
		#region Properties
			
			[Browsable(false)]
			[XmlIgnore()]
			public Series<double> CombinedVolume
			{
				get { return Values[0]; }
			}

			[NinjaScriptProperty]
			[Range(1, int.MaxValue)]
			[Display(Name = "POC Start Display Timespan On Bar (in milliseconds)", Description = "Sets Starting Timespan (in milliseconds) of POC Display On the Bar: For example 7000 = 7 seconds Before the Bar closes", Order = 1, GroupName = "#1 POC Display Time Span (In Miliseconds) — \n For Example: 7000 / 2000 = \n  from 7 secs to 2 secs before the Bar closes")]
			public int StartDisplay
			{ get; set; }

			[NinjaScriptProperty]
			[Range(1, int.MaxValue)]
			[Display(Name = "POC End Display Timespan On Bar (in milliseconds)", Description = "Sets Ending Timespan (in milliseconds) of POC Display On the Bar:  For example 2000 = 2 seconds Before the Bar closes", Order = 2, GroupName = "#1 POC Display Time Span (In Miliseconds) — \n For Example: 7000 / 2000 = \n  from 7 secs to 2 secs before the Bar closes")]
			public int EndDisplay
			{ get; set; }

			[NinjaScriptProperty]
			[Range(1, int.MaxValue)]
			[Display(Name = "POC Dash Width", Description = "Sets The POC Dash Width", Order = 3, GroupName = "#2 POC Dash's Input Settings")]
			public int DashWidth
			{ get; set; }

			[NinjaScriptProperty]
			[Range(1, int.MaxValue)]
			[Display(Name = "POC Bars Back limit", Description = "Sets The number of bars Back to limit the POC on", Order = 4, GroupName = "#3 Bars Back Input Settings")]
			public int BarsBack
			{ get; set; }
		
		#endregion
	}
}

#region NinjaScript generated code. Neither change nor remove.

namespace NinjaTrader.NinjaScript.Indicators
{
	public partial class Indicator : NinjaTrader.Gui.NinjaScript.IndicatorRenderBase
	{
		private SimplePOC[] cacheSimplePOC;
		public SimplePOC SimplePOC(int startDisplay, int endDisplay, int dashWidth, int barsBack)
		{
			return SimplePOC(Input, startDisplay, endDisplay, dashWidth, barsBack);
		}

		public SimplePOC SimplePOC(ISeries<double> input, int startDisplay, int endDisplay, int dashWidth, int barsBack)
		{
			if (cacheSimplePOC != null)
				for (int idx = 0; idx < cacheSimplePOC.Length; idx++)
					if (cacheSimplePOC[idx] != null && cacheSimplePOC[idx].StartDisplay == startDisplay && cacheSimplePOC[idx].EndDisplay == endDisplay && cacheSimplePOC[idx].DashWidth == dashWidth && cacheSimplePOC[idx].BarsBack == barsBack && cacheSimplePOC[idx].EqualsInput(input))
						return cacheSimplePOC[idx];
			return CacheIndicator<SimplePOC>(new SimplePOC(){ StartDisplay = startDisplay, EndDisplay = endDisplay, DashWidth = dashWidth, BarsBack = barsBack }, input, ref cacheSimplePOC);
		}
	}
}

namespace NinjaTrader.NinjaScript.MarketAnalyzerColumns
{
	public partial class MarketAnalyzerColumn : MarketAnalyzerColumnBase
	{
		public Indicators.SimplePOC SimplePOC(int startDisplay, int endDisplay, int dashWidth, int barsBack)
		{
			return indicator.SimplePOC(Input, startDisplay, endDisplay, dashWidth, barsBack);
		}

		public Indicators.SimplePOC SimplePOC(ISeries<double> input , int startDisplay, int endDisplay, int dashWidth, int barsBack)
		{
			return indicator.SimplePOC(input, startDisplay, endDisplay, dashWidth, barsBack);
		}
	}
}

namespace NinjaTrader.NinjaScript.Strategies
{
	public partial class Strategy : NinjaTrader.Gui.NinjaScript.StrategyRenderBase
	{
		public Indicators.SimplePOC SimplePOC(int startDisplay, int endDisplay, int dashWidth, int barsBack)
		{
			return indicator.SimplePOC(Input, startDisplay, endDisplay, dashWidth, barsBack);
		}

		public Indicators.SimplePOC SimplePOC(ISeries<double> input , int startDisplay, int endDisplay, int dashWidth, int barsBack)
		{
			return indicator.SimplePOC(input, startDisplay, endDisplay, dashWidth, barsBack);
		}
	}
}

#endregion
