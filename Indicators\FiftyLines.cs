#region Using declarations
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Runtime.CompilerServices;	/// For MemberCallerName
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Gui.Tools;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.DrawingTools;
#endregion

//This namespace holds Indicators in this folder and is required. Do not change it. 
namespace NinjaTrader.NinjaScript.Indicators
{
	public class FiftyLines : Indicator
	{
		private const string	VERSION = "2";

		private int				mult100 = 0;
		private int				price0 = 0;
		private int				price100 = 0;
		private int				price50 = 0;
		private string			lastLogMsg = "";
		private string			lastCaller = "";
		private string			lastLogTime = "";

		private Brush			color100 = Brushes.Green;
		private int				width100 = 1;
		private DashStyleHelper	style100 = DashStyleHelper.Solid;

		private Brush			color50 = Brushes.Blue;
		private int				width50 = 1;
		private DashStyleHelper	style50 = DashStyleHelper.Solid;

		private Brush			color26 = Brushes.Purple;
		private int				width26 = 1;
		private DashStyleHelper	style26 = DashStyleHelper.Dash;

		protected override void OnStateChange()
		{
			if (State == State.SetDefaults)
			{
				Description									= @"Draws lines for Fifty Level strategy, at levels mod 50, 100, and 26";
				Name										= "FiftyLines";
				Calculate									= Calculate.OnBarClose;
				IsOverlay									= true;
				DisplayInDataBox							= false;
				DrawOnPricePanel							= true;
				DrawHorizontalGridLines						= false;
				DrawVerticalGridLines						= false;
				PaintPriceMarkers							= false;
				ScaleJustification							= NinjaTrader.Gui.Chart.ScaleJustification.Overlay;
				//Disable this property if your indicator requires custom values that cumulate with each new market data event. 
				//See Help Guide for additional information.
				IsSuspendedWhileInactive					= true;
				Increment									= 100;
				BuyTrigger									= 50;
				EntryLine									= 26;
				
				Color100									= Brushes.Green;
				Width100									= 1;
				Style100									= DashStyleHelper.Solid;
				
				Color50										= Brushes.Blue;
				Width50										= 1;
				Style50										= DashStyleHelper.Solid;
				
				Color26										= Brushes.Purple;
				Width26										= 1;
				Style26										= DashStyleHelper.Dash;
				
				DisableLogging								= false;
			}
			else if (State == State.Configure)
			{
				AddDataSeries(Data.BarsPeriodType.Minute, 1);
			}
		}

		protected override void OnBarUpdate()
		{
			UpdateLevels();
		}
		
		/// Update the fifty-level vars
		private void UpdateLevels()
		{
			/// Update century prices to work with
			mult100 = (int)(Close[0] / Increment);
			price0 = Increment * mult100;
			price100 = price0 + Increment;
			price50 = price0 + BuyTrigger;
			double price26 = price0 + EntryLine;
			double price126 = price100 + EntryLine;

			//Log("Close[0] = " + Close[0] + "; price0 = " + price0 + "; price100 = " + price100 + "; price50 = " + price50);
			
			//Log("Set mult100 = " + mult100 + ", price0 = " + price0 + ", price100 = " + price100 + ", price50 = " + price50);
			Draw.HorizontalLine(this, "50Line" + price50, price50, Color50, Style50, Width50, true);
			Draw.HorizontalLine(this, "0Line" + price100, price100, Color100, Style100, Width100, true);
			Draw.HorizontalLine(this, "0Line" + price0, price0, Color100, Style100, Width100, true);
			Draw.HorizontalLine(this, "26Line" + price126, price126, Color26, Style26, Width26, true);
			Draw.HorizontalLine(this, "26Line" + price26, price26, Color26, Style26, Width26, true);
		}
		
		private void Log(string message, [CallerMemberName] string memberName = "")
		{
			if (DisableLogging  ||  lastLogMsg == message)
				return;

			/// Debug time filtering...
			//if (Time[0] < dbg1  ||  Time[0] > dbg2)	return;
			
			/// Set this scripts Print() calls to the second output tab?
			//if (UseOutput2)		PrintTo = PrintTo.OutputTab2;
			//else				PrintTo = PrintTo.OutputTab1;
			
			string id = "FiftyLines ";
			
			DateTime date = (State == State.Historical) ? Time[0] : DateTime.Now;
			string dateStr = (State == State.Historical) ? date.ToString() : date.ToString("HH:mm:ss");
			
			if (lastCaller != memberName)
			{
				Print("\n" + id + " - " + this.Instrument.FullName + " - [" + memberName + "]  -  " + dateStr + "  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -\n");
				Print(message);
			}
			else if (lastLogMsg != message)
			{
				if (lastLogTime != dateStr)	// Output just time if diff time but not new caller
					Print(message + "   ( " + dateStr + " )");
				else
					Print(message);
			}

			lastLogMsg = message;
			lastCaller = memberName;
			lastLogTime = dateStr;
		}		

		#region PROPERTIES
		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name="Increment", Description="Where to place the Zero / 100 lines", Order=1, GroupName="01. Parameters")]
		public int Increment
		{ get; set; }

		[NinjaScriptProperty]
		[Range(30, int.MaxValue)]
		[Display(Name="BuyTrigger", Description="Where to place the Fifty lines", Order=2, GroupName="01. Parameters")]
		public int BuyTrigger
		{ get; set; }

		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name="EntryLine", Description="Where to place the Entry lines", Order=3, GroupName="01. Parameters")]
		public int EntryLine
		{ get; set; }

		
		
		
		[XmlIgnore]
		[Display(Name = "Line Color", Order = 1, GroupName = "02. Hundred Line")]
		public Brush Color100
		{
		    get { return color100; }
		    set { color100 = value; }
		}
		
		[Browsable(false)] // Serialize the color for saving
		public string Color100Serializable
		{
		    get { return Serialize.BrushToString(Color100); }
		    set { Color100 = Serialize.StringToBrush(value); }
		}
		
		[Display(Name = "Line Width", Order = 2, GroupName = "02. Hundred Line")]
		public int Width100
		{
		    get { return width100; }
		    set { width100 = Math.Max(1, value); } // Ensure width is at least 1
		}
		
		[Display(Name = "Line Style", Order = 3, GroupName = "02. Hundred Line")]
		public DashStyleHelper Style100
		{
		    get { return style100; }
		    set { style100 = value; }
		}		

		
		[XmlIgnore]
		[Display(Name = "Line Color", Order = 1, GroupName = "03. Fifty Line")]
		public Brush Color50
		{
		    get { return color50; }
		    set { color50 = value; }
		}
		
		[Browsable(false)] // Serialize the color for saving
		public string Color50Serializable
		{
		    get { return Serialize.BrushToString(Color50); }
		    set { Color50 = Serialize.StringToBrush(value); }
		}
		
		[Display(Name = "Line Width", Order = 2, GroupName = "03. Fifty Line")]
		public int Width50
		{
		    get { return width50; }
		    set { width50 = Math.Max(1, value); } // Ensure width is at least 1
		}
		
		[Display(Name = "Line Style", Order = 3, GroupName = "03. Fifty Line")]
		public DashStyleHelper Style50
		{
		    get { return style50; }
		    set { style50 = value; }
		}		

		
		[XmlIgnore]
		[Display(Name = "Line Color", Order = 1, GroupName = "04. TwentySix Line")]
		public Brush Color26
		{
		    get { return color26; }
		    set { color26 = value; }
		}
		
		[Browsable(false)] // Serialize the color for saving
		public string Color26Serializable
		{
		    get { return Serialize.BrushToString(Color26); }
		    set { Color26 = Serialize.StringToBrush(value); }
		}
		
		[Display(Name = "Line Width", Order = 2, GroupName = "04. TwentySix Line")]
		public int Width26
		{
		    get { return width26; }
		    set { width26 = Math.Max(1, value); } // Ensure width is at least 1
		}
		
		[Display(Name = "Line Style", Order = 3, GroupName = "04. TwentySix Line")]
		public DashStyleHelper Style26
		{
		    get { return style26; }
		    set { style26 = value; }
		}		


		
		
		[NinjaScriptProperty]
		[Display(Name="Disable Logging", Description = "Trade long direction", Order=20, GroupName = "05. Debug")]
		public bool DisableLogging
		{ get; set; }
		#endregion
	}
}

#region NinjaScript generated code. Neither change nor remove.

namespace NinjaTrader.NinjaScript.Indicators
{
	public partial class Indicator : NinjaTrader.Gui.NinjaScript.IndicatorRenderBase
	{
		private FiftyLines[] cacheFiftyLines;
		public FiftyLines FiftyLines(int increment, int buyTrigger, int entryLine, bool disableLogging)
		{
			return FiftyLines(Input, increment, buyTrigger, entryLine, disableLogging);
		}

		public FiftyLines FiftyLines(ISeries<double> input, int increment, int buyTrigger, int entryLine, bool disableLogging)
		{
			if (cacheFiftyLines != null)
				for (int idx = 0; idx < cacheFiftyLines.Length; idx++)
					if (cacheFiftyLines[idx] != null && cacheFiftyLines[idx].Increment == increment && cacheFiftyLines[idx].BuyTrigger == buyTrigger && cacheFiftyLines[idx].EntryLine == entryLine && cacheFiftyLines[idx].DisableLogging == disableLogging && cacheFiftyLines[idx].EqualsInput(input))
						return cacheFiftyLines[idx];
			return CacheIndicator<FiftyLines>(new FiftyLines(){ Increment = increment, BuyTrigger = buyTrigger, EntryLine = entryLine, DisableLogging = disableLogging }, input, ref cacheFiftyLines);
		}
	}
}

namespace NinjaTrader.NinjaScript.MarketAnalyzerColumns
{
	public partial class MarketAnalyzerColumn : MarketAnalyzerColumnBase
	{
		public Indicators.FiftyLines FiftyLines(int increment, int buyTrigger, int entryLine, bool disableLogging)
		{
			return indicator.FiftyLines(Input, increment, buyTrigger, entryLine, disableLogging);
		}

		public Indicators.FiftyLines FiftyLines(ISeries<double> input , int increment, int buyTrigger, int entryLine, bool disableLogging)
		{
			return indicator.FiftyLines(input, increment, buyTrigger, entryLine, disableLogging);
		}
	}
}

namespace NinjaTrader.NinjaScript.Strategies
{
	public partial class Strategy : NinjaTrader.Gui.NinjaScript.StrategyRenderBase
	{
		public Indicators.FiftyLines FiftyLines(int increment, int buyTrigger, int entryLine, bool disableLogging)
		{
			return indicator.FiftyLines(Input, increment, buyTrigger, entryLine, disableLogging);
		}

		public Indicators.FiftyLines FiftyLines(ISeries<double> input , int increment, int buyTrigger, int entryLine, bool disableLogging)
		{
			return indicator.FiftyLines(input, increment, buyTrigger, entryLine, disableLogging);
		}
	}
}

#endregion
