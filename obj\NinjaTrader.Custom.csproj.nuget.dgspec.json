{"format": 1, "restore": {"S:\\Documents\\NinjaTrader 8\\bin\\Custom\\NinjaTrader.Custom.csproj": {}}, "projects": {"S:\\Documents\\NinjaTrader 8\\bin\\Custom\\NinjaTrader.Custom.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "S:\\Documents\\NinjaTrader 8\\bin\\Custom\\NinjaTrader.Custom.csproj", "projectName": "NinjaTrader.Custom", "projectPath": "S:\\Documents\\NinjaTrader 8\\bin\\Custom\\NinjaTrader.Custom.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "S:\\Documents\\NinjaTrader 8\\bin\\Custom\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net48"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net48": {"targetAlias": "net48", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net48": {"targetAlias": "net48", "dependencies": {"System.ComponentModel.Annotations": {"target": "Package", "version": "[5.0.0, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.204\\RuntimeIdentifierGraph.json"}}}}}