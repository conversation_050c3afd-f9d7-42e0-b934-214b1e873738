#region Using declarations
using System;
using System.Collections.Generic;
using System.Runtime.CompilerServices;
using NinjaTrader.Cbi;
using System.Windows.Media;
using DL = NinjaTrader.NinjaScript.DerkLog;
#endregion

//	DERKUTILS_VERSION = "*******";

namespace NinjaTrader.NinjaScript.Strategies
{
	#region DerkTM Interface
	/// <summary>
	/// Interface for strategies that want to use DerkTM (Trade Management) functionality.
	/// Implement this interface in your strategy to use the DerkTM class.
	/// </summary>
	public interface IDerkTMStrategy
	{

		/// <summary>
		/// Gets the entry order
		/// </summary>
		/// <returns>The entry order or null if none exists</returns>
		Order GetEntryOrder();

		/// <summary>
		/// Gets the first stop loss order
		/// </summary>
		/// <returns>The first stop loss order or null if none exists</returns>
		Order GetSlOrder1();

		/// <summary>
		/// Gets the second stop loss order
		/// </summary>
		/// <returns>The second stop loss order or null if none exists</returns>
		Order GetSlOrder2();

		/// <summary>
		/// Gets the first take profit order
		/// </summary>
		/// <returns>The first take profit order or null if none exists</returns>
		Order GetTpOrder1();

		/// <summary>
		/// Gets the second take profit order
		/// </summary>
		/// <returns>The second take profit order or null if none exists</returns>
		Order GetTpOrder2();

		/// <summary>
		/// Sets the entry order reference
		/// </summary>
		/// <param name="order">The order to set as the entry order, or null to clear</param>
		void SetEntryOrder(Order order);

		/// <summary>
		/// Sets the first stop loss order reference
		/// </summary>
		/// <param name="order">The order to set as the first stop loss, or null to clear</param>
		void SetSlOrder1(Order order);

		/// <summary>
		/// Sets the second stop loss order reference
		/// </summary>
		/// <param name="order">The order to set as the second stop loss, or null to clear</param>
		void SetSlOrder2(Order order);

		/// <summary>
		/// Sets the first take profit order reference
		/// </summary>
		/// <param name="order">The order to set as the first take profit, or null to clear</param>
		void SetTpOrder1(Order order);

		/// <summary>
		/// Sets the second take profit order reference
		/// </summary>
		/// <param name="order">The order to set as the second take profit, or null to clear</param>
		void SetTpOrder2(Order order);

		/// <summary>
		/// Cancels all stop and limit orders associated with the current position
		/// </summary>
		void CancelAllStops();

		/// <summary>
		/// Gets the current bid price
		/// </summary>
		/// <returns>The current bid price</returns>
		double GetCurrentBid();

		/// <summary>
		/// Gets the current ask price
		/// </summary>
		/// <returns>The current ask price</returns>
		double GetCurrentAsk();

		/// <summary>
		/// Submits an unmanaged order to the broker
		/// </summary>
		/// <param name="signalName">Signal identifier</param>
		/// <param name="action">Order action (Buy, Sell, etc.)</param>
		/// <param name="orderType">Type of order (Market, Limit, etc.)</param>
		/// <param name="quantity">Number of contracts/shares</param>
		/// <param name="limitPrice">Limit price (0 if not applicable)</param>
		/// <param name="stopPrice">Stop price (0 if not applicable)</param>
		/// <param name="fromEntrySignal">Entry signal identifier</param>
		/// <param name="signalName2">Secondary signal name</param>
		/// <returns>The submitted order</returns>
		Order SubmitOrderUnmanaged(int signalName, OrderAction action, OrderType orderType, int quantity, double limitPrice, double stopPrice, string fromEntrySignal, string signalName2);

		/// <summary>
		/// Cancels an existing order
		/// </summary>
		/// <param name="order">The order to cancel</param>
		void CancelOrder(Order order);

		/// <summary>
		/// Changes parameters of an existing order
		/// </summary>
		/// <param name="order">The order to modify</param>
		/// <param name="quantity">New quantity</param>
		/// <param name="limitPrice">New limit price</param>
		/// <param name="stopPrice">New stop price</param>
		void ChangeOrder(Order order, int quantity, double limitPrice, double stopPrice);

		/// <summary>
		/// Gets the current position
		/// </summary>
		Position Position { get; }

		/// <summary>
		/// Gets the strategy as a NinjaScriptBase for logging and other operations
		/// </summary>
		NinjaScriptBase NSBase { get; }
	}
	#endregion

	/// <summary>
	/// DerkTM (Trade Management) class provides common trade management functionality
	/// that can be shared across multiple strategies.
	/// </summary>
	public class DerkTM
	{
		private IDerkTMStrategy S;

		/// <summary>
		/// Constructor for the DerkTM class
		/// </summary>
		/// <param name="strategy">The strategy implementing the IDerkTMStrategy interface</param>
		public DerkTM(IDerkTMStrategy strategy)
		{
			S = strategy;
		}


		/// <summary>
		/// Places a limit order for entry into a position
		/// </summary>
		/// <param name="dir">Direction of the trade ("Long" or "Short")</param>
		/// <param name="price">Limit price for the order</param>
		/// <param name="qty">Number of contracts/shares to trade</param>
		/// <returns>True if order was placed, false otherwise</returns>
		public bool PlaceEntryLimitOrder(string dir, double price, int qty)
		{
			// Cannot place entry order if market is not flat
			if (S.Position.MarketPosition != MarketPosition.Flat)
			{
				DL.Log(S.NSBase, "MarketPosition is not Flat; cannot place new Entry Limit Order", "DerkTM");
				return false;
			}

			// Getting multiple entries sometimes: cannot trust entryOrder to properly
			// reflect the status, so loop through all orders to see if it already exists
			Order openOrder = S.GetEntryOrder();
			bool placed = false;

			// If we are already in a limit order, cancel or ignore
			if (openOrder != null)
			{
				// If a pending STOP order is already active, we close it...
				if (openOrder.OrderType == OrderType.StopMarket)
					DL.Log(S.NSBase, "Already in Pending " + dir + " Stop order @ " + openOrder.StopPrice + " - Cancelling");

				else if (openOrder.OrderType == OrderType.Limit)
				{
					if ((openOrder.OrderAction == OrderAction.SellShort && dir == "Short")
					|| (openOrder.OrderAction == OrderAction.Buy && dir == "Long"))
					{
						if (openOrder.LimitPrice == price)
						{
							DL.Log(S.NSBase, "Already in Pending " + dir + " Limit order @ " + price + " - Doing nothing");
							return false;
						}
						else
							DL.Log(S.NSBase, "Already in Pending " + dir + " Limit order, but at diff price (" + openOrder.LimitPrice + ", not requested price of " + price + ") - Cancelling old " + openOrder.OrderAction + " pending limit order");
					}
					else
						DL.Log(S.NSBase, "REVERSAL?: Already in Pending " + openOrder.OrderAction + " Entry Limit order, but " + dir + " requested - Cancelling old order");
				}
				else
					DL.Log(S.NSBase, "SHOULD NEVER HAPPEN: Already in Pending " + openOrder.OrderAction + " " + openOrder.OrderType + " order - Cancelling old order");

				CancelEntryOrder();
			}

			double curPrice = (dir == "Short") ? S.GetCurrentBid() : S.GetCurrentAsk();
			if (curPrice <= price && dir == "Long")
				DL.Log(S.NSBase, "Current Ask " + curPrice + " <= requested Limit price " + price + "; Cannot place Limit order");
			else if (curPrice >= price && dir == "Short")
				DL.Log(S.NSBase, "Current Bid " + curPrice + " >= requested Limit price " + price + "; Cannot place Limit order");
			else
			{
				DL.Log(S.NSBase, "Place limit order @ " + price + ": dir = " + dir + ", qty = " + qty + ", name = Entry");
				var action = (dir == "Long") ? OrderAction.Buy : OrderAction.SellShort;
				S.SubmitOrderUnmanaged(1, action, OrderType.Limit, qty, price, 0, "", "Entry");
				placed = true;
			}

			return placed;
		}

		/// <summary>
		/// Places a stop order for entry into a position
		/// </summary>
		/// <param name="dir">Direction of the trade ("Long" or "Short")</param>
		/// <param name="price">Stop price for the order</param>
		/// <param name="qty">Number of contracts/shares to trade</param>
		/// <returns>True if order was placed, false otherwise</returns>
		public bool PlaceEntryStopOrder(string dir, double price, int qty)
		{
			// Cannot place entry order if market is not flat
			if (S.Position.MarketPosition != MarketPosition.Flat)
			{
				DL.Log(S.NSBase, "MarketPosition is not Flat; cannot place new Entry Stop Order");
				return false;
			}

			// Getting multiple entries sometimes: cannot trust entryOrder to properly
			// reflect the status, so loop through all orders to see if it already exists
			int orderStatus = 0;
			Order openOrder = S.GetEntryOrder();
			bool placed = false;

			// If we are already in a limit order, cancel or ignore
			if (openOrder != null || orderStatus == 2 || orderStatus == 3)
			{
				// If order exists but in unreliable state, we need to cancel it
				if (orderStatus == 2)
				{
					DL.Log(S.NSBase, "Cancelling unreliable order to prevent duplicate orders");
					CancelEntryOrder();
				}
				// Special handling for filled orders - can't cancel them
				else if (orderStatus == 3)
				{
					DL.Log(S.NSBase, "Order already filled with contracts - cannot cancel, but will prevent duplicate orders");
					return false;
				}

				// If a pending LIMIT order is already active, we close it...
				if (openOrder.OrderType == OrderType.Limit)
					DL.Log(S.NSBase, "Already in Pending " + dir + " Limit order @ " + openOrder.LimitPrice + " - Cancelling");

				else if (openOrder.OrderType == OrderType.StopMarket)
				{
					if ((openOrder.OrderAction == OrderAction.SellShort && dir == "Short")
					|| (openOrder.OrderAction == OrderAction.Buy && dir == "Long"))
					{
						if (openOrder.StopPrice == price)
						{
							DL.Log(S.NSBase, "Already in Pending " + dir + " Stop order @ " + price + " - Doing nothing");
							return false;
						}
						else
							DL.Log(S.NSBase, "Already in Pending " + dir + " Stop order, but at diff price (" + openOrder.StopPrice + ", not requested price of " + price + ") - Cancelling old " + openOrder.OrderAction + " pending stop order");
					}
					else
						DL.Log(S.NSBase, "REVERSAL?: Already in Pending " + openOrder.OrderAction + " StopMarket order, but " + dir + " requested, so cancelling old order");
				}
				else
					DL.Log(S.NSBase, "SHOULD NEVER HAPPEN: Already in Pending " + openOrder.OrderAction + " " + openOrder.OrderType + " order - Cancelling old order");

				CancelEntryOrder();
			}

			double curPrice = (dir == "Short") ? S.GetCurrentBid() : S.GetCurrentAsk();
			if (curPrice >= price && dir == "Long")
				DL.Log(S.NSBase, dir + ": Current Ask " + curPrice + " >= requested Limit price " + price + "; Cannot place Stop order");
			else if (curPrice <= price && dir == "Short")
				DL.Log(S.NSBase, dir + ": Current Bid " + curPrice + " <= requested Limit price " + price + "; Cannot place Stop order");
			else
			{
				DL.Log(S.NSBase, "Place stop order @ " + price + ": dir = " + dir + ", qty = " + qty + ", name = Entry");
				var action = (dir == "Long") ? OrderAction.Buy : OrderAction.SellShort;
				S.SubmitOrderUnmanaged(1, action, OrderType.StopMarket, qty, 0, price, "", "Entry");
				placed = true;
			}

			return placed;
		}

		/// <summary>
		/// Places a market order for immediate execution
		/// </summary>
		/// <param name="dir">Direction of the trade ("Long" or "Short")</param>
		/// <param name="qty">Number of contracts/shares to trade</param>
		/// <param name="name">Name of the order (default: "Entry")</param>
		/// <returns>True if order was placed</returns>
		public bool PlaceMarketOrder(string dir, int qty, string name="Entry")
		{
			DL.Log(S.NSBase, "Placing market order, dir = " + dir + ", qty = " + qty + ", name = " + name);

			var action = (dir == "Long") ? OrderAction.Buy : OrderAction.SellShort;
			double price = (dir == "Long") ? S.GetCurrentAsk() : S.GetCurrentBid();
			DL.Log(S.NSBase, "Placing " + dir + " Market order named '" + name + "' for " + qty + " contract(s). Current price is: " + price);
			S.SubmitOrderUnmanaged(1, action, OrderType.Market, qty, 0, 0, "", name);

			return true;	// Always places order (so far -return false if later checks prevent placement)
		}

		/// <summary>
		/// Modifies an existing stop loss order
		/// </summary>
		/// <param name="orderSL">The stop loss order to modify</param>
		/// <param name="newSL">New stop price (0 to keep existing price)</param>
		/// <param name="reason">Reason for the modification (for logging)</param>
		/// <param name="qty">New quantity (0 to use existing quantity)</param>
		public void ModifyStopLoss(Order orderSL, double newSL, string reason, int qty=0)
		{
			if (orderSL == null)
			{
				DL.Log(S.NSBase, "Cannot move SL because order passed in == null");
				return;
			}

			// Used only for logging
			string dir = S.Position.MarketPosition.ToString();
			double curPrice = (dir == "Long") ? S.GetCurrentBid() : S.GetCurrentAsk();

			// If no newSL specified, use whole position
			if (qty == 0)
				qty = orderSL.Quantity;
			else if (qty > S.Position.Quantity)
			{
				DL.Log(S.NSBase, "qty (" + qty + ") > open quantity; setting to " + S.Position.Quantity);
				qty = S.Position.Quantity;
			}

			// If no newSL specified, we are just changing quantity
			if (newSL == 0)
			{
				newSL = orderSL.StopPrice;
				DL.Log(S.NSBase, "No new SL specified; using existing SL (" + newSL + ") w/Qty = " + qty + ". Order State is '" + orderSL.OrderState + "'; Trying to set SL now...");
			}

			try
			{
				if (curPrice <= newSL && dir == "Long")
					DL.Log(S.NSBase, "Dir is " + dir + ": Current Bid " + curPrice + " <= requested Limit price " + newSL + " - Cannot place Stop order; Aborting");
				else if (curPrice >= newSL && dir == "Short")
					DL.Log(S.NSBase, "Dir is " + dir + ": Current Ask " + curPrice + " >= requested Limit price " + newSL + " - Cannot modify Stop order; Aborting");
				else
				{
					// If there is no existing SL, we do NOT place a new one; we fail out
					DL.Log(S.NSBase, "Moving SL for " + qty + " contract(s) to new price @ " + newSL + " (" + reason + ")");

					S.ChangeOrder(orderSL, qty, 0, newSL);
				}
			}
			catch (Exception ex)
			{
				DL.Log(S.NSBase, "Error changing SL to " + newSL + ".  Current price = " + curPrice + ". Error: " + ex.Message);
				NinjaScript.Log("Error changing SL to " + newSL + ".  Current price = " + curPrice + ". Error: " + ex.Message, LogLevel.Error);	// Logs to console
			}
		}

		/// <summary>
		/// Modifies an existing take profit order
		/// </summary>
		/// <param name="orderTP">The take profit order to modify</param>
		/// <param name="newTP">New limit price</param>
		/// <param name="reason">Reason for the modification (for logging)</param>
		/// <param name="qty">New quantity (0 to use existing quantity)</param>
		public void ModifyTakeProfit(Order orderTP, double newTP, string reason, int qty=0)
		{
			// An existing TP is required; we do NOT place a new one if it does not exist
			if (orderTP == null)
			{
				DL.Log(S.NSBase, "Cannot move TP because order passed in == null");
				return;
			}

			// Used only for logging
			string dir = S.Position.MarketPosition.ToString();
			double curPrice = (dir == "Long") ? S.GetCurrentBid() : S.GetCurrentAsk();

			// If no qty specified, use appropriate quantity based on which TP order
			if (qty == 0)
				qty = orderTP.Quantity;
			if (qty > S.Position.Quantity)
			{
				DL.Log(S.NSBase, "qty (" + qty + ") > open quantity; setting to " + S.Position.Quantity);
				qty = S.Position.Quantity;
			}

			try
			{
				if (curPrice >= newTP && dir == "Long")
					DL.Log(S.NSBase, "Dir is " + dir + ": Current Ask " + curPrice + " >= requested Limit price " + newTP + " - Cannot modify TP order; Aborting");
				else if (curPrice <= newTP && dir == "Short")
					DL.Log(S.NSBase, "Dir is " + dir + ": Current Bid " + curPrice + " <= requested Limit price " + newTP + " - Cannot modify TP order; Aborting");
				else
				{
					DL.Log(S.NSBase, "Moving TP for " + qty + " contract(s) to new price @ " + newTP + " (" + reason + ")");

					S.ChangeOrder(orderTP, qty, newTP, 0);
				}
			}
			catch (Exception ex)
			{
				DL.Log(S.NSBase, "Error changing TP to " + newTP + ".  Current price = " + curPrice + ". Error: " + ex.Message);
				NinjaScript.Log("Error changing TP to " + newTP + ".  Current price = " + curPrice + ". Error: " + ex.Message, LogLevel.Error);	// Logs to console
			}
		}

		/// <summary>
		/// Closes an open position with a market order
		/// </summary>
		/// <param name="name">Name of the order (default: "Close")</param>
		/// <param name="dir">Direction of the position to close (default: current position direction)</param>
		/// <param name="qty">Number of contracts/shares to close (default: all)</param>
		public void ClosePosition(string name="Close", string dir="", int qty=0)
		{
			if (S.Position.MarketPosition == MarketPosition.Flat)
				return;

			if (qty == 0) qty = S.Position.Quantity;
			if (dir == "") dir = S.Position.MarketPosition.ToString();
			var action = (dir == "Long") ? OrderAction.Sell : OrderAction.BuyToCover;

			if (qty == S.Position.Quantity)
			{
				if (S.GetSlOrder1() != null || S.GetTpOrder2() != null)
				{
					DL.Log(S.NSBase, "Closing all contracts, and slOrder1 != null || tpOrder2 != null, so Cancelling Stops");
					S.CancelAllStops();
				}
			}

			DL.Log(S.NSBase, "Closing Position: name = " + name + ", dir = " + dir + ", qty = " + qty);
			S.SubmitOrderUnmanaged(1, action, OrderType.Market, qty, 0, 0, "", name);
		}

		/// <summary>
		/// Cancels the current entry order if it exists and is not filled
		/// </summary>
		/// <param name="memberName">Name of the calling method (automatically populated)</param>
		public void CancelEntryOrder([CallerMemberName] string memberName = "")
		{
			Order entryOrder = S.GetEntryOrder();
			if (entryOrder == null)
				return;

			if (entryOrder.OrderState == OrderState.Filled)
			{
				DL.Log(S.NSBase, "Cannot cancel filled order; aborting [from " + memberName + "]");
				return;
			}

			DL.Log(S.NSBase, "Cancelling entryOrder... [from " + memberName + "]");
			S.CancelOrder(entryOrder);
			S.SetEntryOrder(null);
		}

		/// <summary>
		/// Gets the average entry price of the current position, rounded to the nearest tick
		/// </summary>
		/// <returns>The average entry price</returns>
		public double AveEntryPrice()
		{
			double avePrice = S.Position.AveragePrice;

			// If ave price does not fall on tick boundary, round up
			int numTicks = (int)(avePrice / S.NSBase.TickSize);
			if (numTicks * S.NSBase.TickSize != avePrice)
			{
				double prc = S.NSBase.Instrument.MasterInstrument.RoundToTickSize(avePrice);
				avePrice = prc;
			}
			return avePrice;
		}
	}

	#region DerkMU Interface
	/// <summary>
	/// Interface for strategies that want to use DerkMU (Miscellaneous Utilities)
	/// Implement this interface in your strategy to use the DerkMU class.
	/// </summary>
	public interface IDerkMUStrategy
	{
		// Strategy properties
		Order GetEntryOrder();
		bool LongEnabled { get; }
		bool ShortEnabled { get; }
		Position Position { get; }
		State State { get; }
		DateTime CurBarTime { get; }

		// Timezone-adjusted session times
		DateTime AdjustedStartTime1 { get; }
		DateTime AdjustedEndTime1 { get; }
		DateTime AdjustedCloseTime1 { get; }
		DateTime AdjustedStartTime2 { get; }
		DateTime AdjustedEndTime2 { get; }
		DateTime AdjustedCloseTime2 { get; }
		DateTime AdjustedStartTime3 { get; }
		DateTime AdjustedEndTime3 { get; }
		DateTime AdjustedCloseTime3 { get; }
		DateTime AdjustedStartTime4 { get; }
		DateTime AdjustedEndTime4 { get; }
		DateTime AdjustedCloseTime4 { get; }
		DateTime AdjustedStartTime5 { get; }
		DateTime AdjustedEndTime5 { get; }
		DateTime AdjustedCloseTime5 { get; }
		DateTime AdjustedCloseTime { get; }

		// Price accessors
		double GetCurrentBid();
		double GetCurrentAsk();

		// Session properties
		bool UseSession1 { get; }
		bool UseSession2 { get; }
		bool UseSession3 { get; }
		bool UseSession4 { get; }
		bool UseSession5 { get; }
		DateTime StartTime1 { get; }
		DateTime EndTime1	{ get; }
		DateTime StartTime2 { get; }
		DateTime EndTime2	{ get; }
		DateTime StartTime3 { get; }
		DateTime EndTime3	{ get; }
		DateTime StartTime4 { get; }
		DateTime EndTime4	{ get; }
		DateTime StartTime5 { get; }
		DateTime EndTime5	{ get; }

		// Close time properties
		bool UseCloseTime { get; }
		bool UseCloseTime1 { get; }
		bool UseCloseTime2 { get; }
		bool UseCloseTime3 { get; }
		bool UseCloseTime4 { get; }
		bool UseCloseTime5 { get; }
		DateTime CloseTime	{ get; }
		DateTime CloseTime1 { get; }
		DateTime CloseTime2 { get; }
		DateTime CloseTime3 { get; }
		DateTime CloseTime4 { get; }
		DateTime CloseTime5 { get; }

		// Trading days
		bool TradeSunday { get; }
		bool TradeMonday { get; }
		bool TradeTuesday { get; }
		bool TradeWednesday { get; }
		bool TradeThursday { get; }
		bool TradeFriday { get; }

		// Volume properties
		int MinAveVolume { get; }
		int AveVolumePeriod { get; }
		double VolMA();

		// Drawing methods
		void DrawDiamond(string tag, bool isAutoScale, int barsAgo, double y, Brush brush);

		// Logging
		//void Log(string message);

		// Cast to NinjaScriptBase for logging
		NinjaScriptBase NSBase { get; }
	}
	#endregion

	/// <summary>
	/// DerkMU (Miscellaneous Utilities) class provides common utility functionality
	/// that can be shared across multiple strategies.
	/// </summary>
	public class DerkMU
	{
		private IDerkMUStrategy S;

		// Static variables for Crossed()
		private static double prvLvl = 0;
		private static string prvDir = "None"; // "Up" or "Down"
		private static HashSet<string> wasCloseToLevel = new HashSet<string>();
		private const double CLOSE_THRESHOLD = 4.0; // Points to consider "close"

		/// <summary>
		/// Constructor for the DerkMU class
		/// </summary>
		/// <param name="strategy">The strategy implementing the IDerkMUStrategy interface</param>
		public DerkMU(IDerkMUStrategy strategy)
		{
			S = strategy;
		}

		/// <summary>
		/// Detects price crosses of a specified level with enhanced detection capabilities.
		/// More robust than NinjaTrader's CrossAbove/Below functions as it prevents consecutive
		/// crosses in the same direction and includes "close zone" detection.
		/// </summary>
		/// <param name="level">The price level to check for crosses</param>
		/// <param name="dir">Direction of the cross ("Up" or "Down")</param>
		/// <param name="cPrc">Current price</param>
		/// <param name="pPrc">Previous price</param>
		/// <param name="skipDup">Whether to prevent duplicate crosses in the same direction (default: true)</param>
		/// <returns>True if a valid cross occurred, false otherwise</returns>
		public bool Crossed(double level, string dir, double cPrc, double pPrc, bool skipDup=false)
		{
			// This is an expanded function to check if a price crossed a given price level
			// The simple algo is 'standardCross' shown below, but it has been witnessed to
			// miss crosses (in real time).  This should not be possible (if truly called
			// each tick), yet it occurs.  The solution is to keep track of when the current
			// price is 'nearby' the given level, then if the standardCross is not triggered,
			// we can additionally check that cPrc is now on a different side of the level
			// that it used to be, and therefore know it crossed.  To implement this, we use
			// a hash table with a price/dir combo key.  If it exists, then it was close to
			// crossing.  If no longer valid, we remove the key.
			//
			// Yet I still see this seems to fail.  Either something is wrong with the
			// duplicate check, or.. something else is wrong.  I decided to shelve [the use of]
			// this in FiftyLevel for now and use a diff method.......

			bool didCross = false;

			// Create a unique key for this level+direction combination
			string key = $"{level}_{dir}";

			if (pPrc == 0)
				return false;

			// Standard crossing check
			bool standardCross = (dir == "Down") ? (pPrc >= level && cPrc < level) : (pPrc <= level && cPrc > level);

			// If standard cross detected, clear any "close" state and return true
			if (standardCross)
			{
				wasCloseToLevel.Remove(key);
				//DL.Log(S.NSBase, $"\n\nStandard {dir} cross of level {level} detected");
				didCross = true;
			}
			else
			{
				// Define the "close zone"
				double closeZoneEdge = (dir == "Down") ? level + CLOSE_THRESHOLD : level - CLOSE_THRESHOLD;

				if (wasCloseToLevel.Contains(key))
				{
					// Did price move away from the 'close zone'?
					if ((cPrc > closeZoneEdge  &&  dir == "Down")  ||  (cPrc < closeZoneEdge  &&  dir == "Up"))
					{
						wasCloseToLevel.Remove(key);
						//DL.Log(S.NSBase, $"\n\nPrice moved away from close zone for level {level} ({cPrc}), removing tracking");
					}

					// Check for enhanced cross detection
					else if ((cPrc < level  &&  dir == "Down")  ||  (cPrc > level  &&  dir == "Up"))
					{
						wasCloseToLevel.Remove(key);
						didCross = true;
						DL.Log(S.NSBase, $"\n\nEnhanced {dir} cross of level {level} detected using wasClose");
					}
				}
				else
				{
					// Update "close" state based on price position
					if ((cPrc <= closeZoneEdge  &&  cPrc > level  &&  dir == "Down")
					||  (cPrc >= closeZoneEdge  &&  cPrc < level  &&  dir == "Up"))
					{
						wasCloseToLevel.Add(key);
						//DL.Log(S.NSBase, $"\n\nPrice is in close zone for level {level} ({cPrc}), marking for potential {dir} cross");
					}
				}
			}

			// If enabled (true by default), do not report two crosses
			// in a row that have the same level and same direction
			if (skipDup  &&  didCross)
			{
				// Check if this is a consecutive cross (same direction, same level)
				if (prvDir == dir  &&  Math.Abs(prvLvl - level) < S.NSBase.TickSize)
				{
					DL.Log(S.NSBase, $"\nPrevented consecutive {dir} cross of level {level}");
					didCross = false;
				}
				else
				{
					prvLvl = level;
					prvDir = dir;
				}
			}
			return didCross;
		}

		/// <summary>
		/// Checks if all trade filters are satisfied for entering a trade
		/// </summary>
		/// <param name="dir">Direction of the trade ("Long" or "Short")</param>
		/// <returns>True if all filters pass, false otherwise</returns>
		public bool TradeFiltersOkay(string dir)
		{
			// Enter a trade if all enabled filters are satisfied
			bool passed = true;
			bool log = true;//(S.State == State.Realtime);
			string logStr = "";

			if (dir != "Long"  &&  dir != "Short")
			{
				logStr = $"Direction passed into TradeFiltersOkay ({dir}) must be 'Long' or 'Short'; Aborting";
				passed = false;
			}

			if (S.Position.Quantity != 0  ||  S.Position.MarketPosition != MarketPosition.Flat)
			{
				logStr = $"Cannot enter -already in position: MarketPosition = {S.Position.MarketPosition.ToString()}, Position.Quantity = {S.Position.Quantity}";
				passed = false;
			}

			if (!InTradeSession())
			{
				logStr = $"Out of session; Aborting {dir} entry";
				passed = false;
			}

			// Get volume average directly from the strategy
			double aveVol = S.VolMA();
			if (aveVol < S.MinAveVolume)
			{
				logStr = $"Volume insufficient ({aveVol} vs. {S.MinAveVolume} minimum); Aborting {dir} entry";
				S.DrawDiamond("VolFilter", false, 0, 0, Brushes.HotPink);
				passed = false;
			}

			if (dir == "Long")
			{
				if (!S.LongEnabled)
				{
					logStr = $"Long trade disabled; Aborting entry";
					passed = false;
				}
				else if (S.Position.MarketPosition == MarketPosition.Long)
				{
					logStr = $"Long trade already open; Aborting entry";
					passed = false;
				}
			}
			else if (dir == "Short")
			{
				if (!S.ShortEnabled)
				{
					logStr = $"Short trade disabled; Aborting entry";
					passed = false;
				}
				else if (S.Position.MarketPosition == MarketPosition.Short)
				{
					logStr = $"Short trade already open; Aborting entry";
					passed = false;
				}
			}

			if (log  &&  logStr != "") DL.Log(S.NSBase, "TRADE FILTER FAILED: " + logStr);
			return passed;
		}

		/// <summary>
		/// Checks if trading is allowed based on current time and configured trading sessions
		/// </summary>
		/// <returns>True if trading is allowed, false otherwise</returns>
		public bool InTradeSession()
		{
			// Check trading sessions / days
			int activeSessionNum = 0;

			switch (S.CurBarTime.DayOfWeek)
			{
				case DayOfWeek.Sunday:		if (!S.TradeSunday)		return false;   else break;
				case DayOfWeek.Monday:		if (!S.TradeMonday)		return false;   else break;
				case DayOfWeek.Tuesday:		if (!S.TradeTuesday)	return false;   else break;
				case DayOfWeek.Wednesday:	if (!S.TradeWednesday)	return false;   else break;
				case DayOfWeek.Thursday:	if (!S.TradeThursday)	return false;   else break;
				case DayOfWeek.Friday:		if (!S.TradeFriday)		return false;   else break;
				default:															break;
			}

			if (!(S.UseSession1  ||  S.UseSession2  ||  S.UseSession3  ||  S.UseSession4  ||  S.UseSession5))
				return true;

			if (S.UseSession1)
				if (CheckSession(1, S.AdjustedStartTime1, S.AdjustedEndTime1))
					return true;
			if (S.UseSession2)
				if (CheckSession(2, S.AdjustedStartTime2, S.AdjustedEndTime2))
					return true;
			if (S.UseSession3)
				if (CheckSession(3, S.AdjustedStartTime3, S.AdjustedEndTime3))
					return true;
			if (S.UseSession4)
				if (CheckSession(4, S.AdjustedStartTime4, S.AdjustedEndTime4))
					return true;
			if (S.UseSession5)
				if (CheckSession(5, S.AdjustedStartTime5, S.AdjustedEndTime5))
					return true;

			return false;
		}

		/// <summary>
		/// Checks if the current time is within a specific trading session
		/// </summary>
		/// <param name="num">Session number (for logging purposes)</param>
		/// <param name="startTime">Start time of the session</param>
		/// <param name="endTime">End time of the session</param>
		/// <returns>True if current time is within the session, false otherwise</returns>
		private bool CheckSession(int num, DateTime startTime, DateTime endTime)
		{
			// Check one trading session; return false if out-of-session
			bool okay = false;
			if (startTime.TimeOfDay < endTime.TimeOfDay)
			{
				if (S.CurBarTime.TimeOfDay >= startTime.TimeOfDay  &&  S.CurBarTime.TimeOfDay < endTime.TimeOfDay)
					okay = true;
			}
			else if (startTime.TimeOfDay > endTime.TimeOfDay)
			{
				if (S.CurBarTime.TimeOfDay >= startTime.TimeOfDay  ||  S.CurBarTime.TimeOfDay < endTime.TimeOfDay)
					okay = true;
			}
			else // (startTime.TimeOfDay == endTime.TimeOfDay)
			{
				DL.Log(S.NSBase, $"Start Time ({startTime.TimeOfDay.ToString()}) is the same as End Time ({endTime.TimeOfDay.ToString()}); Trading (always) approved");
				okay = true;
			}

			return okay;
		}

		/// <summary>
		/// Checks if it's time to close positions based on session close times
		/// </summary>
		/// <param name="activeSessionNum">The currently active trading session number (1-5)</param>
		/// <returns>True if positions should be closed, false otherwise</returns>
		public bool IsCloseTime(int activeSessionNum)
		{
			// Check if it's time to close positions based on session close times
			Order entryOrder = S.GetEntryOrder();
			if (S.Position.MarketPosition == MarketPosition.Flat  &&  entryOrder != null)
				return false;

			// If we are not using any trading session (trade all times), then just check the main close time
			if (!(S.UseSession1  ||  S.UseSession2  ||  S.UseSession3  ||  S.UseSession4  ||  S.UseSession5))
			{
				// Close all trades at end of user-defined session
				if (S.UseCloseTime  &&  S.CurBarTime.TimeOfDay >= S.AdjustedCloseTime.TimeOfDay)
					return true;
				return false;
			}

			// Otherwise, check which session is active
			// Close all trades at end of user-defined session
			if (activeSessionNum == 1  &&  S.UseCloseTime1  &&  S.CurBarTime.TimeOfDay >= S.AdjustedCloseTime1.TimeOfDay)
				return true;
			if (activeSessionNum == 2  &&  S.UseCloseTime2  &&  S.CurBarTime.TimeOfDay >= S.AdjustedCloseTime2.TimeOfDay)
				return true;
			if (activeSessionNum == 3  &&  S.UseCloseTime3  &&  S.CurBarTime.TimeOfDay >= S.AdjustedCloseTime3.TimeOfDay)
				return true;
			if (activeSessionNum == 4  &&  S.UseCloseTime4  &&  S.CurBarTime.TimeOfDay >= S.AdjustedCloseTime4.TimeOfDay)
				return true;
			if (activeSessionNum == 5  &&  S.UseCloseTime5  &&  S.CurBarTime.TimeOfDay >= S.AdjustedCloseTime5.TimeOfDay)
				return true;
			return false;
		}
	}
}

namespace NinjaTrader.NinjaScript
{
	/// <summary>
	/// DerkLog class provides common logging functionality
	/// that can be shared across multiple NinjaScript types (strategies, indicators, etc.)
	/// </summary>
	public static class DerkLog
	{
		// Static configuration - initialized from calling script
		public static bool		DisableLogging { get; set; } = false;
		public static bool		UseOutput2 { get; set; } = false;
		public static string	LogToFileName { get; set; } = "";

		// State tracking - some kept within this class, some exposed for strategy to set
		private static string	lastLogMsg = "";
		private static string	lastLogTime = "";

		// These need to be set from the strategy
		public static string	LastCaller { get; set; } = "";
		public static string	LogPath { get; set; } = "";

		/// <summary>
		/// Logs a message to the NinjaTrader output window and optionally to a file
		/// </summary>
		/// <param name="caller">The NinjaScript object (strategy, indicator, etc.) calling the log method</param>
		/// <param name="message">The message to log</param>
		/// <param name="memberName">The name of the calling method (automatically populated)</param>
		public static void Log(NinjaScriptBase caller, string message, [CallerMemberName] string memberName = "")
		{
			if (DisableLogging || message == lastLogMsg)
				return;

			// Set output tab
			caller.PrintTo = UseOutput2 ? PrintTo.OutputTab2 : PrintTo.OutputTab1;

			// Get appropriate timestamp based on state
			string dateStr = (caller.State == State.Realtime)
				? DateTime.Now.ToString("HH:mm:ss")
				: caller.Time[0].ToString();

			string scriptName = caller.GetType().Name;
			caller.Print($"\nTESTING LOG: {scriptName} - [{memberName}] - {dateStr}");

			// Format message with header if needed
			string output = message;

			// Add time if same caller but different time
			if (lastLogTime != dateStr  &&  LastCaller == memberName)
				output = message + " ( " + dateStr + " )";

			// Add header if new caller
			//if (LastCaller != memberName)
			{
				string header = $"\n{scriptName} - [{memberName}] - {dateStr} - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -";
				if (LastCaller != memberName)
				{
					caller.Print(header);
					LogToFile(caller, header);
				}
			}

			// Print and log the message
			caller.Print(output);
			LogToFile(caller, output);

			// Update tracking variables
			lastLogMsg = message;
			LastCaller = memberName;
			lastLogTime = dateStr;
		}

		/// <summary>
		/// Writes a log message to a file
		/// </summary>
		/// <param name="caller">The NinjaScript object for error reporting</param>
		/// <param name="message">The message to write to the log file</param>
		private static void LogToFile(NinjaScriptBase caller, string message)
		{
			// Skip file logging if no file name
			if (string.IsNullOrEmpty(LogToFileName))
				return;

			try
			{
				if (string.IsNullOrEmpty(LogPath))
				{
					LogPath = System.IO.Path.Combine(NinjaTrader.Core.Globals.UserDataDir, "log", LogToFileName, LogToFileName + "Log.txt");

					// Ensure directory exists
					string folder = System.IO.Path.GetDirectoryName(LogPath);
					if (!System.IO.Directory.Exists(folder))
						System.IO.Directory.CreateDirectory(folder);
				}

				// Append to log file
				System.IO.File.AppendAllText(LogPath, message + Environment.NewLine);
			}
			catch (Exception ex)
			{
				caller.Print($"LogToFile() error: {ex.Message}");
			}
		}
	}
}
