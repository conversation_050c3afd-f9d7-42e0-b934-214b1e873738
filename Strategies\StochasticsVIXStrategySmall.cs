using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Gui.Tools;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.Indicators;
using NinjaTrader.NinjaScript.DrawingTools;

namespace NinjaTrader.NinjaScript.Strategies
{
    public class StochasticsVIXStrategySmall : Strategy
    {
        private StochasticsFast stochastics; // Single stochastic indicator
        
        private bool longCondition;
        private bool shortCondition;
        private bool longExitCondition;
        private bool shortExitCondition;
        
        // VIX variables
        private double currentVIX = 0;
        private bool vixTradingEnabled = false;

        protected override void OnStateChange()
        {
            if (State == State.SetDefaults)
            {
                Description                     = @"Stochastics-based trading strategy with VIX control for SMALL Renko charts";
                Name                           = "StochasticsVIXStrategySmall";
                Calculate                      = Calculate.OnBarClose;
                EntriesPerDirection            = 1;
                EntryHandling                  = EntryHandling.AllEntries;
                IsExitOnSessionCloseStrategy   = true;
                ExitOnSessionCloseSeconds      = 30;
                IsFillLimitOnTouch             = false;
                MaximumBarsLookBack            = MaximumBarsLookBack.TwoHundredFiftySix;
                OrderFillResolution            = OrderFillResolution.Standard;
                Slippage                       = 0;
                StartBehavior                  = StartBehavior.WaitUntilFlat;
                TimeInForce                    = TimeInForce.Gtc;
                TraceOrders                    = false;
                RealtimeErrorHandling          = RealtimeErrorHandling.StopCancelClose;
                StopTargetHandling             = StopTargetHandling.PerEntryExecution;
                BarsRequiredToTrade            = 50;
                IsInstantiatedOnEachOptimizationIteration = true;
                
                // D% Toggle
                UseDPercent = true;
                
                // Session Filters
                UseUSSession = true;
                UseAsianSession = true;
                UseEuropeanSession = true;
                
                // Risk Management
                ProfitTarget = 0;
                StopLoss = 0;
                
                // Position Size
                NumberOfContracts = 1;
                
                // Chart Display
                ShowStochasticOnChart = true;
                
                // VIX Control Settings - SMALL RENKO DEFAULTS
                UseVIXControl = true;
                VIXThreshold = 17.0;
                VIXTimeFrame = 5;
                IsSmallRenkoChart = true;  // DEFAULT: Small Renko Chart
                ShowVIXDebug = false;
                
                // Session Time Inputs
                USSessionStartTime = DateTime.Parse("09:30", System.Globalization.CultureInfo.InvariantCulture);
                USSessionEndTime = DateTime.Parse("16:00", System.Globalization.CultureInfo.InvariantCulture);
                AsianSessionStartTime = DateTime.Parse("18:00", System.Globalization.CultureInfo.InvariantCulture);
                AsianSessionEndTime = DateTime.Parse("11:59", System.Globalization.CultureInfo.InvariantCulture);
                EuropeanSessionStartTime = DateTime.Parse("12:00", System.Globalization.CultureInfo.InvariantCulture);
                EuropeanSessionEndTime = DateTime.Parse("09:29", System.Globalization.CultureInfo.InvariantCulture);
            }
            else if (State == State.Configure)
            {
                // Add VIX data series if VIX control is enabled
                if (UseVIXControl)
                {
                    AddDataSeries("^VIX", Data.BarsPeriodType.Minute, VIXTimeFrame);
                    Print(String.Format("SMALL RENKO: VIX data series added - TimeFrame: {0} minutes, Threshold: {1}", 
                        VIXTimeFrame, VIXThreshold));
                }
            }
            else if (State == State.DataLoaded)
            {
                stochastics = StochasticsFast(3, 1); // Single stochastic with K=3, D=1
                
                // Add indicator to chart if enabled
                if (ShowStochasticOnChart)
                {
                    AddChartIndicator(stochastics);
                }
                
                // Initialize VIX trading state
                if (!UseVIXControl)
                {
                    vixTradingEnabled = true; // Always enabled if VIX control is off
                    Print("SMALL RENKO: VIX Control disabled - Trading always enabled");
                }
                
                // Force clean state on contract change
                Print(String.Format("SMALL RENKO: Strategy initialized on contract: {0}", Instrument.FullName));
            }
        }

        protected override void OnBarUpdate()
        {
            // Handle VIX data updates
            if (UseVIXControl && BarsInProgress == 1) // VIX data series
            {
                if (CurrentBars[1] < 1) return;
                
                currentVIX = Close[0]; // VIX closing price
                
                // SMALL RENKO: Trade when VIX < threshold
                vixTradingEnabled = currentVIX < VIXThreshold;
                
                if (ShowVIXDebug)
                {
                    Print(String.Format("SMALL RENKO VIX UPDATE - Value: {0:F2}, Threshold: {1:F2}, Trading Enabled: {2}", 
                        currentVIX, VIXThreshold, vixTradingEnabled));
                }
                
                return; // Don't process trading logic on VIX bar updates
            }
            
            // Main instrument trading logic
            if (BarsInProgress != 0 || CurrentBars[0] < 50)
                return;

            // Check VIX trading permission first
            if (UseVIXControl && !vixTradingEnabled)
            {
                if (ShowVIXDebug && CurrentBar % 50 == 0) // Reduce debug spam
                {
                    Print(String.Format("SMALL RENKO VIX BLOCK - VIX: {0:F2}, Threshold: {1:F2}, Trading Blocked", 
                        currentVIX, VIXThreshold));
                }
                return;
            }

            // Add contract/instrument debugging
            if (CurrentBar % 200 == 0) // Every 200 bars
            {
                Print(String.Format("SMALL RENKO Contract Info - Instrument: {0}, MasterInstrument: {1}, Date/Time: {2}", 
                    Instrument.FullName, Instrument.MasterInstrument.Name, Time[0].ToString("MM/dd/yyyy HH:mm")));
            }

            // Check if we're in an allowed trading session
            if (!IsInTradingSession())
            {
                // Add debug print to see if session filter is blocking trades
                if (CurrentBar % 50 == 0) // Print every 50 bars to avoid spam
                {
                    Print(String.Format("SMALL RENKO Outside trading session - Date/Time: {0}", Time[0].ToString("MM/dd/yyyy HH:mm")));
                }
                return;
            }

            // Get current stochastics values from single indicator
            double currentK = stochastics.K[0];
            double currentD = stochastics.D[0];
            
            // Exit conditions use K% with tolerance
            longExitCondition = currentK <= 0.001;
            shortExitCondition = currentK >= 99.999;
            
            // Comprehensive debug logging with detailed condition breakdown
            bool debugLongCond = (UseDPercent ? currentD >= 100 : currentK >= 100);
            bool debugShortCond = (UseDPercent ? currentD <= 0 : currentK <= 0);
            
            Print(String.Format("SMALL RENKO FULL DEBUG - Time: {0}, K: {1}, D: {2}, Position: {3}, InSession: {4}, VIX: {5:F2}, VIXEnabled: {6}",
                Time[0].ToString("MM/dd HH:mm:ss"), 
                currentK, currentD, 
                Position.MarketPosition,
                IsInTradingSession(),
                currentVIX,
                vixTradingEnabled
            ));
            
            Print(String.Format("SMALL RENKO CONDITION DEBUG - UseDPercent: {0}, D>=100: {1}, K>=100: {2}, D<=0: {3}, K<=0: {4}, LongCond: {5}, ShortCond: {6}",
                UseDPercent,
                currentD >= 100,
                currentK >= 100,
                currentD <= 0,
                currentK <= 0,
                debugLongCond,
                debugShortCond
            ));
            
            // Session debugging
            TimeSpan currentTimeOfDay = Time[0].TimeOfDay;
            TimeSpan usStart = USSessionStartTime.TimeOfDay;
            TimeSpan usEnd = USSessionEndTime.TimeOfDay;
            Print(String.Format("SMALL RENKO SESSION DEBUG - CurrentTime: {0}, USStart: {1}, USEnd: {2}, UseUSSession: {3}, TimeInRange: {4}",
                currentTimeOfDay,
                usStart,
                usEnd,
                UseUSSession,
                (currentTimeOfDay >= usStart && currentTimeOfDay <= usEnd)
            ));
            
            // Stochastic value type debugging
            Print(String.Format("SMALL RENKO VALUE DEBUG - K Type: {0}, D Type: {1}, K Raw: {2}, D Raw: {3}",
                currentK.GetType(),
                currentD.GetType(),
                currentK.ToString("F10"),
                currentD.ToString("F10")
            ));
            
            // Direct comparison testing
            bool directDTest = currentD >= 100;
            bool directDTest2 = currentD >= 100.0;
            bool directDTest3 = currentD >= 99.999;
            double hundredValue = 100.0;
            bool directDTest4 = currentD >= hundredValue;
            
            Print(String.Format("SMALL RENKO COMPARISON TEST - D:{0}, D>=100:{1}, D>=100.0:{2}, D>=99.999:{3}, D>=hundredVar:{4}",
                currentD, directDTest, directDTest2, directDTest3, directDTest4));

            // Entry Logic
            if (Position.MarketPosition == MarketPosition.Flat)
            {
                // Add debug for position state
                if (CurrentBar % 100 == 0) // Every 100 bars
                {
                    Print(String.Format("SMALL RENKO Position Check - State: {0}, Quantity: {1}, Date/Time: {2}", 
                        Position.MarketPosition, Position.Quantity, Time[0].ToString("MM/dd/yyyy HH:mm")));
                }
                
                // Long Entry Logic
                if (UseDPercent)
                {
                    // Use D% for long entries with tolerance
                    longCondition = currentD >= 99.999;
                }
                else
                {
                    // Use K% for long entries with tolerance
                    longCondition = currentK >= 99.999;
                }
                
                // Add debug print for long conditions
                if (longCondition)
                {
                    Print(String.Format("SMALL RENKO LONG CONDITION MET - Date/Time: {0}, CurrentK: {1}, CurrentD: {2}, UseDPercent: {3}, VIX: {4:F2}", 
                        Time[0].ToString("MM/dd/yyyy HH:mm"), currentK, currentD, UseDPercent, currentVIX));
                }
                
                if (longCondition)
                {
                    EnterLong(NumberOfContracts, "StochLong");
                    Print(String.Format("SMALL RENKO LONG ENTRY EXECUTED - Date/Time: {0}, Contracts: {1}, VIX: {2:F2}", Time[0].ToString("MM/dd/yyyy HH:mm"), NumberOfContracts, currentVIX));
                }
                
                // Short Entry Logic
                if (UseDPercent)
                {
                    // Use D% for short entries with tolerance
                    shortCondition = currentD <= 0.001;
                }
                else
                {
                    // Use K% for short entries with tolerance
                    shortCondition = currentK <= 0.001;
                }
                
                // Add debug print for short conditions
                if (shortCondition)
                {
                    Print(String.Format("SMALL RENKO SHORT CONDITION MET - Date/Time: {0}, CurrentK: {1}, CurrentD: {2}, UseDPercent: {3}, VIX: {4:F2}", 
                        Time[0].ToString("MM/dd/yyyy HH:mm"), currentK, currentD, UseDPercent, currentVIX));
                }
                
                if (shortCondition)
                {
                    EnterShort(NumberOfContracts, "StochShort");
                    Print(String.Format("SMALL RENKO SHORT ENTRY EXECUTED - Date/Time: {0}, Contracts: {1}, VIX: {2:F2}", Time[0].ToString("MM/dd/yyyy HH:mm"), NumberOfContracts, currentVIX));
                }
            }
            
            // Exit Logic
            if (Position.MarketPosition == MarketPosition.Long && longExitCondition)
            {
                ExitLong("StochLongExit", "StochLong");
            }
            else if (Position.MarketPosition == MarketPosition.Short && shortExitCondition)
            {
                ExitShort("StochShortExit", "StochShort");
            }
        }

        protected override void OnExecutionUpdate(Execution execution, string executionId, double price, int quantity, MarketPosition marketPosition, string orderId, DateTime time)
        {
            // Set profit target and stop loss on entry fills
            if (execution.Order.Name == "StochLong" || execution.Order.Name == "StochShort")
            {
                if (ProfitTarget > 0)
                {
                    if (execution.Order.Name == "StochLong")
                        ExitLongLimit(NumberOfContracts, price + ProfitTarget * TickSize, "ProfitTarget", "StochLong");
                    else
                        ExitShortLimit(NumberOfContracts, price - ProfitTarget * TickSize, "ProfitTarget", "StochShort");
                }
                
                if (StopLoss > 0)
                {
                    if (execution.Order.Name == "StochLong")
                        ExitLongStopMarket(NumberOfContracts, price - StopLoss * TickSize, "StopLoss", "StochLong");
                    else
                        ExitShortStopMarket(NumberOfContracts, price + StopLoss * TickSize, "StopLoss", "StochShort");
                }
            }
        }

        private bool IsInTradingSession()
        {
            DateTime currentTime = Time[0];
            TimeSpan currentTimeOfDay = currentTime.TimeOfDay;
            
            bool inSession = false;
            
            // US Session: Use configurable times
            if (UseUSSession)
            {
                TimeSpan usStart = USSessionStartTime.TimeOfDay;
                TimeSpan usEnd = USSessionEndTime.TimeOfDay;
                
                if (currentTimeOfDay >= usStart && currentTimeOfDay <= usEnd)
                    inSession = true;
            }
            
            // Asian Session: Use configurable times
            if (UseAsianSession)
            {
                TimeSpan asianStart = AsianSessionStartTime.TimeOfDay;
                TimeSpan asianEnd = AsianSessionEndTime.TimeOfDay;
                
                // Handle sessions that cross midnight
                if (asianStart > asianEnd)
                {
                    if (currentTimeOfDay >= asianStart || currentTimeOfDay <= asianEnd)
                        inSession = true;
                }
                else
                {
                    if (currentTimeOfDay >= asianStart && currentTimeOfDay <= asianEnd)
                        inSession = true;
                }
            }
            
            // European Session: Use configurable times
            if (UseEuropeanSession)
            {
                TimeSpan euroStart = EuropeanSessionStartTime.TimeOfDay;
                TimeSpan euroEnd = EuropeanSessionEndTime.TimeOfDay;
                
                if (currentTimeOfDay >= euroStart && currentTimeOfDay <= euroEnd)
                    inSession = true;
            }
            
            return inSession;
        }

        #region Properties
        
        // Stochastics parameters are now hardcoded (K=3, D=1)

        [NinjaScriptProperty]
        [Display(Name="Use D% for Entries", Description="When checked, use %D instead of %K for entry signals", Order=1, GroupName="Stochastics Parameters")]
        public bool UseDPercent
        { get; set; }

        [NinjaScriptProperty]
        [Display(Name="Show Stochastics on Chart", Description="Display stochastic indicator on chart", Order=2, GroupName="Stochastics Parameters")]
        public bool ShowStochasticOnChart
        { get; set; }

        [NinjaScriptProperty]
        [Display(Name="Use VIX Control", Description="Enable VIX-based chart control", Order=1, GroupName="VIX Control")]
        public bool UseVIXControl
        { get; set; }

        [NinjaScriptProperty]
        [Range(1.0, 100.0)]
        [Display(Name="VIX Threshold", Description="VIX level threshold for chart switching", Order=2, GroupName="VIX Control")]
        public double VIXThreshold
        { get; set; }

        [NinjaScriptProperty]
        [Range(1, 60)]
        [Display(Name="VIX Time Frame (Minutes)", Description="Time frame for VIX data updates", Order=3, GroupName="VIX Control")]
        public int VIXTimeFrame
        { get; set; }

        [NinjaScriptProperty]
        [Display(Name="Is Small Renko Chart", Description="SMALL RENKO VERSION: Always checked - trades when VIX < threshold", Order=4, GroupName="VIX Control")]
        public bool IsSmallRenkoChart
        { get; set; }

        [NinjaScriptProperty]
        [Display(Name="Show VIX Debug", Description="Show VIX-related debug information", Order=5, GroupName="VIX Control")]
        public bool ShowVIXDebug
        { get; set; }

        [NinjaScriptProperty]
        [Display(Name="US Session", Description="Enable trading during US session", Order=1, GroupName="Session Filters")]
        public bool UseUSSession
        { get; set; }

        [NinjaScriptProperty]
        [Display(Name="Asian Session", Description="Enable trading during Asian session", Order=2, GroupName="Session Filters")]
        public bool UseAsianSession
        { get; set; }

        [NinjaScriptProperty]
        [Display(Name="European Session", Description="Enable trading during European session", Order=3, GroupName="Session Filters")]
        public bool UseEuropeanSession
        { get; set; }

        [NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name="Profit Target (Ticks)", Description="Profit target in ticks (0 to disable)", Order=1, GroupName="Risk Management")]
        public int ProfitTarget
        { get; set; }

        [NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name="Stop Loss (Ticks)", Description="Stop loss in ticks (0 to disable)", Order=2, GroupName="Risk Management")]
        public int StopLoss
        { get; set; }

        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name="Number of Contracts", Description="Number of contracts to trade", Order=3, GroupName="Risk Management")]
        public int NumberOfContracts
        { get; set; }

        [NinjaScriptProperty]
        [PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
        [Display(Name="US Session Start", Description="US session start time", Order=1, GroupName="Session Times")]
        public DateTime USSessionStartTime
        { get; set; }

        [NinjaScriptProperty]
        [PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
        [Display(Name="US Session End", Description="US session end time", Order=2, GroupName="Session Times")]
        public DateTime USSessionEndTime
        { get; set; }

        [NinjaScriptProperty]
        [PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
        [Display(Name="Asian Session Start", Description="Asian session start time", Order=3, GroupName="Session Times")]
        public DateTime AsianSessionStartTime
        { get; set; }

        [NinjaScriptProperty]
        [PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
        [Display(Name="Asian Session End", Description="Asian session end time", Order=4, GroupName="Session Times")]
        public DateTime AsianSessionEndTime
        { get; set; }

        [NinjaScriptProperty]
        [PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
        [Display(Name="European Session Start", Description="European session start time", Order=5, GroupName="Session Times")]
        public DateTime EuropeanSessionStartTime
        { get; set; }

        [NinjaScriptProperty]
        [PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
        [Display(Name="European Session End", Description="European session end time", Order=6, GroupName="Session Times")]
        public DateTime EuropeanSessionEndTime
        { get; set; }

        #endregion
    }
}