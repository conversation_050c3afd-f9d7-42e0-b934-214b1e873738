using NinjaTrader.Gui.Tools;					// For EnumConverter, property display attributes
using NinjaTrader.Gui.Chart;					// For ChartControl, ChartScale, rendering utilities
using NinjaTrader.NinjaScript;					// For core NinjaTrader scripting components
using NinjaTrader.NinjaScript.Indicators;		// For accessing NinjaTrader indicators
using System;									// For Math.Max
using System.ComponentModel;					// For property-related attributes
using System.ComponentModel.DataAnnotations;	// For DisplayAttribute
using System.Windows.Media;						// For Brush, Brushes, colors, etc.
using System.Xml.Serialization;					// For XML stuff in properties


using NinjaTrader.Gui;


namespace NinjaTrader.NinjaScript.Indicators
{
	public enum MAType
	{
		SMA,
		EMA,
		VWMA
	}
	
	public class MATypesConverter : System.ComponentModel.EnumConverter
	{
		public MATypesConverter() : base(typeof(MAType)) { }
	}
	
	public static class BrushSerializer
	{
		public static string BrushToString(Brush brush)
		{
			if (brush is SolidColorBrush solidColorBrush)
				return solidColorBrush.Color.ToString();
			return null;
		}
	
		public static Brush StringToBrush(string brushString)
		{
			if (!string.IsNullOrEmpty(brushString))
				return new SolidColorBrush((Color)ColorConverter.ConvertFromString(brushString));
			return Brushes.Transparent;
		}
	}

	public class rgMovAves : Indicator
	{
		private Series<double>[] maSeries;
		private Brush[] maBrushes;
		private DashStyle[] maLineStyles;
		private int[] maLineWidths;
		
		protected override void OnStateChange()
		{
			if (State == State.SetDefaults)
			{
				Description = "Displays up to 7 configurable moving averages in one indicator.";
				Name = "rgMovAves";
				IsOverlay = true;
				AddPlot(Brushes.Transparent, "HiddenPlot"); // Placeholder for indicator logic
				
				// Initialize defaults for MA1
				EnableMA1 = true;
				MA1Type = MAType.SMA;
				MA1Period = 5;
				MA1Color = Brushes.Khaki;
				MA1LineStyle = DashStyleHelper.Solid;
				
				EnableMA2 = true;
				MA2Type = MAType.SMA;
				MA2Period = 8;
				MA2Color = Brushes.Gold;
				MA2LineStyle = DashStyleHelper.Solid;
				
				EnableMA3 = true;
				MA3Type = MAType.SMA;
				MA3Period = 13;
				MA3Color = Brushes.Goldenrod;
				MA3LineStyle = DashStyleHelper.Solid;
				
				EnableMA4 = true;
				MA4Type = MAType.SMA;
				MA4Period = 21;
				MA4Color = Brushes.Orange;
				MA4LineStyle = DashStyleHelper.Solid;
				
				EnableMA5 = true;
				MA5Type = MAType.SMA;
				MA5Period = 34;
				MA5Color = Brushes.DarkOrange;
				MA5LineStyle = DashStyleHelper.Solid;
				
				EnableMA6 = true;
				MA6Type = MAType.SMA;
				MA6Period = 55;
				MA6Color = Brushes.Chocolate;
				MA6LineStyle = DashStyleHelper.Solid;
				
				EnableMA7 = true;
				MA7Type = MAType.SMA;
				MA7Period = 89;
				MA7Color = Brushes.SaddleBrown;
				MA7LineStyle = DashStyleHelper.Solid;
			}
			else if (State == State.DataLoaded)
			{
				maSeries = new Series<double>[7];
				maBrushes = new Brush[7];
				maLineStyles = new DashStyle[7];
				
				// Initialize each series and settings
				for (int i = 0; i < 7; i++)
				{
					maSeries[i] = new Series<double>(this);
				}
			}
		}
		
		protected override void OnBarUpdate()
		{
			if (CurrentBar < Math.Max(MA1Period, 
							 Math.Max(MA2Period, 
							 Math.Max(MA3Period, 
							 Math.Max(MA4Period, 
							 Math.Max(MA5Period, 
							 Math.Max(MA6Period, MA7Period)))))))
				return;
	
			// Update moving averages dynamically
			if (EnableMA1)	maSeries[0][0] = CalculateMA(MA1Type, MA1Period);
			if (EnableMA2)	maSeries[1][0] = CalculateMA(MA2Type, MA2Period);
			if (EnableMA3)	maSeries[2][0] = CalculateMA(MA3Type, MA3Period);
			if (EnableMA4)	maSeries[3][0] = CalculateMA(MA4Type, MA4Period);
			if (EnableMA5)	maSeries[4][0] = CalculateMA(MA5Type, MA5Period);
			if (EnableMA6)	maSeries[5][0] = CalculateMA(MA6Type, MA6Period);
			if (EnableMA7)	maSeries[6][0] = CalculateMA(MA7Type, MA7Period);
		}
		
		private double CalculateMA(MAType type, int period)
		{
			switch (type)
			{
				case MAType.SMA:		return SMA(period)[0];
				case MAType.EMA:		return EMA(period)[0];
				case MAType.VWMA:		return VWMA(period)[0];
				default:				return 0;
			}
		}
		
		protected override void OnRender(ChartControl chartControl, ChartScale chartScale)
		{
			/*
			base.OnRender(chartControl, chartScale);
			
			for (int i = 0; i < 7; i++)
			{
				if (EnableMA1 && maSeries[i] != null && maSeries[i].Count > 0)
				{
					RenderLine(maSeries[i], maBrushes[i], maLineStyles[i], maLineWidths[i], chartControl, chartScale);
				}
			}*/
		}
		
		private void RenderLine(Series<double> series, Brush brush, DashStyleHelper dashStyle, int lineWidth, ChartControl chartControl, ChartScale chartScale)
		{/*
			// Ensure there is data to render
			if (series == null || series.Count < 2) return;
			
			// Create a PathGeometry for the line
			PathGeometry pathGeometry = new PathGeometry();
			PathFigure pathFigure = new PathFigure { IsClosed = false };
			
			// Loop through the bars and calculate screen coordinates
			for (int i = ChartBars.FromIndex; i <= ChartBars.ToIndex; i++)
			{
				// Get the X coordinate for the current bar
				double x = chartControl.GetXByBarIndex(ChartBars, i);
				
				// Get the Y coordinate for the current value in the series
				double y = chartScale.GetYByValue(series[i]);
				
				// Add the point to the PathFigure
				if (i == ChartBars.FromIndex)
					pathFigure.StartPoint = new Point(x, y); // Starting point of the line
				else
					pathFigure.Segments.Add(new LineSegment(new Point(x, y), true));
			}
			
			// Add the PathFigure to the PathGeometry
			pathGeometry.Figures.Add(pathFigure);
			
			// Create a Pen for rendering the line
			Pen pen = new Pen(brush, lineWidth)
			{
				DashStyle = dashStyle.ToDashStyle() // Converts DashStyleHelper to DashStyle
			};
			
			// Render the line
			RenderTarget.DrawGeometry(null, pen, pathGeometry);*/
		}


		#region PROPERTIES - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
		
		[NinjaScriptProperty]
		[Display(Name = "Enable MA1", GroupName = "MA1", Order = 0)]
		public bool EnableMA1 { get; set; }
		
		[NinjaScriptProperty]
		[TypeConverter(typeof(MATypesConverter))]
		[Display(Name = "Type", GroupName = "MA1", Order = 3)]
		public MAType MA1Type { get; set; }
		
		[NinjaScriptProperty]
		[Display(Name = "Period", GroupName = "MA1", Order = 6)]
		public int MA1Period { get; set; }
		
		[XmlIgnore]
		[Display(Name = "Color", GroupName = "MA1", Order = 9)]
		public Brush MA1Color { get; set; }
		
		[Browsable(false)]
		public string MA1ColorSerializable
		{
			get { return BrushSerializer.BrushToString(MA1Color); }
			set { MA1Color = BrushSerializer.StringToBrush(value); }
		}
		
		[NinjaScriptProperty]
		[Display(Name = "Line Width", GroupName = "MA1", Order = 12)]
		public int MA1LineWidth { get; set; }
		
		[NinjaScriptProperty]
		[Display(Name = "Line Style", GroupName = "MA1", Order = 15)]
		public DashStyleHelper MA1LineStyle { get; set; }



		[NinjaScriptProperty]
		[Display(Name = "Enable MA2", GroupName = "MA2", Order = 0)]
		public bool EnableMA2 { get; set; }
		
		[NinjaScriptProperty]
		[TypeConverter(typeof(MATypesConverter))]
		[Display(Name = "Type", GroupName = "MA2", Order = 3)]
		public MAType MA2Type { get; set; }
		
		[NinjaScriptProperty]
		[Display(Name = "Period", GroupName = "MA2", Order = 6)]
		public int MA2Period { get; set; }
		
		[XmlIgnore]
		[Display(Name = "Color", GroupName = "MA2", Order = 9)]
		public Brush MA2Color { get; set; }
		
		[Browsable(false)]
		public string MA2ColorSerializable
		{
			get { return BrushSerializer.BrushToString(MA2Color); }
			set { MA2Color = BrushSerializer.StringToBrush(value); }
		}
		
		[NinjaScriptProperty]
		[Display(Name = "Line Width", GroupName = "MA2", Order = 12)]
		public int MA2LineWidth { get; set; }
		
		[NinjaScriptProperty]
		[Display(Name = "Line Style", GroupName = "MA2", Order = 15)]
		public DashStyleHelper MA2LineStyle { get; set; }



		[NinjaScriptProperty]
		[Display(Name = "Enable MA3", GroupName = "MA3", Order = 0)]
		public bool EnableMA3 { get; set; }
		
		[NinjaScriptProperty]
		[TypeConverter(typeof(MATypesConverter))]
		[Display(Name = "Type", GroupName = "MA3", Order = 3)]
		public MAType MA3Type { get; set; }
		
		[NinjaScriptProperty]
		[Display(Name = "Period", GroupName = "MA3", Order = 6)]
		public int MA3Period { get; set; }
		
		[XmlIgnore]
		[Display(Name = "Color", GroupName = "MA3", Order = 9)]
		public Brush MA3Color { get; set; }
		
		[Browsable(false)]
		public string MA3ColorSerializable
		{
			get { return BrushSerializer.BrushToString(MA3Color); }
			set { MA3Color = BrushSerializer.StringToBrush(value); }
		}
		
		[NinjaScriptProperty]
		[Display(Name = "Line Width", GroupName = "MA3", Order = 12)]
		public int MA3LineWidth { get; set; }
		
		[NinjaScriptProperty]
		[Display(Name = "Line Style", GroupName = "MA3", Order = 15)]
		public DashStyleHelper MA3LineStyle { get; set; }



		[NinjaScriptProperty]
		[Display(Name = "Enable MA4", GroupName = "MA4", Order = 0)]
		public bool EnableMA4 { get; set; }
		
		[NinjaScriptProperty]
		[TypeConverter(typeof(MATypesConverter))]
		[Display(Name = "Type", GroupName = "MA4", Order = 3)]
		public MAType MA4Type { get; set; }
		
		[NinjaScriptProperty]
		[Display(Name = "Period", GroupName = "MA4", Order = 6)]
		public int MA4Period { get; set; }
		
		[XmlIgnore]
		[Display(Name = "Color", GroupName = "MA4", Order = 9)]
		public Brush MA4Color { get; set; }
		
		[Browsable(false)]
		public string MA4ColorSerializable
		{
			get { return BrushSerializer.BrushToString(MA4Color); }
			set { MA4Color = BrushSerializer.StringToBrush(value); }
		}
		
		[NinjaScriptProperty]
		[Display(Name = "Line Width", GroupName = "MA4", Order = 12)]
		public int MA4LineWidth { get; set; }
		
		[NinjaScriptProperty]
		[Display(Name = "Line Style", GroupName = "MA4", Order = 15)]
		public DashStyleHelper MA4LineStyle { get; set; }



		[NinjaScriptProperty]
		[Display(Name = "Enable MA5", GroupName = "MA5", Order = 0)]
		public bool EnableMA5 { get; set; }
		
		[NinjaScriptProperty]
		[TypeConverter(typeof(MATypesConverter))]
		[Display(Name = "Type", GroupName = "MA5", Order = 3)]
		public MAType MA5Type { get; set; }
		
		[NinjaScriptProperty]
		[Display(Name = "Period", GroupName = "MA5", Order = 6)]
		public int MA5Period { get; set; }
		
		[XmlIgnore]
		[Display(Name = "Color", GroupName = "MA5", Order = 9)]
		public Brush MA5Color { get; set; }
		
		[Browsable(false)]
		public string MA5ColorSerializable
		{
			get { return BrushSerializer.BrushToString(MA5Color); }
			set { MA5Color = BrushSerializer.StringToBrush(value); }
		}
		
		[NinjaScriptProperty]
		[Display(Name = "Line Width", GroupName = "MA5", Order = 12)]
		public int MA5LineWidth { get; set; }
		
		[NinjaScriptProperty]
		[Display(Name = "Line Style", GroupName = "MA5", Order = 15)]
		public DashStyleHelper MA5LineStyle { get; set; }



		[NinjaScriptProperty]
		[Display(Name = "Enable MA6", GroupName = "MA6", Order = 0)]
		public bool EnableMA6 { get; set; }
		
		[NinjaScriptProperty]
		[TypeConverter(typeof(MATypesConverter))]
		[Display(Name = "Type", GroupName = "MA6", Order = 3)]
		public MAType MA6Type { get; set; }
		
		[NinjaScriptProperty]
		[Display(Name = "Period", GroupName = "MA6", Order = 6)]
		public int MA6Period { get; set; }
		
		[XmlIgnore]
		[Display(Name = "Color", GroupName = "MA6", Order = 9)]
		public Brush MA6Color { get; set; }
		
		[Browsable(false)]
		public string MA6ColorSerializable
		{
			get { return BrushSerializer.BrushToString(MA6Color); }
			set { MA6Color = BrushSerializer.StringToBrush(value); }
		}
		
		[NinjaScriptProperty]
		[Display(Name = "Line Width", GroupName = "MA6", Order = 12)]
		public int MA6LineWidth { get; set; }
		
		[NinjaScriptProperty]
		[Display(Name = "Line Style", GroupName = "MA6", Order = 15)]
		public DashStyleHelper MA6LineStyle { get; set; }



		[NinjaScriptProperty]
		[Display(Name = "Enable MA7", GroupName = "MA7", Order = 0)]
		public bool EnableMA7 { get; set; }
		
		[NinjaScriptProperty]
		[TypeConverter(typeof(MATypesConverter))]
		[Display(Name = "Type", GroupName = "MA7", Order = 3)]
		public MAType MA7Type { get; set; }
		
		[NinjaScriptProperty]
		[Display(Name = "Period", GroupName = "MA7", Order = 6)]
		public int MA7Period { get; set; }
		
		[XmlIgnore]
		[Display(Name = "Color", GroupName = "MA7", Order = 9)]
		public Brush MA7Color { get; set; }
		
		[Browsable(false)]
		public string MA7ColorSerializable
		{
			get { return BrushSerializer.BrushToString(MA7Color); }
			set { MA7Color = BrushSerializer.StringToBrush(value); }
		}
		
		[NinjaScriptProperty]
		[Display(Name = "Line Width", GroupName = "MA7", Order = 12)]
		public int MA7LineWidth { get; set; }
		
		[NinjaScriptProperty]
		[Display(Name = "Line Style", GroupName = "MA7", Order = 15)]
		public DashStyleHelper MA7LineStyle { get; set; }
		#endregion
	}
}

#region NinjaScript generated code. Neither change nor remove.

namespace NinjaTrader.NinjaScript.Indicators
{
	public partial class Indicator : NinjaTrader.Gui.NinjaScript.IndicatorRenderBase
	{
		private rgMovAves[] cachergMovAves;
		public rgMovAves rgMovAves(bool enableMA1, MAType mA1Type, int mA1Period, int mA1LineWidth, DashStyleHelper mA1LineStyle, bool enableMA2, MAType mA2Type, int mA2Period, int mA2LineWidth, DashStyleHelper mA2LineStyle, bool enableMA3, MAType mA3Type, int mA3Period, int mA3LineWidth, DashStyleHelper mA3LineStyle, bool enableMA4, MAType mA4Type, int mA4Period, int mA4LineWidth, DashStyleHelper mA4LineStyle, bool enableMA5, MAType mA5Type, int mA5Period, int mA5LineWidth, DashStyleHelper mA5LineStyle, bool enableMA6, MAType mA6Type, int mA6Period, int mA6LineWidth, DashStyleHelper mA6LineStyle, bool enableMA7, MAType mA7Type, int mA7Period, int mA7LineWidth, DashStyleHelper mA7LineStyle)
		{
			return rgMovAves(Input, enableMA1, mA1Type, mA1Period, mA1LineWidth, mA1LineStyle, enableMA2, mA2Type, mA2Period, mA2LineWidth, mA2LineStyle, enableMA3, mA3Type, mA3Period, mA3LineWidth, mA3LineStyle, enableMA4, mA4Type, mA4Period, mA4LineWidth, mA4LineStyle, enableMA5, mA5Type, mA5Period, mA5LineWidth, mA5LineStyle, enableMA6, mA6Type, mA6Period, mA6LineWidth, mA6LineStyle, enableMA7, mA7Type, mA7Period, mA7LineWidth, mA7LineStyle);
		}

		public rgMovAves rgMovAves(ISeries<double> input, bool enableMA1, MAType mA1Type, int mA1Period, int mA1LineWidth, DashStyleHelper mA1LineStyle, bool enableMA2, MAType mA2Type, int mA2Period, int mA2LineWidth, DashStyleHelper mA2LineStyle, bool enableMA3, MAType mA3Type, int mA3Period, int mA3LineWidth, DashStyleHelper mA3LineStyle, bool enableMA4, MAType mA4Type, int mA4Period, int mA4LineWidth, DashStyleHelper mA4LineStyle, bool enableMA5, MAType mA5Type, int mA5Period, int mA5LineWidth, DashStyleHelper mA5LineStyle, bool enableMA6, MAType mA6Type, int mA6Period, int mA6LineWidth, DashStyleHelper mA6LineStyle, bool enableMA7, MAType mA7Type, int mA7Period, int mA7LineWidth, DashStyleHelper mA7LineStyle)
		{
			if (cachergMovAves != null)
				for (int idx = 0; idx < cachergMovAves.Length; idx++)
					if (cachergMovAves[idx] != null && cachergMovAves[idx].EnableMA1 == enableMA1 && cachergMovAves[idx].MA1Type == mA1Type && cachergMovAves[idx].MA1Period == mA1Period && cachergMovAves[idx].MA1LineWidth == mA1LineWidth && cachergMovAves[idx].MA1LineStyle == mA1LineStyle && cachergMovAves[idx].EnableMA2 == enableMA2 && cachergMovAves[idx].MA2Type == mA2Type && cachergMovAves[idx].MA2Period == mA2Period && cachergMovAves[idx].MA2LineWidth == mA2LineWidth && cachergMovAves[idx].MA2LineStyle == mA2LineStyle && cachergMovAves[idx].EnableMA3 == enableMA3 && cachergMovAves[idx].MA3Type == mA3Type && cachergMovAves[idx].MA3Period == mA3Period && cachergMovAves[idx].MA3LineWidth == mA3LineWidth && cachergMovAves[idx].MA3LineStyle == mA3LineStyle && cachergMovAves[idx].EnableMA4 == enableMA4 && cachergMovAves[idx].MA4Type == mA4Type && cachergMovAves[idx].MA4Period == mA4Period && cachergMovAves[idx].MA4LineWidth == mA4LineWidth && cachergMovAves[idx].MA4LineStyle == mA4LineStyle && cachergMovAves[idx].EnableMA5 == enableMA5 && cachergMovAves[idx].MA5Type == mA5Type && cachergMovAves[idx].MA5Period == mA5Period && cachergMovAves[idx].MA5LineWidth == mA5LineWidth && cachergMovAves[idx].MA5LineStyle == mA5LineStyle && cachergMovAves[idx].EnableMA6 == enableMA6 && cachergMovAves[idx].MA6Type == mA6Type && cachergMovAves[idx].MA6Period == mA6Period && cachergMovAves[idx].MA6LineWidth == mA6LineWidth && cachergMovAves[idx].MA6LineStyle == mA6LineStyle && cachergMovAves[idx].EnableMA7 == enableMA7 && cachergMovAves[idx].MA7Type == mA7Type && cachergMovAves[idx].MA7Period == mA7Period && cachergMovAves[idx].MA7LineWidth == mA7LineWidth && cachergMovAves[idx].MA7LineStyle == mA7LineStyle && cachergMovAves[idx].EqualsInput(input))
						return cachergMovAves[idx];
			return CacheIndicator<rgMovAves>(new rgMovAves(){ EnableMA1 = enableMA1, MA1Type = mA1Type, MA1Period = mA1Period, MA1LineWidth = mA1LineWidth, MA1LineStyle = mA1LineStyle, EnableMA2 = enableMA2, MA2Type = mA2Type, MA2Period = mA2Period, MA2LineWidth = mA2LineWidth, MA2LineStyle = mA2LineStyle, EnableMA3 = enableMA3, MA3Type = mA3Type, MA3Period = mA3Period, MA3LineWidth = mA3LineWidth, MA3LineStyle = mA3LineStyle, EnableMA4 = enableMA4, MA4Type = mA4Type, MA4Period = mA4Period, MA4LineWidth = mA4LineWidth, MA4LineStyle = mA4LineStyle, EnableMA5 = enableMA5, MA5Type = mA5Type, MA5Period = mA5Period, MA5LineWidth = mA5LineWidth, MA5LineStyle = mA5LineStyle, EnableMA6 = enableMA6, MA6Type = mA6Type, MA6Period = mA6Period, MA6LineWidth = mA6LineWidth, MA6LineStyle = mA6LineStyle, EnableMA7 = enableMA7, MA7Type = mA7Type, MA7Period = mA7Period, MA7LineWidth = mA7LineWidth, MA7LineStyle = mA7LineStyle }, input, ref cachergMovAves);
		}
	}
}

namespace NinjaTrader.NinjaScript.MarketAnalyzerColumns
{
	public partial class MarketAnalyzerColumn : MarketAnalyzerColumnBase
	{
		public Indicators.rgMovAves rgMovAves(bool enableMA1, MAType mA1Type, int mA1Period, int mA1LineWidth, DashStyleHelper mA1LineStyle, bool enableMA2, MAType mA2Type, int mA2Period, int mA2LineWidth, DashStyleHelper mA2LineStyle, bool enableMA3, MAType mA3Type, int mA3Period, int mA3LineWidth, DashStyleHelper mA3LineStyle, bool enableMA4, MAType mA4Type, int mA4Period, int mA4LineWidth, DashStyleHelper mA4LineStyle, bool enableMA5, MAType mA5Type, int mA5Period, int mA5LineWidth, DashStyleHelper mA5LineStyle, bool enableMA6, MAType mA6Type, int mA6Period, int mA6LineWidth, DashStyleHelper mA6LineStyle, bool enableMA7, MAType mA7Type, int mA7Period, int mA7LineWidth, DashStyleHelper mA7LineStyle)
		{
			return indicator.rgMovAves(Input, enableMA1, mA1Type, mA1Period, mA1LineWidth, mA1LineStyle, enableMA2, mA2Type, mA2Period, mA2LineWidth, mA2LineStyle, enableMA3, mA3Type, mA3Period, mA3LineWidth, mA3LineStyle, enableMA4, mA4Type, mA4Period, mA4LineWidth, mA4LineStyle, enableMA5, mA5Type, mA5Period, mA5LineWidth, mA5LineStyle, enableMA6, mA6Type, mA6Period, mA6LineWidth, mA6LineStyle, enableMA7, mA7Type, mA7Period, mA7LineWidth, mA7LineStyle);
		}

		public Indicators.rgMovAves rgMovAves(ISeries<double> input , bool enableMA1, MAType mA1Type, int mA1Period, int mA1LineWidth, DashStyleHelper mA1LineStyle, bool enableMA2, MAType mA2Type, int mA2Period, int mA2LineWidth, DashStyleHelper mA2LineStyle, bool enableMA3, MAType mA3Type, int mA3Period, int mA3LineWidth, DashStyleHelper mA3LineStyle, bool enableMA4, MAType mA4Type, int mA4Period, int mA4LineWidth, DashStyleHelper mA4LineStyle, bool enableMA5, MAType mA5Type, int mA5Period, int mA5LineWidth, DashStyleHelper mA5LineStyle, bool enableMA6, MAType mA6Type, int mA6Period, int mA6LineWidth, DashStyleHelper mA6LineStyle, bool enableMA7, MAType mA7Type, int mA7Period, int mA7LineWidth, DashStyleHelper mA7LineStyle)
		{
			return indicator.rgMovAves(input, enableMA1, mA1Type, mA1Period, mA1LineWidth, mA1LineStyle, enableMA2, mA2Type, mA2Period, mA2LineWidth, mA2LineStyle, enableMA3, mA3Type, mA3Period, mA3LineWidth, mA3LineStyle, enableMA4, mA4Type, mA4Period, mA4LineWidth, mA4LineStyle, enableMA5, mA5Type, mA5Period, mA5LineWidth, mA5LineStyle, enableMA6, mA6Type, mA6Period, mA6LineWidth, mA6LineStyle, enableMA7, mA7Type, mA7Period, mA7LineWidth, mA7LineStyle);
		}
	}
}

namespace NinjaTrader.NinjaScript.Strategies
{
	public partial class Strategy : NinjaTrader.Gui.NinjaScript.StrategyRenderBase
	{
		public Indicators.rgMovAves rgMovAves(bool enableMA1, MAType mA1Type, int mA1Period, int mA1LineWidth, DashStyleHelper mA1LineStyle, bool enableMA2, MAType mA2Type, int mA2Period, int mA2LineWidth, DashStyleHelper mA2LineStyle, bool enableMA3, MAType mA3Type, int mA3Period, int mA3LineWidth, DashStyleHelper mA3LineStyle, bool enableMA4, MAType mA4Type, int mA4Period, int mA4LineWidth, DashStyleHelper mA4LineStyle, bool enableMA5, MAType mA5Type, int mA5Period, int mA5LineWidth, DashStyleHelper mA5LineStyle, bool enableMA6, MAType mA6Type, int mA6Period, int mA6LineWidth, DashStyleHelper mA6LineStyle, bool enableMA7, MAType mA7Type, int mA7Period, int mA7LineWidth, DashStyleHelper mA7LineStyle)
		{
			return indicator.rgMovAves(Input, enableMA1, mA1Type, mA1Period, mA1LineWidth, mA1LineStyle, enableMA2, mA2Type, mA2Period, mA2LineWidth, mA2LineStyle, enableMA3, mA3Type, mA3Period, mA3LineWidth, mA3LineStyle, enableMA4, mA4Type, mA4Period, mA4LineWidth, mA4LineStyle, enableMA5, mA5Type, mA5Period, mA5LineWidth, mA5LineStyle, enableMA6, mA6Type, mA6Period, mA6LineWidth, mA6LineStyle, enableMA7, mA7Type, mA7Period, mA7LineWidth, mA7LineStyle);
		}

		public Indicators.rgMovAves rgMovAves(ISeries<double> input , bool enableMA1, MAType mA1Type, int mA1Period, int mA1LineWidth, DashStyleHelper mA1LineStyle, bool enableMA2, MAType mA2Type, int mA2Period, int mA2LineWidth, DashStyleHelper mA2LineStyle, bool enableMA3, MAType mA3Type, int mA3Period, int mA3LineWidth, DashStyleHelper mA3LineStyle, bool enableMA4, MAType mA4Type, int mA4Period, int mA4LineWidth, DashStyleHelper mA4LineStyle, bool enableMA5, MAType mA5Type, int mA5Period, int mA5LineWidth, DashStyleHelper mA5LineStyle, bool enableMA6, MAType mA6Type, int mA6Period, int mA6LineWidth, DashStyleHelper mA6LineStyle, bool enableMA7, MAType mA7Type, int mA7Period, int mA7LineWidth, DashStyleHelper mA7LineStyle)
		{
			return indicator.rgMovAves(input, enableMA1, mA1Type, mA1Period, mA1LineWidth, mA1LineStyle, enableMA2, mA2Type, mA2Period, mA2LineWidth, mA2LineStyle, enableMA3, mA3Type, mA3Period, mA3LineWidth, mA3LineStyle, enableMA4, mA4Type, mA4Period, mA4LineWidth, mA4LineStyle, enableMA5, mA5Type, mA5Period, mA5LineWidth, mA5LineStyle, enableMA6, mA6Type, mA6Period, mA6LineWidth, mA6LineStyle, enableMA7, mA7Type, mA7Period, mA7LineWidth, mA7LineStyle);
		}
	}
}

#endregion
