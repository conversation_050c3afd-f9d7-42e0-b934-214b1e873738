#region Using declarations
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Net.Http;			/// Added for HttpClient	
using System.Net.Http.Headers;	/// Added for AuthenticationHeaderValue
using Newtonsoft.Json;			/// Added for automatic parsing of http response
using Npgsql;					/// Added for Benoit new signal get method Sep2024

using System.Linq;
using System.Runtime.CompilerServices;	/// For MemberCallerName
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Gui.Tools;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.Indicators;
using NinjaTrader.NinjaScript.Strategies;
using NinjaTrader.NinjaScript.DrawingTools;
#endregion

/// This namespace holds Strategies in this folder and is required. Do not change it. 
namespace NinjaTrader.NinjaScript.Strategies
{
	/// Obsidian is merely ladder with the default parameters changed such that it 
	/// will run like the "Control" template I made, where it takes one contract 
	/// and runs flip-to-flip, no SL or TP. No filters or Trail, none if it.
	public class Obsidian : Strategy
	{
		#region Globals
		
		/// HttpClient is intended to be instantiated once per application, rather than per-use
		/// THIS IS OLD URL METHOD
		//private static readonly HttpClient Client = new HttpClient();
		//private const string 	URL = "http://mb.almanac.ai:3338/api/signal/gamma";
		
        private NpgsqlConnection	postgreSqlConnection;
        private string 				connectionString = "Host=mb.almanac.ai;Port=5432;Username=algotrader1;Password=**********;Database=accrue";
        private List<GreeksData8>	signalList = new List<GreeksData8>();

		private const int 		MIN_LOOKBACK = 61;
		private const int 		ADD_LOOKBACK = 20;
		private const int 		MAX_LOOKBACK = 120;
		private const int		TS_SWING_STRENGTH = 5;

		private double			lastSetPnL = 0;
		
		private int 			checkSeconds;
		private int				contTradesTaken = 0;
		private int				maxContTrades;
		private int				firstEntryBarIdx = -1;
		private int				firstEntryBarsAgo = -1;
		
		private string 			signalDir = "None";
		private string 			lastTradeDir = "None";
		private string 			manualSignal = "None";
		private string 			lastDashboard = "";
		private string 			lastLogMsg = "";
		private string 			lastCaller = "";
		private string			lastLogTime = "";
		private string			chartInstrument;
		
		private bool 			entriesEnabled = true;
		private bool			longOn = true;
		private bool			shortOn = true;
		private bool			firstSignalRecevied = false;
		private bool			userRequestedExit = false;
		private bool			inSession = true;
		
		/// These variables were taken from NinjaTrader sample called UnmanagedTemplate
		private Order 			entryOrder = null;
		private Order 			slOrder = null;
		private Order 			tpOrder = null;
		private string			oco;
		private bool			stopsSubmitted = false;
		
		//private List<double>	tradeSetsPnL;
		private DateTime		prvSignalTime = DateTime.MinValue;
		private DateTime		signalTime = DateTime.MinValue.AddSeconds(1);
        private DateTime		nextCheckTime = DateTime.MinValue.AddSeconds(1);
		
		private System.Windows.Controls.Button	shortButton;
		private System.Windows.Controls.Button	longButton;
		private System.Windows.Controls.Button	exitButton;
		private System.Windows.Controls.Button	logSignalsButton;
		private System.Windows.Controls.Button	manualEnterLong;
		private System.Windows.Controls.Button	manualEnterShort;
		private System.Windows.Controls.Grid	myGrid;
		
		#endregion
		
		protected override void OnStateChange()
		{
			#region State Change Info
			/// By printing out "State", dicovered this order:
			/*
				OnStateChange(), State = SetDefaults	// 1st clone for listing the strategies available in UI
				OnStateChange(), State = Configure
				OnStateChange(), State = Configure
				OnStateChange(), State = SetDefaults	// 2nd clone for configuring the one selected
				OnStateChange(), State = Terminated
			
				// Then the real one that we care about after we apply the settings:
				OnStateChange(), State = Configure		// Cloned again, so already has defaults set
				OnStateChange(), State = DataLoaded
				OnStateChange(), State = Historical
				OnStateChange(), State = Transition
				OnStateChange(), State = Realtime
				OnStateChange(), State = Terminated		// would be called again when terminated
			*/
			#endregion
			
			/// Set Input Defaults
			if (State == State.SetDefaults)
			{
				Description						= @"Strategy to automatically trade Jacob signals, no stops";
				Name							= "Obsidian";
				Calculate						= Calculate.OnBarClose;
				EntriesPerDirection				= 1;
				EntryHandling					= EntryHandling.AllEntries;
				IsExitOnSessionCloseStrategy	= true;
				ExitOnSessionCloseSeconds		= 2700;		// 45 mins: Exit @ 13:15 PST
				IsFillLimitOnTouch				= false;
				MaximumBarsLookBack				= MaximumBarsLookBack.TwoHundredFiftySix;
				OrderFillResolution				= OrderFillResolution.Standard;
				Slippage						= 0;
				StartBehavior					= StartBehavior.WaitUntilFlat; //AdoptAccountPosition;
				TimeInForce						= TimeInForce.Gtc;
				TraceOrders						= false;
				RealtimeErrorHandling			= RealtimeErrorHandling.StopCancelClose;
				//StopTargetHandling			= StopTargetHandling.ByStrategyPosition;	// method from managed Razor version
				StopTargetHandling				= StopTargetHandling.PerEntryExecution;
				BarsRequiredToTrade				= 20;
				IsUnmanaged 					= true;
				IsAdoptAccountPositionAware 	= true;		// Not sure if this is correct/needed
				
				/// Disable this property for performance gains in Strategy Analyzer optimizations
				/// See the Help Guide for additional information
				IsInstantiatedOnEachOptimizationIteration	= true;
				
				
				/// Quantity of contract to trade
				Quantity				= 1;
				
				/// Maximum delay between time stamp and trade entry
				LastTradeDir			= "None";
				CheckSeconds			= 10;
				MaxDelaySeconds			= 300;		// Seems to be ~1-2 min delay built-in....
				TimeZoneOffset			= 0; 		// Signals are posted EST, so we need to subtract 3 to get to PT
				
				/// StopLoss & TakeProfit
				StopLoss				= 0;
				TakeProfit				= 0;
				
				LimitTradingHours		= true;
				StartTime				= DateTime.Parse("09:34", System.Globalization.CultureInfo.InvariantCulture);
				EndTime 				= DateTime.Parse("15:55", System.Globalization.CultureInfo.InvariantCulture);
				UseCloseTime			= true;
				CloseTime 				= DateTime.Parse("15:59", System.Globalization.CultureInfo.InvariantCulture);
				
				StrategyName			= "Obsidian";
				DisplayOCD				= true;
				DisableLogging			= false;
				UseOutput2				= false;
				UniqueID				= "1";
			}
			
			/// Initialize Member Variables
			else if (State == State.Configure)
			{
				checkSeconds = CheckSeconds;
				chartInstrument = this.Instrument.FullName;

				AddDataSeries(BarsPeriodType.Tick, 1);
                ConnectToDatabase();
			}
			
			else if (State == State.DataLoaded)
			{
			}
			
			else if (State == State.Historical)
			{
				if (ChartControl != null)
				{
					ChartControl.Dispatcher.InvokeAsync(() =>
					{
						CreateButtons();
					});
				}	
			}
			
			else if (State == State.Terminated)
			{
				if (ChartControl != null)
				{
					ChartControl.Dispatcher.InvokeAsync(() =>
					{
						DisposeButtons();
					});
				}
                CloseDatabaseConnection();
			}
		}

		protected override void OnExecutionUpdate(Cbi.Execution execution, string executionId, double price, int quantity, 
												  Cbi.MarketPosition marketPosition, string orderId, DateTime time)
		{
			Log($"Name = {execution.Name}, IsEntryStrategy = {execution.IsEntryStrategy},  OrderAction = {execution.Order.OrderAction},   marketPosition = {marketPosition},   quantity (parameter) = {quantity},   Position.Quantity = {Position.Quantity}");
			
			// We advise monitoring OnExecution to trigger submission of stop/target orders instead of OnOrderUpdate()
			// since OnExecution() is called after OnOrderUpdate() which ensures your strategy has received the execution
			// which is used for internal signal tracking.
			
			double m = (Position.MarketPosition == MarketPosition.Long) ? 1.0 : -1.0;
			double avePrice = AvePrice();

			/// Handle actions for this execution being an entry order (execution.Order.OrderAction.Buy or SellShort)
			if (execution.IsEntryStrategy)
			{
				if (StopLoss > 0  ||  TakeProfit > 0)
				{
					/// [Only] if we are using both stops do we need to link OCO
					oco = "";
					/// TakeProfit is required (if using any TP), TakeProfit 1 & 2 are incidental
					if (State == State.Historical)
						oco = DateTime.Now.ToString() + "_" + CurrentBar + "_" + marketPosition.ToString() + "Exits";
					else
						oco = GetAtmStrategyUniqueId() + "_" + marketPosition.ToString() + "Exits";
					
					if (entryOrder != null  &&  entryOrder == execution.Order)
					{
						Log($"IsEntryStrategy = True, execution.Order.OrderState = {execution.Order.OrderState}");
						
						// We are not specifically handing this case, so hope it never happens:
						if (execution.Order.OrderState == OrderState.PartFilled)
							Log($"   ENTRY ORDER ONLY PARTIALLY FILLED; execution.Order.Filled = {execution.Order.Filled}");
						
						else if (execution.Order.OrderState == OrderState.Cancelled)
						{
							/// If the order was somehow cancelled, then we need to just and position that partially opened
							Log($"   ENTRY ORDER CANCELLED; dir = {marketPosition.ToString()}, execution.Order.Filled = {execution.Order.Filled}");
							if (execution.Order.Filled > 0)
							{
								Log($"Partial order was filled, so closing now");
								ClosePosition("Partial Fill", marketPosition.ToString(), execution.Order.Filled);
							}
						}
						else if (execution.Order.OrderState == OrderState.Filled)
						{
							if (!stopsSubmitted)
							{
								/// This used to check that slOrder & tpOrders were null, because we were initially setting, 
								/// but it won't place the stops until order is filled, so no need to check for that. 
								var action = (marketPosition == MarketPosition.Long) ? OrderAction.Sell : OrderAction.BuyToCover;

								Log($"INIT PLACEMENT OF SL & TP; Action = {action.ToString()}");
								if (StopLoss > 0)
								{
									Log($"Submitting StopLoss for Qty {Position.Quantity}");
									SubmitOrderUnmanaged(0, action, OrderType.StopMarket, Position.Quantity, 0, avePrice - m * StopLoss * TickSize, oco, "StopLoss");
								}
								if (TakeProfit > 0)
								{
									Log($"Submitting TakeProfit for Qty {Position.Quantity}");
									SubmitOrderUnmanaged(0, action, OrderType.Limit, Position.Quantity, avePrice + m * TakeProfit * TickSize, 0, oco, "TakeProfit");
								}
								stopsSubmitted = true;
							}
							else
								Log($"execution.Order.OrderState == OrderState.Filled & stops already submitted; DOING NOTHING");
						}
					}
					else
						Log($"entryOrder = {entryOrder}, execution.Order.OrderState = {execution.Order.OrderState}");
				}
			}
			
			//if (execution.Order.OrderAction == OrderAction.Sell  ||  execution.Order.OrderAction == OrderAction.BuyToCover)
			else	// Is exit strategy
			{
				/// If we did NOT just close *entire* position, update SL quantity
				if (Position.Quantity > 0)
				{
					Log($"Position.Quantity > 0; Changing SL order to Qty ({Position.Quantity}) @ price {avePrice - m * StopLoss * TickSize}");
					ModifyStopLoss(avePrice - m * StopLoss * TickSize);
				}
				else
				{
					/// Cancel any stops that are left open. SL & TP2 OCO, but check all to be sure
					Log($"Position.Quantity == 0; cancelling stops & Resetting orders to null");
					CancelStops();
					entryOrder = slOrder = tpOrder = null;
				}
			}
		}

		
		protected override void OnOrderUpdate(Cbi.Order order, double limitPrice, double stopPrice, int quantity, 
											  int filled, double averageFillPrice, Cbi.OrderState orderState, 
											  DateTime time, Cbi.ErrorCode error, string comment)
        {
			Log($"order.Name = {order.Name}");
			/// This was taken from a NinjaTrader sample called UnmanagedTemplate
			
			/// Assign Order objects here
			/// This is more reliable than assigning Order objects in OnBarUpdate, as the assignment 
			/// is not guaranteed to be complete if it is referenced immediately after submitting
			if (order.Name == "Entry")
			{
				//Log($"Setting entryOrder");
				entryOrder = order;
			}
			else if (order.Name == "StopLoss")
			{
				//Log($"Setting slOrder");
				slOrder = order;
			}
			else if (order.Name == "TakeProfit")
			{
				//Log($"Setting tpOrder");
				tpOrder = order;
			}
		}
		
		
		protected override void OnBarUpdate()
		{
			if (CurrentBars[0] < BarsRequiredToTrade  ||  CurrentBars[1] < BarsRequiredToTrade)
				return;
			
			/// No point in trying to do historical trades without the signals
			if (State == State.Historical)
			{
				return;
			}
			// Don't waste time on OCD for historical
			else 
				ManageOCD();	// Update the On-Chart Display
			
			/// Entered on close of each candle of chart TF where strategy is applied (first data series)
			bool highFractal, lowFractal;
			if (BarsInProgress == 0)
			{
				if (Bars.IsFirstBarOfSession)
				{
					/// This is not useful for us because we do not let the EA run 24/5 such that this would trigger at 6pm ET
					/// And more importantly, we do not do anthing with historical (yet); that is where this is important:
					/// to reset some daily things that will be done with running over historical bars.
					firstSignalRecevied = false;
					lastTradeDir = "None";
					Log($"\nNEW SESSION (DAY) - RESET EVERYTHING\n");
				}
				/// Close all trades at end of user-defined session
				if (UseCloseTime  &&  Position.MarketPosition != MarketPosition.Flat)
				{
					if (Time[0].TimeOfDay >= CloseTime.TimeOfDay)//  &&  Time[1].TimeOfDay < CloseTime.TimeOfDay)
					{
						// This does not seem to be working... Add logs
						Log($"Time[0] ({Time[0].TimeOfDay.ToString()}) >= CloseTime.TimeOfDay ({CloseTime.TimeOfDay.ToString()})");//  &&  Time[1] ({Time[1].TimeOfDay.ToString()}) < CloseTime.TimeOfDay");
						Log($"End of Session Close activated @ {DateTime.Now.ToString()}; Closing all open orders");
						ClosePosition(Position.MarketPosition.ToString(), "EoD Close");
					}
				}
			}

			/// Entered on each tick (third data series)
			else if (BarsInProgress == 1)
			{
				// Check each tick whether we are in-session (when it can place new trades)
				inSession = CheckSession();
				
				/// If not in market and not in-session (or limit hit), no need to go further
				if (!inSession  &&  manualSignal == "None"  &&  Position.MarketPosition == MarketPosition.Flat)
					return;
				
				#region MAIN SIGNAL ENTRY
				signalDir = "None";
				if (entriesEnabled)
				{
					if (manualSignal == "Long"  ||  manualSignal == "Short")
						signalDir = manualSignal;
					
					/// If URL read fails, increase checkSeconds & nextCheckTime time
					if (DateTime.Now >= nextCheckTime  ||  manualSignal != "None")
					{
						//Log($"Checked NOW ( {DateTime.Now.ToString()} ) against nextCheckTime ( {nextCheckTime.ToString()} ) - CALLING CheckSignal()");
						// OLD METHOD:
						//urlRetries = 0;
						//CheckSignalSync();  // This will wait for the async method to complete
						
						/// New DB method
						if (manualSignal == "None")
				            ProcessSignal();
						nextCheckTime = DateTime.Now.AddSeconds(checkSeconds);
						//Log($"Back from CheckSignalSync, signalDir = {signalDir}");

						if (signalDir != "None")
						{
							string extra = (manualSignal == "None") ? "" : "[MANUAL] ";
							Log(extra + $"Jacob Signal Received @ {DateTime.Now.ToString()} - signalDir = {signalDir}; checking trade filters...");
							
							PlaceMarketOrder(signalDir, Quantity);
							lastTradeDir = signalDir;
							manualSignal = "None";
						}
					}
				}
				#endregion
			}
		}
		
		#region JACOB SIGNAL
		/// This class needed for JSON parsing
		public class GreeksData8
		{
			public DateTime Est_time { get; set; }
			public int gammaflip { get; set; }
			public int deltaflip { get; set; }
			public int thetaflip { get; set; }
			public int vegaflip { get; set; }
		}
		
		private void ConnectToDatabase()
		{
			try
			{
				postgreSqlConnection = new NpgsqlConnection(connectionString);
				postgreSqlConnection.Open();
				Log("Connected to PostgreSQL database.", LogLevel.Information);
			}
			catch (Exception ex)
			{
				Log($"Error connecting to database: {ex.Message}", LogLevel.Error);
			}
		}
		
		private void CloseDatabaseConnection()
		{
			if (postgreSqlConnection != null)
			{
				postgreSqlConnection.Close();
				postgreSqlConnection.Dispose();
				Log("Closed database connection.", LogLevel.Information);
			}
		}

		private void FetchSignalsFromDatabase()
		{
			if (postgreSqlConnection == null)
			{
				Log($"NO POSTGRES SQL CONNECTION");
				return;
			}
			
			try
			{
				string query = @"
					SELECT est_time, gammaflip, deltaflip, thetaflip, vegaflip 
					FROM es_signals 
					WHERE est_time::date = current_date 
					ORDER BY est_time DESC LIMIT 1;";

				using (var cmd = new NpgsqlCommand(query, postgreSqlConnection))
				{
					//Log($"Using 1");
					using (var reader = cmd.ExecuteReader())
					{
						Log($"Using 2");
						if (reader.Read())
						{
							Log($"reader Read");
							var signal = new GreeksData8
							{
								Est_time = reader.GetDateTime(0),
								gammaflip = reader.GetInt32(1),
								deltaflip = reader.GetInt32(2),
								thetaflip = reader.GetInt32(3),
								vegaflip = reader.GetInt32(4)
							};
							signalList.Add(signal);
							Log($"Fetched signal: GammaFlip={signal.gammaflip}, DeltaFlip={signal.deltaflip}, ThetaFlip={signal.thetaflip}, VegaFlip={signal.vegaflip}", LogLevel.Information);
						}
					}
				}
			}
			catch (Exception ex)
			{
				Log($"Error fetching data from PostgreSQL: {ex.Message}", LogLevel.Error);
			}
        }

		private void ProcessSignal(bool log=false)
		{
			// Query DB
            FetchSignalsFromDatabase();
			
			try
			{
				/// Check if there are any signals in the list
	            if (signalList.Count == 0)
				{
					//Log($"No signals yet in signalList = {signalList}, count = {signalList.Count}");
					return;
				}
				
				/// For debugging, we log all [today's] symbols when button is pressed
				if (log)
				{
					Log($"================================");
					Log($"  Response from URL Query");
					Log($"================================");
					foreach (var sig in signalList)
					{
						if (sig.Est_time.Date != DateTime.Now.Date)
							continue;
						if (sig.gammaflip != 0)
						{
							string dir = (sig.gammaflip > 0) ? "Long" : "Short";
							Log($"  Time: {sig.Est_time.ToString()}      Direction: {dir}");
						}
					}
					Log($"================================");
					return;
				}
				
				/// Process the latest signal from the list
				var lastSignal = signalList[signalList.Count - 1];
			
				/// If the last signal seen isn't even today, do nothing (wait)
				if (lastSignal.Est_time.Date != DateTime.Now.Date)
					return;
				
				/// ---------------------------------------------------------------------
				/// NOTE: Unlike Ladder code, we do nothing to get "Prevailing Direction" 
				/// because Obsidian does not have continuation trades option, so we 
				/// really only need to track it as trivia to output to OCD
				/// ---------------------------------------------------------------------
				
				/// If signal is zero, then it's just continuation; no new signal
				if (lastSignal.gammaflip == 0)
				{
					/// Update prvSignalTime to the current signal's timestamp
					/// (doesn't really matter unless prvSignalTime is zero)
					if (lastSignal.Est_time > prvSignalTime)
						prvSignalTime = lastSignal.Est_time;
					return;
				}
				
				/// If most recent signal (regardless of validity) is same as current market direction, skip
				/// This is a quick shortcut made in an attempt to reduce redundant debug logging
				if (Position.MarketPosition != MarketPosition.Flat)
				{
					if ((lastSignal.gammaflip ==  1  &&  Position.MarketPosition == MarketPosition.Long)
					||  (lastSignal.gammaflip == -1  &&  Position.MarketPosition == MarketPosition.Short))
						return;
				}
				
				/// Check if the last signal's timestamp is newer than the previous one
				if (lastSignal.Est_time > prvSignalTime)
				{
					/// Update prvSignalTime to the current signal's timestamp
					prvSignalTime = lastSignal.Est_time;
					
					/// Check that there was not too much delay between the signal post time and now
					DateTime myTimeZone = lastSignal.Est_time.AddHours(TimeZoneOffset);
					DateTime delayLimit = myTimeZone.AddSeconds(MaxDelaySeconds);
					DateTime currentTime = DateTime.Now;
					
					if (MaxDelaySeconds == -1)
						Log($"Ignoring delay between Current Time ( {currentTime.ToString()} ) & Adjusted Post Time ( {myTimeZone.ToString()} )");
					else if (currentTime > delayLimit)
					{
						Log($"Rejected: Difference between Current Time ( {currentTime.ToString()} ) & Adjusted Post Time ( {myTimeZone.ToString()} ) Too Large (Max {MaxDelaySeconds} seconds)");
						return;
					}
					
					/// Handle buy/sell signals based on gammaflip value
					signalDir = (lastSignal.gammaflip > 0) ? "Long" : "Short";
					Log($"Signal received: {signalDir} at {lastSignal.Est_time}");
					firstSignalRecevied = true;
				}
				else if (lastSignal.Est_time < prvSignalTime)
					Log($"SHOULD NOT HAPPEN : Rejected: Post Time ( {lastSignal.Est_time.ToString()} ) older than previous signal ( {prvSignalTime.ToString()} )");
				//else Log($"Last signal rejected: Post Time ( {lastSignal.Est_time.ToString()} ) is the same as previous signal ( {prvSignalTime.ToString()} )");
		    }
			catch (Exception ex)
			{
				Log("Error parsing JSON response: " + ex.Message);
			}
		}

		#endregion
	
		#region TRADE MANAGEMENT
		private void PlaceMarketOrder(string dir, int qty)
		{
			Log($"Placing market order, dir = {dir}, qty = {qty}");
			if (Position.MarketPosition != MarketPosition.Flat  &&  Position.MarketPosition.ToString() != dir)
			{
				Log($"Existing position is {Position.MarketPosition.ToString()}, so closing position first");
				ClosePosition("CloseOnRev");
				
				// Assume the close worked; need to reset this NOW because it's multi-threaded
				stopsSubmitted = false;
			}
			var action = (dir == "Long") ? OrderAction.Buy : OrderAction.SellShort;
			SubmitOrderUnmanaged(0, action, OrderType.Market, qty, 0, 0, "", "Entry");
		}
		
		private void ClosePosition(string name="Close", string dir="", int qty=0)
		{
			if (Position.MarketPosition == MarketPosition.Flat)
				return;
			
			if (qty == 0)	qty = Position.Quantity;
			if (dir == "")	dir = Position.MarketPosition.ToString();
			var action = (dir == "Long") ? OrderAction.Sell : OrderAction.BuyToCover;
			
			if (qty == Position.Quantity)
			{
				if (slOrder != null  ||  tpOrder != null)
				{
					Log($"Closing all contracts, and slOrder != null  ||  tpOrder != null, so Cancelling Stops");
					CancelStops();
				}
			}
			Log($"Closing Position: name = {name}, dir = {dir}, qty = {qty}");
			SubmitOrderUnmanaged(0, action, OrderType.Market, qty, 0, 0, "", name);
		}

		private void ModifyStopLoss(double newSL)
		{
			/// If there is no existing SL, we must place the order
			if (slOrder == null)
			{
				var action = (Position.MarketPosition == MarketPosition.Long) ? OrderAction.Sell : OrderAction.BuyToCover;
				oco = (tpOrder != null) ? tpOrder.Oco : "";
				Log($"No SL existed on Long position, so placing new SL order, price @ {newSL}");
				SubmitOrderUnmanaged(0, action, OrderType.StopMarket, Position.Quantity, 0, newSL, oco, "StopLoss");
			}
			else
			{
				Log($"Moving SL to new price @ {newSL} (either trail or move to breakeven)");
				ChangeOrder(slOrder, Position.Quantity, 0, newSL);
			}
		}

		private void ModifyTakeProfit3(double newTP)
		{
			/// If there is no existing TP (e.g. No TakeProfit3 set, but TS_TakeProfitTicks set), then we must place the order
			if (tpOrder == null)
			{
				var action = (Position.MarketPosition == MarketPosition.Long) ? OrderAction.Sell : OrderAction.BuyToCover;
				oco = (slOrder != null) ? slOrder.Oco : "";
				Log($"No TP existed on Long position, so placing new TP order, price @ {newTP}");
				SubmitOrderUnmanaged(0, action, OrderType.Limit, Position.Quantity, newTP, 0, oco, "TakeProfit");
			}
			else
			{
				Log($"Moving TP3 to new price @ {newTP}");
				ChangeOrder(tpOrder, Position.Quantity, newTP, 0);
			}
		}
		
		private void CancelStops()
		{
			if (slOrder != null)
			{
				//Log($"Cancelling slOrder");
				CancelOrder(slOrder);
				slOrder = null;
			}
			if (tpOrder != null)
			{
				//Log($"Cancelling tpOrder");
				CancelOrder(tpOrder);
				tpOrder = null;
			}
		}
		
		private double AvePrice()
		{
			double avePrice = Position.AveragePrice;
			
			/// If ave price does not fall on tick boundary, round up
			int numTicks = (int)(avePrice / TickSize);
			if (numTicks * TickSize != avePrice)
			{
				double prc = Instrument.MasterInstrument.RoundToTickSize(avePrice);
				Log($"\n      AvePrice between ticks, Rounding, from {avePrice} to {prc}\n\n");
				avePrice = prc;
			}
			return(avePrice);
		}
		#endregion
		
		#region DEBUG & OCD
		private void ManageOCD()
		{
			string text = $"\n\n Strategy Name            :   {StrategyName}";
			text += $"\n Trade Session Active   :   {inSession}";
			text += $"\n Last Signal Direction   :   {lastTradeDir}";

			if (nextCheckTime != DateTime.MinValue)
			{
				TimeSpan remaining = nextCheckTime - DateTime.Now;
				text += $"\n\n Next Signal Check in    :   {remaining.ToString(@"hh\:mm\:ss")}";
			}

			if (lastDashboard == text)
				return;
			
			Draw.TextFixed(this, "OCD", text, TextPosition.TopLeft);	
			lastDashboard = text;
		}
		
		private void Log(string message, [CallerMemberName] string memberName = "")
		{
			if (DisableLogging  ||  lastLogMsg == message)
				return;

			// Set this scripts Print() calls to the second output tab?
			if (UseOutput2)		PrintTo = PrintTo.OutputTab2;
			else				PrintTo = PrintTo.OutputTab1;
			
			string id = "EA #" + UniqueID;
			
			DateTime date = (State == State.Historical) ? Time[0] : DateTime.Now;
			string dateStr = (State == State.Historical) ? date.ToString() : date.ToString(@"HH\:mm\:ss");
			
			if (lastCaller != memberName)
			{
				Print($"\n{id} - {chartInstrument} - [{memberName}]  -  {dateStr}  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -\n");
				Print(message);
			}
			else if (lastLogMsg != message)
			{
				if (lastLogTime != dateStr)	// Output just time if diff time but not new caller
					Print(message + $"   ( {dateStr} )");
				else
					Print(message);
			}

			lastLogMsg = message;
			lastCaller = memberName;
			lastLogTime = dateStr;
		}		
		
		#endregion
		
		/// Check trading sessions; return false if out-of-session
		private bool CheckSession()
		{
			if (!LimitTradingHours)
				return true;
			
			bool isInSession = false; 
			if (StartTime.TimeOfDay < EndTime.TimeOfDay)
			{
				if (Time[0].TimeOfDay >= StartTime.TimeOfDay  &&  Time[0].TimeOfDay < EndTime.TimeOfDay)
					isInSession = true;
			}
			else if (StartTime.TimeOfDay > EndTime.TimeOfDay)
			{
				if (Time[0].TimeOfDay >= StartTime.TimeOfDay  ||  Time[0].TimeOfDay < EndTime.TimeOfDay)
					isInSession = true;
			}
			else // (StartTime1.TimeOfDay == EndTime1.TimeOfDay)
			{
				Log($"Start Time 1 is the same as End Time 1; Trading [always] approved");
				isInSession = true;
			}
			return isInSession;
		}
		
		#region BUTTONS
		private void OnMyButtonClick(object sender, RoutedEventArgs rea)
		{
			System.Windows.Controls.Button button = sender as System.Windows.Controls.Button;
			
			Log("\n");
			bool lWasOn = longOn;
			bool sWasOn = shortOn;
			if (button.Name == "longButton")
			{
				if (longOn)
				{
					button.Background = Brushes.Gray;
					button.Content = "Long Disabled";
					longOn = false;
					Log("\nLONG ENTRIES DISABLED!");
				}
				else
				{
					button.Background = Brushes.Green;
					button.Content = "Long Enabled";
					longOn = true;
					Log("\nLong entries Enabled!");
				}
			}
			else if (button.Name == "shortButton")
			{
				if (shortOn)
				{
					button.Background = Brushes.Gray;
					button.Content = "Short Disabled";
					shortOn = false;
					Log("\nSHORT ENTRIES DISABLED!");
				}
				else
				{
					button.Background = Brushes.Crimson;
					button.Content = "Short Enabled";
					shortOn = true;
					Log("\nShort entries Enabled!");
				}
			}
			else if (button.Name == "exitButton")
			{
				if (Position.MarketPosition == MarketPosition.Flat)
				Log("\nUser-Instigated Close of All Open Strategy Positions, but nothing open..");
				else
				{
					Log("\n\n=====================================================");
					Log("User-Instigated Close of All Open Strategy Positions");
					Log("=====================================================\n");
					ClosePosition();
				}
			}
			else if (button.Name == "logSignalsButton")
			{
				ProcessSignal(true);
			}
			if (button.Name == "manualEnterLong")
			{
				Log($"    ....MANUAL LONG ENTRY ON #{UniqueID}....");
				manualSignal = "Long";
			}
			else if (button.Name == "manualEnterShort")
			{
				Log($"    ....MANUAL SHORT ENTRY ON #{UniqueID}....");
				manualSignal = "Short";
			}
			
			if (!longOn  &&  !shortOn)
				if (lWasOn  ||  sWasOn)
					Log("ALL ENTRIES DISABLED!");
			else if (longOn  &&  shortOn)
				if (!lWasOn  ||  !sWasOn)
					Log("ALL ENTRIES ENABLED!");
			Log("\n");

			entriesEnabled = (longOn  ||  shortOn);
		}
		
		
		protected void CreateButtons()
		{
			if (UserControlCollection.Contains(myGrid))
		        return;
		
			myGrid = new System.Windows.Controls.Grid
	        {
	          Name = "MyCustomGrid",
	          HorizontalAlignment = HorizontalAlignment.Left,
	          VerticalAlignment = VerticalAlignment.Bottom,
	        };
	 	 
	        myGrid.ColumnDefinitions.Add(new System.Windows.Controls.ColumnDefinition());
	        myGrid.ColumnDefinitions.Add(new System.Windows.Controls.ColumnDefinition());
	        myGrid.ColumnDefinitions.Add(new System.Windows.Controls.ColumnDefinition());
			myGrid.RowDefinitions.Add(new System.Windows.Controls.RowDefinition());
			myGrid.RowDefinitions.Add(new System.Windows.Controls.RowDefinition());
			myGrid.ColumnDefinitions[0].Width = new GridLength(80);
			myGrid.ColumnDefinitions[1].Width = new GridLength(80);
			myGrid.ColumnDefinitions[2].Width = new GridLength(80);
			myGrid.RowDefinitions[0].Height = new GridLength(25);
			myGrid.RowDefinitions[1].Height = new GridLength(25);
	 
	        longButton = new System.Windows.Controls.Button
	        {
	          	Name = "longButton",
	          	Content = "Long Enabled",
	          	Foreground = Brushes.White,
	          	Background = Brushes.Green,
				Height = 25,
				Width = 80,
				FontSize = 10,
				HorizontalAlignment = HorizontalAlignment.Center,
				Focusable = false
	        };
			
			shortButton = new System.Windows.Controls.Button
	        {
	          	Name = "shortButton",
	          	Content = "Short Enabled",
	          	Foreground = Brushes.White,
	          	Background = Brushes.Firebrick,
				Height = 25,
				Width = 80,
				FontSize = 10,
				HorizontalAlignment = HorizontalAlignment.Center,
				Focusable = false
	        };
	 
			exitButton = new System.Windows.Controls.Button
	        {
	          	Name = "exitButton",
	          	Content = "Exit All",
	          	Foreground = Brushes.White,
	          	Background = Brushes.DarkMagenta,
				Height = 25,
				Width = 80,
				FontSize = 10,
				HorizontalAlignment = HorizontalAlignment.Center,
				Focusable = false
	        };
	 
			logSignalsButton = new System.Windows.Controls.Button
	        {
	          	Name = "logSignalsButton",
	          	Content = "Log Signals",
	          	Foreground = Brushes.White,
	          	Background = Brushes.Blue,
				Height = 25,
				Width = 80,
				FontSize = 10,
				HorizontalAlignment = HorizontalAlignment.Center,
				Focusable = false
	        };
	 
			manualEnterLong = new System.Windows.Controls.Button
	        {
	          	Name = "manualEnterLong",
	          	Content = "Enter Long",
	          	Foreground = Brushes.Black,
	          	Background = Brushes.Lime,
				Height = 25,
				Width = 80,
				FontSize = 10,
				HorizontalAlignment = HorizontalAlignment.Center,
				Focusable = false
	        };
	 
			manualEnterShort = new System.Windows.Controls.Button
	        {
	          	Name = "manualEnterShort",
	          	Content = "Enter Short",
	          	Foreground = Brushes.White,
	          	Background = Brushes.Crimson,
				Height = 25,
				Width = 80,
				FontSize = 10,
				HorizontalAlignment = HorizontalAlignment.Center,
				Focusable = false
	        };
			
			longButton.Click += OnMyButtonClick;
			shortButton.Click += OnMyButtonClick;
			exitButton.Click += OnMyButtonClick;
			logSignalsButton.Click += OnMyButtonClick;
			manualEnterLong.Click += OnMyButtonClick;
			manualEnterShort.Click += OnMyButtonClick;
			
	        System.Windows.Controls.Grid.SetColumn(shortButton, 0);
			System.Windows.Controls.Grid.SetRow(shortButton, 1);
			System.Windows.Controls.Grid.SetColumn(longButton, 0);
			System.Windows.Controls.Grid.SetRow(longButton, 0);
			System.Windows.Controls.Grid.SetColumn(exitButton, 1);
			System.Windows.Controls.Grid.SetRow(exitButton, 0);
			System.Windows.Controls.Grid.SetColumn(logSignalsButton, 1);
			System.Windows.Controls.Grid.SetRow(logSignalsButton, 1);
			System.Windows.Controls.Grid.SetColumn(manualEnterLong, 2);
			System.Windows.Controls.Grid.SetRow(manualEnterLong, 0);
			System.Windows.Controls.Grid.SetColumn(manualEnterShort, 2);
			System.Windows.Controls.Grid.SetRow(manualEnterShort, 1);
			
	        myGrid.Children.Add(longButton);
			myGrid.Children.Add(shortButton);
			myGrid.Children.Add(exitButton);
			myGrid.Children.Add(logSignalsButton);
			myGrid.Children.Add(manualEnterLong);
			myGrid.Children.Add(manualEnterShort);

			UserControlCollection.Add(myGrid);
		}
		
		
		public void DisposeButtons() 
		{
			if (myGrid != null)
	        {
				if (longButton != null)
				{
					myGrid.Children.Remove(longButton);
					longButton.Click -= OnMyButtonClick;
					longButton = null;
				}
				if (shortButton != null)
				{
					myGrid.Children.Remove(shortButton);
					shortButton.Click -= OnMyButtonClick;
					shortButton = null;
				}
				if (exitButton != null)
				{
					myGrid.Children.Remove(exitButton);
					exitButton.Click -= OnMyButtonClick;
					exitButton = null;
				}
				if (logSignalsButton != null)
				{
					myGrid.Children.Remove(logSignalsButton);
					logSignalsButton.Click -= OnMyButtonClick;
					logSignalsButton = null;
				}
				if (manualEnterLong != null)
				{
					myGrid.Children.Remove(manualEnterLong);
					manualEnterLong.Click -= OnMyButtonClick;
					manualEnterLong = null;
				}
				if (manualEnterShort != null)
				{
					myGrid.Children.Remove(manualEnterShort);
					manualEnterShort.Click -= OnMyButtonClick;
					manualEnterShort = null;
				}
	        }
		}
		#endregion

		#region PROPERTIES
		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name = "Order Quantity", Description = "The initial number of contracts to place per order", Order=0, GroupName = "01. Entry")]
		public int Quantity
		{ get; set; }
		
		[NinjaScriptProperty]
        [Display(Name = "Last Trade Direction", Description = "Override for initial Last Trade Direction", Order=1, GroupName = "01. Entry")]
        public string LastTradeDir
        { get; set; }

		[NinjaScriptProperty]
		[Range(1, 300)]
		[Display(Name = "Check URL Interval", Description = "How many seconds between URL signal checks", Order=2, GroupName = "01. Entry")]
		public int CheckSeconds
		{ get; set; }

		[NinjaScriptProperty]
		[Range(1, 300)]
		[Display(Name = "Signal Delay Max Seconds", Description = "Maximum seconds allowed between signal time stamp and entry time", Order=3, GroupName = "01. Entry")]
		public int MaxDelaySeconds
		{ get; set; }

		[NinjaScriptProperty]
		[Range(-23, 23)]
		[Display(Name = "Timezone Offset", Description = "Number of hours to add the signal PostTime to compare with local time", Order=4, GroupName = "01. Entry")]
		public int TimeZoneOffset
		{ get; set; }

		
		
		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name = "Stop Loss", Description = "Stop loss in ticks", Order=10, GroupName = "02. Exits")]
		public int StopLoss
		{ get; set; }
		
		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name = "Take Profit", Description = "Take profit in ticks", Order=15, GroupName = "02. Exits")]
		public int TakeProfit
		{ get; set; }

		
		
		[NinjaScriptProperty]
        [Display(Name = "Limit Trading Hours", Description = "Use Trading Session #1", Order=20, GroupName = "03. Trading Hours")]
        public bool LimitTradingHours
        { get; set; }

        [NinjaScriptProperty]
        [PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
        [Display(Name = "Start Time", Description = "Enter Start Time for Entries", Order=21, GroupName = "03. Trading Hours")]
        public DateTime StartTime
        { get; set; }

        [NinjaScriptProperty]
        [PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
        [Display(Name = "End Time", Description = "Enter End Time for Entries", Order=22, GroupName = "03. Trading Hours")]
        public DateTime EndTime
        { get; set; }

		[NinjaScriptProperty]
        [Display(Name = "Use Close Time", Description = "Enable to close all trades at given time", Order=23, GroupName = "03. Trading Hours")]
        public bool UseCloseTime
        { get; set; }

        [NinjaScriptProperty]
        [PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
        [Display(Name = "Close Time", Description = "Enter Time to Close All Trades", Order=24, GroupName = "03. Trading Hours")]
        public DateTime CloseTime
        { get; set; }

		
		
		[NinjaScriptProperty]
        [Display(Name = "Display OCD", Description = "Show the On-Chart Display", Order=30, GroupName = "04. Misc / Debug")]
        public bool DisplayOCD
        { get; set; }

		[NinjaScriptProperty]
        [Display(Name = "Strategy Name", Description = "Name of Strategy", Order=31, GroupName = "04. Misc / Debug")]
        public string StrategyName
        { get; set; }

		[NinjaScriptProperty]
        [Display(Name = "Disable Logging", Description = "Disable logging to Ninjascript Output windows", Order=32, GroupName = "04. Misc / Debug")]
        public bool DisableLogging
        { get; set; }

		[NinjaScriptProperty]
        [Display(Name = "Use Output 2", Description = "Send logging to second Output window", Order=33, GroupName = "04. Misc / Debug")]
        public bool UseOutput2
        { get; set; }

		[NinjaScriptProperty]
        [Display(Name = "Unique ID", Description = "Unique number to identify strategy in Output window", Order=34, GroupName = "04. Misc / Debug")]
        public string UniqueID
        { get; set; }
		#endregion
	}
}
